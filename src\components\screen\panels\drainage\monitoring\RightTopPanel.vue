<template>
  <PanelBox title="报警类型统计">
    <template #extra>
      <div class="com-select">
        <CommonSelect v-model="timeRange" :options="timeOptions" @change="handleTimeChange" />
      </div>
    </template>
    <div class="panel-content">
      <div class="chart-header">
        <div class="chart-legend">
          <div class="legend-item">
            <span class="legend-icon total"></span>
            <span class="legend-text">总数</span>
          </div>
          <div class="legend-item">
            <span class="legend-icon deployed"></span>
            <span class="legend-text">已处置</span>
          </div>
        </div>
      </div>
      <div class="chart-wrapper" ref="chartRef"></div>
    </div>
  </PanelBox>
</template>

<script setup>
import { ref, onMounted, onUnmounted, reactive, nextTick } from 'vue'
import * as echarts from 'echarts'
import PanelBox from '@/components/screen/PanelBox.vue'
import CommonSelect from '@/components/screen/CommonSelect.vue'

// 时间选择
const timeRange = ref('week')
const timeOptions = [
  { label: '近一周', value: 'week' },
  { label: '近一月', value: 'month' },
  { label: '近一年', value: 'year' }
]

// 页面可见性状态
let isPageVisible = true
// 定时器ID
let refreshTimer = null

// 图表DOM引用
const chartRef = ref(null)
let chartInstance = null

// 不同时间范围的数据
const timeRangeData = {
  week: {
    categories: ['一级报警', '二级报警', '三级报警'],
    series: [
      { name: '总数', values: [65, 68, 66] },
      { name: '已处置', values: [50, 48, 50] }
    ]
  },
  month: {
    categories: ['一级报警', '二级报警', '三级报警'],
    series: [
      { name: '总数', values: [85, 88, 82] },
      { name: '已处置', values: [60, 58, 60] }
    ]
  },
  year: {
    categories: ['一级报警', '二级报警', '三级报警'],
    series: [
      { name: '总数', values: [120, 125, 118] },
      { name: '已处置', values: [90, 88, 85] }
    ]
  }
}

// 图表数据
const chartData = reactive({
  categories: timeRangeData.week.categories,
  series: timeRangeData.week.series
})

// 处理时间范围变化
const handleTimeChange = (value) => {
  console.log('时间范围变更为:', value)
  
  // 更新图表数据
  chartData.categories = timeRangeData[value].categories
  chartData.series[0].values = timeRangeData[value].series[0].values
  chartData.series[1].values = timeRangeData[value].series[1].values
  
  // 更新图表
  updateChart()
}

// 更新图表
const updateChart = () => {
  if (!chartInstance) return
  
  const option = createChartOption()
  chartInstance.setOption(option)
}

// 创建图表配置
const createChartOption = () => {
  return {
    backgroundColor: 'transparent',
    grid: {
      top: '17%',
      left: '3%',
      right: '4%',
      bottom: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: chartData.categories,
      axisLine: {
        lineStyle: {
          color: '#5F5F60',
          width: 1
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#FFFFFF',
        fontSize: 12,
        margin: 16,
        rotate: 0
      }
    },
    yAxis: {
      type: 'value',
      name: '单位(个)',
      nameTextStyle: {
        color: '#FFFFFF',
        fontSize: 12,
        padding: [0, -18, 0, 0]
      },
      max: 100,
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)',
          type: 'dashed'
        }
      },
      axisLabel: {
        color: '#FFFFFF',
        fontSize: 12,
        formatter: '{value}'
      }
    },
    series: [
      {
        name: '总数',
        type: 'bar',
        data: chartData.series[0].values,
        barWidth: 19,
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: '#7AAFFF' },
              { offset: 1, color: '#055ADB' }
            ]
          },
          borderRadius: [2, 2, 0, 0],
          borderColor: '#0A93FF',
          borderWidth: 1,
          shadowColor: 'rgba(5, 122, 255, 0.3)',
          shadowBlur: 10,
          shadowOffsetX: 3,
          shadowOffsetY: 3
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 15,
            shadowColor: 'rgba(5, 90, 219, 0.7)'
          }
        },
        showBackground: true,
        backgroundStyle: {
          color: 'rgba(5, 90, 219, 0.2)',
          borderRadius: [0, 0, 0, 0]
        }
      },
      {
        name: '已处置',
        type: 'bar',
        data: chartData.series[1].values,
        barWidth: 19,
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: '#FFFFFF' },
              { offset: 1, color: '#23CAFF' }
            ]
          },
          borderRadius: [2, 2, 0, 0],
          borderColor: '#34D6FF',
          borderWidth: 1,
          shadowColor: 'rgba(35, 202, 255, 0.3)',
          shadowBlur: 10,
          shadowOffsetX: 3,
          shadowOffsetY: 3
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 15,
            shadowColor: 'rgba(35, 202, 255, 0.7)'
          }
        },
        showBackground: true,
        backgroundStyle: {
          color: 'rgba(35, 202, 255, 0.2)',
          borderRadius: [0, 0, 0, 0]
        }
      }
    ]
  };
}

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return
  
  chartInstance = echarts.init(chartRef.value)
  const option = createChartOption()
  chartInstance.setOption(option)
  
  window.addEventListener('resize', handleResize)
}

// 处理窗口大小调整
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

// 处理页面可见性变化
const handleVisibilityChange = () => {
  if (document.hidden) {
    isPageVisible = false
    // 页面不可见时清除定时器
    if (refreshTimer) {
      clearInterval(refreshTimer)
      refreshTimer = null
    }
  } else {
    isPageVisible = true
    // 页面可见时重新启动定时器
    startDataRefresh()
  }
}

// 启动数据刷新
const startDataRefresh = () => {
  if (!refreshTimer && isPageVisible) {
    const refreshInterval = 600000 // 10分钟刷新一次
    refreshTimer = setInterval(() => {
      // 这里可以添加实际的数据刷新逻辑
      // 模拟数据更新
      updateChart()
    }, refreshInterval)
  }
}

onMounted(async () => {
  await nextTick()
  // 初始化图表
  initChart()
  // 启动数据刷新
  startDataRefresh()
  // 添加页面可见性变化监听
  document.addEventListener('visibilitychange', handleVisibilityChange)
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
  // 清理事件监听和定时器
  window.removeEventListener('resize', handleResize)
  document.removeEventListener('visibilitychange', handleVisibilityChange)
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
})
</script>

<style scoped>
.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.com-select {
  margin-right: 20px;
}

.chart-header {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.chart-legend {
  display: flex;
  gap: 15px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.legend-icon {
  width: 10px;
  height: 10px;
  border-radius: 2px;
}

.legend-icon.total {
  background: #055ADB;
}

.legend-icon.deployed {
  background: #23CAFF;
}

.legend-text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
}

.chart-wrapper {
  flex: 1;
  width: 100%;
  height: 100%;
  min-height: 180px;
}

/* 媒体查询适配不同分辨率 */
@media screen and (min-width: 1920px) and (max-width: 2560px) {
  .panel-content {
    padding: 15px;
  }
  
  .chart-wrapper {
    min-height: 200px;
  }
}

@media screen and (max-width: 1919px) {
  .panel-content {
    padding: 12px;
  }
  
  .chart-wrapper {
    min-height: 160px;
  }
}

@media screen and (min-width: 2561px) {
  .panel-content {
    padding: 18px;
  }
  
  .chart-wrapper {
    min-height: 240px;
  }
}

/* 添加全屏模式(1080px高度)的适配 */
@media screen and (min-height: 1056px) and (max-height: 1080px) {
  .panel-content {
    padding: 15px;
  }
  
  .chart-wrapper {
    min-height: 210px;
  }
}

@media screen and (min-height: 940px) and (max-height: 1055px) {
  .panel-content {
    padding: 10px;
    gap: 10px;
  }
  
  .chart-wrapper {
    min-height: 190px;
  }
}

@media screen and (min-height: 900px) and (max-height: 940px) {
  .panel-content {
    padding: 8px;
    gap: 8px;
  }
  
  .chart-wrapper {
    min-height: 170px;
  }
}
</style> 