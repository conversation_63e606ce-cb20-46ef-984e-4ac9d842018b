<template>
  <PanelBox title="高发报警设备" class="drainage-monitoring-right-bottom-panel">
    <template #extra>
      <div class="com-select">
        <CommonSelect v-model="timeRange" :options="timeOptions" @change="handleTimeChange" />
      </div>
    </template>
    <div class="panel-content">
      <!-- 使用滚动表格组件 -->
      <ScrollTable 
        :columns="tableColumns" 
        :data="deviceList" 
        :autoScroll="true" 
        :scrollSpeed="3000"
        :tableHeight="tableHeight" 
        :visibleRows="4" 
        @row-click="openDetailModal"
      >
        <!-- 自定义排名列 -->
        <template #ranking="{ row, index }">
          <div class="rank-box" :class="`rank-${row.ranking}`">
            {{ row.ranking }}
          </div>
        </template>
      </ScrollTable>
    </div>
  </PanelBox>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import PanelBox from '@/components/screen/PanelBox.vue'
import CommonSelect from '@/components/screen/CommonSelect.vue'
import ScrollTable from '@/components/screen/common/ScrollTable.vue'

// 时间选择
const timeRange = ref('week')
const timeOptions = [
  { label: '近一周', value: 'week' },
  { label: '近一月', value: 'month' },
  { label: '近一年', value: 'year' }
]

// 表格列配置
const tableColumns = [
  { title: '排名', dataIndex: 'ranking', width: '10%', fontSize: '13px' },
  { title: '设备名称', dataIndex: 'deviceName', width: '50%', fontSize: '13px' },
  { title: '报警数量', dataIndex: 'alarmCount', width: '15%', fontSize: '13px' },
  { title: '处置完成率', dataIndex: 'resolveRate', width: '25%', fontSize: '13px' }
]

// 动态计算表格高度
const tableHeight = computed(() => {
  // 屏幕高度小于 1055px 时，表格高度为 225px 
  if (window.innerHeight < 1055) {
    return '220px'
  }
  if (window.innerHeight > 1054) {
    return '295px'
  }
  return '315px' // 可以根据不同分辨率动态调整
})

// 当前展示的设备列表数据
const deviceList = ref([])

// 处理时间范围变化
const handleTimeChange = (value) => {
  console.log('时间范围变更为:', value)
  generateMockData()
}

// 生成模拟数据
const generateMockData = () => {
  const mockData = []
  const baseCount = timeRange.value === 'week' ? 50 : timeRange.value === 'month' ? 200 : 1000
  
  for (let i = 1; i <= 10; i++) {
    mockData.push({
      ranking: i,
      deviceName:i<6 ?  `固定点式激光甲烷气体监测仪${i}` : `井盖位移传感器00${i-1}`,
      alarmCount: Math.floor(baseCount * (1 - (i - 1) * 0.1) + Math.random() * 20),
      resolveRate: `${Math.floor(80 + Math.random() * 20)}%`,
      deviceId: `device_${i}`
    })
  }
  
  deviceList.value = mockData
}

// 打开详情弹窗
const openDetailModal = (row) => {
  console.log('打开设备详情', row.deviceId)
  // 实际项目中可能需要打开设备详情弹窗
}

onMounted(() => {
  generateMockData()
})
</script>

<style scoped>
.drainage-monitoring-right-bottom-panel {
  height: 340px; /* 默认高度为340px */
}

.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 15px; 
  position: relative;
}

.com-select {
  margin-right: 20px;
}

/* 排名样式 */
.rank-box {
  width: 21px;
  height: 21px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto;
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 14px;
  color: #FFFFFF;
}

.rank-1 {
  background: rgba(242,140,45,0.2);
  border: 1px solid #F28C2D;
}

.rank-2 {
  background: rgba(212,212,212,0.2);
  border: 1px solid #D4D4D4;
}

.rank-3 {
  background: rgba(254,208,138,0.2);
  border: 1px solid #FED08A;
}

.rank-4, .rank-5, .rank-6, .rank-7, .rank-8, .rank-9, .rank-10 {
  background: rgba(0,170,255,0.2);
  border: 1px solid #00AAFF;
  opacity: 0.7;
}

/* 点击行样式 */
:deep(.scroll-table tr) {
  cursor: pointer;
  transition: background-color 0.2s;
}

:deep(.scroll-table tr:hover) {
  background-color: rgba(0, 163, 255, 0.2) !important;
}

/* 媒体查询适配不同分辨率 */
@media screen and (min-width: 1920px) and (max-width: 2560px) {
  .drainage-monitoring-right-bottom-panel {
    height: 340px;
  }
  :deep(.scroll-table th) {
    font-size: 14px;
    height: 40px;
    line-height: 40px;
  }
  
  :deep(.scroll-table td) {
    font-size: 14px;
    height: 40px;
    line-height: 40px;
  }
}

@media screen and (max-width: 1919px) {
  .drainage-monitoring-right-bottom-panel {
    height: 320px;
  }
}

@media screen and (min-width: 2561px) {
  .drainage-monitoring-right-bottom-panel {
    height: 380px;
  }
} 

/* 940px-1055px高度的屏幕特别优化 */
@media screen and (min-height: 910px) and (max-height: 1055px) {
  .drainage-monitoring-right-bottom-panel {
    height: 320px;
  }
  
  .panel-content {
    padding: 5px 10px;
    gap: 0px;
  }
  
  .com-select {
    margin-right: 25px;
  }
  
  .rank-box {
    width: 19px;
    height: 19px;
    font-size: 13px;
  }
  
  :deep(.scroll-table th) {
    font-size: 13px;
    height: 36px;
    line-height: 36px;
  }
  
  :deep(.scroll-table td) {
    font-size: 13px;
    height: 36px;
    line-height: 36px;
  }
}
</style> 