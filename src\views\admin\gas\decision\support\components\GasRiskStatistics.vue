<template>
  <div class="gas-risk-statistics">
    <h3 class="component-title">管网汇总</h3>
    
    <!-- 顶部卡片区域 -->
    <div class="risk-cards">
      <div class="risk-card high">
        <div class="risk-value">{{ pipelineRiskData.bigRiskLength || 0 }}<span class="unit">KM</span></div>
        <div class="risk-label">重大风险</div>
      </div>
      <div class="risk-card medium">
        <div class="risk-value">{{ pipelineRiskData.largerRiskLength || 0 }}<span class="unit">KM</span></div>
        <div class="risk-label">较大风险</div>
      </div>
      <div class="risk-card normal">
        <div class="risk-value">{{ pipelineRiskData.generalRiskLength || 0 }}<span class="unit">KM</span></div>
        <div class="risk-label">一般风险</div>
      </div>
      <div class="risk-card low">
        <div class="risk-value">{{ pipelineRiskData.lowRiskLength || 0 }}<span class="unit">KM</span></div>
        <div class="risk-label">低风险</div>
      </div>
    </div>

    <!-- 主体内容区 -->
    <div class="risk-content">
      <!-- 左侧图表区域 -->
      <div class="chart-container">
        <div class="chart-header">
          <h4 class="chart-title">管网风险统计</h4>
          <div class="tab-group">
            <el-radio-group v-model="selectedTab" size="small" @change="handleTabChange">
              <el-radio-button label="按压力分级">按压力分级</el-radio-button>
              <el-radio-button label="按企业">按企业</el-radio-button>
            </el-radio-group>
          </div>
        </div>
        
        <div class="chart-content">
          <!-- 图表区域 -->
          <div class="chart-box" ref="chartRef"></div>
          
          <!-- 统计信息框 -->
          <div class="statistics-box">
            <div class="stat-item">
              <div class="stat-label">重大风险：</div>
              <div class="stat-value">{{ riskStats.high }}km</div>
            </div>
            <div class="stat-item">
              <div class="stat-label">较大风险：</div>
              <div class="stat-value">{{ riskStats.medium }}km</div>
            </div>
            <div class="stat-item">
              <div class="stat-label">一般风险：</div>
              <div class="stat-value">{{ riskStats.normal }}km</div>
            </div>
            <div class="stat-item">
              <div class="stat-label">低风险：</div>
              <div class="stat-value">{{ riskStats.low }}km</div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 右侧风险明细区域 -->
      <div class="risk-detail">
        <h4 class="detail-title">场站风险</h4>
        
        <div class="risk-detail-cards">
          <div class="detail-card high">
            <div class="detail-value">{{ getStationRiskCount('7001') }}<span class="unit">个</span></div>
            <div class="detail-label">重大风险</div>
          </div>
          <div class="detail-card medium">
            <div class="detail-value">{{ getStationRiskCount('7002') }}<span class="unit">个</span></div>
            <div class="detail-label">较大风险</div>
          </div>
          <div class="detail-card normal">
            <div class="detail-value">{{ getStationRiskCount('7003') }}<span class="unit">个</span></div>
            <div class="detail-label">一般风险</div>
          </div>
          <div class="detail-card low">
            <div class="detail-value">{{ getStationRiskCount('7004') }}<span class="unit">个</span></div>
            <div class="detail-label">低风险</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, nextTick, watch, computed } from 'vue';
import * as echarts from 'echarts';
import { 
  getPipelineRiskPipelineStatistics,
  getPipelineRiskPressureStatistics,
  getPipelineRiskEnterpriseStatistics,
  getStationRiskStatistics
} from '@/api/gas';

// 切换标签页
const selectedTab = ref('按压力分级');
// 图表引用
const chartRef = ref(null);
// 图表实例
let chartInstance = null;

// 风险数据
const pipelineRiskData = ref({
  bigRiskLength: 0,
  largerRiskLength: 0, 
  generalRiskLength: 0,
  lowRiskLength: 0
});

// 按压力分级的数据
const pressureData = ref({
  categories: [],
  series: [
    {
      name: '重大风险',
      data: [],
      color: '#FF0000'
    },
    {
      name: '较大风险',
      data: [],
      color: '#FFBF00'
    },
    {
      name: '一般风险',
      data: [],
      color: '#FFE500'
    },
    {
      name: '低风险',
      data: [],
      color: '#0086FF'
    }
  ],
  stats: {
    high: '0',
    medium: '0',
    normal: '0',
    low: '0'
  }
});

// 按企业的数据
const companyData = ref({
  categories: [],
  series: [
    {
      name: '重大风险',
      data: [],
      color: '#FF0000'
    },
    {
      name: '较大风险',
      data: [],
      color: '#FFBF00'
    },
    {
      name: '一般风险',
      data: [],
      color: '#FFE500'
    },
    {
      name: '低风险',
      data: [],
      color: '#0086FF'
    }
  ],
  stats: {
    high: '0',
    medium: '0',
    normal: '0',
    low: '0'
  }
});

// 场站风险数据
const stationRiskData = ref({
  riskLevelStatistics: []
});

// 获取场站风险数量
const getStationRiskCount = (code) => {
  if (!stationRiskData.value.riskLevelStatistics) return 0;
  const item = stationRiskData.value.riskLevelStatistics.find(item => item.code === code);
  return item ? item.count : 0;
};

// 计算当前选中类型的风险统计数据
const riskStats = computed(() => {
  return selectedTab.value === '按压力分级' 
    ? pressureData.value.stats 
    : companyData.value.stats;
});

// 获取管网风险管线统计数据
const fetchPipelineRiskData = async () => {
  try {
    const res = await getPipelineRiskPipelineStatistics();
    if (res.code === 200 && res.data) {
      pipelineRiskData.value = res.data;
    }
  } catch (error) {
    console.error('获取管网风险统计数据失败:', error);
  }
};

// 获取管网风险按压力级别统计数据
const fetchPressureRiskData = async () => {
  try {
    const res = await getPipelineRiskPressureStatistics();
    if (res.code === 200 && res.data && res.data.pressureRiskStatistics) {
      const stats = res.data.pressureRiskStatistics;
      
      // 提取分类
      pressureData.value.categories = stats.map(item => item.pressureLevelName);
      
      // 提取各风险级别数据
      pressureData.value.series[0].data = stats.map(item => item.bigRiskLength || 0);
      pressureData.value.series[1].data = stats.map(item => item.largerRiskLength || 0);
      pressureData.value.series[2].data = stats.map(item => item.generalRiskLength || 0);
      pressureData.value.series[3].data = stats.map(item => item.lowRiskLength || 0);
      
      // 计算总风险数据
      const totalHigh = stats.reduce((sum, item) => sum + (item.bigRiskLength || 0), 0);
      const totalMedium = stats.reduce((sum, item) => sum + (item.largerRiskLength || 0), 0);
      const totalNormal = stats.reduce((sum, item) => sum + (item.generalRiskLength || 0), 0);
      const totalLow = stats.reduce((sum, item) => sum + (item.lowRiskLength || 0), 0);
      
      pressureData.value.stats = {
        high: totalHigh.toString(),
        medium: totalMedium.toString(),
        normal: totalNormal.toString(),
        low: totalLow.toString()
      };
      
      // 如果当前是按压力分级，则更新图表
      if (selectedTab.value === '按压力分级' && chartInstance) {
        updateChartData('按压力分级');
      }
    }
  } catch (error) {
    console.error('获取管网风险按压力级别统计数据失败:', error);
  }
};

// 获取管网风险按企业统计数据
const fetchEnterpriseRiskData = async () => {
  try {
    const res = await getPipelineRiskEnterpriseStatistics();
    if (res.code === 200 && res.data && res.data.enterpriseRiskStatistics) {
      const stats = res.data.enterpriseRiskStatistics;
      
      // 提取分类
      companyData.value.categories = stats.map(item => item.enterpriseName);
      
      // 提取各风险级别数据
      companyData.value.series[0].data = stats.map(item => item.bigRiskLength || 0);
      companyData.value.series[1].data = stats.map(item => item.largerRiskLength || 0);
      companyData.value.series[2].data = stats.map(item => item.generalRiskLength || 0);
      companyData.value.series[3].data = stats.map(item => item.lowRiskLength || 0);
      
      // 计算总风险数据
      const totalHigh = stats.reduce((sum, item) => sum + (item.bigRiskLength || 0), 0);
      const totalMedium = stats.reduce((sum, item) => sum + (item.largerRiskLength || 0), 0);
      const totalNormal = stats.reduce((sum, item) => sum + (item.generalRiskLength || 0), 0);
      const totalLow = stats.reduce((sum, item) => sum + (item.lowRiskLength || 0), 0);
      
      companyData.value.stats = {
        high: totalHigh.toString(),
        medium: totalMedium.toString(),
        normal: totalNormal.toString(),
        low: totalLow.toString()
      };
      
      // 如果当前是按企业，则更新图表
      if (selectedTab.value === '按企业' && chartInstance) {
        updateChartData('按企业');
      }
    }
  } catch (error) {
    console.error('获取管网风险按企业统计数据失败:', error);
  }
};

// 获取场站风险统计数据
const fetchStationRiskData = async () => {
  try {
    const res = await getStationRiskStatistics();
    if (res.code === 200 && res.data) {
      stationRiskData.value = res.data;
    }
  } catch (error) {
    console.error('获取场站风险统计数据失败:', error);
  }
};

// 窗口大小调整处理函数
const handleResize = () => {
  chartInstance && chartInstance.resize();
};

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return;
  
  // 创建图表实例
  chartInstance = echarts.init(chartRef.value);
  
  // 更新图表数据
  updateChartData(selectedTab.value);
  
  // 窗口大小变化时，重新调整图表大小
  window.addEventListener('resize', handleResize);
};

// 更新图表数据
const updateChartData = (tabName) => {
  if (!chartInstance) return;
  
  // 根据选中的标签页选择数据
  const chartData = tabName === '按压力分级' ? pressureData.value : companyData.value;
  
  // 配置图表选项
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: chartData.categories,
      axisLine: {
        lineStyle: {
          color: '#E0E0E0'
        }
      },
      axisLabel: {
        color: '#282828',
        fontSize: 12
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      splitLine: {
        lineStyle: {
          color: '#E0E0E0',
          type: 'dashed'
        }
      },
      axisLabel: {
        color: '#282828',
        fontSize: 12
      }
    },
    legend: {
      data: chartData.series.map(item => item.name),
      right: 10,
      top: 10
    },
    series: chartData.series.map(item => ({
      name: item.name,
      type: 'bar',
      stack: 'total',
      barWidth: 24,
      emphasis: {
        focus: 'series'
      },
      data: item.data,
      itemStyle: {
        color: item.color
      }
    }))
  };
  
  // 设置图表选项
  chartInstance.setOption(option, true);
};

// 监听标签页变化
const handleTabChange = (value) => {
  // 根据标签切换更新图表数据
  updateChartData(value);
};

// 组件挂载后初始化图表和获取数据
onMounted(() => {
  // 获取各类风险数据
  fetchPipelineRiskData();
  fetchPressureRiskData();
  fetchEnterpriseRiskData();
  fetchStationRiskData();
  
  nextTick(() => {
    initChart();
  });
});

// 组件销毁前清理事件监听
onBeforeUnmount(() => {
  // 移除窗口大小变化监听
  window.removeEventListener('resize', handleResize);
  
  // 销毁图表实例
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }
});

// 监听selectedTab的变化
watch(selectedTab, (newValue) => {
  updateChartData(newValue);
});
</script>

<style scoped>
.gas-risk-statistics {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  box-sizing: border-box;
  overflow: hidden;
}

.component-title {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 18px;
  color: #282828;
  margin-bottom: 16px;
}

/* 顶部风险卡片样式 */
.risk-cards {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  width: 100%;
}

@media (max-width: 1280px) {
  .risk-cards {
    flex-wrap: wrap;
  }
  
  .risk-card {
    flex: 0 0 48%;
    margin-bottom: 16px;
  }
}

@media (max-width: 768px) {
  .risk-cards {
    flex-direction: column;
  }
  
  .risk-card {
    width: 100%;
    margin-right: 0;
    margin-bottom: 16px;
  }
  
  .risk-content {
    flex-direction: column;
  }
  
  .chart-container {
    margin-right: 0;
    margin-bottom: 16px;
  }
}

.risk-card {
  flex: 1;
  height: 104px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #FFFFFF;
  border-radius: 4px;
  margin-right: 16px;
  transition: all 0.3s ease;
}

.risk-card:last-child {
  margin-right: 0;
}

.risk-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
}

.risk-card.high {
  background-color: #FF0000;
}

.risk-card.medium {
  background-color: #FFBF00;
}

.risk-card.normal {
  background-color: #FFE500;
}

.risk-card.low {
  background-color: #0086FF;
}

.risk-value {
  font-size: 32px;
  font-weight: 500;
  line-height: 1.2;
}

.unit {
  font-size: 16px;
  margin-left: 4px;
}

.risk-label {
  font-size: 14px;
  margin-top: 4px;
}

/* 主体内容样式 */
.risk-content {
  display: flex;
  flex: 1;
  width: 100%;
}

.chart-container {
  flex: 3;
  background-color: #FFFFFF;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  padding: 16px;
  margin-right: 16px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.chart-title {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 16px;
  color: #282828;
  margin: 0;
}

.chart-content {
  display: flex;
  height: calc(100% - 40px);
}

.chart-box {
  flex: 3;
  height: 100%;
  min-height: 300px;
}

.statistics-box {
  flex: 1;
  background-color: #F5F5F5;
  padding: 20px;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.stat-item {
  display: flex;
  margin-bottom: 12px;
}

.stat-item:last-child {
  margin-bottom: 0;
}

.stat-label {
  color: #282828;
  font-size: 14px;
}

.stat-value {
  color: #282828;
  font-size: 14px;
  font-weight: 500;
}

/* 右侧风险明细样式 */
.risk-detail {
  flex: 1;
  background-color: #FFFFFF;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  padding: 16px;
}

.detail-title {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 16px;
  color: #282828;
  margin: 0 0 16px 0;
}

.risk-detail-cards {
  display: flex;
  flex-direction: column;
  height: calc(100% - 40px);
}

.detail-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #FFFFFF;
  border-radius: 4px;
  margin-bottom: 16px;
}

.detail-card:last-child {
  margin-bottom: 0;
}

.detail-card.high {
  background-color: #FF0000;
}

.detail-card.medium {
  background-color: #FFBF00;
}

.detail-card.normal {
  background-color: #FFE500;
}

.detail-card.low {
  background-color: #0086FF;
}

.detail-value {
  font-size: 24px;
  font-weight: 500;
  line-height: 1.2;
}

.detail-label {
  font-size: 14px;
  margin-top: 4px;
}

/* Element Plus 组件样式覆盖 */
:deep(.el-radio-button__inner) {
  padding: 8px 15px;
  font-size: 14px;
}

:deep(.el-radio-button__orig-radio:checked + .el-radio-button__inner) {
  background-color: #0086FF;
  border-color: #0086FF;
  box-shadow: -1px 0 0 0 #0086FF;
}
</style>