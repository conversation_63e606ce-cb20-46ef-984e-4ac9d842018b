/**
 *    ████████                   ██                   ██       ██                  ██      ██
 *   ██░░░░░░██                 ░░                   ░██      ░██                 ░██     ░██
 *  ██      ░░   █████  ███████  ██ ██   ██  ██████  ░██   █  ░██  ██████  ██████ ░██     ░██
 * ░██          ██░░░██░░██░░░██░██░██  ░██ ██░░░░   ░██  ███ ░██ ██░░░░██░░██░░█ ░██  ██████
 * ░██    █████░███████ ░██  ░██░██░██  ░██░░█████   ░██ ██░██░██░██   ░██ ░██ ░  ░██ ██░░░██
 * ░░██  ░░░░██░██░░░░  ░██  ░██░██░██  ░██ ░░░░░██  ░████ ░░████░██   ░██ ░██    ░██░██  ░██
 *  ░░████████ ░░██████ ███  ░██░██░░██████ ██████   ░██░   ░░░██░░██████ ░███    ███░░██████
 *   ░░░░░░░░   ░░░░░░ ░░░   ░░ ░░  ░░░░░░ ░░░░░░    ░░       ░░  ░░░░░░  ░░░    ░░░  ░░░░░░
 *
 * @license
 * GeniusWorld Web SDK - http://zydlxx.cn:1234/
 * Version 3.5.0#develop-c1086f5b@202310131719
 *
 *
 * Copyright © 2020 - 2023 正元地理信息集团股份有限公司. All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
define(["exports","./Transforms-8a405532","./Matrix3-5f307711","./Matrix2-0f368b7a","./ComponentDatatype-c2528e2d","./defaultValue-c9f7e7c2","./GeometryAttribute-46a1fd01","./GeometryAttributes-0dbeaa6b","./Math-a28f0fcd","./Plane-dc9e7055","./VertexFormat-09a6da37"],(function(t,e,i,a,n,r,s,o,f,u,l){"use strict";function h(t){this.planes=r.defaultValue(t,[])}const c=[new i.Cartesian3,new i.Cartesian3,new i.Cartesian3];i.Cartesian3.clone(i.Cartesian3.UNIT_X,c[0]),i.Cartesian3.clone(i.Cartesian3.UNIT_Y,c[1]),i.Cartesian3.clone(i.Cartesian3.UNIT_Z,c[2]);const p=new i.Cartesian3,d=new i.Cartesian3,m=new u.Plane(new i.Cartesian3(1,0,0),0);function C(t){t=r.defaultValue(t,r.defaultValue.EMPTY_OBJECT),this.left=t.left,this._left=void 0,this.right=t.right,this._right=void 0,this.top=t.top,this._top=void 0,this.bottom=t.bottom,this._bottom=void 0,this.near=r.defaultValue(t.near,1),this._near=this.near,this.far=r.defaultValue(t.far,5e8),this._far=this.far,this._cullingVolume=new h,this._orthographicMatrix=new a.Matrix4}function _(t){t.top===t._top&&t.bottom===t._bottom&&t.left===t._left&&t.right===t._right&&t.near===t._near&&t.far===t._far||(t._left=t.left,t._right=t.right,t._top=t.top,t._bottom=t.bottom,t._near=t.near,t._far=t.far,t._orthographicMatrix=a.Matrix4.computeOrthographicOffCenter(t.left,t.right,t.bottom,t.top,t.near,t.far,t._orthographicMatrix))}h.fromBoundingSphere=function(t,e){r.defined(e)||(e=new h);const n=c.length,s=e.planes;s.length=2*n;const o=t.center,f=t.radius;let u=0;for(let t=0;t<n;++t){const e=c[t];let n=s[u],l=s[u+1];r.defined(n)||(n=s[u]=new a.Cartesian4),r.defined(l)||(l=s[u+1]=new a.Cartesian4),i.Cartesian3.multiplyByScalar(e,-f,p),i.Cartesian3.add(o,p,p),n.x=e.x,n.y=e.y,n.z=e.z,n.w=-i.Cartesian3.dot(e,p),i.Cartesian3.multiplyByScalar(e,f,p),i.Cartesian3.add(o,p,p),l.x=-e.x,l.y=-e.y,l.z=-e.z,l.w=-i.Cartesian3.dot(i.Cartesian3.negate(e,d),p),u+=2}return e},h.prototype.computeVisibility=function(t){const i=this.planes;let a=!1;for(let n=0,r=i.length;n<r;++n){const r=t.intersectPlane(u.Plane.fromCartesian4(i[n],m));if(r===e.Intersect.OUTSIDE)return e.Intersect.OUTSIDE;r===e.Intersect.INTERSECTING&&(a=!0)}return a?e.Intersect.INTERSECTING:e.Intersect.INSIDE},h.prototype.computeVisibilityWithPlaneMask=function(t,i){if(i===h.MASK_OUTSIDE||i===h.MASK_INSIDE)return i;let a=h.MASK_INSIDE;const n=this.planes;for(let r=0,s=n.length;r<s;++r){const s=r<31?1<<r:0;if(r<31&&0==(i&s))continue;const o=t.intersectPlane(u.Plane.fromCartesian4(n[r],m));if(o===e.Intersect.OUTSIDE)return h.MASK_OUTSIDE;o===e.Intersect.INTERSECTING&&(a|=s)}return a},h.MASK_OUTSIDE=4294967295,h.MASK_INSIDE=0,h.MASK_INDETERMINATE=2147483647,Object.defineProperties(C.prototype,{projectionMatrix:{get:function(){return _(this),this._orthographicMatrix}}});const y=new i.Cartesian3,w=new i.Cartesian3,g=new i.Cartesian3,x=new i.Cartesian3;function v(t){t=r.defaultValue(t,r.defaultValue.EMPTY_OBJECT),this._offCenterFrustum=new C,this.width=t.width,this._width=void 0,this.aspectRatio=t.aspectRatio,this._aspectRatio=void 0,this.near=r.defaultValue(t.near,1),this._near=this.near,this.far=r.defaultValue(t.far,5e8),this._far=this.far}function M(t){const e=t._offCenterFrustum;if(t.width!==t._width||t.aspectRatio!==t._aspectRatio||t.near!==t._near||t.far!==t._far){t._aspectRatio=t.aspectRatio,t._width=t.width,t._near=t.near,t._far=t.far;const i=1/t.aspectRatio;e.right=.5*t.width,e.left=-e.right,e.top=i*e.right,e.bottom=-e.top,e.near=t.near,e.far=t.far}}function b(t){t=r.defaultValue(t,r.defaultValue.EMPTY_OBJECT),this.left=t.left,this._left=void 0,this.right=t.right,this._right=void 0,this.top=t.top,this._top=void 0,this.bottom=t.bottom,this._bottom=void 0,this.near=r.defaultValue(t.near,1),this._near=this.near,this.far=r.defaultValue(t.far,5e8),this._far=this.far,this._cullingVolume=new h,this._perspectiveMatrix=new a.Matrix4,this._infinitePerspective=new a.Matrix4}function V(t){const e=t.top,i=t.bottom,n=t.right,r=t.left,s=t.near,o=t.far;e===t._top&&i===t._bottom&&r===t._left&&n===t._right&&s===t._near&&o===t._far||(t._left=r,t._right=n,t._top=e,t._bottom=i,t._near=s,t._far=o,t._perspectiveMatrix=a.Matrix4.computePerspectiveOffCenter(r,n,i,e,s,o,t._perspectiveMatrix),t._infinitePerspective=a.Matrix4.computeInfinitePerspectiveOffCenter(r,n,i,e,s,t._infinitePerspective))}C.prototype.computeCullingVolume=function(t,e,n){const s=this._cullingVolume.planes,o=this.top,f=this.bottom,u=this.right,l=this.left,h=this.near,c=this.far,p=i.Cartesian3.cross(e,n,y);i.Cartesian3.normalize(p,p);const d=w;i.Cartesian3.multiplyByScalar(e,h,d),i.Cartesian3.add(t,d,d);const m=g;i.Cartesian3.multiplyByScalar(p,l,m),i.Cartesian3.add(d,m,m);let C=s[0];return r.defined(C)||(C=s[0]=new a.Cartesian4),C.x=p.x,C.y=p.y,C.z=p.z,C.w=-i.Cartesian3.dot(p,m),i.Cartesian3.multiplyByScalar(p,u,m),i.Cartesian3.add(d,m,m),C=s[1],r.defined(C)||(C=s[1]=new a.Cartesian4),C.x=-p.x,C.y=-p.y,C.z=-p.z,C.w=-i.Cartesian3.dot(i.Cartesian3.negate(p,x),m),i.Cartesian3.multiplyByScalar(n,f,m),i.Cartesian3.add(d,m,m),C=s[2],r.defined(C)||(C=s[2]=new a.Cartesian4),C.x=n.x,C.y=n.y,C.z=n.z,C.w=-i.Cartesian3.dot(n,m),i.Cartesian3.multiplyByScalar(n,o,m),i.Cartesian3.add(d,m,m),C=s[3],r.defined(C)||(C=s[3]=new a.Cartesian4),C.x=-n.x,C.y=-n.y,C.z=-n.z,C.w=-i.Cartesian3.dot(i.Cartesian3.negate(n,x),m),C=s[4],r.defined(C)||(C=s[4]=new a.Cartesian4),C.x=e.x,C.y=e.y,C.z=e.z,C.w=-i.Cartesian3.dot(e,d),i.Cartesian3.multiplyByScalar(e,c,m),i.Cartesian3.add(t,m,m),C=s[5],r.defined(C)||(C=s[5]=new a.Cartesian4),C.x=-e.x,C.y=-e.y,C.z=-e.z,C.w=-i.Cartesian3.dot(i.Cartesian3.negate(e,x),m),this._cullingVolume},C.prototype.getPixelDimensions=function(t,e,i,a,n){_(this);const r=a*(this.right-this.left)/t,s=a*(this.top-this.bottom)/e;return n.x=r,n.y=s,n},C.prototype.clone=function(t){return r.defined(t)||(t=new C),t.left=this.left,t.right=this.right,t.top=this.top,t.bottom=this.bottom,t.near=this.near,t.far=this.far,t._left=void 0,t._right=void 0,t._top=void 0,t._bottom=void 0,t._near=void 0,t._far=void 0,t},C.prototype.equals=function(t){return r.defined(t)&&t instanceof C&&this.right===t.right&&this.left===t.left&&this.top===t.top&&this.bottom===t.bottom&&this.near===t.near&&this.far===t.far},C.prototype.equalsEpsilon=function(t,e,i){return t===this||r.defined(t)&&t instanceof C&&f.CesiumMath.equalsEpsilon(this.right,t.right,e,i)&&f.CesiumMath.equalsEpsilon(this.left,t.left,e,i)&&f.CesiumMath.equalsEpsilon(this.top,t.top,e,i)&&f.CesiumMath.equalsEpsilon(this.bottom,t.bottom,e,i)&&f.CesiumMath.equalsEpsilon(this.near,t.near,e,i)&&f.CesiumMath.equalsEpsilon(this.far,t.far,e,i)},v.packedLength=4,v.pack=function(t,e,i){return i=r.defaultValue(i,0),e[i++]=t.width,e[i++]=t.aspectRatio,e[i++]=t.near,e[i]=t.far,e},v.unpack=function(t,e,i){return e=r.defaultValue(e,0),r.defined(i)||(i=new v),i.width=t[e++],i.aspectRatio=t[e++],i.near=t[e++],i.far=t[e],i},Object.defineProperties(v.prototype,{projectionMatrix:{get:function(){return M(this),this._offCenterFrustum.projectionMatrix}}}),v.prototype.computeCullingVolume=function(t,e,i){return M(this),this._offCenterFrustum.computeCullingVolume(t,e,i)},v.prototype.getPixelDimensions=function(t,e,i,a,n){return M(this),this._offCenterFrustum.getPixelDimensions(t,e,i,a,n)},v.prototype.clone=function(t){return r.defined(t)||(t=new v),t.aspectRatio=this.aspectRatio,t.width=this.width,t.near=this.near,t.far=this.far,t._aspectRatio=void 0,t._width=void 0,t._near=void 0,t._far=void 0,this._offCenterFrustum.clone(t._offCenterFrustum),t},v.prototype.equals=function(t){return!!(r.defined(t)&&t instanceof v)&&(M(this),M(t),this.width===t.width&&this.aspectRatio===t.aspectRatio&&this._offCenterFrustum.equals(t._offCenterFrustum))},v.prototype.equalsEpsilon=function(t,e,i){return!!(r.defined(t)&&t instanceof v)&&(M(this),M(t),f.CesiumMath.equalsEpsilon(this.width,t.width,e,i)&&f.CesiumMath.equalsEpsilon(this.aspectRatio,t.aspectRatio,e,i)&&this._offCenterFrustum.equalsEpsilon(t._offCenterFrustum,e,i))},Object.defineProperties(b.prototype,{projectionMatrix:{get:function(){return V(this),this._perspectiveMatrix}},infiniteProjectionMatrix:{get:function(){return V(this),this._infinitePerspective}}});const F=new i.Cartesian3,P=new i.Cartesian3,E=new i.Cartesian3,O=new i.Cartesian3;function z(t){t=r.defaultValue(t,r.defaultValue.EMPTY_OBJECT),this._offCenterFrustum=new b,this.fov=t.fov,this._fov=void 0,this._fovy=void 0,this._sseDenominator=void 0,this.aspectRatio=t.aspectRatio,this._aspectRatio=void 0,this.near=r.defaultValue(t.near,1),this._near=this.near,this.far=r.defaultValue(t.far,5e8),this._far=this.far,this.xOffset=r.defaultValue(t.xOffset,0),this._xOffset=this.xOffset,this.yOffset=r.defaultValue(t.yOffset,0),this._yOffset=this.yOffset,this.reflect=!1}function R(t){const e=t._offCenterFrustum;t.fov===t._fov&&t.aspectRatio===t._aspectRatio&&t.near===t._near&&t.far===t._far&&t.xOffset===t._xOffset&&t.yOffset===t._yOffset||(t._aspectRatio=t.aspectRatio,t._fov=t.fov,t._fovy=t.aspectRatio<=1?t.fov:2*Math.atan(Math.tan(.5*t.fov)/t.aspectRatio),t._near=t.near,t._far=t.far,t._sseDenominator=2*Math.tan(.5*t._fovy),t._xOffset=t.xOffset,t._yOffset=t.yOffset,e.top=t.near*Math.tan(.5*t._fovy),e.bottom=-e.top,e.right=t.aspectRatio*e.top,e.left=-e.right,e.near=t.near,e.far=t.far,e.right+=t.xOffset,e.left+=t.xOffset,e.top+=t.yOffset,e.bottom+=t.yOffset)}b.prototype.computeCullingVolume=function(t,e,n){const s=this._cullingVolume.planes,o=this.top,f=this.bottom,u=this.right,l=this.left,h=this.near,c=this.far,p=i.Cartesian3.cross(e,n,F),d=P;i.Cartesian3.multiplyByScalar(e,h,d),i.Cartesian3.add(t,d,d);const m=E;i.Cartesian3.multiplyByScalar(e,c,m),i.Cartesian3.add(t,m,m);const C=O;i.Cartesian3.multiplyByScalar(p,l,C),i.Cartesian3.add(d,C,C),i.Cartesian3.subtract(C,t,C),i.Cartesian3.normalize(C,C),i.Cartesian3.cross(C,n,C),i.Cartesian3.normalize(C,C);let _=s[0];return r.defined(_)||(_=s[0]=new a.Cartesian4),_.x=C.x,_.y=C.y,_.z=C.z,_.w=-i.Cartesian3.dot(C,t),i.Cartesian3.multiplyByScalar(p,u,C),i.Cartesian3.add(d,C,C),i.Cartesian3.subtract(C,t,C),i.Cartesian3.cross(n,C,C),i.Cartesian3.normalize(C,C),_=s[1],r.defined(_)||(_=s[1]=new a.Cartesian4),_.x=C.x,_.y=C.y,_.z=C.z,_.w=-i.Cartesian3.dot(C,t),i.Cartesian3.multiplyByScalar(n,f,C),i.Cartesian3.add(d,C,C),i.Cartesian3.subtract(C,t,C),i.Cartesian3.cross(p,C,C),i.Cartesian3.normalize(C,C),_=s[2],r.defined(_)||(_=s[2]=new a.Cartesian4),_.x=C.x,_.y=C.y,_.z=C.z,_.w=-i.Cartesian3.dot(C,t),i.Cartesian3.multiplyByScalar(n,o,C),i.Cartesian3.add(d,C,C),i.Cartesian3.subtract(C,t,C),i.Cartesian3.cross(C,p,C),i.Cartesian3.normalize(C,C),_=s[3],r.defined(_)||(_=s[3]=new a.Cartesian4),_.x=C.x,_.y=C.y,_.z=C.z,_.w=-i.Cartesian3.dot(C,t),_=s[4],r.defined(_)||(_=s[4]=new a.Cartesian4),_.x=e.x,_.y=e.y,_.z=e.z,_.w=-i.Cartesian3.dot(e,d),i.Cartesian3.negate(e,C),_=s[5],r.defined(_)||(_=s[5]=new a.Cartesian4),_.x=C.x,_.y=C.y,_.z=C.z,_.w=-i.Cartesian3.dot(C,m),this._cullingVolume},b.prototype.getPixelDimensions=function(t,e,i,a,n){V(this);const r=1/this.near;let s=this.top*r;const o=2*a*i*s/e;s=this.right*r;const f=2*a*i*s/t;return n.x=f,n.y=o,n},b.prototype.clone=function(t){return r.defined(t)||(t=new b),t.right=this.right,t.left=this.left,t.top=this.top,t.bottom=this.bottom,t.near=this.near,t.far=this.far,t._left=void 0,t._right=void 0,t._top=void 0,t._bottom=void 0,t._near=void 0,t._far=void 0,t},b.prototype.equals=function(t){return r.defined(t)&&t instanceof b&&this.right===t.right&&this.left===t.left&&this.top===t.top&&this.bottom===t.bottom&&this.near===t.near&&this.far===t.far},b.prototype.equalsEpsilon=function(t,e,i){return t===this||r.defined(t)&&t instanceof b&&f.CesiumMath.equalsEpsilon(this.right,t.right,e,i)&&f.CesiumMath.equalsEpsilon(this.left,t.left,e,i)&&f.CesiumMath.equalsEpsilon(this.top,t.top,e,i)&&f.CesiumMath.equalsEpsilon(this.bottom,t.bottom,e,i)&&f.CesiumMath.equalsEpsilon(this.near,t.near,e,i)&&f.CesiumMath.equalsEpsilon(this.far,t.far,e,i)},b.prototype.resetProjectionMatrix=function(){if(!(r.defined(this.right)&&r.defined(this.left)&&r.defined(this.top)&&r.defined(this.bottom)&&r.defined(this.near)&&r.defined(this.far)))throw new a.DeveloperError("right, left, top, bottom, near, or far RenderStaterameters are not set.");if(this.near<0||this.near>this.far)throw new a.DeveloperError("near must be greater than zero and less than far.");this._left=this.left,this._right=this.right,this._top=this.top,this._bottom=this.bottom,this._near=this.near,this._far=this.far,this._perspectiveMatrix=a.Matrix4.computePerspectiveOffCenter(this.left,this.right,this.bottom,this.top,this.near,this.far,this._perspectiveMatrix),this._infinitePerspective=a.Matrix4.computeInfinitePerspectiveOffCenter(this.left,this.right,this.bottom,this.top,this.near,this._infinitePerspective)},z.packedLength=6,z.pack=function(t,e,i){return i=r.defaultValue(i,0),e[i++]=t.fov,e[i++]=t.aspectRatio,e[i++]=t.near,e[i++]=t.far,e[i++]=t.xOffset,e[i]=t.yOffset,e},z.unpack=function(t,e,i){return e=r.defaultValue(e,0),r.defined(i)||(i=new z),i.fov=t[e++],i.aspectRatio=t[e++],i.near=t[e++],i.far=t[e++],i.xOffset=t[e++],i.yOffset=t[e],i};const S=new u.Plane(i.Cartesian3.UNIT_Z,1),T=new a.Cartesian4,k=new a.Cartesian4,A=new a.Cartesian4;Object.defineProperties(z.prototype,{projectionMatrix:{get:function(){if(R(this),this.reflect&&r.defined(this.clipPlane)&&r.defined(this.currentViewMatrix)){const t=this._offCenterFrustum.projectionMatrix;a.Matrix4.multiplyByPlane(this.currentViewMatrix,this.clipPlane,S),T.x=(f.CesiumMath.sign(S.normal.x)+t[8])/t[0],T.y=(f.CesiumMath.sign(S.normal.y)+t[9])/t[5],T.z=-1,T.w=(1+t[10])/t[14],k.x=S.normal.x,k.y=S.normal.y,k.z=S.normal.z,k.w=S.distance,a.Cartesian4.multiplyByScalar(k,2/a.Cartesian4.dot(k,T),A),t[2]=A.x,t[6]=A.y,t[10]=A.z+1,t[14]=A.w}return this._offCenterFrustum.projectionMatrix}},infiniteProjectionMatrix:{get:function(){return R(this),this._offCenterFrustum.infiniteProjectionMatrix}},fovy:{get:function(){return R(this),this._fovy}},sseDenominator:{get:function(){return R(this),this._sseDenominator}}}),z.prototype.computeCullingVolume=function(t,e,i){return R(this),this._offCenterFrustum.computeCullingVolume(t,e,i)},z.prototype.getPixelDimensions=function(t,e,i,a,n){return R(this),this._offCenterFrustum.getPixelDimensions(t,e,i,a,n)},z.prototype.clone=function(t){return r.defined(t)||(t=new z),t.aspectRatio=this.aspectRatio,t.fov=this.fov,t.near=this.near,t.far=this.far,t.reflect=this.reflect,t.clipPlane=this.clipPlane,t.currentViewMatrix=this.currentViewMatrix,t._aspectRatio=void 0,t._fov=void 0,t._near=void 0,t._far=void 0,this._offCenterFrustum.clone(t._offCenterFrustum),t},z.prototype.equals=function(t){return!!(r.defined(t)&&t instanceof z)&&(R(this),R(t),this.fov===t.fov&&this.aspectRatio===t.aspectRatio&&this._offCenterFrustum.equals(t._offCenterFrustum))},z.prototype.equalsEpsilon=function(t,e,i){return!!(r.defined(t)&&t instanceof z)&&(R(this),R(t),f.CesiumMath.equalsEpsilon(this.fov,t.fov,e,i)&&f.CesiumMath.equalsEpsilon(this.aspectRatio,t.aspectRatio,e,i)&&this._offCenterFrustum.equalsEpsilon(t._offCenterFrustum,e,i))},z.prototype.resetProjectionMatrix=function(){return this._offCenterFrustum.resetProjectionMatrix()};function D(t){const a=t.frustum,n=t.orientation,s=t.origin,o=r.defaultValue(t.vertexFormat,l.VertexFormat.DEFAULT),f=r.defaultValue(t._drawNearPlane,!0);let u,h;a instanceof z?(u=0,h=z.packedLength):a instanceof v&&(u=1,h=v.packedLength),this._frustumType=u,this._frustum=a.clone(),this._origin=i.Cartesian3.clone(s),this._orientation=e.Quaternion.clone(n),this._drawNearPlane=f,this._vertexFormat=o,this._workerName="createFrustumGeometry",this.packedLength=2+h+i.Cartesian3.packedLength+e.Quaternion.packedLength+l.VertexFormat.packedLength}D.pack=function(t,a,n){n=r.defaultValue(n,0);const s=t._frustumType,o=t._frustum;return a[n++]=s,0===s?(z.pack(o,a,n),n+=z.packedLength):(v.pack(o,a,n),n+=v.packedLength),i.Cartesian3.pack(t._origin,a,n),n+=i.Cartesian3.packedLength,e.Quaternion.pack(t._orientation,a,n),n+=e.Quaternion.packedLength,l.VertexFormat.pack(t._vertexFormat,a,n),a[n+=l.VertexFormat.packedLength]=t._drawNearPlane?1:0,a};const I=new z,q=new v,B=new e.Quaternion,N=new i.Cartesian3,L=new l.VertexFormat;function j(t,e,i,a,n,s,o,f){const u=t/3*2;for(let n=0;n<4;++n)r.defined(e)&&(e[t]=s.x,e[t+1]=s.y,e[t+2]=s.z),r.defined(i)&&(i[t]=o.x,i[t+1]=o.y,i[t+2]=o.z),r.defined(a)&&(a[t]=f.x,a[t+1]=f.y,a[t+2]=f.z),t+=3;n[u]=0,n[u+1]=0,n[u+2]=1,n[u+3]=0,n[u+4]=1,n[u+5]=1,n[u+6]=0,n[u+7]=1}D.unpack=function(t,a,n){a=r.defaultValue(a,0);const s=t[a++];let o;0===s?(o=z.unpack(t,a,I),a+=z.packedLength):(o=v.unpack(t,a,q),a+=v.packedLength);const f=i.Cartesian3.unpack(t,a,N);a+=i.Cartesian3.packedLength;const u=e.Quaternion.unpack(t,a,B);a+=e.Quaternion.packedLength;const h=l.VertexFormat.unpack(t,a,L),c=1===t[a+=l.VertexFormat.packedLength];if(!r.defined(n))return new D({frustum:o,origin:f,orientation:u,vertexFormat:h,_drawNearPlane:c});const p=s===n._frustumType?n._frustum:void 0;return n._frustum=o.clone(p),n._frustumType=s,n._origin=i.Cartesian3.clone(f,n._origin),n._orientation=e.Quaternion.clone(u,n._orientation),n._vertexFormat=l.VertexFormat.clone(h,n._vertexFormat),n._drawNearPlane=c,n};const G=new i.Matrix3,U=new a.Matrix4,Q=new a.Matrix4,K=new i.Cartesian3,Y=new i.Cartesian3,J=new i.Cartesian3,Z=new i.Cartesian3,W=new i.Cartesian3,X=new i.Cartesian3,H=new Array(3),$=new Array(4);$[0]=new a.Cartesian4(-1,-1,1,1),$[1]=new a.Cartesian4(1,-1,1,1),$[2]=new a.Cartesian4(1,1,1,1),$[3]=new a.Cartesian4(-1,1,1,1);const tt=new Array(4);for(let t=0;t<4;++t)tt[t]=new a.Cartesian4;D._computeNearFarPlanes=function(t,e,n,s,o,f,u,l){const h=i.Matrix3.fromQuaternion(e,G);let c=r.defaultValue(f,K),p=r.defaultValue(u,Y),d=r.defaultValue(l,J);c=i.Matrix3.getColumn(h,0,c),p=i.Matrix3.getColumn(h,1,p),d=i.Matrix3.getColumn(h,2,d),i.Cartesian3.normalize(c,c),i.Cartesian3.normalize(p,p),i.Cartesian3.normalize(d,d),i.Cartesian3.negate(c,c);const m=a.Matrix4.computeView(t,d,p,c,U);let C,_;if(0===n){const t=s.projectionMatrix,e=a.Matrix4.multiply(t,m,Q);_=a.Matrix4.inverse(e,Q)}else C=a.Matrix4.inverseTransformation(m,Q);r.defined(_)?(H[0]=s.near,H[1]=s.far):(H[0]=0,H[1]=s.near,H[2]=s.far);for(let e=0;e<2;++e)for(let n=0;n<4;++n){let f=a.Cartesian4.clone($[n],tt[n]);if(r.defined(_)){f=a.Matrix4.multiplyByVector(_,f,f);const n=1/f.w;i.Cartesian3.multiplyByScalar(f,n,f),i.Cartesian3.subtract(f,t,f),i.Cartesian3.normalize(f,f);const r=i.Cartesian3.dot(d,f);i.Cartesian3.multiplyByScalar(f,H[e]/r,f),i.Cartesian3.add(f,t,f)}else{r.defined(s._offCenterFrustum)&&(s=s._offCenterFrustum);const t=H[e],i=H[e+1];f.x=.5*(f.x*(s.right-s.left)+s.left+s.right),f.y=.5*(f.y*(s.top-s.bottom)+s.bottom+s.top),f.z=.5*(f.z*(t-i)-t-i),f.w=1,a.Matrix4.multiplyByVector(C,f,f)}o[12*e+3*n]=f.x,o[12*e+3*n+1]=f.y,o[12*e+3*n+2]=f.z}},D.createGeometry=function(t){const a=t._frustumType,f=t._frustum,u=t._origin,l=t._orientation,h=t._drawNearPlane,c=t._vertexFormat,p=h?6:5;let d=new Float64Array(72);D._computeNearFarPlanes(u,l,a,f,d);let m=24;d[m]=d[12],d[m+1]=d[13],d[m+2]=d[14],d[m+3]=d[0],d[m+4]=d[1],d[m+5]=d[2],d[m+6]=d[9],d[m+7]=d[10],d[m+8]=d[11],d[m+9]=d[21],d[m+10]=d[22],d[m+11]=d[23],m+=12,d[m]=d[15],d[m+1]=d[16],d[m+2]=d[17],d[m+3]=d[3],d[m+4]=d[4],d[m+5]=d[5],d[m+6]=d[0],d[m+7]=d[1],d[m+8]=d[2],d[m+9]=d[12],d[m+10]=d[13],d[m+11]=d[14],m+=12,d[m]=d[3],d[m+1]=d[4],d[m+2]=d[5],d[m+3]=d[15],d[m+4]=d[16],d[m+5]=d[17],d[m+6]=d[18],d[m+7]=d[19],d[m+8]=d[20],d[m+9]=d[6],d[m+10]=d[7],d[m+11]=d[8],m+=12,d[m]=d[6],d[m+1]=d[7],d[m+2]=d[8],d[m+3]=d[18],d[m+4]=d[19],d[m+5]=d[20],d[m+6]=d[21],d[m+7]=d[22],d[m+8]=d[23],d[m+9]=d[9],d[m+10]=d[10],d[m+11]=d[11],h||(d=d.subarray(12));const C=new o.GeometryAttributes({position:new s.GeometryAttribute({componentDatatype:n.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:d})});if(r.defined(c.normal)||r.defined(c.tangent)||r.defined(c.bitangent)||r.defined(c.st)){const t=r.defined(c.normal)?new Float32Array(12*p):void 0,e=r.defined(c.tangent)?new Float32Array(12*p):void 0,a=r.defined(c.bitangent)?new Float32Array(12*p):void 0,o=r.defined(c.st)?new Float32Array(8*p):void 0,f=K,u=Y,l=J,d=i.Cartesian3.negate(f,Z),_=i.Cartesian3.negate(u,W),y=i.Cartesian3.negate(l,X);m=0,h&&(j(m,t,e,a,o,y,f,u),m+=12),j(m,t,e,a,o,l,d,u),m+=12,j(m,t,e,a,o,d,y,u),m+=12,j(m,t,e,a,o,_,y,d),m+=12,j(m,t,e,a,o,f,l,u),m+=12,j(m,t,e,a,o,u,l,d),r.defined(t)&&(C.normal=new s.GeometryAttribute({componentDatatype:n.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:t})),r.defined(e)&&(C.tangent=new s.GeometryAttribute({componentDatatype:n.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:e})),r.defined(a)&&(C.bitangent=new s.GeometryAttribute({componentDatatype:n.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:a})),r.defined(o)&&(C.st=new s.GeometryAttribute({componentDatatype:n.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:o}))}const _=new Uint16Array(6*p);for(let t=0;t<p;++t){const e=6*t,i=4*t;_[e]=i,_[e+1]=i+1,_[e+2]=i+2,_[e+3]=i,_[e+4]=i+2,_[e+5]=i+3}return new s.Geometry({attributes:C,indices:_,primitiveType:s.PrimitiveType.TRIANGLES,boundingSphere:e.BoundingSphere.fromVertices(d)})},t.FrustumGeometry=D,t.OrthographicFrustum=v,t.PerspectiveFrustum=z}));
