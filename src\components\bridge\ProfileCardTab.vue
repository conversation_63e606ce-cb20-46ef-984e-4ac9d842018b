<template>
  <div class="profile-card-tab">
    <el-tabs v-model="activeTab" type="border-card">
      <!-- 一般资料 -->
      <el-tab-pane label="一般资料" name="general">
        <el-form
          ref="generalFormRef"
          :model="generalData"
          label-width="120px"
          :disabled="readonly"
        >
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="卡号">
                <el-input v-model="generalData.cardNo" placeholder="" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="桥梁总长">
                <el-input v-model="generalData.totalLength" placeholder="">
                  <template #append>m</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="跨径组合">
                <el-input v-model="generalData.spanCombination" placeholder="">
                  <template #append>m</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="桥梁面积">
                <el-input v-model="generalData.totalArea" placeholder="">
                  <template #append>m2</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="人行道净宽">
                <el-input v-model="generalData.sidewalkWidth" placeholder="">
                  <template #append>m</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="车行道净宽">
                <el-input v-model="generalData.carriagewayWidth" placeholder="">
                  <template #append>m</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="道路等级">
                <el-input v-model="generalData.roadGrade" placeholder="" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="设计荷载">
                <el-input v-model="generalData.designLoad" placeholder="" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="抗震烈度">
                <el-input v-model="generalData.seismicIntensity" placeholder="" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="设计河床标高">
                <el-input v-model="generalData.designRiverbedElev" placeholder="">
                  <template #append>m</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="设计单位">
                <el-input v-model="generalData.designUnitName" placeholder="" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="建设单位">
                <el-input v-model="generalData.buildUnitName" placeholder="" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="施工单位">
                <el-input v-model="generalData.constructUnitName" placeholder="" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="监理单位">
                <el-input v-model="generalData.superviseUnitName" placeholder="" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="总造价">
                <el-input v-model="generalData.totalCost" placeholder="">
                  <template #append>万</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-tab-pane>

      <!-- 上部结构 -->
      <el-tab-pane label="上部结构" name="superstructure">
        <el-form
          ref="superstructureFormRef"
          :model="superstructureData"
          label-width="120px"
          :disabled="readonly"
        >
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="主梁类型">
                <el-select v-model="superstructureData.mainBeamType" placeholder="请选择" style="width: 100%">
                  <el-option v-for="option in MAIN_BEAM_TYPE_OPTIONS" :key="option.value" :label="option.label" :value="option.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="主梁尺寸(宽X高X长)">
                <el-input v-model="superstructureData.mainBeamSize" placeholder="" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="支座类型">
                <el-select v-model="superstructureData.supportType" placeholder="请选择" style="width: 100%">
                  <el-option v-for="option in SUPPORT_TYPE_OPTIONS" :key="option.value" :label="option.label" :value="option.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="支座数量">
                <el-input v-model="superstructureData.supportCount" placeholder="">
                  <template #append>个</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="桥面结构">
                <el-input v-model="superstructureData.deckStructure" placeholder="" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="伸缩缝类型">
                <el-select v-model="superstructureData.shrinkageJointType" placeholder="请选择" style="width: 100%">
                  <el-option v-for="option in SHRINKAGE_JOINT_TYPE_OPTIONS" :key="option.value" :label="option.label" :value="option.value" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="伸缩缝数量">
                <el-input v-model="superstructureData.shrinkageJointCount" placeholder="">
                  <template #append>条</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="桥面标高">
                <el-input v-model="superstructureData.deckElev" placeholder="">
                  <template #append>m</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="主桥纵坡">
                <el-input v-model="superstructureData.mainBridgeSlope" placeholder="">
                  <template #append>%</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="拱桥矢跨比">
                <el-input v-model="superstructureData.cableSagRatio" placeholder="" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-tab-pane>

      <!-- 下部结构 -->
      <el-tab-pane label="下部结构" name="substructure">
        <el-form
          ref="substructureFormRef"
          :model="substructureData"
          label-width="120px"
          :disabled="readonly"
        >
          <div class="section-title">桥墩</div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="类型">
                <el-select v-model="substructureData.pierType" placeholder="请选择" style="width: 100%">
                  <el-option v-for="option in PIER_TYPE_OPTIONS" :key="option.value" :label="option.label" :value="option.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="标高">
                <el-input v-model="substructureData.pierBaseElev" placeholder="">
                  <template #append>m</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="基础标高">
                <el-input v-model="substructureData.pierBaseElev" placeholder="">
                  <template #append>m</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="基础尺寸/根数">
                <el-input v-model="substructureData.pierBaseSizeCount" placeholder="" />
              </el-form-item>
            </el-col>
          </el-row>

          <div class="section-title">桥台</div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="类型">
                <el-select v-model="substructureData.abutmentType" placeholder="请选择" style="width: 100%">
                  <el-option v-for="option in ABUTMENT_TYPE_OPTIONS" :key="option.value" :label="option.label" :value="option.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="标高">
                <el-input v-model="substructureData.abutmentBaseElev" placeholder="">
                  <template #append>m</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="台帽尺寸">
                <el-input v-model="substructureData.abutmentCoverSize" placeholder="" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="基础尺寸/根数">
                <el-input v-model="substructureData.abutmentBaseSizeCount" placeholder="" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="挡土板厚度">
                <el-input v-model="substructureData.retainingWallThickness" placeholder="">
                  <template #append>m</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="翼墙长度">
                <el-input v-model="substructureData.wingWallLength" placeholder="">
                  <template #append>m</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-tab-pane>

      <!-- 附属工程 -->
      <el-tab-pane label="附属工程" name="accessory">
        <el-form
          ref="accessoryFormRef"
          :model="accessoryData"
          label-width="120px"
          :disabled="readonly"
        >
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="栏杆总长">
                <el-input v-model="accessoryData.railingTotalLength" placeholder="">
                  <template #append>m</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="栏杆结构">
                <el-input v-model="accessoryData.railingStructure" placeholder="" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="端柱尺寸">
                <el-input v-model="accessoryData.endColumnSize" placeholder="" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="护岸类型">
                <el-select v-model="accessoryData.bankProtectionType" placeholder="请选择" style="width: 100%">
                  <el-option v-for="option in BANK_PROTECTION_TYPE_OPTIONS" :key="option.value" :label="option.label" :value="option.value" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="引桥扶壁类型">
                <el-select v-model="accessoryData.cableAnchorWallType" placeholder="请选择" style="width: 100%">
                  <el-option v-for="option in CABLE_ANCHOR_WALL_TYPE_OPTIONS" :key="option.value" :label="option.label" :value="option.value" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-tab-pane>

      <!-- 附挂管线 -->
      <el-tab-pane label="附挂管线" name="pipeline">
        <el-form
          ref="pipelineFormRef"
          :model="pipelineData"
          label-width="120px"
          :disabled="readonly"
        >
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="给水管">
                <el-input v-model="pipelineData.waterPipe" placeholder="" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="燃气管">
                <el-input v-model="pipelineData.gasPipe" placeholder="" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="电力管">
                <el-input v-model="pipelineData.powerPipe" placeholder="" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="通讯电缆">
                <el-input v-model="pipelineData.telecomCable" placeholder="" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="其他管线">
                <el-input v-model="pipelineData.otherPipe" placeholder="" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import {
  MAIN_BEAM_TYPE_OPTIONS,
  SUPPORT_TYPE_OPTIONS,
  SHRINKAGE_JOINT_TYPE_OPTIONS,
  PIER_TYPE_OPTIONS,
  ABUTMENT_TYPE_OPTIONS,
  BANK_PROTECTION_TYPE_OPTIONS,
  CABLE_ANCHOR_WALL_TYPE_OPTIONS
} from '@/constants/bridge'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  },
  readonly: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue'])

const activeTab = ref('general')
const generalFormRef = ref()
const superstructureFormRef = ref()
const substructureFormRef = ref()
const accessoryFormRef = ref()
const pipelineFormRef = ref()

// 各个子表单的数据
const generalData = reactive({
  cardNo: '',
  totalLength: '',
  spanCombination: '',
  totalArea: '',
  sidewalkWidth: '',
  carriagewayWidth: '',
  roadGrade: '',
  designLoad: '',
  seismicIntensity: '',
  designRiverbedElev: '',
  designUnitName: '',
  buildUnitName: '',
  constructUnitName: '',
  superviseUnitName: '',
  totalCost: ''
})

const superstructureData = reactive({
  mainBeamType: '',
  mainBeamSize: '',
  supportType: '',
  supportCount: '',
  deckStructure: '',
  shrinkageJointType: '',
  shrinkageJointCount: '',
  deckElev: '',
  mainBridgeSlope: '',
  cableSagRatio: ''
})

const substructureData = reactive({
  pierType: '',
  pierBaseElev: '',
  pierBaseSizeCount: '',
  abutmentType: '',
  abutmentBaseElev: '',
  abutmentCoverSize: '',
  abutmentBaseSizeCount: '',
  retainingWallThickness: '',
  wingWallLength: ''
})

const accessoryData = reactive({
  railingTotalLength: '',
  railingStructure: '',
  endColumnSize: '',
  bankProtectionType: '',
  cableAnchorWallType: ''
})

const pipelineData = reactive({
  waterPipe: '',
  gasPipe: '',
  powerPipe: '',
  telecomCable: '',
  otherPipe: ''
})

// 监听props变化
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    if (newVal.general) Object.assign(generalData, newVal.general)
    if (newVal.superstructure) Object.assign(superstructureData, newVal.superstructure)
    if (newVal.substructure) Object.assign(substructureData, newVal.substructure)
    if (newVal.accessory) Object.assign(accessoryData, newVal.accessory)
    if (newVal.pipeline) Object.assign(pipelineData, newVal.pipeline)
  }
}, { immediate: true, deep: true })

// 监听表单数据变化
watch([generalData, superstructureData, substructureData, accessoryData, pipelineData], () => {
  emit('update:modelValue', {
    general: { ...generalData },
    superstructure: { ...superstructureData },
    substructure: { ...substructureData },
    accessory: { ...accessoryData },
    pipeline: { ...pipelineData }
  })
}, { deep: true })

// 表单验证方法
const validate = async () => {
  const results = await Promise.allSettled([
    generalFormRef.value?.validate(),
    superstructureFormRef.value?.validate(),
    substructureFormRef.value?.validate(),
    accessoryFormRef.value?.validate(),
    pipelineFormRef.value?.validate()
  ])
  
  return results.every(result => result.status === 'fulfilled')
}

// 重置表单
const resetForm = () => {
  generalFormRef.value?.resetFields()
  superstructureFormRef.value?.resetFields()
  substructureFormRef.value?.resetFields()
  accessoryFormRef.value?.resetFields()
  pipelineFormRef.value?.resetFields()
}

// 暴露方法给父组件
defineExpose({
  validate,
  resetForm
})
</script>

<style scoped>
.profile-card-tab {
  padding: 20px;
}

.section-title {
  font-weight: bold;
  margin: 20px 0 10px 0;
  color: #409eff;
  border-left: 3px solid #409eff;
  padding-left: 10px;
}

:deep(.el-tabs--border-card) {
  border: none;
  box-shadow: none;
}

:deep(.el-tabs--border-card > .el-tabs__content) {
  padding: 20px;
}
</style> 