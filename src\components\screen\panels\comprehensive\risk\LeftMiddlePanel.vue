<template>
  <PanelBox title="排水风险">
    <template #extra>
      <div class="com-select">
        <CommonSelect v-model="selectedPipeType" :options="pipeTypeOptions" @change="handlePipeTypeChange" />
      </div>
    </template>
    <div class="panel-content">
      <div class="chart-wrapper">
        <div class="chart-container" ref="chartContainer"></div>
        <div class="chart-bg"></div>

        <!-- 图例 -->
        <div class="low-risk-legend">
          <div class="legend-box" style="border-color: #00E1B9;">
            <div class="legend-dot" style="background: #00E1B9;"></div>
          </div>
          <div class="legend-text">低风险</div>
        </div>

        <div class="normal-risk-legend">
          <div class="legend-box" style="border-color: #D8F115;">
            <div class="legend-dot" style="background: #D8F115;"></div>
          </div>
          <div class="legend-text">一般风险</div>
        </div>

        <div class="high-risk-legend">
          <div class="legend-box" style="border-color: #FE9150;">
            <div class="legend-dot" style="background: #FE9150;"></div>
          </div>
          <div class="legend-text">较大风险</div>
        </div>

        <div class="severe-risk-legend">
          <div class="legend-box" style="border-color: #DE6970;">
            <div class="legend-dot" style="background: #DE6970;"></div>
          </div>
          <div class="legend-text">重大风险</div>
        </div>
      </div>
    </div>
  </PanelBox>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch, nextTick } from 'vue'
import PanelBox from '@/components/screen/PanelBox.vue'
import CommonSelect from '@/components/screen/CommonSelect.vue'
import * as echarts from 'echarts'

// 综合态势总览左中面板组件

// 管线类型选项
const pipeTypeOptions = [
  { value: 'rain', label: '雨水管线' },
  { value: 'sewage', label: '污水管线' },
  { value: 'plant', label: '污水厂' },
  { value: 'station', label: '泵站' }
]

// 默认选择雨水管线
const selectedPipeType = ref('rain')

// 图表实例
let chartInstance = null
const chartContainer = ref(null)

// 模拟数据
const mockData = {
  rain: [
    { name: '低风险', value: 0, distance: '0公里', color: 'rgba(0, 225, 185, 1)' },
    { name: '一般风险', value: 0, distance: '0公里', color: 'rgba(216, 241, 21, 1)' },
    { name: '较大风险', value: 0, distance: '0公里', color: 'rgba(254, 145, 80, 1)' },
    { name: '重大风险', value: 0, distance: '0公里', color: 'rgba(222, 105, 112, 1)' }
  ],
  sewage: [
    { name: '低风险', value: 0, distance: '0公里', color: 'rgba(0, 225, 185, 1)' },
    { name: '一般风险', value: 0, distance: '0公里', color: 'rgba(216, 241, 21, 1)' },
    { name: '较大风险', value: 0, distance: '0公里', color: 'rgba(254, 145, 80, 1)' },
    { name: '重大风险', value:0, distance: '0公里', color: 'rgba(222, 105, 112, 1)' }
  ],
  plant: [
    { name: '低风险', value: 0, distance: '0公里', color: 'rgba(0, 225, 185, 1)' },
    { name: '一般风险', value: 0, distance: '0公里', color: 'rgba(216, 241, 21, 1)' },
    { name: '较大风险', value:0, distance: '0公里', color: 'rgba(254, 145, 80, 1)' },
    { name: '重大风险', value:0, distance: '0公里', color: 'rgba(222, 105, 112, 1)' }
  ],
  station: [
    { name: '低风险', value: 0, distance: '0处', color: 'rgba(0, 225, 185, 1)' },
    { name: '一般风险', value: 0, distance: '0处', color: 'rgba(216, 241, 21, 1)' },
    { name: '较大风险', value:0, distance: '0处', color: 'rgba(254, 145, 80, 1)' },
    { name: '重大风险', value:0, distance: '0处', color: 'rgba(222, 105, 112, 1)' }
  ]
}

// 创建图表
const initChart = async () => {
  if (!chartContainer.value) return

  // 等待DOM渲染完成
  await nextTick()

  // 销毁之前的图表实例
  if (chartInstance) {
    chartInstance.dispose()
  }

  // 创建新的图表实例
  chartInstance = echarts.init(chartContainer.value)
  updateChart()

  // 监听窗口大小变化，重新绘制图表
  window.addEventListener('resize', handleResize)
}

// 处理窗口大小变化
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

// 更新图表数据
const updateChart = () => {
  if (!chartInstance) return

  const data = mockData[selectedPipeType.value]

  const option = {
    backgroundColor: 'transparent',
    title: {
      text: selectedPipeType.value === 'rain' ? '管网' :
        selectedPipeType.value === 'sewage' ? '污水' :
          selectedPipeType.value === 'plant' ? '厂站' : '泵站',
      left: '40%',
      top: 'center',
      textStyle: {
        fontSize: 16,
        color: '#FFFFFF',
        fontFamily: 'PingFangSC, PingFang SC',
        fontWeight: 500
      },
      textAlign: 'center'
    },
    series: [
      {
        name: '外环',
        type: 'pie',
        radius: ['67px', '85px'],
        center: ['40%', '50%'],
        startAngle: 90,
        clockwise: false,
        avoidLabelOverlap: false,
        label: {
          show: true,
          position: 'outside',
          formatter: (params) => {
            const targetData = data.find(item => item.name === params.name)
            if (params.name === '重大风险') {
              return `{risk4|重大风险:}\n{value|${params.value}% | ${targetData.distance}}`;
            } else if (params.name === '较大风险') {
              return `{risk3|较大风险:}\n{value|${params.value}% | ${targetData.distance}}`;
            } else if (params.name === '一般风险') {
              return `{risk2|一般风险:}\n{value|${params.value}% | ${targetData.distance}}`;
            } else {
              return `{risk1|低风险:}\n{value|${params.value}% | ${targetData.distance}}`;
            }
          },
          rich: {
            risk1: {
              fontSize: 12,
              fontFamily: 'PingFangSC, PingFang SC',
              fontWeight: 400,
              color: '#FFFFFF',
              padding: [0, 0, 4, 0]
            },
            risk2: {
              fontSize: 12,
              fontFamily: 'PingFangSC, PingFang SC',
              fontWeight: 400,
              color: '#FFFFFF',
              padding: [0, 0, 4, 0]
            },
            risk3: {
              fontSize: 12,
              fontFamily: 'PingFangSC, PingFang SC',
              fontWeight: 400,
              color: '#FFFFFF',
              padding: [0, 0, 4, 0]
            },
            risk4: {
              fontSize: 12,
              fontFamily: 'PingFangSC, PingFang SC',
              fontWeight: 400,
              color: '#FFFFFF',
              padding: [0, 0, 4, 0]
            },
            value: {
              fontSize: 12,
              fontFamily: 'PingFangSC, PingFang SC',
              fontWeight: 400,
              color: '#FFFFFF'
            }
          },
          alignTo: 'none',
          edgeDistance: '10%'
        },
        labelLine: {
          show: true,
          length: 10,
          length2: 20,
          smooth: true,
          lineStyle: {
            width: 1,
            type: 'dashed',
            color: 'inherit'
          }
        },
        itemStyle: {
          borderWidth: 1,
          borderColor: '#0A2E5B'
        },
        data: data.map(item => {
          // 为每个扇区设置特定的属性
          let itemConfig = {
            value: item.value,
            name: item.name,
            itemStyle: {
              color: item.color,
              opacity: 0.4
            },
            label: {
              show: true,
              opacity: 0.9,
            },
            labelLine: {
              show: true
            }
          };

          // 根据风险类型设置特定的标签和标签线属性
          if (item.name === '低风险') {
            itemConfig.label.position = ['-30%', '0%'];
            itemConfig.labelLine = {
              show: true,
              length: 8,
              length2: 15,
              smooth: true,
              lineStyle: {
                width: 1,
                type: 'solid',
                color: item.color
              }
            };
          } else if (item.name === '一般风险') {
            itemConfig.label.position = ['0%', '30%'];
            itemConfig.labelLine = {
              show: true,
              length: 8,
              length2: 15,
              smooth: true,
              lineStyle: {
                width: 1,
                type: 'solid',
                color: item.color
              }
            };
          } else if (item.name === '较大风险') {
            itemConfig.label.position = ['30%', '0%'];
            itemConfig.labelLine = {
              show: true,
              length: 8,
              length2: 15,
              smooth: true,
              lineStyle: {
                width: 1,
                type: 'solid',
                color: item.color
              }
            };
          } else if (item.name === '重大风险') {
            itemConfig.label.position = ['0%', '-30%'];
            itemConfig.labelLine = {
              show: true,
              length: 8,
              length2: 15,
              smooth: true,
              lineStyle: {
                width: 1,
                type: 'solid',
                color: item.color
              }
            };
          }

          return itemConfig;
        })
      },
      {
        name: '内环',
        type: 'pie',
        radius: ['52px', '67px'],
        center: ['40%', '50%'],
        startAngle: 90,
        clockwise: false,
        avoidLabelOverlap: false,
        label: {
          show: false
        },
        labelLine: {
          show: false
        },
        itemStyle: {
          borderWidth: 1,
          borderColor: '#0A2E5B'
        },
        emphasis: {
          scale: false,
          disabled: true
        },
        data: data.map(item => ({
          value: item.value,
          name: item.name,
          itemStyle: {
            color: item.color
          }
        }))
      }
    ]
  }

  // 添加四周虚线边框的图形元素
  option.graphic = [];

  chartInstance.setOption(option, true)
}

// 处理管线类型变化
const handlePipeTypeChange = () => {
  updateChart()
}

// 强制重新计算图表尺寸的方法
const resizeChart = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

// 在视图挂载后初始化图表
onMounted(async () => {
  // 延迟初始化，确保DOM已完全渲染
  setTimeout(async () => {
    await initChart()
    // 初始化后再次强制resize以确保正确渲染
    setTimeout(resizeChart, 200)
  }, 100)
})

// 在视图销毁前清理图表实例
onBeforeUnmount(() => {
  if (chartInstance) {
    window.removeEventListener('resize', handleResize)
    chartInstance.dispose()
    chartInstance = null
  }
})

// 监听管线类型变化
watch(selectedPipeType, () => {
  nextTick(() => {
    updateChart()
  })
})

// TODO: 从后端获取数据的方法
const fetchData = async (pipeType) => {
  try {
    // const response = await api.getPipelineRiskData(pipeType)
    // 更新数据并重绘图表
    // mockData[pipeType] = response.data
    // updateChart()
  } catch (error) {
    console.error('获取管线风险数据失败:', error)
  }
}
</script>

<style scoped>
.panel-content {
  flex: 1;
  width: 100%;
  height: 100%;
  min-height: 219px;
  padding: 0 30px 15px;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
  justify-content: center;
  align-items: center;
}
.com-select{
  margin-right: 20px;
}
.chart-wrapper {
  width: 417px;
  height: 219px;
  position: relative;
  margin: 0 auto;
  display: flex;
  justify-content: center;
  align-items: center;
}

.chart-container {
  width: 100%;
  height: 100%;
  position: relative;
  z-index: 2;
}

.chart-bg {
  position: absolute;
  width: 210px;
  height: 210px;
  left: 40%;
  top: 50%;
  transform: translate(-50%, -50%);
  background-image: url('@/assets/images/screen/drainage/ring_bg.png');
  background-position: center;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  z-index: 1;
}


/* 图例样式 */
.chart-wrapper::after {
  content: '';
  position: absolute;
  right: 30px;
  top: 50%;
  transform: translateY(-50%);
  width: 60px;
  z-index: 2;
}

/* 风险级别图例 - 分别放在四个角落 */
.chart-wrapper::before {
  content: '';
  position: absolute;
  right: 30px;
  top: 40px;
  color: #FFFFFF;
  font-size: 12px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  z-index: 2;
}

.low-risk-legend,
.normal-risk-legend,
.high-risk-legend,
.severe-risk-legend {
  position: absolute;
  z-index: 3;
  display: flex;
  align-items: center;
}

.low-risk-legend {
  right: 6%;
  bottom: 10%;
}

.normal-risk-legend {
  right: 3%;
  bottom: 20%;
}

.high-risk-legend {
  right: 3%;
  bottom: 30%;
}

.severe-risk-legend {
  right: 3%;
  bottom: 40%;
}

.legend-box {
  width: 10px;
  height: 10px;
  border: 1px solid;
  position: relative;
  margin-right: 5px;
}

.legend-dot {
  position: absolute;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.legend-text {
  color: #FFFFFF;
  font-size: 12px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  white-space: nowrap;
}

/* 响应式布局 */
@media screen and (max-width: 768px) {
  .panel-content {
    min-height: 219px;
    padding: 0 20px 10px;
  }

  .chart-wrapper {
    width: 350px;
    height: 219px;
  }

  .chart-bg {
    width: 170px;
    height: 170px;
  }
}

@media screen and (min-width: 1921px) {
  .chart-wrapper {
    width: 450px;
    height: 219px;
  }

  .chart-bg {
    width: 210px;
    height: 210px;
  }
}
</style>