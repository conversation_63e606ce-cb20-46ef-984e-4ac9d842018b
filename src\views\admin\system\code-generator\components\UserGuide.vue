<template>
  <el-dialog
    title="使用说明"
    v-model="dialogVisible"
    width="800px"
    :close-on-click-modal="false"
    :before-close="handleClose"
  >
    <div class="user-guide">
      <el-tabs>
        <el-tab-pane label="基本使用">
          <div class="guide-content">
            <h3>代码生成器使用指南</h3>
            <p>代码生成器是一个快速生成标准化CRUD页面代码的工具，通过简单的配置，即可生成包含列表、搜索、表单等功能的完整页面代码。</p>
            
            <h4>基本步骤</h4>
            <ol>
              <li>填写基础信息：包括模块名称、API前缀、页面标题等</li>
              <li>配置表格列：添加表格需要显示的列</li>
              <li>配置搜索字段：添加搜索表单需要的字段</li>
              <li>配置表单字段：添加编辑表单需要的字段</li>
              <li>配置API方法：选择需要的API方法</li>
              <li>生成代码：点击"生成代码"按钮，预览生成的代码</li>
              <li>下载代码：点击"下载代码"按钮，下载生成的代码文件</li>
            </ol>
            
            <h4>功能说明</h4>
            <ul>
              <li><strong>生成代码</strong>：生成代码并在右侧预览</li>
              <li><strong>下载代码</strong>：将生成的代码打包下载</li>
              <li><strong>模板管理</strong>：管理常用的代码模板</li>
              <li><strong>配置管理</strong>：保存和加载配置</li>
              <li><strong>加载示例</strong>：加载示例配置</li>
              <li><strong>重置配置</strong>：重置所有配置</li>
            </ul>
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="配置说明">
          <div class="guide-content">
            <h3>配置项说明</h3>
            
            <h4>基础信息</h4>
            <ul>
              <li><strong>模块名称</strong>：用于生成文件名和组件名，建议使用小写英文</li>
              <li><strong>API前缀</strong>：用于生成API请求路径</li>
              <li><strong>页面标题</strong>：显示在页面顶部的标题</li>
              <li><strong>功能描述</strong>：对该功能模块的简要描述，将作为注释添加到代码中</li>
              <li><strong>作者</strong>：代码作者，将作为注释添加到代码中</li>
            </ul>
            
            <h4>表格配置</h4>
            <ul>
              <li><strong>列标签</strong>：表格列的显示名称</li>
              <li><strong>属性名</strong>：表格列对应的数据属性名</li>
              <li><strong>列宽</strong>：表格列的宽度，如：100px</li>
              <li><strong>对齐方式</strong>：表格列的对齐方式，可选：左对齐、居中、右对齐</li>
              <li><strong>是否排序</strong>：是否启用该列的排序功能</li>
              <li><strong>是否固定</strong>：是否固定该列，可选：不固定、左固定、右固定</li>
            </ul>
            
            <h4>搜索配置</h4>
            <ul>
              <li><strong>字段标签</strong>：搜索字段的显示名称</li>
              <li><strong>属性名</strong>：搜索字段对应的数据属性名</li>
              <li><strong>组件类型</strong>：搜索字段使用的组件类型，如：输入框、选择器、日期选择器等</li>
              <li><strong>占位文本</strong>：搜索字段的占位提示文本</li>
              <li><strong>选项</strong>：当组件类型为选择器时，可配置的选项</li>
            </ul>
            
            <h4>表单配置</h4>
            <ul>
              <li><strong>字段标签</strong>：表单字段的显示名称</li>
              <li><strong>属性名</strong>：表单字段对应的数据属性名</li>
              <li><strong>组件类型</strong>：表单字段使用的组件类型，如：输入框、选择器、日期选择器等</li>
              <li><strong>占位文本</strong>：表单字段的占位提示文本</li>
              <li><strong>是否必填</strong>：是否为必填字段</li>
              <li><strong>验证规则</strong>：表单字段的验证规则，如：邮箱、手机号、URL等</li>
              <li><strong>错误提示</strong>：验证失败时的错误提示</li>
              <li><strong>选项</strong>：当组件类型为选择器、单选框组、复选框组时，可配置的选项</li>
            </ul>
            
            <h4>API配置</h4>
            <ul>
              <li><strong>标准API方法</strong>：包括获取列表、获取详情、创建、更新、删除等标准方法</li>
              <li><strong>自定义API方法</strong>：可以添加自定义的API方法</li>
            </ul>
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="常见问题">
          <div class="guide-content">
            <h3>常见问题</h3>
            
            <h4>1. 生成的代码如何使用？</h4>
            <p>生成的代码包含主页面、搜索组件、表单组件和API请求文件，下载后可以直接复制到项目中使用。</p>
            
            <h4>2. 如何自定义生成的代码样式？</h4>
            <p>目前代码生成器使用预设的模板，如需自定义样式，可以在下载代码后手动修改。</p>
            
            <h4>3. 生成的代码是否需要额外的依赖？</h4>
            <p>生成的代码依赖于Element Plus组件库，请确保项目中已安装该依赖。</p>
            
            <h4>4. 如何保存我的配置？</h4>
            <p>点击"配置管理"按钮，可以保存当前配置，下次使用时可以直接加载。</p>
            
            <h4>5. 如何共享配置给其他人？</h4>
            <p>在配置管理中，可以导出配置为JSON文件，然后分享给其他人，其他人可以通过导入功能使用该配置。</p>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </el-dialog>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch } from 'vue'

const props = defineProps({
  visible: {
    type: Boolean,
    required: true
  }
})

const emit = defineEmits(['update:visible'])

// 对话框可见性
const dialogVisible = ref(false)

// 监听visible属性变化
watch(() => props.visible, (val) => {
  dialogVisible.value = val
})

// 监听dialogVisible变化
watch(() => dialogVisible.value, (val) => {
  emit('update:visible', val)
})

// 处理关闭
const handleClose = () => {
  dialogVisible.value = false
}
</script>

<style scoped>
.user-guide {
  width: 100%;
  height: 500px;
  overflow-y: auto;
}

.guide-content {
  padding: 0 20px;
}

h3 {
  font-size: 20px;
  margin-bottom: 16px;
}

h4 {
  font-size: 16px;
  margin: 16px 0 8px;
}

p {
  margin: 8px 0;
  line-height: 1.6;
}

ul, ol {
  padding-left: 20px;
  margin: 8px 0;
}

li {
  margin: 8px 0;
  line-height: 1.6;
}
</style>
