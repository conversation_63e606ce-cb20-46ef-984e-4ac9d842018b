<template>
  <div class="bridge-alarm-disposal-search">
    <div class="search-form">
      <!-- 第一行查询条件 -->
      <div class="form-row">
        <div class="form-item">
          <span class="label">报警来源:</span>
          <el-select v-model="formData.alarmSource" class="form-input" placeholder="请选择">
            <el-option v-for="item in BRIDGE_ALARM_SOURCE_OPTIONS" :key="item.value" :label="item.label"
              :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <span class="label">报警等级:</span>
          <el-select v-model="formData.alarmLevel" class="form-input" placeholder="请选择">
            <el-option v-for="item in BRIDGE_ALARM_LEVEL_OPTIONS" :key="item.value" :label="item.label"
              :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <span class="label">报警类型:</span>
          <el-select v-model="formData.alarmType" class="form-input" placeholder="请选择">
            <el-option v-for="item in alarmTypes" :key="item.alarmType" :label="item.alarmTypeName"
              :value="item.alarmType" />
          </el-select>
        </div>
        <div class="form-item">
          <span class="label">报警时间:</span>
          <el-date-picker v-model="formData.timeRange" type="datetimerange" class="form-input time-range-input"
            start-placeholder="开始时间" end-placeholder="结束时间" format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]" />
        </div>
      </div>

      <!-- 第二行查询条件 -->
      <div class="form-row">
        <div class="form-item">
          <span class="label">设备编号:</span>
          <el-input v-model="formData.deviceId" class="form-input" placeholder="请输入设备编号" />
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="button-row">
        <el-button type="primary" @click="handleSearch">查询</el-button>
        <el-button @click="handleReset">重置</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { 
  BRIDGE_ALARM_LEVEL_OPTIONS, 
  BRIDGE_ALARM_SOURCE_OPTIONS, 
} from '@/constants/bridge';
import { getBridgeAlarmTypes } from '@/api/bridge';

const emit = defineEmits(['search', 'reset']);

// 查询表单数据
const formData = reactive({
  alarmSource: '',
  alarmLevel: '',
  alarmType: '',
  timeRange: [],
  deviceId: ''
});

const alarmTypes = ref([]);
const getAlarmTypes = async () => {
  const res = await getBridgeAlarmTypes();
  alarmTypes.value = res.data;
};
getAlarmTypes();
// 处理搜索
const handleSearch = () => {
  emit('search', { ...formData });
};

// 处理重置
const handleReset = () => {
  Object.keys(formData).forEach(key => {
    if (key === 'timeRange') {
      formData[key] = [];
    } else {
      formData[key] = '';
    }
  });
  emit('reset');
};

onMounted(() => {
  console.log('桥梁报警处置搜索组件已挂载');
});
</script>

<style scoped>
.bridge-alarm-disposal-search {
  width: 100%;
  background: #FFFFFF;
  border-radius: 4px;
}

.search-form {
  padding: 16px;
}

.form-row {
  display: flex;
  gap: 20px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.form-item {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 200px;
  flex: 1;
}

.label {
  min-width: 80px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #333333;
}

.form-input {
  flex: 1;
  min-width: 160px;
}

.time-range-input {
  min-width: 320px;
}

.button-row {
  display: flex;
  gap: 16px;
  justify-content: flex-end;
  margin-top: 16px;
}

:deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px #CED3DA inset;
}

:deep(.el-select .el-input__wrapper) {
  box-shadow: 0 0 0 1px #CED3DA inset;
}

:deep(.el-date-editor.el-input) {
  box-shadow: 0 0 0 1px #CED3DA inset;
}

:deep(.el-button) {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .form-item {
    min-width: 280px;
  }
}

@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
  }
  
  .form-item {
    min-width: 100%;
  }
  
  .button-row {
    justify-content: center;
  }
}
</style> 