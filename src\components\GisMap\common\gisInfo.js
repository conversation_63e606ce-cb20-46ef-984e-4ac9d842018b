import {
    postBridgeMapDevicePointsList,
    postDrainUsmBasicFloodPointList,
    postDrainUsmBasicPointList,
    postDrainUsmBasicWellList,
    postDrainUsmMonitorDeviceList,
    postDrainUsmRiskDangerList,
    postDrainUsmRiskProtectList,
    postHeatUsmBasicEnterpriseList,
    postHeatUsmBasicHeatFactoryList,
    postHeatUsmBasicHeatStationList,
    postHeatUsmBasicPointList, postHeatUsmBasicUserList,
    postHeatUsmBasicWellList,
    postHeatUsmMonitorDeviceList, postHeatUsmRiskDangerList, postHeatUsmRiskProtectList,
    postUsmBasicDrainOutletList,
    postUsmBasicPumpStationList,
    postUsmBasicSewageFactoryList,
    postUsmMonitorDeviceList,
    postUsmZyGasDangerList,
    postUsmZyGasPointList,
    postUsmZyGasProtectList,
    postUsmZyGasStationList,
    postUsmZyGasWellList
} from "@/api/layerData.js";

export const treeInfo = {
    misGasMonitor: [
        {id: "xldxeviceList", label: "相邻地下空间", icon: "default"},
        {id: "gsyUserDeviceList", label: "工商业公户", icon: "default"},
        {id: "userDeviceList", label: "居民用户", icon: "default"},
        {id: "mdylDeviceList", label: "末端压力", icon: "default"},
        {id: "sqsfDeviceList", label: "四氢噻吩", icon: "default"},
        {id: "gasVideo", label: "视频监控", icon: "default"},
    ],
    misHeatMonitor: [
        {id: "wdjn", label: "温度胶囊", icon: "default"},
        {id: "swcjq", label: "室温采集器", icon: "default"},
        {id: "heatVideo", label: "视频监控", icon: "default"},
    ],
    misDrainWaterMonitor: [
        {id: "drainWaterLevelMeter", label: "液位计", icon: "default"},
        {id: "drainWaterFlowmeter", label: "流量计", icon: "default"},
        {id: "drainWaterRainGauge", label: "雨量计", icon: "default"},
        {id: "drainWaterVideo", label: "视频监控", icon: "default"},
    ],
    misSupplyWaterMonitor: [
        {id: "supplyWaterFlowmeter", label: "流量计", icon: "default"},
        {id: "supplyWaterManometer", label: "压力计", icon: "default"},
        {id: "supplyWaterQualityAnalyzer", label: "水质监测仪", icon: "default"},
        {id: "supplyWaterLeakDetector", label: "漏失监测仪", icon: "default"},
        {id: "supplyWaterVideo", label: "视频监控", icon: "default"},
    ],
};

export const defaultShowLayersMap = {
    "/comprehensive/overview": [
        'gas_station', 'gas_well', 'gas_dangerous_source', 'gas_protection_target',
    ], // 综合管理-总览
    "/comprehensive/risk": [
        'gas_station', 'gas_well', 'gas_dangerous_source', 'gas_protection_target',
    ], // 综合管理-风险隐患
    "/comprehensive/monitoring": [
        'gas_station', 'gas_well', 'gas_dangerous_source', 'gas_protection_target',
    ], // 综合管理-运行监测
    "/comprehensive/coordination": [
        'gas_station', 'gas_well', 'gas_dangerous_source', 'gas_protection_target',
    ], // 综合管理-协同指挥
    "/comprehensive/emergency": [
        'gas_station', 'gas_well', 'gas_dangerous_source', 'gas_protection_target',
    ], // 综合管理-应急辅助决策
    "/gas/overview": [
        'gas_station', 'gas_well', 'gas_dangerous_source', 'gas_protection_target',
    ], // 燃气管理-总览
    "/gas/network-risk": [
        'gas_station', 'gas_well', 'gas_dangerous_source', 'gas_protection_target',
    ], // 燃气管理-管线风险
    "/gas/monitoring": [
        'gas_station', 'gas_well', 'gas_dangerous_source', 'gas_protection_target',
    ], // 燃气管理-运行监测
    "/gas/decision-screen": [
        'gas_station', 'gas_well', 'gas_dangerous_source', 'gas_protection_target',
    ], // 燃气管理-辅助决策
    "/drainage/overview": [
        'drainage_pump_station', 'drainage_sewage_works', 'drainage_water_outlet',
        'drainage_well', 'drainage_flooding_point', 'drainage_dangerous_source', 'drainage_protection_target',
    ], // 排水管理-总览
    "/drainage/risk": [
        'drainage_pump_station', 'drainage_sewage_works', 'drainage_water_outlet',
        'drainage_well', 'drainage_flooding_point', 'drainage_dangerous_source', 'drainage_protection_target',
    ], // 排水管理-风险隐患
    "/drainage/flooding-risk": [
        'drainage_pump_station', 'drainage_sewage_works', 'drainage_water_outlet',
        'drainage_well', 'drainage_flooding_point', 'drainage_dangerous_source', 'drainage_protection_target',
    ], // 排水管理-易涝点风险
    "/drainage/monitoring": [
        'drainage_pump_station', 'drainage_sewage_works', 'drainage_water_outlet',
        'drainage_well', 'drainage_flooding_point', 'drainage_dangerous_source', 'drainage_protection_target',
    ], // 排水管理-运行监测
    "/drainage/decision": [
        'drainage_pump_station', 'drainage_sewage_works', 'drainage_water_outlet',
        'drainage_well', 'drainage_flooding_point', 'drainage_dangerous_source', 'drainage_protection_target',
    ], // 排水管理-辅助决策
    "/heating/overview": [
        'heating_well', 'heating_enterprise', 'heating_source_works', 'heating_station',
        'heating_user', 'heating_dangerous_source', 'heating_protection_target',
    ], // 供热管理-总览
    "/heating/risk": [
        'heating_well', 'heating_enterprise', 'heating_source_works', 'heating_station',
        'heating_user', 'heating_dangerous_source', 'heating_protection_target',
    ], // 供热管理-风险隐患
    "/heating/monitoring": [
        'heating_well', 'heating_enterprise', 'heating_source_works', 'heating_station',
        'heating_user', 'heating_dangerous_source', 'heating_protection_target',
    ], // 供热管理-运行监测
    "/heating/decision": [
        'heating_well', 'heating_enterprise', 'heating_source_works', 'heating_station',
        'heating_user', 'heating_dangerous_source', 'heating_protection_target',
    ], // 供热管理-辅助决策
    "/bridge": [
        'bridge_info', 'bridge_safety_rating',
    ], // 桥梁管理-总览
    "/bridge/overview": [
        'bridge_info', 'bridge_safety_rating',
    ], // 桥梁管理-总览
    "/bridge/risk": [
        'bridge_info', 'bridge_safety_rating',
    ], // 桥梁管理-风险隐患
    "/bridge/monitoring": [
        'bridge_info', 'bridge_safety_rating',
    ], // 桥梁管理-运行监测
    "/bridge/decision": [
        'bridge_info', 'bridge_safety_rating',
    ], // 桥梁管理-辅助决策
};


export const layerQueryInfo = {
    misGasMonitor: [
        "xldxeviceList",
        "gsyUserDeviceList",
        "userDeviceList",
        "mdylDeviceList",
        "sqsfDeviceList",
        "gasVideo",
    ],
    misHeatMonitor: ["wdjn", "swcjq", "heatVideo"],
    misDrainWaterMonitor: [
        "drainWaterLevelMeter",
        "drainWaterFlowmeter",
        "drainWaterRainGauge",
        "drainWaterVideo",
    ],
    misSupplyWaterMonitor: [
        "supplyWaterFlowmeter",
        "supplyWaterManometer",
        "supplyWaterQualityAnalyzer",
        "supplyWaterLeakDetector",
        "supplyWaterVideo",
    ],
    xldxeviceList: ["xldxeviceList"],
    gsyUserDeviceList: ["gsyUserDeviceList"],
    userDeviceList: ["userDeviceList"],
    mdylDeviceList: ["mdylDeviceList"],
    sqsfDeviceList: ["sqsfDeviceList"],
    gasVideo: ["gasVideo"],
    wdjn: ["wdjn"],
    swcjq: ["swcjq"],
    heatVideo: ["heatVideo"],
    drainWaterLevelMeter: ["drainWaterLevelMeter"],
    drainWaterFlowmeter: ["drainWaterFlowmeter"],
    drainWaterRainGauge: ["drainWaterRainGauge"],
    drainWaterVideo: ["drainWaterVideo"],
    supplyWaterManometer: ["supplyWaterManometer"],
    supplyWaterFlowmeter: ["supplyWaterFlowmeter"],
    supplyWaterQualityAnalyzer: ["supplyWaterQualityAnalyzer"],
    supplyWaterLeakDetector: ["supplyWaterLeakDetector"],
    supplyWaterVideo: ["supplyWaterVideo"],
    supplyWater_Source: ["supplyWater_Source"],
    supplyWater_Yj_Source: ["supplyWater_Yj_Source"],
    supplyWater_Factory: ["supplyWater_Factory"],
    supplyWater_Facility: ["supplyWater_Facility"],
    supplyWater_Station: ["supplyWater_Station"],
};

export const layerParentId = {
    gas_combustible: "gas_combustible",
};

export const legendInfo = {
    misMonitor: [
        {label: "正常设备", color: "#25F1FF"},
        {label: "离线设备", color: "#A6A6A6"},
        {label: "报警设备", color: "#FF0000"},
    ],
};

//请求数据的图层对应的入参
export const requestDataMap = {
    gas_pipeline_point: {
        api: postUsmZyGasPointList,
        params: {},
    }, // 燃气管线点
    gas_station: {
        api: postUsmZyGasStationList,
        params: {},
    },
    gas_well: {
        api: postUsmZyGasWellList,
        params: {},
    },
    gas_dangerous_source: {
        api: postUsmZyGasDangerList,
        params: {},
    },
    gas_protection_target: {
        api: postUsmZyGasProtectList,
        params: {},
    },
    gas_combustible: {
        api: postUsmMonitorDeviceList,
        params: {},
        filterList: ['laserMethane', 'wbFixedCh4GrassDetector'], // 过滤器
        filterColumn: 'deviceType',  //字段过滤
    },
    gas_manhole_cover: {
        api: postUsmMonitorDeviceList,
        params: {},
        filterList: ['manholeCoverDevice', 'wbManholeCoverSensor'], // 过滤器
        filterColumn: 'deviceType',  //字段过滤
    },
    drainage_pump_station:{
        api: postUsmBasicPumpStationList,
        params: {},
    },
    drainage_sewage_works: {
        api: postUsmBasicSewageFactoryList,
        params: {},
    },
    drainage_water_outlet: {
        api: postUsmBasicDrainOutletList,
        params: {},
    },
    drainage_pipeline_point: {
        api: postDrainUsmBasicPointList,
        params: {},
    },
    drainage_well: {
        api: postDrainUsmBasicWellList,
        params: {},
    },
    drainage_flooding_point: {
        api: postDrainUsmBasicFloodPointList,
        params: {},
    },
    drainage_dangerous_source: {
        api: postDrainUsmRiskDangerList,
        params: {},
    },
    drainage_protection_target: {
        api: postDrainUsmRiskProtectList,
        params: {},
    },
    drainage_combustible: {
        api: postDrainUsmMonitorDeviceList,
        params: {},
        filterList: ['laserMethane', 'aoruideMethane'], // 过滤器
        filterColumn: 'deviceType',  //字段过滤
    },
    drainage_manhole_cover: {
        api: postDrainUsmMonitorDeviceList,
        params: {},
        filterList: ['manholeCoverDevice', 'wbManholeCoverSensor', 'thManholeCover'], // 过滤器
        filterColumn: 'deviceType',  //字段过滤
    },
    drainage_level: {
        api: postDrainUsmMonitorDeviceList,
        params: {},
        filterList: ['wbUgLevelGauger'], // 过滤器
        filterColumn: 'deviceType',  //字段过滤
    },
    drainage_water_quality: {
        api: postDrainUsmMonitorDeviceList,
        params: {},
        filterList: ['waterQualityMonitor'], // 过滤器
        filterColumn: 'deviceType',  //字段过滤
    },
    heating_pipeline_point: {
        api: postHeatUsmBasicPointList,
        params: {},
    },
    heating_well: {
        api: postHeatUsmBasicWellList,
        params: {},
    },
    heating_enterprise: {
        api: postHeatUsmBasicEnterpriseList,
        params: {},
    },
    heating_source_works: {
        api: postHeatUsmBasicHeatFactoryList,
        params: {},
    },
    heating_station: {
        api: postHeatUsmBasicHeatStationList,
        params: {},
    },
    heating_user: {
        api: postHeatUsmBasicUserList,
        params: {},
    },
    heating_dangerous_source: {
        api: postHeatUsmRiskDangerList,
        params: {},
    },
    heating_protection_target: {
        api: postHeatUsmRiskProtectList,
        params: {},
    },
    heating_combustible: {
        api: postHeatUsmMonitorDeviceList,
        params: {},
        filterList: ['laserMethane', 'wbFixedCh4GrassDetector'], // 过滤器
        filterColumn: 'deviceType',  //字段过滤
    },
    heating_manhole_cover: {
        api: postHeatUsmMonitorDeviceList,
        params: {},
        filterList: ['wbManholeCoverSensor', 'manholeCoverDevice'], // 过滤器
        filterColumn: 'deviceType',  //字段过滤
    },
    heating_temperature: {
        api: postHeatUsmMonitorDeviceList,
        params: {},
        filterList: ['wbManholeThSensor'], // 过滤器
        filterColumn: 'deviceType',  //字段过滤
    },
    bridge_temperature: {
        api: postBridgeMapDevicePointsList,
        params: {},
        filterList: ['bridgeGxWd'], // 过滤器
        filterColumn: 'deviceType',  //字段过滤
    },
    bridge_displacement: {
        api: postBridgeMapDevicePointsList,
        params: {},
        filterList: ['bridgeGxWy'], // 过滤器
        filterColumn: 'deviceType',  //字段过滤
    },
    bridge_static_level: {
        api: postBridgeMapDevicePointsList,
        params: {},
        filterList: ['bridgeGxJl'], // 过滤器
        filterColumn: 'deviceType',  //字段过滤
    },
    bridge_crack_sensor: {
        api: postBridgeMapDevicePointsList,
        params: {},
        filterList: ['bridgeGxLf'], // 过滤器
        filterColumn: 'deviceType',  //字段过滤
    },
    bridge_strain: {
        api: postBridgeMapDevicePointsList,
        params: {},
        filterList: ['bridgeGxYb'], // 过滤器
        filterColumn: 'deviceType',  //字段过滤
    },
};

export const offsetPopupList = [
    'gas_flowmeter', // 流量计
    'gas_manometer', // 压力计
    'gas_combustible', // 可燃气体探测器
    'gas_temperature', // 温度计
    'gas_manhole_cover', // 井盖传感器
    'drainage_level', // 水位计
    'drainage_flowmeter', // 水流计
    'drainage_rain', // 雨量计
    'drainage_water_quality', // 水质计
    'drainage_combustible', // 可燃气体探测器
    'drainage_manhole_cover', // 井盖传感器
    'heating_level', // 水位计
    'heating_flowmeter', // 水流计
    'heating_rain', // 雨量计
    'heating_water_quality', // 水质计
    'heating_combustible', // 可燃气体探测器
    'heating_temperature', // 温度计
    'heating_manhole_cover', // 井盖传感器
    'bridge_wind_speed', // 风速计
    'bridge_static_level', // 静态水准仪
    'bridge_temperature', // 温度计
    'bridge_humidity', // 湿度计
    'bridge_vibration', // 振动传感器
    'bridge_dynamic_weight', // 动静称重计
    'bridge_displacement', // 位移计
    'bridge_tilt', // 倾角计
    'bridge_deflection', // 挠度计
    'bridge_strain', // 应变计
    'bridge_load_cell', // 负载传感器
    'bridge_acceleration', // 加速度计
    'bridge_crack', // 车船撞击传感器
    'bridge_tilt_alarm', // 倾角报警
    'bridge_crack_sensor', // 裂缝传感器
    'bridge_vibration_sensor', // 振动传感器
]
