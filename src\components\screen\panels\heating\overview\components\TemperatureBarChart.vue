<template>
  <div ref="chartRef" class="temperature-bar-chart"></div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import * as echarts from 'echarts'

// 图表实例
const chartRef = ref(null)
let chartInstance = null

// 模拟数据
const chartData = [
  { name: '18°C 以下', value: 35, color: '#5ECBEC' },
  { name: '18-20°C', value: 80, color: '#5EEC92' },
  { name: '20-24°C', value: 10, color: '#ECD55E' },
  { name: '24°C以上', value: 25, color: '#EC5E5E' }
]

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return
  
  // 设置一个定时器，确保DOM已经完全渲染
  setTimeout(() => {
    // 创建图表实例
    chartInstance = echarts.init(chartRef.value)
    
    // 渲染图表
    updateChart()
    
    // 监听窗口大小变化
    window.addEventListener('resize', handleResize)
  }, 0)
}

// 更新图表
const updateChart = () => {
  const option = {
    grid: {
      left: '5%',
      right: '2%',
      bottom: '10%',
      top: '10%',
      containLabel: true
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: '{b}: {c}%'
    },
    xAxis: {
      type: 'category',
      data: chartData.map(item => item.name),
      axisLine: {
        show: true,
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.3)'
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#fff',
        fontSize: 12,
        interval: 0,
        margin: 10
      }
    },
    yAxis: {
      type: 'value',
      max: 100,
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        show: false
      },
      splitLine: {
        show: false
      }
    },
    series: [
      // 背景柱子（100%）
      {
        type: 'bar',
        barWidth: 22,
        data: chartData.map(() => 100),
        barGap: '-100%', // 使背景柱和数据柱重叠
        itemStyle: {
          color: 'rgba(15, 202, 255, 0.14)',
          borderRadius: 11
        },
        z: 1,
        silent: true, // 不触发交互事件
        animation: false
      },
      // 数据柱子
      {
        type: 'bar',
        barWidth: 22,
        data: chartData.map(item => ({
          value: item.value,
          itemStyle: {
            color: item.color,
            borderRadius: 11
          },
          label: {
            show: true,
            position: 'top',
            formatter: '{c}%',
            fontSize: 12,
            color: '#fff'
          }
        })),
        z: 2
      }
    ]
  }
  
  // 设置图表配置
  chartInstance.setOption(option)
}

// 处理窗口大小变化
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

// 组件挂载时初始化图表
onMounted(() => {
  // 使用requestAnimationFrame确保DOM已渲染
  window.requestAnimationFrame(() => {
    initChart()
  })
})

// 组件卸载时销毁图表
onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.temperature-bar-chart {
  width: 100%;
  height: 100%;
}
</style> 