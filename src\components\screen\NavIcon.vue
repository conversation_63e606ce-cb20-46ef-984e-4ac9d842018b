<template>
  <div 
    class="nav-icon-wrapper" 
    :class="{ 'active': isActive }"
    @click="$emit('click', name)"
  >
    <div class="nav-icon-inner">
      <i class="fas" :class="iconName"></i>
    </div>
    <div class="nav-icon-text">{{ name }}</div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  name: {
    type: String,
    required: true
  },
  isActive: {
    type: Boolean,
    default: false
  }
})

// 根据导航名称映射到对应的图标
const iconName = computed(() => {
  const iconMap = {
    '管理': 'fa-cog',
    '监控': 'fa-video',
    '预警': 'fa-bell',
    '设置': 'fa-sliders-h',
    '数据': 'fa-database',
    '报告': 'fa-file-alt',
    '用户': 'fa-user',
    '统计': 'fa-chart-bar'
  }
  
  return iconMap[props.name] || 'fa-circle'
})

defineEmits(['click'])
</script>

<style scoped>
.nav-icon-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  padding: 5px 15px;
}

.nav-icon-inner {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(0, 242, 241, 0.05);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid rgba(0, 242, 241, 0.2);
  transition: all 0.3s ease;
  margin-bottom: 5px;
}

.nav-icon-wrapper:hover .nav-icon-inner {
  background: rgba(0, 242, 241, 0.1);
  transform: translateY(-5px);
}

.nav-icon-wrapper.active .nav-icon-inner {
  background: rgba(0, 242, 241, 0.2);
  border-color: rgba(0, 242, 241, 0.4);
  box-shadow: 0 0 15px rgba(0, 242, 241, 0.3);
}

.nav-icon-wrapper.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 3px;
  background: #00f2f1;
  border-radius: 2px;
}

.nav-icon-wrapper i {
  font-size: 1.2rem;
  color: #00f2f1;
}

.nav-icon-text {
  color: white;
  font-size: 0.8rem;
  margin-top: 2px;
}

.nav-icon-wrapper.active .nav-icon-text {
  color: #00f2f1;
}
</style> 