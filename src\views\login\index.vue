<template>
  <div class="login-container">
    <el-card class="form-outter border-none bg-transparent w-full sm:w-4/5">
      <div class="text-center relative">
        <h2>东明县城市生命线物联感知平台</h2>
      </div>

      <el-form
        ref="loginFormRef"
        :model="loginData"
        :rules="loginRules"
        class="login-form"
      >
        <!-- 用户名 -->
        <el-form-item prop="username">
          <div class="flex items-center w-full">
            <el-icon class="mx-2"><User /></el-icon>
            <el-input
              ref="username"
              v-model="loginData.username"
              placeholder="请输入用户名"
              name="username"
              size="large"
              class="h-12"
            />
          </div>
        </el-form-item>

        <!-- 密码 -->
        <el-form-item prop="password">
          <div class="flex items-center w-full">
            <el-icon class="mx-2"><Lock /></el-icon>
            <el-input
              v-model="loginData.password"
              placeholder="请输入密码"
              type="password"
              name="password"
              @keyup.enter="handleLogin"
              size="large"
              class="h-12 pr-2"
              show-password
            />
          </div>
        </el-form-item>

        <!-- 登录按钮 -->
        <el-button
          :loading="loading"
          type="primary"
          size="large"
          class="w-full"
          @click.prevent="handleLogin"
        >
          {{ loading ? '登录中...' : '登录' }}
        </el-button>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { User, Lock } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

const loading = ref(false)
const loginFormRef = ref(null)

const loginData = ref({
  username: 'super-admin',
  password: 'Gxgas@2022'
})

const loginRules = computed(() => {
  return {
    username: [
      {
        required: true,
        trigger: 'blur',
        message: '请输入用户名'
      }
    ],
    password: [
      {
        required: true,
        trigger: 'blur',
        message: '请输入密码'
      },
      {
        min: 6,
        message: '密码长度至少为6位',
        trigger: 'blur'
      }
    ]
  }
})

function handleLogin() {
  loginFormRef.value.validate((valid) => {
    if (valid) {
      loading.value = true
      userStore
        .login(loginData.value)
        .then(() => {
          const redirect = route.query.redirect || '/'
          const otherQuery = Object.keys(route.query).reduce((acc, cur) => {
            if (cur !== 'redirect') {
              acc[cur] = route.query[cur]
            }
            return acc
          }, {})

          router.push({ path: redirect, query: otherQuery })
            .catch(err => {
              console.warn(err)
            })
        })
        .catch((error) => {
          ElMessage.error(error.message || '登录失败')
        })
        .finally(() => {
          loading.value = false
        })
    }
  })
}
</script>

<style lang="scss" scoped>
.login-container {
  @apply min-h-screen w-full flex items-center justify-center overflow-y-auto;
  background: url("@/assets/images/login/login_bg1920.png") center right;
  background-size: cover;

  .form-outter {
    width: 560px;
    height: 620px;
    padding: 102px 90px;
    background: url("@/assets/images/login/login_form_bg.png") no-repeat;
    background-size: 100% 100%;

    .relative {
      h2 {
        font-size: 24px;
        font-weight: 700;
        color: #fff;
        margin-bottom: 30px;
      }
    }
  }

  .login-form {
    padding: 30px 10px;
  }
}

.el-form-item {
  margin-bottom: 32px;
  background: var(--el-input-bg-color);
  border: 1px solid var(--el-border-color);
  border-radius: 5px;

  :deep(.el-form-item__content) {
    background-color: #fff;
  }
}

:deep(.el-input) {
  .el-input__wrapper {
    padding: 0;
    background-color: transparent;
    box-shadow: none;

    &.is-focus,
    &:hover {
      box-shadow: none !important;
    }

    input:-webkit-autofill {
      transition: background-color 1000s ease-in-out 0s;
    }
  }
}
</style> 