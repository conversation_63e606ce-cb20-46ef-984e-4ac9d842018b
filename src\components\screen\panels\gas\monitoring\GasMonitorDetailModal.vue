<template>
  <teleport to="body">
    <transition name="fade">
      <div v-if="modelValue" class="modal-overlay" @click.self="closeModal">
        <div class="modal-container">
          <div class="modal-header">
            <div class="modal-title">可燃气体监测仪详情</div>
            <div class="close-icon" @click="closeModal">×</div>
          </div>
          <div class="modal-tabs">
            <div 
              v-for="(tab, index) in tabs" 
              :key="index"
              :class="['tab-item', { active: activeTab === index }]"
              @click="setActiveTab(index)"
            >
              {{ tab }}
            </div>
          </div>
          <div class="modal-content">
            <!-- 报警信息 -->
            <div v-if="activeTab === 0" class="tab-content alarm-info">
              <div class="info-row">
                <span class="info-label">报警编号:</span>
                <span class="info-value">{{ alarmData.code || '-' }}</span>
              </div>
              <div class="info-row">
                <span class="info-label">报警指标:</span>
                <span class="info-value">{{ alarmData.indicator || '甲烷浓度' }}</span>
              </div>
              <div class="info-row">
                <span class="info-label">当前报警等级:</span>
                <span class="info-value">{{ alarmData.currentLevel || '-' }}</span>
              </div>
              <div class="info-row">
                <span class="info-label">最高报警等级:</span>
                <span class="info-value">{{ alarmData.highestLevel || 'I级报警' }}</span>
              </div>
              <div class="info-row">
                <span class="info-label">报警值:</span>
                <span class="info-value">{{ alarmData.value || '3.0' }} (%VOL)</span>
              </div>
              <div class="info-row">
                <span class="info-label">报警状态:</span>
                <span class="info-value">{{ alarmData.status || '-' }}</span>
              </div>
              <div class="info-row">
                <span class="info-label">报警时间:</span>
                <span class="info-value">{{ alarmData.time || '-' }}</span>
              </div>
            </div>
            
            <!-- 设备信息 -->
            <div v-if="activeTab === 1" class="tab-content device-info">
              <div class="device-gauge">
                <div ref="gaugeChartRef" class="gauge-container"></div>
                <div class="gauge-select">
                  <select v-model="selectedGaugeType">
                    <option value="concentration">浓度</option>
                    <option value="voltage">电量</option>
                  </select>
                </div>
              </div>
              <div class="device-info-grid">
                <div class="info-row">
                  <span class="info-label">设备名称:</span>
                  <span class="info-value">{{ deviceData.name || '可燃气体监测仪' }}</span>
                </div>
                <div class="info-row">
                  <span class="info-label">设备编号:</span>
                  <span class="info-value">{{ deviceData.code || 'BD-PT001' }}</span>
                </div>
                <div class="info-row">
                  <span class="info-label">设备类型:</span>
                  <span class="info-value">{{ deviceData.type || '扩散式' }}</span>
                </div>
                <div class="info-row">
                  <span class="info-label">监测指标:</span>
                  <span class="info-value">{{ deviceData.indicator || 'xxxxx' }}</span>
                </div>
                <div class="info-row">
                  <span class="info-label">安装时间:</span>
                  <span class="info-value">{{ deviceData.installTime || '2019年12月2日' }}</span>
                </div>
                <div class="info-row">
                  <span class="info-label">权属单位:</span>
                  <span class="info-value">{{ deviceData.owner || '华瑞燃气' }}</span>
                </div>
                <div class="info-row">
                  <span class="info-label">设备状态:</span>
                  <span class="info-value">{{ deviceData.status || '在线' }}</span>
                </div>
                <div class="info-row">
                  <span class="info-label">位置信息:</span>
                  <span class="info-value">{{ deviceData.location || '******' }}</span>
                </div>
              </div>
            </div>
            
            <!-- 监测曲线 -->
            <div v-if="activeTab === 2" class="tab-content curve-info">
              <div class="curve-header">
                <div class="time-filter">
                  <div 
                    v-for="(period, idx) in timePeriods" 
                    :key="idx"
                    :class="['period-btn', { active: selectedPeriod === period.value }]"
                    @click="setTimePeriod(period.value)"
                  >
                    {{ period.label }}
                  </div>
                </div>
                
                <div class="curve-controls">
                  <div class="date-range">
                    {{ dateRange }}
                  </div>
                  <div class="indicator-select">
                    <select v-model="selectedIndicator">
                      <option value="concentration">浓度</option>
                      <option value="voltage">电量</option>
                    </select>
                  </div>
                </div>
              </div>
              <div ref="lineChartRef" class="line-chart-container"></div>
            </div>
            
            <!-- 处置流程 -->
            <div v-if="activeTab === 3" class="tab-content process-info">
              <div class="process-status">
                <div class="process-status-item">
                  <div class="status-label blue-bg">报警</div>
                  <div class="process-group">
                    <div class="process-item">
                      <div class="process-label">报警时间</div>
                      <div class="process-value">{{ processData.alarmTime || '2020.02.29 22:00:00' }}</div>
                    </div>
                    <div class="process-item">
                      <div class="process-label">报警级别</div>
                      <div class="process-value">{{ processData.alarmLevel || '三级报警' }}</div>
                    </div>
                  </div>
                </div>
                
                <div class="process-status-item">
                  <div class="status-label blue-bg">管办</div>
                  <div class="process-group">
                    <div class="process-item">
                      <div class="process-label">管办单位</div>
                      <div class="process-value">{{ processData.department || 'XXXXX公司' }}</div>
                    </div>
                    <div class="process-item">
                      <div class="process-label">处置期限</div>
                      <div class="process-value">{{ processData.deadline || '2020.02.29 22:00:00' }}</div>
                    </div>
                  </div>
                </div>
                
                <div class="process-status-item">
                  <div class="status-label blue-bg">处置</div>
                  <div class="process-group">
                    <div class="process-item">
                      <div class="process-label">处置时间</div>
                      <div class="process-value">{{ processData.handlingTime || '2020.02.29 22:00:00' }}</div>
                    </div>
                    <div class="process-item">
                      <div class="process-label">处置说明</div>
                      <div class="process-value">{{ processData.description || '更换配件' }}</div>
                    </div>
                    <div class="process-item">
                      <div class="process-label">处置图片</div>
                      <div class="process-images">
                        <div 
                          v-for="(image, imgIdx) in (processData.images || ['', ''])" 
                          :key="imgIdx" 
                          class="image-preview"
                        >
                          <SvgIcon name="image" color="#0088FF" size="18px" />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </transition>
  </teleport>
</template>

<script setup>
import { ref, defineProps, defineEmits, onMounted, watch, nextTick } from 'vue';
import * as echarts from 'echarts';
import SvgIcon from '@/components/SvgIcon.vue';

const props = defineProps({
  modelValue: Boolean,
  alarmId: String,
  deviceId: String
});

const emit = defineEmits(['update:model-value']);

// 标签页相关数据
const tabs = ['报警信息', '设备信息', '监测曲线', '处置流程'];
const activeTab = ref(0);

// 图表引用
const gaugeChartRef = ref(null);
const lineChartRef = ref(null);
let gaugeChart = null;
let lineChart = null;

// 时间周期选项
const timePeriods = [
  { label: '24小时', value: '24h' },
  { label: '7天', value: '7d' },
  { label: '30天', value: '30d' }
];
const selectedPeriod = ref('24h');
const dateRange = ref('2023-08-12 ~ 2023-08-18');

// 选择的指标类型
const selectedGaugeType = ref('concentration');
const selectedIndicator = ref('concentration');

// 模拟数据 - 实际项目中应该从API获取
const alarmData = ref({
  code: '-',
  indicator: '甲烷浓度',
  currentLevel: '-',
  highestLevel: 'I级报警',
  value: '3.0',
  status: '-',
  time: '-'
});

const deviceData = ref({
  name: '可燃气体监测仪',
  code: 'BD-PT001',
  type: '扩散式',
  indicator: 'xxxxx',
  installTime: '2019年12月2日',
  owner: '华瑞燃气',
  status: '在线',
  location: '******'
});

const processData = ref({
  alarmTime: '2020.02.29 22:00:00',
  alarmLevel: '三级报警',
  department: 'XXXXX公司',
  deadline: '2020.02.29 22:00:00',
  handlingTime: '2020.02.29 22:00:00',
  description: '更换配件',
  images: ['/img/example1.jpg', '/img/example2.jpg']
});

// 关闭弹窗
const closeModal = () => {
  emit('update:model-value', false);
};

// 切换标签页
const setActiveTab = (index) => {
  activeTab.value = index;
  nextTick(() => {
    switch (index) {
      case 1:
        initGaugeChart();
        break;
      case 2:
        initLineChart();
        break;
    }
  });
};

// 设置时间周期
const setTimePeriod = (period) => {
  selectedPeriod.value = period;
  // 根据所选周期更新日期范围显示
  switch (period) {
    case '24h':
      dateRange.value = '2023-08-18';
      break;
    case '7d':
      dateRange.value = '2023-08-12 ~ 2023-08-18';
      break;
    case '30d':
      dateRange.value = '2023-07-19 ~ 2023-08-18';
      break;
  }
  nextTick(() => {
    initLineChart();
  });
};

// 初始化仪表盘图表
const initGaugeChart = () => {
  if (!gaugeChartRef.value) return;
  
  if (gaugeChart) {
    gaugeChart.dispose();
  }
  
  gaugeChart = echarts.init(gaugeChartRef.value);
  
  const option = {
    series: [
      {
        type: 'gauge',
        radius: '100%',
        startAngle: 180,
        endAngle: 0,
        min: 0,
        max: 100,
        splitNumber: 5,
        axisLine: {
          lineStyle: {
            width: 15,
            color: [
              [0.3, '#006CFF'],
              [0.7, '#90FF40'],
              [1, '#FF4D4D']
            ]
          }
        },
        pointer: {
          icon: 'path://M2090.36389,615.30999 L2090.36389,615.30999 C2091.48372,615.30999 2092.40383,616.23010 2092.40383,617.34993 L2092.40383,617.34993 C2092.40383,618.46975 2091.48372,619.38987 2090.36389,619.38987 L2090.36389,619.38987 C2089.24406,619.38987 2088.32395,618.46975 2088.32395,617.34993 L2088.32395,617.34993 C2088.32395,616.23010 2089.24406,615.30999 2090.36389,615.30999 Z',
          length: '75%',
          width: 5,
          offsetCenter: [0, '5%'],
          itemStyle: {
            color: '#FFFFFF'
          }
        },
        axisTick: {
          length: 12,
          lineStyle: {
            color: 'auto',
            width: 2
          }
        },
        splitLine: {
          length: 20,
          lineStyle: {
            color: 'auto',
            width: 3
          }
        },
        axisLabel: {
          color: '#FFFFFF',
          fontSize: 12,
          distance: -40,
          formatter: function(value) {
            if (value === 0 || value === 100) {
              return value + (selectedGaugeType.value === 'concentration' ? '%vol' : '%');
            }
            return '';
          }
        },
        title: {
          offsetCenter: [0, '50%'],
          fontSize: 12,
          color: '#FFFFFF',
          fontWeight: 'normal',
          formatter: '监测值'
        },
        detail: {
          fontSize: 36,
          offsetCenter: [0, '0%'],
          valueAnimation: true,
          formatter: function(value) {
            return value.toFixed(1) + (selectedGaugeType.value === 'concentration' ? '%vol' : '%');
          },
          color: '#FFFFFF',
          fontWeight: 'bold'
        },
        data: [
          {
            value: 1.5,
            name: '监测值'
          }
        ]
      }
    ]
  };
  
  gaugeChart.setOption(option);
};

// 初始化折线图表
const initLineChart = () => {
  if (!lineChartRef.value) return;
  
  if (lineChart) {
    lineChart.dispose();
  }
  
  lineChart = echarts.init(lineChartRef.value);
  
  // 模拟数据
  const days = ['8-12', '8-13', '8-14', '8-15', '8-16', '8-17', '8-18'];
  const data = [1.5, 1.4, 1.3, 1.2, 0.8, 1.3, 1.5];
  
  const option = {
    grid: {
      top: 10,
      right: 10,
      bottom: 20,
      left: 40,
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: days,
      axisLine: {
        lineStyle: {
          color: '#5F5F60'
        }
      },
      axisTick: {
        show: true,
        alignWithLabel: true,
        lineStyle: {
          color: '#5F5F60'
        }
      },
      axisLabel: {
        color: '#FFFFFF',
        fontSize: 12
      }
    },
    yAxis: {
      type: 'value',
      name: selectedIndicator.value === 'concentration' ? '甲烷浓度 (%VOL)' : '电量 (%)',
      nameTextStyle: {
        color: '#FFFFFF',
        fontSize: 12,
        align: 'left'
      },
      min: 0,
      max: 2.5,
      interval: 0.5,
      axisLine: {
        show: true,
        lineStyle: {
          color: '#5F5F60'
        }
      },
      axisTick: {
        show: true,
        lineStyle: {
          color: '#5F5F60'
        }
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)',
          type: 'dashed'
        }
      },
      axisLabel: {
        color: '#FFFFFF',
        fontSize: 12
      }
    },
    series: [
      {
        data: data,
        type: 'line',
        smooth: true,
        symbol: 'circle',
        symbolSize: 8,
        itemStyle: {
          color: '#F8DE48'
        },
        lineStyle: {
          color: '#F8DE48',
          width: 3
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: 'rgba(248, 222, 72, 0.3)'
            },
            {
              offset: 1,
              color: 'rgba(248, 222, 72, 0.1)'
            }
          ])
        }
      }
    ]
  };
  
  lineChart.setOption(option);
};

// 获取设备详情数据
const fetchDeviceDetail = () => {
  // 这里预留接口请求
  // 示例：
  // const response = await api.getDeviceDetail(props.deviceId);
  // if (response.code === 200) {
  //   deviceData.value = response.data;
  // }
};

// 获取报警详情数据
const fetchAlarmDetail = () => {
  // 这里预留接口请求
  // 示例：
  // const response = await api.getAlarmDetail(props.alarmId);
  // if (response.code === 200) {
  //   alarmData.value = response.data;
  // }
};

// 获取监测曲线数据
const fetchMonitoringData = () => {
  // 这里预留接口请求
  // 示例：
  // const params = { deviceId: props.deviceId, period: selectedPeriod.value };
  // const response = await api.getMonitoringData(params);
  // if (response.code === 200) {
  //   // 更新曲线图数据
  //   initLineChart(response.data);
  // }
};

// 获取处置流程数据
const fetchProcessData = () => {
  // 这里预留接口请求
  // 示例：
  // const response = await api.getProcessData(props.alarmId);
  // if (response.code === 200) {
  //   processData.value = response.data;
  // }
};

// 监听弹窗显示状态变化
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    fetchDeviceDetail();
    fetchAlarmDetail();
    fetchProcessData();
    
    nextTick(() => {
      setActiveTab(0);
    });
  } else {
    // 清理资源
    if (gaugeChart) {
      gaugeChart.dispose();
      gaugeChart = null;
    }
    
    if (lineChart) {
      lineChart.dispose();
      lineChart = null;
    }
  }
});

// 监听指标类型选择变化
watch([selectedGaugeType, selectedIndicator], () => {
  nextTick(() => {
    if (activeTab.value === 1) {
      initGaugeChart();
    } else if (activeTab.value === 2) {
      initLineChart();
    }
  });
});

onMounted(() => {
  if (props.modelValue) {
    fetchDeviceDetail();
    fetchAlarmDetail();
    fetchProcessData();
  }
});
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.modal-container {
  width: 550px;
  background: linear-gradient(180deg, rgba(0, 22, 72, 0.9) 0%, rgba(0, 35, 91, 0.9) 100%);
  border: 1px solid rgba(59, 141, 242, 0.5);
  box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.3);
  border-radius: 4px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid rgba(59, 141, 242, 0.3);
}

.modal-title {
  font-family: PingFangSC, 'PingFang SC';
  font-weight: 500;
  font-size: 16px;
  color: #FFFFFF;
}

.close-icon {
  font-size: 20px;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
}

.close-icon:hover {
  color: #FFFFFF;
}

.modal-tabs {
  display: flex;
  border-bottom: 1px solid rgba(59, 141, 242, 0.3);
}

.tab-item {
  flex: 1;
  padding: 10px 0;
  text-align: center;
  font-family: PingFangSC, 'PingFang SC';
  font-weight: 400;
  font-size: 14px;
  color: #FFFFFF;
  cursor: pointer;
  position: relative;
  background-color: #003366;
}

.tab-item.active {
  background-color: #0066CC;
  font-weight: 500;
}

.tab-item:not(:last-child) {
  border-right: 1px solid rgba(59, 141, 242, 0.3);
}

.modal-content {
  padding: 15px 20px;
  max-height: 400px;
  overflow-y: auto;
}

/* 报警信息样式 */
.alarm-info .info-row {
  margin-bottom: 15px;
  display: flex;
}

.info-label {
  font-family: PingFangSC, 'PingFang SC';
  font-weight: 500;
  font-size: 14px;
  color: #FFFFFF;
  width: 120px;
}

.info-value {
  font-family: PingFangSC, 'PingFang SC';
  font-weight: 400;
  font-size: 14px;
  color: #FFFFFF;
  flex: 1;
}

/* 设备信息样式 */
.device-info {
  display: flex;
  flex-direction: column;
}

.device-gauge {
  display: flex;
  position: relative;
  height: 180px;
  margin-bottom: 20px;
}

.gauge-container {
  width: 100%;
  height: 100%;
}

.gauge-select {
  position: absolute;
  top: 10px;
  right: 10px;
}

.gauge-select select,
.indicator-select select {
  background-color: transparent;
  border: 1px solid rgba(59, 141, 242, 0.5);
  border-radius: 4px;
  color: #FFFFFF;
  padding: 5px 10px;
  font-size: 14px;
  outline: none;
}

.gauge-select select option,
.indicator-select select option {
  background-color: #003366;
  color: #FFFFFF;
}

.device-info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

/* 监测曲线样式 */
.curve-header {
  display: flex;
  flex-direction: column;
  margin-bottom: 15px;
  gap: 10px;
}

.time-filter {
  display: flex;
  align-items: center;
}

.curve-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.period-btn {
  padding: 5px 15px;
  background-color: #003366;
  border: 1px solid rgba(59, 141, 242, 0.5);
  border-radius: 4px;
  color: #FFFFFF;
  font-size: 14px;
  cursor: pointer;
  margin-right: 10px;
}

.period-btn.active {
  background-color: #0066CC;
  border-color: #0088FF;
}

.date-range {
  color: #FFFFFF;
  font-size: 14px;
}

.line-chart-container {
  width: 100%;
  height: 250px;
}

/* 处置流程样式 */
.process-status {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.process-status-item {
  border: 1px solid rgba(59, 141, 242, 0.5);
  border-radius: 4px;
  overflow: hidden;
}

.status-label {
  padding: 8px 10px;
  font-size: 14px;
  font-weight: 500;
  color: #FFFFFF;
  text-align: center;
}

.blue-bg {
  background-color: rgba(0, 102, 204, 0.3);
}

.process-group {
  padding: 10px;
}

.process-item {
  display: flex;
  margin-bottom: 10px;
}

.process-item:last-child {
  margin-bottom: 0;
}

.process-label {
  width: 100px;
  font-size: 14px;
  color: #D3E5FF;
  text-align: left;
}

.process-value {
  flex: 1;
  font-size: 14px;
  color: #FFFFFF;
}

.process-images {
  display: flex;
  gap: 10px;
}

.image-preview {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style> 