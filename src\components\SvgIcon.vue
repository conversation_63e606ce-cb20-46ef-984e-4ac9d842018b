<template>
  <div 
    class="svg-icon" 
    :style="{ 
      width: size, 
      height: size,
      color: color
    }"
  >
    <!-- 方式1：使用已注册的图标 -->
    <svg v-if="name && icons[name]" aria-hidden="true" class="svg-icon-inner">
      <use :xlink:href="'#icon-' + name" />
    </svg>
    
    <!-- 方式2：直接使用svg字符串 -->
    <span v-else-if="raw" class="svg-icon-raw" v-html="raw"></span>
    
    <!-- 方式3：使用src路径加载svg -->
    <img 
      v-else-if="src" 
      :src="src" 
      class="svg-icon-img" 
      :style="{ width: '100%', height: '100%' }" 
    />
    
    <!-- 图标未找到时的替代内容 -->
    <slot v-else>
      <span class="svg-icon-placeholder"></span>
    </slot>
  </div>
</template>

<script setup>
import { computed, onMounted } from 'vue';
import icons from '@/assets/icons';

const props = defineProps({
  // 图标名称（对应assets/icons/svg下的文件名，不含.svg后缀）
  name: {
    type: String,
    default: ''
  },
  // 图标颜色
  color: {
    type: String,
    default: 'currentColor'
  },
  // 图标大小
  size: {
    type: String,
    default: '1em'
  },
  // 图标外部路径
  src: {
    type: String,
    default: ''
  },
  // 原始svg字符串
  raw: {
    type: String,
    default: ''
  },
  // 是否旋转
  spin: {
    type: Boolean,
    default: false
  }
});

// 导出组件属性，方便外部访问
defineExpose({
  icons
});

// SVG sprite ID 前缀
const iconPrefix = 'icon-';
</script>

<style scoped>
.svg-icon {
  display: inline-block;
  vertical-align: middle;
  fill: currentColor;
  overflow: hidden;
}

.svg-icon.spin {
  animation: rotate 1.5s linear infinite;
}

.svg-icon-inner {
  width: 100%;
  height: 100%;
}

.svg-icon-raw {
  display: inline-block;
  width: 100%;
  height: 100%;
}

.svg-icon-raw :deep(svg) {
  width: 100%;
  height: 100%;
  fill: currentColor;
}

.svg-icon-placeholder {
  display: inline-block;
  width: 100%;
  height: 100%;
  background-color: #eee;
  border-radius: 4px;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style> 