<template>
  <div class="drainage-manhole-container">
    <!-- 搜索区域 -->
    <div class="drainage-manhole-search">
      <div class="search-form">
        <div class="form-item">
          <span class="label">窨井类型:</span>
          <el-select v-model="formData.wellType" class="form-input" placeholder="请选择">
            <el-option v-for="item in WELL_TYPE_OPTIONS" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <span class="label">窨井材质:</span>
          <el-select v-model="formData.wellMaterial" class="form-input" placeholder="请选择">
            <el-option v-for="item in WELL_MATERIAL_OPTIONS" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <span class="label">窨井形状:</span>
          <el-select v-model="formData.wellShape" class="form-input" placeholder="请选择">
            <el-option v-for="item in WELL_SHAPE_OPTIONS" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <span class="label">权属单位:</span>
          <el-select v-model="formData.managementUnit" class="form-input" placeholder="请选择">
            <el-option v-for="unit in managementUnits" :key="unit.id" :label="unit.enterpriseName" :value="unit.enterpriseName" />
          </el-select>
        </div>
        <div class="form-item">
          <el-button type="primary" class="search-btn" @click="handleSearch">查询</el-button>
          <el-button class="reset-btn" @click="handleReset">重置</el-button>
        </div>
      </div>
    </div>
    
    <!-- 按钮区域 -->
    <div class="table-header">
      <div class="button-group">
        <el-button type="primary" class="operation-btn" @click="handleAdd">新增</el-button>
        <el-button type="primary" class="operation-btn" @click="handleImport">导入</el-button>
        <el-button type="primary" class="operation-btn" @click="handleExport">导出</el-button>
      </div>
    </div>
    
    <!-- 表格区域 -->
    <div class="table-container">
      <el-table :data="tableData" style="width: 100%" :header-cell-style="headerCellStyle"
        :row-class-name="tableRowClassName" @row-click="handleRowClick" height="calc(100vh - 380px)"
        empty-text="暂无数据">
        <el-table-column label="序号" min-width="60">
          <template #default="{ $index }">
            {{ (currentPage - 1) * pageSize + $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="wellCode" label="窨井编码" min-width="120" />
        <el-table-column prop="wellTypeName" label="窨井类型" min-width="120" />
        <el-table-column prop="wellMaterialName" label="窨井材质" min-width="120" />
        <el-table-column prop="wellShapeName" label="窨井形状" min-width="120" />
        <el-table-column prop="wellSize" label="窨井尺寸" min-width="120" />
        <el-table-column prop="buriedDepth" label="埋深 (m)" min-width="120" />
        <el-table-column prop="elevation" label="高程 (m)" min-width="120" />
        <el-table-column prop="roadName" label="位置" min-width="150" />
        <el-table-column prop="managementUnit" label="权属单位" min-width="150" />
        <el-table-column label="操作" fixed="right" min-width="220" align="center">
          <template #default="{ row }">
            <div class="operation-btns">
              <div class="operation-btn-row">
                <span class="operation-btn-text" @click.stop="handleEdit(row)">编辑</span>
                <span class="operation-divider">|</span>
                <span class="operation-btn-text" @click.stop="handleDetail(row)">详情</span>
                <span class="operation-divider">|</span>
                <span class="operation-btn-text" @click.stop="handleDelete(row)">删除</span>
                <span class="operation-divider">|</span>
                <span class="operation-btn-text" @click.stop="handleLocation(row)">定位</span>
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    
    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :pager-count="5"
      />
    </div>

    <!-- 窨井弹窗组件 -->
    <DrainageManholeDialog
      v-model:visible="dialogVisible"
      :mode="dialogMode"
      :data="dialogData"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { getManholeWellPage, getManholeWellDetail, deleteManholeWell, getEnterpriseList } from '@/api/drainage';
import { misPosition } from '@/hooks/gishooks';
import { WELL_TYPE_OPTIONS, WELL_MATERIAL_OPTIONS, WELL_SHAPE_OPTIONS } from '@/constants/drainage';
import DrainageManholeDialog from './components/DrainageManholeDialog.vue';

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const tableData = ref([]);

// 权属单位列表
const managementUnits = ref([]);

// 表单数据
const formData = ref({
  wellType: '',
  wellMaterial: '',
  wellShape: '',
  managementUnit: ''
});

// 弹窗相关
const dialogVisible = ref(false);
const dialogMode = ref('add');
const dialogData = ref({});

// 表格样式
const headerCellStyle = {
  background: '#F4F4F4',
  fontFamily: 'PingFangSC, PingFang SC',
  fontWeight: '500',
  fontSize: '14px',
  color: '#282828',
  height: '64px'
};

// 表格行样式
const tableRowClassName = ({ rowIndex }) => {
  if (rowIndex % 2 === 1) {
    return 'zebra-row';
  }
  return '';
};

// 获取权属单位列表
const fetchManagementUnits = async () => {
  try {
    const res = await getEnterpriseList();
    if (res && res.code === 200) {
      managementUnits.value = res.data || [];
    }
  } catch (error) {
    console.error('获取权属单位列表失败:', error);
    ElMessage.error('获取权属单位列表失败');
  }
};

// 处理查询
const handleSearch = () => {
  fetchManholeData();
};

// 处理重置
const handleReset = () => {
  formData.value = {
    wellType: '',
    wellMaterial: '',
    wellShape: '',
    managementUnit: ''
  };
  fetchManholeData();
};

// 获取窨井分页数据
const fetchManholeData = async () => {
  try {
    const params = {
      wellType: formData.value.wellType,
      wellMaterial: formData.value.wellMaterial,
      wellShape: formData.value.wellShape,
      managementUnit: formData.value.managementUnit,
      pageNum: currentPage.value,
      pageSize: pageSize.value
    };
    
    const res = await getManholeWellPage(params);
    
    if (res && res.code === 200) {
      tableData.value = res.data.records;
      total.value = res.data.total;
    }
  } catch (error) {
    console.error('获取窨井数据失败:', error);
    ElMessage.error('获取窨井数据失败');
    tableData.value = [];
    total.value = 0;
  }
};

// 处理分页大小变化
const handleSizeChange = (size) => {
  pageSize.value = size;
  fetchManholeData();
};

// 处理页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page;
  fetchManholeData();
};

// 处理行点击
const handleRowClick = (row) => {
  console.log('行点击', row);
};

// 处理新增
const handleAdd = () => {
  dialogMode.value = 'add';
  dialogData.value = {};
  dialogVisible.value = true;
};

// 处理编辑
const handleEdit = async (row) => {
  dialogMode.value = 'edit';
  dialogData.value = { id: row.id };
  dialogVisible.value = true;
};

// 处理详情
const handleDetail = async (row) => {
  dialogMode.value = 'view';
  dialogData.value = { id: row.id };
  dialogVisible.value = true;
};

// 处理删除
const handleDelete = (row) => {
  ElMessageBox.confirm('确定要删除该窨井吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await deleteManholeWell(row.id);
      if (res && res.code === 200) {
        ElMessage.success('删除成功');
        fetchManholeData();
      } else {
        ElMessage.error(res?.msg || '删除失败');
      }
    } catch (error) {
      console.error('删除窨井失败:', error);
      ElMessage.error('删除窨井失败');
    }
  }).catch(() => {
    // 取消删除
  });
};

// 处理定位
const handleLocation = (row) => {
  if (
    row.latitude &&
    row.latitude != '' &&
    row.longitude &&
    row.longitude != ''
  ) {
    misPosition.value = {
      longitude: row.longitude, //经度
      latitude: row.latitude //纬度
    }
  } else {
    ElMessage.warning('没有经纬度，无法定位！')
  }
};

// 处理弹窗成功
const handleDialogSuccess = () => {
  fetchManholeData();
};

// 处理导入
const handleImport = () => {
  console.log('导入');
  ElMessage.info('导入窨井功能开发中...');
};

// 处理导出
const handleExport = () => {
  console.log('导出');
  ElMessage.info('导出窨井功能开发中...');
};

// 在组件挂载后获取数据
onMounted(async () => {
  try {
    await fetchManagementUnits();
    await fetchManholeData();
  } catch (error) {
    console.error('初始化数据失败:', error);
    ElMessage.error('初始化数据失败');
  }
});
</script>

<style scoped>
.drainage-manhole-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  background-color: white;
}

/* 搜索区域样式 */
.drainage-manhole-search {
  width: 100%;
  margin-bottom: 16px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.form-item {
  display: flex;
  align-items: center;
  margin-right: 16px;
  margin-bottom: 16px;
}

.label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #282828;
  margin-right: 8px;
  white-space: nowrap;
}

.form-input {
  width: 180px;
  height: 32px;
}

:deep(.el-input__wrapper) {
  background: #FFFFFF;
  border-radius: 2px;
  border: 1px solid #CED3DA;
  height: 32px;
  box-shadow: none !important;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #0066FF inset !important;
}

.search-btn {
  width: 80px;
  height: 32px;
  background: #0277FD;
  border-radius: 2px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 14px;
  color: #FFFFFF;
  padding: 0;
  margin-right: 8px;
}

.reset-btn {
  width: 80px;
  height: 32px;
  padding: 0;
  border-radius: 2px;
  border: 1px solid #DCDFE6;
}

/* 表格头部样式 */
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.button-group {
  display: flex;
  gap: 8px;
}

.operation-btn {
  width: 80px;
  height: 32px;
  background: #0277FD;
  border-radius: 2px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 14px;
  color: #FFFFFF;
  padding: 0;
}

/* 表格样式 */
.table-container {
  flex: 1;
  width: 100%;
  overflow: hidden;
  margin-bottom: 16px;
}

:deep(.el-table) {
  overflow-x: hidden !important;
}

:deep(.el-table th) {
  background-color: #F4F4F4;
  height: 64px;
}

:deep(.el-table tr) {
  height: 48px;
}

:deep(.zebra-row) {
  background-color: #F8FAFD;
}

:deep(.el-table__row:hover) {
  background: #EEF5FF !important;
}

:deep(.el-table__row.selected-row) {
  background: #EEF5FF !important;
}

.operation-btns {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.operation-btn-row {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 4px 0;
}

.operation-btn-text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #0086FF;
  cursor: pointer;
}

.operation-divider {
  margin: 0 4px;
  color: #CED3DA;
}

/* 分页样式 */
.pagination-container {
  display: flex;
  justify-content: flex-end;
  padding: 0;
  margin-top: 16px;
  min-height: 32px;
}

:deep(.el-pagination) {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #222222;
  padding-right: 0;
}

:deep(.el-pagination .el-pager li) {
  width: 24px;
  height: 24px;
  background: rgba(255,255,255,0.99);
  border-radius: 2px;
  border: 1px solid #EEEEEE;
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.el-pagination .el-pager li.is-active) {
  width: 24px;
  height: 24px;
  background: #0086FF;
  border-radius: 2px;
  color: #FFFFFF;
  border: none;
}

/* 防止表格过长时遮挡分页组件 */
:deep(.el-table__body-wrapper) {
  overflow-y: auto !important;
  min-height: 200px;
}

/* 确保操作列文字不换行 */
:deep(.cell) {
  white-space: nowrap;
}
</style> 