/*Cesium webGL渲染*/
//流动材质
export const glslFlow = `
#extension GL_OES_standard_derivatives : enable
varying float v_polylineAngle;
mat2 rotate(float rad) {
  float c = cos(rad);
  float s = sin(rad);
  return mat2(
      c, s,
      -s, c
  );
}
czm_material czm_getMaterial(czm_materialInput materialInput)
{
    czm_material material = czm_getDefaultMaterial(materialInput);
    vec2 st = materialInput.st;
    // float s = st.s/(abs(fwidth(st.t))*czm_pixelRatio);
    // s = s-czm_frameNumber / time;//增加运动效果
    // float s = st.s;
    // float s = st.s/(abs(fwidth(st.t))*imageW*czm_pixelRatio);
    vec2 pos = rotate(v_polylineAngle) * gl_FragCoord.xy;
    float s = pos.x / (imageW * czm_pixelRatio);
    s = s-czm_frameNumber / time;//增加运动效果
    float t = st.t;

    vec4 colorImage = texture2D(image, vec2(fract(s),t));
    material.alpha = colorImage.a;
    material.diffuse = colorImage.rgb;
    return material;
}
`;

// v-line:竖直线：
export const glslVLine = `
#extension GL_OES_standard_derivatives : enable
varying float v_polylineAngle;
mat2 rotate(float rad) {
  float c = cos(rad);
  float s = sin(rad);
  return mat2(
      c, s,
      -s, c
  );
}
czm_material czm_getMaterial(czm_materialInput materialInput)
{
    czm_material material = czm_getDefaultMaterial(materialInput);
    vec2 st = materialInput.st;
    // float s = st.s/(abs(fwidth(st.t))*czm_pixelRatio);
    // s = s-czm_frameNumber / time;//增加运动效果
    // float s = st.s;
    // float s = st.s/(abs(fwidth(st.t))*imageW*czm_pixelRatio);
    vec2 pos = rotate(v_polylineAngle) * gl_FragCoord.xy;
    float s = pos.x / (imageW * czm_pixelRatio);
    s = s-czm_frameNumber / time;//增加运动效果
    float t = st.t;

    vec4 colorImage = texture2D(image, vec2(fract(s),t));
    material.alpha = colorImage.a*(3.5*st.s*(1.0-st.s));
    material.diffuse = colorImage.rgb * imageColor;
    return material;
}
`;

export const glslCity = `
void fragmentMain(FragmentInput fsInput, inout czm_modelMaterial material) {
         float _baseHeight = 0.0; // 物体的基础高度，需要修改成一个合适的建筑基础高度
      float _heightRange = 60.0; // 高亮的范围(_baseHeight ~ _baseHeight + _      heightRange) 默认是 0-60米
      float _glowRange = 200.0; // 光环的移动范围(高度)
       float vtxf_height = fsInput.attributes.positionMC.z - _baseHeight;
       float vtxf_a11 = fract(0.0 / 120.0) * 3.14159265 * 2.0;
       float vtxf_a12 = vtxf_height / _heightRange + sin(vtxf_a11) * 0.1;
       material.diffuse*= vec3(vtxf_a12, vtxf_a12, vtxf_a12);
       float vtxf_a13 = fract(czm_frameNumber / 360.0);
       float vtxf_h = clamp(vtxf_height / _glowRange, 0.0, 1.0);
       vtxf_a13 = abs(vtxf_a13 - 0.5) * 2.0;
       float vtxf_diff = step(0.005, abs(vtxf_h - vtxf_a13));
       material.diffuse += material.diffuse * (1.0 - vtxf_diff);
}
`;

export const glslCity2 = `
void fragmentMain(FragmentInput fsInput, inout czm_modelMaterial material) {
    float vtxf_height = fsInput.attributes.positionMC.z;
    float vtxf_a13 = fsInput.attributes.positionMC.z * fsInput.attributes.positionMC.z;
    float vtxf_a14 = vtxf_a13 / 8000.0;
    //float vtxf_a11 = fract(czm_frameNumber / 360.0) * 3.14159265 * 2.0;
    //float vtxf_a12 = vtxf_height / _heightRange + sin(vtxf_a11) * 0.1;
    material.diffuse*= vec3(vtxf_a14, vtxf_a14, vtxf_a14);
  }
`;
