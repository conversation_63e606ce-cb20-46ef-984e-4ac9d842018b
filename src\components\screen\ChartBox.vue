<template>
  <div class="chart-box" :style="{ height: height }">
    <div ref="chartRef" class="chart-container"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, onUnmounted, watchEffect } from 'vue'
import createECharts from '@/utils/echarts'

const props = defineProps({
  options: {
    type: Object,
    required: true
  },
  height: {
    type: String,
    default: '300px'
  },
  autoResize: {
    type: Boolean,
    default: true
  }
})

const chartRef = ref(null)
const echarts = createECharts()

onMounted(() => {
  if (chartRef.value) {
    echarts.initChart(chartRef.value)
    echarts.updateChart(props.options)
  }
})

onUnmounted(() => {
  echarts.disposeChart()
})

// 监听options变化
watch(() => props.options, (newOptions) => {
  echarts.updateChart(newOptions)
}, { deep: true })

// 处理窗口大小变化
let resizeListener = null

watchEffect(() => {
  if (props.autoResize) {
    if (!resizeListener) {
      resizeListener = () => {
        echarts.resizeChart()
      }
      window.addEventListener('resize', resizeListener)
    }
  } else if (resizeListener) {
    window.removeEventListener('resize', resizeListener)
    resizeListener = null
  }
})
</script>

<style scoped>
.chart-box {
  width: 100%;
  position: relative;
}

.chart-container {
  width: 100%;
  height: 100%;
}
</style> 