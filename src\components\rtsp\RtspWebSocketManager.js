/**
 * RTSP WebSocket连接管理器
 * 负责管理单个WebSocket连接处理多路视频流
 */
class RtspWebSocketManager {
  constructor(wsUrl) {
    this.wsUrl = wsUrl;
    this.ws = null;
    this.isConnected = false;
    this.isConnecting = false;  // 新增连接中状态标记
    this.connectionPromise = null; // 用于跟踪连接Promise
    this.listeners = new Map(); // 存储不同视频ID的消息监听器
    this.pendingVideoIds = new Set(); // 存储待播放的视频ID
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectInterval = 2000;
    this.currentVideoId = null; // 当前正在播放的视频ID
    this.receivedDataCount = 0; // 接收数据计数器，用于调试
  }

  /**
   * 连接WebSocket服务器
   * @returns {Promise} 连接结果的Promise
   */
  connect() {
    // 如果已经连接，直接返回resolved promise
    if (this.isConnected) {
      return Promise.resolve();
    }
    
    // 如果正在连接中，返回已存在的连接promise
    if (this.isConnecting && this.connectionPromise) {
      return this.connectionPromise;
    }
    
    // 创建新的连接Promise
    this.isConnecting = true;
    this.connectionPromise = new Promise((resolve, reject) => {
      try {
        console.log('创建新的WebSocket连接...');
        this.ws = new WebSocket(this.wsUrl);
        // 设置二进制类型为arraybuffer
        this.ws.binaryType = 'arraybuffer';

        this.ws.onopen = () => {
          console.log('WebSocket连接已打开，等待传输数据...');
          this.isConnected = true;
          this.isConnecting = false;
          this.reconnectAttempts = 0;
          console.log('WebSocket连接成功确认');
          
          // 连接成功后，发送所有待播放的视频ID请求
          setTimeout(() => {
            this._sendPendingPlayRequests();
            resolve();
          }, 500); // 给连接一点稳定的时间
        };

        this.ws.onclose = (event) => {
          console.log('WebSocket连接关闭', event);
          this.isConnected = false;
          this.isConnecting = false;
          this.connectionPromise = null;
          this._attemptReconnect();
        };

        this.ws.onerror = (error) => {
          console.error('WebSocket错误', error);
          this.isConnecting = false;
          if (!this.isConnected) {
            reject(error);
            this.connectionPromise = null;
          }
        };

        this.ws.onmessage = (event) => {
          try {
            // 增加计数器
            this.receivedDataCount++;
            
            // 每50帧输出一次日志，避免过多日志输出
            if (this.receivedDataCount % 50 === 0) {
              console.log(`已接收 ${this.receivedDataCount} 帧数据`);
            }
            
            // 检查数据类型
            if (event.data instanceof ArrayBuffer) {
              // 处理二进制数据
              this._handleBinaryData(event.data);
            } else if (event.data instanceof Blob) {
              // 处理Blob数据，转换为ArrayBuffer
              this._handleBlobData(event.data);
            } else {
              console.log('收到文本消息:', event.data);
              
              // 如果是连接成功消息
              if (typeof event.data === 'string' && event.data.includes('连接成功')) {
                // 连接已成功，可以在这里做一些初始化工作
              } else {
                // 尝试解析可能的JSON消息
                try {
                  const message = JSON.parse(event.data);
                  if (message.type === 110) {
                    const videoId = message.data.videoId;
                    const listener = this.listeners.get(videoId);
                    if (listener) {
                      listener(message.data);
                    }
                  }
                } catch (e) {
                  console.error('解析JSON消息失败', e);
                }
              }
            }
          } catch (error) {
            console.error('处理WebSocket消息失败', error);
          }
        };
      } catch (error) {
        console.error('创建WebSocket连接失败', error);
        this.isConnecting = false;
        this.connectionPromise = null;
        reject(error);
      }
    });
    
    return this.connectionPromise;
  }

  /**
   * 处理Blob类型的数据
   * @param {Blob} blob Blob数据
   * @private
   */
  _handleBlobData(blob) {
    const reader = new FileReader();
    reader.onload = (e) => {
      this._handleBinaryData(e.target.result);
    };
    reader.onerror = (error) => {
      console.error('读取Blob数据失败', error);
    };
    reader.readAsArrayBuffer(blob);
  }

  /**
   * 处理二进制数据
   * @param {ArrayBuffer} buffer 二进制数据
   * @private
   */
  _handleBinaryData(buffer) {
    // 如果有当前播放的视频ID，将数据传递给对应的监听器
    if (this.currentVideoId) {
      const listener = this.listeners.get(this.currentVideoId);
      if (listener) {
        listener(buffer);
      }
    } else {
      // 如果没有当前视频ID但有已注册的监听器，则将数据发送给所有注册的监听器
      if (this.listeners.size > 0) {
        // 向所有监听器发送数据
        for (const [videoId, listener] of this.listeners.entries()) {
          if (listener) {
            listener(buffer);
          }
        }
      } else {
        // 有数据但没有监听器，这是一个异常情况
        if (this.receivedDataCount % 50 === 0) {
          console.warn('收到数据但没有注册任何监听器');
        }
      }
    }
  }

  /**
   * 发送所有待播放的视频请求
   * @private
   */
  _sendPendingPlayRequests() {
    if (this.pendingVideoIds.size > 0) {
      console.log(`发送 ${this.pendingVideoIds.size} 个待播放视频请求...`);
      const pendingIds = Array.from(this.pendingVideoIds);
      
      // 使用顺序发送，避免同时发送多个请求
      const sendNext = (index) => {
        if (index >= pendingIds.length) {
          this.pendingVideoIds.clear();
          return;
        }
        
        const videoId = pendingIds[index];
        this.sendPlayRequest(videoId)
          .then(() => {
            // 成功后发送下一个
            setTimeout(() => sendNext(index + 1), 200);
          })
          .catch(error => {
            console.error(`发送视频ID ${videoId} 的播放请求失败`, error);
            // 失败也继续发送下一个
            setTimeout(() => sendNext(index + 1), 200);
          });
      };
      
      // 开始发送第一个
      sendNext(0);
    }
  }

  /**
   * 尝试重新连接
   * @private
   */
  _attemptReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.log('达到最大重连次数，停止重连');
      return;
    }

    this.reconnectAttempts++;
    console.log(`尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);
    
    setTimeout(() => {
      this.connect().catch(error => {
        console.error('重连失败', error);
      });
    }, this.reconnectInterval);
  }

  /**
   * 注册视频ID的消息监听器
   * @param {string} videoId 视频ID
   * @param {Function} callback 收到该视频流数据的回调函数
   */
  registerVideoListener(videoId, callback) {
    console.log(`注册视频ID ${videoId} 的监听器`);
    
    // 避免重复注册
    if (this.listeners.has(videoId)) {
      console.log(`视频ID ${videoId} 的监听器已存在，更新回调`);
      this.listeners.set(videoId, callback);
      return;
    }
    
    this.listeners.set(videoId, callback);
    
    // 如果已连接，立即发送播放请求
    // 否则，将视频ID添加到待播放列表
    if (this.isConnected) {
      // 使用setTimeout延迟发送，避免多个请求同时发送
      setTimeout(() => {
        this.sendPlayRequest(videoId).catch(error => {
          console.error(`发送视频ID ${videoId} 的播放请求失败`, error);
          // 失败时添加到待播放列表，以便重连后尝试
          this.pendingVideoIds.add(videoId);
        });
      }, Math.random() * 500); // 随机延迟，避免多个请求同时发送
    } else {
      this.pendingVideoIds.add(videoId);
      console.log(`视频ID ${videoId} 添加到待播放列表，等待连接成功后发送请求`);
    }
  }

  /**
   * 移除视频ID的消息监听器
   * @param {string} videoId 要移除的视频ID
   */
  unregisterVideoListener(videoId) {
    console.log(`移除视频ID ${videoId} 的监听器`);
    this.listeners.delete(videoId);
    this.pendingVideoIds.delete(videoId);
    
    // 如果当前播放的视频ID被移除，重置currentVideoId
    if (this.currentVideoId === videoId) {
      this.currentVideoId = this.listeners.size > 0 ? Array.from(this.listeners.keys())[0] : null;
      console.log(`当前视频ID重置为 ${this.currentVideoId}`);
    }
  }

  /**
   * 发送播放视频请求
   * @param {string} videoId 视频ID
   * @returns {Promise} 发送结果的Promise
   */
  sendPlayRequest(videoId) {
    return new Promise((resolve, reject) => {
      if (!this.isConnected) {
        reject(new Error('WebSocket未连接'));
        return;
      }

      if (this.ws.readyState !== WebSocket.OPEN) {
        reject(new Error(`WebSocket未就绪，当前状态: ${this.ws.readyState}`));
        return;
      }

      try {
        const message = {
          type: 110,
          data: videoId
        };
        
        console.log(`发送播放请求: ${JSON.stringify(message)}`);
        this.ws.send(JSON.stringify(message));
        resolve();
      } catch (error) {
        console.error('发送播放请求失败', error);
        reject(error);
      }
    });
  }

  /**
   * 关闭WebSocket连接
   */
  disconnect() {
    if (this.ws) {
      if (this.ws.readyState === WebSocket.OPEN || this.ws.readyState === WebSocket.CONNECTING) {
        this.ws.close();
      }
      this.ws = null;
      this.isConnected = false;
      this.isConnecting = false;
      this.connectionPromise = null;
      this.listeners.clear();
      this.pendingVideoIds.clear();
      this.currentVideoId = null;
      this.receivedDataCount = 0;
      console.log('WebSocket连接已断开');
    }
  }
}

export default RtspWebSocketManager; 