## 📋 列表页面高度自适应调整指南

基于对 `disposal.vue` 和 `info.vue` 的修复经验，我为你总结了一个通用的调整指南，你可以按照这个步骤修复其他列表页面：

### 🎯 核心原理
将固定高度的布局改为 **flex 弹性布局**，让表格区域自适应占据剩余空间，确保分页组件始终可见。

### 📝 具体调整步骤

#### **第一步：修改页面容器高度**
```css
.page-container {  /* 你的页面根容器类名 */
  width: 100%;
  height: calc(100vh - 180px);  /* 减去导航栏高度 */
  display: flex;
  flex-direction: column;
  padding: 16px;
  background-color: #F0F2F5;
  gap: 16px;
  box-sizing: border-box;
  overflow: hidden;  /* 防止整体页面滚动 */
}
```

#### **第二步：设置固定区域不压缩**
给所有**非表格区域**添加 `flex-shrink: 0`：

```css
/* 搜索区域 */
.search-section {
  /* 原有样式... */
  flex-shrink: 0;  /* 新增 */
}

/* 统计卡片区域 */
.statistics-section {
  /* 原有样式... */
  flex-shrink: 0;  /* 新增 */
}

/* 分页区域 */
.pagination-container {
  /* 原有样式... */
  flex-shrink: 0;  /* 新增 */
  padding: 16px 0 0 0;  /* 调整padding */
}
```

#### **第三步：设置表格区域占据剩余空间**
```css
/* 表格区域容器 */
.table-section {
  width: 100%;
  flex: 1;  /* 占据剩余空间 */
  background: white;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  padding: 16px;
  min-height: 0;  /* 重要：确保flex正常工作 */
  overflow: hidden;
}

/* 表格容器 */
.table-container {
  flex: 1;
  width: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 200px;  /* 最小高度保证 */
}
```

#### **第四步：修改Element Plus表格深层样式**
```css
:deep(.el-table) {
  --el-table-border-color: #EBEEF5;
  --el-table-header-bg-color: #F5F8FA;
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

:deep(.el-table__inner-wrapper) {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

:deep(.el-table__header-wrapper) {
  flex-shrink: 0;  /* 表头固定 */
}

:deep(.el-table__body-wrapper) {
  flex: 1;
  overflow-y: auto !important;  /* 表格内容滚动 */
}

:deep(.el-scrollbar) {
  flex: 1;
  display: flex;
  flex-direction: column;
}

:deep(.el-scrollbar__wrap) {
  flex: 1;
  overflow-x: hidden;
}
```

#### **第五步：移除模板中的固定高度**
```html
<!-- 移除 height 属性 -->
<el-table 
  :data="tableData"
  style="width: 100%"
  <!-- 删除这行: height="274" -->
  :header-cell-style="headerCellStyle"
>
```

#### **第六步：添加表格容器包装**
```html
<div class="table-section">
  <!-- 新增表格容器 -->
  <div class="table-container">
    <el-table>
      <!-- 表格列定义... -->
    </el-table>
  </div>
  
  <!-- 分页区域 -->
  <div class="pagination-container">
    <el-pagination />
  </div>
</div>
```

### ⚠️ 注意事项

1. **导航栏高度**：根据实际情况调整 `calc(100vh - 180px)` 中的数值
2. **最小高度**：给表格容器设置 `min-height: 200px` 确保基本可用性
3. **overflow设置**：页面容器设置 `overflow: hidden`，表格内容设置 `overflow-y: auto`
4. **flex-shrink**：所有固定区域都要设置 `flex-shrink: 0`
5. **min-height: 0**：flex容器需要设置此属性确保正常工作

### 🔍 检查清单

修改完成后，检查以下几点：
- [ ] 不同屏幕高度下分页组件都可见
- [ ] 表格内容可以正常滚动
- [ ] 搜索、统计等区域高度固定
- [ ] 页面整体不出现滚动条
- [ ] 表格至少显示几行数据

