<script setup>
import { onBeforeMount } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()

onBeforeMount(() => {
  const { params, query } = route
  const { path } = params
  
  // 将路径数组转换为字符串路径
  const redirectPath = '/' + (Array.isArray(path) ? path.join('/') : path)
  
  // 重定向到目标路径，并保留查询参数
  router.replace({ path: redirectPath, query })
})
</script>

<template>
  <div></div>
</template>