<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>JSMpeg RTSP 播放器</title>
    <style>
        .input-group { margin-bottom: 10px; }
        label { min-width: 120px; display: inline-block; }
        #status { margin-top: 10px; }
    </style>
</head>
<body>
    <div>
        <div class="input-group">
            <label for="wsUrl">WebSocket地址：</label>
            <input type="text" id="wsUrl" value="ws://172.20.130.240:32021/basic/jsmpeg" style="width:400px;">
        </div>
        <div class="input-group">
            <label for="rtspUrl">RTSP地址：</label>
            <input type="text" id="rtspUrl" value="rtsp://admin:123456@*************:554/unicast/c1002/s0/live/forward" style="width:400px;">
        </div>
        <button id="playBtn">播放</button>
        <button id="stopBtn" disabled>停止</button>
    </div>
    <canvas id="video-canvas" width="640" height="360"></canvas>
    <div id="status"></div>
    <script src="https://cdn.jsdelivr.net/gh/phoboslab/jsmpeg@master/jsmpeg.min.js"></script>
    <script>
        let player = null;
        const playBtn = document.getElementById('playBtn');
        const stopBtn = document.getElementById('stopBtn');
        const wsUrlInput = document.getElementById('wsUrl');
        const rtspUrlInput = document.getElementById('rtspUrl');
        const statusDiv = document.getElementById('status');
        const canvas = document.getElementById('video-canvas');

        function updateStatus(msg, isError) {
            statusDiv.textContent = msg;
            statusDiv.style.color = isError ? 'red' : 'green';
        }

        playBtn.onclick = function() {
            const rtspUrl = rtspUrlInput.value.trim();
            const wsUrl = wsUrlInput.value.trim();
            if (!wsUrl || !rtspUrl) {
                updateStatus('请输入WebSocket和RTSP地址', true);
                return;
            }
            if (player) {
                player.destroy();
                player = null;
            }
            const rtspWsUrl = wsUrl + '?rtsp=' + encodeURIComponent(rtspUrl);
            player = new JSMpeg.Player(rtspWsUrl, {
                canvas: canvas,
                autoplay: true,
                audio: false
            });
            playBtn.disabled = true;
            stopBtn.disabled = false;
        };

        stopBtn.onclick = function() {
            if (player) {
                player.destroy();
                player = null;
            }
            playBtn.disabled = false;
            stopBtn.disabled = true;
            updateStatus('已停止播放');
        };

        window.onbeforeunload = stopBtn.onclick;
    </script>
</body>
</html> 