<template>
  <div class="component-area-select">
    <el-select
      suffix-icon="CaretTop"
      v-model="areaCode"
      @change="handleSelectChange"
    >
      <template #prefix>
        <img
          src="@/assets/images/mis/address.png"
          class="location-icon"
          alt=""
        />
      </template>
      <el-option
        v-for="(item, index) in areaList"
        :key="index"
        :label="item.name"
        :value="item.code"
      />
    </el-select>
  </div>
</template>

<script setup>
import {ref} from "vue";
const areaList = ref([]);
const areaCode = ref("320900");

const handleSelectChange = (val) => {
};

const getAreaList = async () => {
};
getAreaList();
</script>

<style lang="scss" scoped>
.component-area-select {
  position: absolute;
  z-index: 999;
  left: 18px;
  top: 18px;
  .location-icon {
    width: 14px;
    height: 16px;
  }
  :deep(.el-select) {
    width: 140px;
    .el-select__placeholder,
    .el-select__caret {
      color: #000;
    }
  }
}
</style>
