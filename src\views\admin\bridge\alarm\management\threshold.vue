<template>
  <div class="bridge-alarm-threshold-container">
    <!-- 搜索区域 -->
    <ThresholdSearch @search="handleSearch" @reset="handleReset" />
    
    <!-- 按钮区域 -->
    <div class="table-header">
      <div class="button-group">
        <el-button type="primary" class="operation-btn" @click="handleAdd">新增</el-button>
      </div>
    </div>
    
    <!-- 表格区域 -->
    <div class="table-container">
      <el-table
        :data="tableData"
        style="width: 100%"
        :header-cell-style="headerCellStyle"
        :row-class-name="tableRowClassName"
        height="calc(100vh - 380px)"
        v-loading="loading"
      >
        <el-table-column label="序号" min-width="60">
          <template #default="{ $index }">
            {{ (currentPage - 1) * pageSize + $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="ruleName" label="规则名称" min-width="120" />
        <el-table-column prop="ruleDesc" label="规则描述" min-width="150" />
        <el-table-column prop="isEnabled" label="生效状态" min-width="100">
          <template #default="{ row }">
            <el-switch 
              v-model="row.isEnabled" 
              :active-value="true" 
              :inactive-value="false"
              disabled
            />
          </template>
        </el-table-column>
        <el-table-column prop="deviceType" label="设备类型" min-width="100">
          <template #default="{ row }">
            {{ getDeviceTypeName(row.deviceType) }}
          </template>
        </el-table-column>
        <el-table-column prop="monitorIndex" label="监测指标" min-width="120">
          <template #default="{ row }">
            {{ getMonitorIndexName(row.monitorIndex) }}
          </template>
        </el-table-column>
        <el-table-column label="设备数量" min-width="80">
          <template #default="{ row }">
            {{ getDeviceCount(row.deviceIds) }}
          </template>
        </el-table-column>
        <el-table-column label="一级报警阈值" min-width="150">
          <template #default="{ row }">
            阈值下限：{{ row.thresholdLevel1Min }}, 阈值上限：{{ row.thresholdLevel1Max }}
          </template>
        </el-table-column>
        <el-table-column label="二级报警阈值" min-width="150">
          <template #default="{ row }">
            阈值下限：{{ row.thresholdLevel2Min }}, 阈值上限：{{ row.thresholdLevel2Max }}
          </template>
        </el-table-column>
        <el-table-column label="三级报警阈值" min-width="150">
          <template #default="{ row }">
            阈值下限：{{ row.thresholdLevel3Min }}, 阈值上限：{{ row.thresholdLevel3Max }}
          </template>
        </el-table-column>
        <el-table-column label="操作" min-width="120" fixed="right" align="center">
          <template #default="scope">
            <div class="operation-btns">
              <div class="operation-btn-row">
                <span class="operation-btn-text" @click="handleView(scope.row)">查看</span>
                <span class="operation-divider">|</span>
                <span class="operation-btn-text" @click="handleEdit(scope.row)">编辑</span>
                <span class="operation-divider">|</span>
                <span class="operation-btn-text" @click="handleDelete(scope.row)">删除</span>
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 30, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :pager-count="5"
      />
    </div>

    <!-- 阈值对话框组件 -->
    <ThresholdDialog 
      v-model:visible="dialogVisible"
      :mode="dialogMode"
      :data="currentRow"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, reactive } from 'vue'
import { ElTable, ElTableColumn, ElPagination, ElSwitch, ElMessage, ElMessageBox } from 'element-plus'
import ThresholdSearch from './components/ThresholdSearch.vue'
import ThresholdDialog from './components/ThresholdDialog.vue'
import { 
  getAlarmThresholdPage, 
  deleteAlarmThreshold,
  getMonitorIndicatorsList 
} from '@/api/bridge'
import { DEVICE_TYPE_MAP, MONITOR_INDEX_MAP } from '@/constants/bridge'

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const tableData = ref([])
const loading = ref(false)

// 查询参数
const queryParams = ref({})

// 对话框相关
const dialogVisible = ref(false)
const dialogMode = ref('add')
const currentRow = ref({})

// 设备类型和监测指标映射
const deviceTypeMap = reactive({})
const monitorIndexMap = reactive({})

// 获取设备类型名称
const getDeviceTypeName = (deviceType) => {
  return deviceTypeMap[deviceType] || DEVICE_TYPE_MAP[deviceType] || deviceType
}

// 获取监测指标名称
const getMonitorIndexName = (monitorIndex) => {
  return monitorIndexMap[monitorIndex] || MONITOR_INDEX_MAP[monitorIndex] || monitorIndex
}

// 获取设备数量
const getDeviceCount = (deviceIds) => {
  if (!deviceIds) return 0
  if (Array.isArray(deviceIds)) {
    return deviceIds.length
  }
  if (typeof deviceIds === 'string') {
    return deviceIds.split(',').filter(id => id.trim()).length
  }
  return 0
}

// 获取设备类型和监测指标映射
const fetchDeviceTypeAndIndexMap = async () => {
  try {
    const res = await getMonitorIndicatorsList({})
    if (res && res.code === 200) {
      const data = res.data || []
      data.forEach(item => {
        if (item.deviceType && item.deviceTypeName) {
          deviceTypeMap[item.deviceType] = item.deviceTypeName
        }
        if (item.monitorIndex && item.monitorIndexName) {
          monitorIndexMap[item.monitorIndex] = item.monitorIndexName
        }
      })
    }
  } catch (error) {
    console.error('获取设备类型和监测指标映射失败', error)
  }
}

// 获取阈值数据
const fetchThresholdData = async () => {
  loading.value = true
  try {
    const res = await getAlarmThresholdPage(currentPage.value, pageSize.value, queryParams.value)
    if (res.code === 200) {
      tableData.value = res.data.records || []
      total.value = res.data.total || 0
    } else {
      ElMessage.error(res.message || '获取数据失败')
    }
  } catch (error) {
    console.error('获取阈值数据失败', error)
    ElMessage.error('获取阈值数据失败')
  } finally {
    loading.value = false
  }
}

// 处理搜索
const handleSearch = (formData) => {
  queryParams.value = formData
  currentPage.value = 1
  fetchThresholdData()
}

// 处理重置
const handleReset = () => {
  queryParams.value = {}
  currentPage.value = 1
  fetchThresholdData()
}

// 处理分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  fetchThresholdData()
}

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchThresholdData()
}

// 表头样式
const headerCellStyle = {
  background: '#F4F4F4',
  fontFamily: 'PingFangSC, PingFang SC',
  fontWeight: '500',
  fontSize: '14px',
  color: '#282828',
  height: '64px'
}

// 斑马纹和选中行样式
const tableRowClassName = ({ rowIndex }) => {
  if (rowIndex % 2 === 1) {
    return 'zebra-row'
  }
  return ''
}

// 操作按钮处理函数
const handleAdd = () => {
  dialogMode.value = 'add'
  currentRow.value = {}
  dialogVisible.value = true
}

const handleView = (row) => {
  dialogMode.value = 'view'
  currentRow.value = { ...row }
  dialogVisible.value = true
}

const handleEdit = (row) => {
  dialogMode.value = 'edit'
  currentRow.value = { ...row }
  dialogVisible.value = true
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm('确定要删除该阈值配置吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const res = await deleteAlarmThreshold(row.id)
    if (res && res.code === 200) {
      ElMessage.success('删除成功')
      fetchThresholdData()
    } else {
      ElMessage.error(res?.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败', error)
      ElMessage.error('删除失败')
    }
  }
}

// 对话框成功回调
const handleDialogSuccess = () => {
  fetchThresholdData()
}

onMounted(() => {
  fetchDeviceTypeAndIndexMap()
  fetchThresholdData()
})
</script>

<style scoped>
.bridge-alarm-threshold-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  background-color: white;
}

/* 表格头部样式 */
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.button-group {
  display: flex;
  gap: 8px;
}

.operation-btn {
  width: 80px;
  height: 32px;
  background: #0277FD;
  border-radius: 2px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 14px;
  color: #FFFFFF;
  padding: 0;
}

/* 表格样式 */
.table-container {
  margin-bottom: 16px;
  flex: 1;
  width: 100%;
  overflow: hidden;
}

:deep(.el-table) {
  overflow-x: hidden !important;
}

:deep(.el-table th) {
  background-color: #F4F4F4;
  height: 64px;
}

:deep(.el-table tr) {
  height: 48px;
}

:deep(.zebra-row) {
  background-color: #F8FAFD;
}

:deep(.el-table__row:hover) {
  background: #EEF5FF !important;
}

:deep(.el-table__row.selected-row) {
  background: #EEF5FF !important;
}

.operation-btns {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.operation-btn-row {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 4px 0;
}

.operation-btn-text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #0086FF;
  cursor: pointer;
}

.operation-divider {
  margin: 0 4px;
  color: #CED3DA;
}

/* 分页样式 */
.pagination-container {
  display: flex;
  justify-content: flex-end;
  padding: 0;
  margin-top: 16px;
  min-height: 32px;
}

:deep(.el-pagination) {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #222222;
  padding-right: 0;
}

:deep(.el-pagination .el-pager li) {
  width: 24px;
  height: 24px;
  background: rgba(255,255,255,0.99);
  border-radius: 2px;
  border: 1px solid #EEEEEE;
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.el-pagination .el-pager li.is-active) {
  width: 24px;
  height: 24px;
  background: #0086FF;
  border-radius: 2px;
  color: #FFFFFF;
  border: none;
}

/* 防止表格过长时遮挡分页组件 */
:deep(.el-table__body-wrapper) {
  overflow-y: auto !important;
  min-height: 200px;
}

/* 确保操作列文字不换行 */
:deep(.cell) {
  white-space: nowrap;
}
</style> 