<template>
  <div class="stat-card bg-[rgba(13,35,65,0.5)] p-2 rounded-md border border-[rgba(0,242,241,0.2)]">
    <div class="stat-title text-white text-xs opacity-70 mb-1">{{ title }}</div>
    <div class="stat-value" :style="{ color }">{{ value }}</div>
  </div>
</template>

<script setup>
defineProps({
  title: {
    type: String,
    required: true
  },
  value: {
    type: [String, Number],
    required: true
  },
  color: {
    type: String,
    default: '#00f2f1'
  }
})
</script>

<style scoped>
.stat-card {
  transition: all 0.3s ease;
  text-align: center;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 0 10px rgba(0, 242, 241, 0.3);
}

.stat-value {
  font-size: 1.25rem;
  font-weight: bold;
  font-family: 'DIN Alternate', 'Arial', sans-serif;
}
</style> 