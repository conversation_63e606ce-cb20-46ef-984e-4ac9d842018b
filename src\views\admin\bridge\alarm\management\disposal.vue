<template>
  <div class="bridge-alarm-disposal">
    <!-- 查询表单区域 -->
    <div class="search-section">
      <BridgeAlarmDisposalSearch @search="handleSearch" @reset="handleReset" />
    </div>

    <!-- 表格区域 -->
    <div class="table-section">
      <!-- 统计卡片区域 -->
      <div class="statistics-cards">
        <div 
          class="stat-card" 
          :class="{ 'active': activeStatus === 'all' }" 
          @click="handleStatusFilter('all')"
        >
          <div class="stat-title">全部</div>
          <div class="stat-value">{{ statistics.total }}</div>
        </div>
        <div 
          class="stat-card" 
          :class="{ 'active': activeStatus === '待确认' }" 
          @click="handleStatusFilter('待确认')"
        >
          <div class="stat-title">待确认</div>
          <div class="stat-value">{{ statistics.pending }}</div>
        </div>
        <div 
          class="stat-card" 
          :class="{ 'active': activeStatus === '待处置' }" 
          @click="handleStatusFilter('待处置')"
        >
          <div class="stat-title">待处置</div>
          <div class="stat-value">{{ statistics.toProcess }}</div>
        </div>
        <div 
          class="stat-card" 
          :class="{ 'active': activeStatus === '处置中' }" 
          @click="handleStatusFilter('处置中')"
        >
          <div class="stat-title">处置中</div>
          <div class="stat-value">{{ statistics.processing }}</div>
        </div>
      </div>

      <!-- 表格区域 -->
      <div class="table-container">
        <el-table
          :data="tableData"
          style="width: 100%"
          :header-cell-style="headerCellStyle"
          :row-class-name="tableRowClassName"
          @row-click="handleRowClick"
        >
          <el-table-column label="序号" width="60" align="center">
            <template #default="{ $index }">
              {{ (currentPage - 1) * pageSize + $index + 1 }}
            </template>
          </el-table-column>
          <el-table-column label="报警来源" width="120" align="center">
            <template #default="scope">
              {{ BRIDGE_ALARM_SOURCE_MAP[scope.row.alarmSource] || scope.row.alarmSource }}
            </template>
          </el-table-column>
          <el-table-column prop="alarmCode" label="报警编号" width="120" align="center" />
          <el-table-column prop="alarmTime" label="报警时间" width="150" align="center">
            <template #default="scope">
              {{ formatAlarmTime(scope.row.alarmTime) }}
            </template>
          </el-table-column>
          <el-table-column prop="deviceId" label="报警设备编码" width="120" align="center" />
          <el-table-column prop="alarmTypeName" label="报警类型" width="120" align="center">
          </el-table-column>
          <el-table-column prop="monitorObjectName" label="监测对象" width="120" align="center" />
          <el-table-column prop="alarmValue" label="报警值" width="100" align="center">
            <template #default="scope">
              {{ scope.row.alarmValue }} {{ scope.row.alarmValueUnit || '' }}
            </template>
          </el-table-column>
          <el-table-column prop="alarmLocation" label="报警位置" min-width="150" align="center" />
          <el-table-column prop="alarmLevelName" label="报警级别" width="120" align="center" />
          <!-- <el-table-column prop="ownershipUnitName" label="权属单位" width="120" align="center" /> -->
          <el-table-column label="报警状态" width="120" align="center">
            <template #default="scope">
              {{ BRIDGE_ALARM_STATUS_MAP[scope.row.alarmStatus] || scope.row.alarmStatusName }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right" align="center">
            <template #default="scope">
              <div class="operation-btns">
                <div class="operation-btn-row">
                  <template v-if="scope.row.alarmStatus === 4003701">
                    <span class="operation-btn-text" @click.stop="handleConfirm(scope.row)">确认</span>
                    <span class="operation-divider">|</span>
                  </template>
                  <template v-if="scope.row.alarmStatus === 4003703 || scope.row.alarmStatus === 4003704">
                    <span class="operation-btn-text" @click.stop="handleDisposal(scope.row)">处置</span>
                    <span class="operation-divider">|</span>
                  </template>
                  <span class="operation-btn-text" @click.stop="handleDetail(scope.row)">详情</span>
                  <span class="operation-divider">|</span>
                  <span class="operation-btn-text" @click.stop="handleLocation(scope.row)">定位</span>
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页区域 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 30, 50]"
          layout="total, prev, pager, next, jumper, sizes"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 报警详情弹窗 -->
    <BridgeAlarmDialog
      v-model:visible="dialogVisible"
      :alarm-id="currentAlarmId"
    />

    <!-- 报警确认弹窗 -->
    <BridgeAlarmConfirmDialog
      v-model:visible="confirmDialogVisible"
      :alarm-data="currentAlarmData"
      @success="handleConfirmSuccess"
    />

    <!-- 报警处置弹窗 -->
    <BridgeAlarmDisposalDialog
      v-model:visible="disposalDialogVisible"
      :alarm-data="currentAlarmData"
      @success="handleDisposalSuccess"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, computed, onUnmounted } from 'vue';
import { ElTable, ElTableColumn, ElPagination, ElMessage } from 'element-plus';
import BridgeAlarmDisposalSearch from './components/BridgeAlarmDisposalSearch.vue';
import BridgeAlarmDialog from './components/BridgeAlarmDialog.vue';
import BridgeAlarmConfirmDialog from './components/BridgeAlarmConfirmDialog.vue';
import BridgeAlarmDisposalDialog from './components/BridgeAlarmDisposalDialog.vue';
import { getBridgeAlarmList, getBridgeAlarmStatistics } from '@/api/bridge';
import { 
  BRIDGE_ALARM_LEVEL_MAP, 
  BRIDGE_ALARM_STATUS_MAP, 
  BRIDGE_ALARM_SOURCE_OPTIONS, 
  BRIDGE_ALARM_SOURCE_MAP, 
  BRIDGE_ALARM_TYPE_OPTIONS, 
  BRIDGE_ALARM_TYPE_MAP 
} from '@/constants/bridge';
import { misPosition } from '@/hooks/gishooks' //地图定位

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const tableData = ref([]);

// 查询参数
const queryParams = ref({});

// 当前选中的状态筛选
const activeStatus = ref('all');

// 统计数据
const statistics = ref({
  total: 0,
  pending: 0,
  toProcess: 0,
  processing: 0,
  handled: 0
});

// 详情弹窗相关
const dialogVisible = ref(false);
const currentAlarmId = ref('');

// 报警确认弹窗相关
const confirmDialogVisible = ref(false);
const currentAlarmData = ref({});

// 报警处置弹窗相关
const disposalDialogVisible = ref(false);

// 格式化报警时间
const formatAlarmTime = (timeObj) => {
  if (!timeObj) return '';
  // 如果是时间戳对象格式
  if (typeof timeObj === 'object' && timeObj.time) {
    return new Date(timeObj.time).toLocaleString('zh-CN');
  }
  // 如果是字符串格式
  if (typeof timeObj === 'string') {
    return timeObj;
  }
  return '';
};

// 获取报警等级文本
const getAlarmLevelText = (level) => {
  return BRIDGE_ALARM_LEVEL_MAP[level] || '';
};

// 获取报警等级样式
const getAlarmLevelClass = (level) => {
  const map = {
    4003601: 'alarm-level-first',
    4003602: 'alarm-level-second',
    4003603: 'alarm-level-third',
    4003604: 'alarm-level-third'
  };
  return ['alarm-level-tag', map[level]];
};

// 处理状态筛选
const handleStatusFilter = (status) => {
  activeStatus.value = status;
  // 根据状态更新查询参数
  if (status === 'all') {
    delete queryParams.value.alarmStatus;
  } else {
    const statusMap = {
      '待确认': 4003701,
      '待处置': 4003703,
      '处置中': 4003704
    };
    queryParams.value.alarmStatus = statusMap[status];
  }
  currentPage.value = 1;
  fetchAlarmData();
};

// 处理搜索
const handleSearch = (formData) => {
  // 转换查询参数
  const params = {};
  
  if (formData.alarmSource && formData.alarmSource !== '') {
    // 查找对应的数值
    const source = BRIDGE_ALARM_SOURCE_OPTIONS.find(item => item.label === formData.alarmSource);
    if (source) {
      params.alarmSource = source.value;
    }
  }
  
  if (formData.alarmLevel && formData.alarmLevel !== '') {
    params.alarmLevel = parseInt(formData.alarmLevel);
  }
  
  if (formData.alarmType && formData.alarmType !== '') {
    // 查找对应的数值
    params.alarmType = formData.alarmType;
  }
  
  if (formData.timeRange && formData.timeRange.length === 2) {
    params.startTime = formData.timeRange[0];
    params.endTime = formData.timeRange[1];
  }
  
  if (formData.deviceId) {
    params.deviceId = formData.deviceId;
  }
  
  queryParams.value = params;
  if (activeStatus.value !== 'all') {
    const statusMap = {
      '待确认': 4003701,
      '待处置': 4003703,
      '处置中': 4003704
    };
    queryParams.value.alarmStatus = statusMap[activeStatus.value];
  }

  currentPage.value = 1;
  fetchAlarmData();
  fetchAlarmStatistics();
};

// 处理重置
const handleReset = () => {
  queryParams.value = {};
  activeStatus.value = 'all';
  currentPage.value = 1;
  fetchAlarmData();
  fetchAlarmStatistics();
};

// 获取统计数据
const fetchAlarmStatistics = async () => {
  try {
    const res = await getBridgeAlarmStatistics({
      startDate: queryParams.value.startTime || "",
      endDate: queryParams.value.endTime || ""
    });
    if (res.code === 200 && res.data) {
      statistics.value = {
        total: res.data.totalAlarms || 0,
        pending: res.data.pendingConfirm || 0,
        toProcess: res.data.pendingHandle || 0,
        processing: res.data.handling || 0,
        handled: res.data.handled || 0
      };
    }
  } catch (error) {
    console.error('获取报警统计数据失败:', error);
    // 使用模拟数据
    statistics.value = {
      total: 10,
      pending: 2,
      toProcess: 2,
      processing: 6,
      handled: 0
    };
  }
};

// 获取报警数据
const fetchAlarmData = async () => {
  try {
    const res = await getBridgeAlarmList(currentPage.value, pageSize.value, queryParams.value);
    if (res.code === 200 && res.data) {
      tableData.value = res.data.rows || [];
      total.value = res.data.total || 0;
    }
  } catch (error) {
    console.error('获取报警数据失败:', error);
    tableData.value = [];
    total.value = 0;
  }
};

// 处理分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val;
  currentPage.value = 1;
  fetchAlarmData();
};

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val;
  fetchAlarmData();
};

// 表头样式
const headerCellStyle = {
  background: '#F4F4F4',
  fontFamily: 'PingFangSC, PingFang SC',
  fontWeight: '500',
  fontSize: '14px',
  color: '#282828',
  height: '64px'
};

// 斑马纹和选中行样式
const tableRowClassName = ({ rowIndex }) => {
  return rowIndex % 2 === 0 ? 'even-row' : 'odd-row';
};

// 处理行点击
const handleRowClick = (row) => {
  console.log('行数据:', row);
};

// 操作按钮处理函数
const handleConfirm = (row) => {
  console.log('确认报警:', row);
  currentAlarmData.value = row;
  confirmDialogVisible.value = true;
};

const handleDisposal = (row) => {
  console.log('处置报警:', row);
  currentAlarmData.value = row;
  disposalDialogVisible.value = true;
};

const handleDetail = (row) => {
  currentAlarmId.value = row.id + "";
  dialogVisible.value = true;
};

const handleLocation = (row) => {
  if (
    row.longitude &&
    row.longitude != '' &&
    row.latitude &&
    row.latitude != ''
  ) {
    misPosition.value = {
      longitude: row.longitude, //经度
      latitude: row.latitude //维度
    }
  } else {
    ElMessage.warning('没有经纬度，无法定位！')
  }
};

// 确认成功回调
const handleConfirmSuccess = () => {
  fetchAlarmData(); // 重新获取数据
  fetchAlarmStatistics(); // 重新获取统计数据
};

// 处置成功回调
const handleDisposalSuccess = () => {
  fetchAlarmData(); // 重新获取数据
  fetchAlarmStatistics(); // 重新获取统计数据
};

onMounted(() => {
  fetchAlarmData();
  fetchAlarmStatistics();
});

// 组件卸载时移除监听器
onUnmounted(() => {
  // 移除监听器
});
</script>

<style scoped>
.bridge-alarm-disposal {
  width: 100%;
  height: calc(100vh - 180px);
  display: flex;
  flex-direction: column;
  padding: 16px;
  background-color: #F0F2F5;
  gap: 16px;
  box-sizing: border-box;
  overflow: hidden;
}

/* 搜索表单区域 */
.search-section {
  background: #FFFFFF;
  padding: 16px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  flex-shrink: 0;
}

/* 表格区域样式 */
.table-section {
  width: 100%;
  flex: 1;
  background: white;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  padding: 16px;
  min-height: 0;
  overflow: hidden;
}

/* 统计卡片区域 */
.statistics-cards {
  display: flex;
  margin-bottom: 16px;
  gap: 16px;
  flex-shrink: 0;
}

.stat-card {
  flex: 1;
  height: 80px;
  background: #F8FAFD;
  border-radius: 4px;
  padding: 16px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  cursor: pointer;
  border: 1px solid transparent;
  transition: all 0.3s ease;
}

.stat-card.active {
  background: #EEF5FF;
  border: 1px solid #0086FF;
}

.stat-title {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #647688;
  margin-bottom: 8px;
}

.stat-value {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 24px;
  color: #282828;
}

/* 表格样式 */
.table-container {
  flex: 1;
  width: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 200px;
}

:deep(.el-table) {
  --el-table-border-color: #EBEEF5;
  --el-table-header-bg-color: #F5F8FA;
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

:deep(.el-table__inner-wrapper) {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

:deep(.el-table__header-wrapper) {
  flex-shrink: 0;
}

:deep(.el-table__body-wrapper) {
  flex: 1;
  overflow-y: auto !important;
}

:deep(.el-scrollbar) {
  flex: 1;
  display: flex;
  flex-direction: column;
}

:deep(.el-scrollbar__wrap) {
  flex: 1;
  overflow-x: hidden;
}

/* 表格行样式 */
:deep(.el-table .even-row) {
  background-color: #FFFFFF;
}

:deep(.el-table .odd-row) {
  background-color: #F5F8FA;
}

:deep(.el-table th) {
  background-color: #F5F8FA;
  color: #333333;
  font-weight: bold;
  height: 40px;
  padding: 0;
  border-bottom: 1px solid #EBEEF5;
}

:deep(.el-table tr) {
  height: 48px;
}

:deep(.el-table__row:hover) {
  background: #EEF5FF !important;
}

:deep(.el-table__row.selected-row) {
  background: #EEF5FF !important;
}

/* 报警等级标签样式 */
.alarm-level-tag {
  width: 72px;
  height: 32px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  margin: 0 auto;
}

.alarm-level-first {
  background: rgba(255,0,0,0.1);
  border: 1px solid #FF0000;
  color: #FF0000;
}

.alarm-level-second {
  background: rgba(255,133,0,0.1);
  border: 1px solid #FF8500;
  color: #FF8500;
}

.alarm-level-third {
  background: rgba(255,211,0,0.1);
  border: 1px solid #FFD300;
  color: #FFD300;
}

.operation-btns {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.operation-btn-row {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 4px 0;
}

.operation-btn-text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #0086FF;
  cursor: pointer;
}

.operation-divider {
  margin: 0 4px;
  color: #CED3DA;
}

/* 分页样式 */
.pagination-container {
  display: flex;
  justify-content: flex-end;
  padding: 16px 0 0 0;
  border-top: 1px solid #EBEEF5;
  flex-shrink: 0;
}

:deep(.el-pagination .el-pagination__total) {
  margin-right: 16px;
}

:deep(.el-pagination .el-input__wrapper) {
  box-shadow: 0 0 0 1px #CED3DA inset;
}

:deep(.el-pagination .el-select .el-input) {
  margin: 0 8px;
}

:deep(.el-pagination button:disabled) {
  background-color: #F5F8FA;
}

:deep(.el-pagination .el-pagination__jump) {
  margin-left: 16px;
}

/* 确保操作列文字不换行 */
:deep(.cell) {
  white-space: nowrap;
}
</style>