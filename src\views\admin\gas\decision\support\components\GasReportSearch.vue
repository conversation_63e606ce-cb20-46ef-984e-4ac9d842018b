<template>
  <div class="gas-report-search">
    <div class="search-form">
      <div class="form-item">
        <span class="label">报告类型:</span>
        <el-select v-model="formData.reportType" class="form-input" placeholder="请选择">
          <el-option label="全部" value="" />
          <el-option 
            v-for="item in REPORT_TYPES" 
            :key="item.value" 
            :label="item.label" 
            :value="item.value" 
          />
        </el-select>
      </div>
      <div class="form-item">
        <span class="label">生成时间:</span>
        <el-date-picker
          v-model="formData.timeRange"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
        />
      </div>
      <div class="form-item">
        <el-input v-model="formData.reportName" class="form-input" placeholder="请输入报告名称" />
      </div>
      <div class="form-item" style="margin-left: auto;">
        <el-button type="primary" class="search-btn" @click="handleSearch">查询</el-button>
        <el-button class="reset-btn" @click="handleReset">重置</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { ElSelect, ElOption, ElInput, ElButton, ElDatePicker } from 'element-plus';
import { REPORT_TYPES } from '@/constants/gas';

const emit = defineEmits(['search', 'reset']);

// 表单数据
const formData = ref({
  reportType: '',
  timeRange: [],
  reportName: ''
});

// 处理查询
const handleSearch = () => {
  const params = {
    reportType: formData.value.reportType,
    reportName: formData.value.reportName,
    startTime: formData.value.timeRange && formData.value.timeRange[0] ? formData.value.timeRange[0] : '',
    endTime: formData.value.timeRange && formData.value.timeRange[1] ? formData.value.timeRange[1] : '',
  };
  emit('search', params);
};

// 处理重置
const handleReset = () => {
  formData.value = {
    reportType: '',
    timeRange: [],
    reportName: ''
  };
  emit('reset');
};
</script>

<style scoped>
.gas-report-search {
  width: 100%;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.form-item {
  display: flex;
  align-items: center;
  margin-right: 16px;
  margin-bottom: 16px;
}

.label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #282828;
  margin-right: 8px;
  white-space: nowrap;
}

.form-input {
  width: 180px;
  height: 32px;
}

:deep(.el-input__wrapper) {
  background: #FFFFFF;
  border-radius: 2px;
  border: 1px solid #CED3DA;
  height: 32px;
  box-shadow: none !important;
}

:deep(.el-select .el-input__wrapper) {
  background: #FFFFFF;
  border-radius: 2px;
  border: 1px solid #CED3DA;
  height: 32px;
  box-shadow: none !important;
}

:deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px #0277FD inset !important;
}

:deep(.el-select .el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px #0277FD inset !important;
}

.search-btn {
  width: 60px;
  height: 32px;
  background: #0277FD;
  border-radius: 2px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #FFFFFF;
  padding: 0;
  margin-right: 8px;
}

.reset-btn {
  width: 60px;
  height: 32px;
  background: #FFFFFF;
  border-radius: 2px;
  border: 1px solid #CED3DA;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #647688;
  padding: 0;
}
</style>