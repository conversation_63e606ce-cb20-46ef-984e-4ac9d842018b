<template>
  <PanelBox title="场站统计">
    <div class="panel-content">
      <template v-if="stationData.length > 0">
        <div class="station-item" v-for="(station, index) in stationData" :key="index">
          <div class="station-info">
            <span class="station-name">{{ station.name }}</span>
            <div class="chart-area">
              <div class="progress-container">
                <div class="progress-bar" :style="{ width: `${station.percentage}%` }">
                  <span class="indicator-mark"></span>
                </div>
              </div>
              <div class="percentage">{{ station.percentage }}%</div>
            </div>
          </div>
        </div>
      </template>
      <div class="no-data" v-else>
        <NoData />
      </div>
    </div>
  </PanelBox>
</template>

<script setup>
import PanelBox from '@/components/screen/PanelBox.vue'
import NoData from '@/components/common/NoData.vue'
import { ref, onMounted } from 'vue'
import { getStationStatistics } from '@/api/gas.js'

// 场站数据
const stationData = ref([])

// 获取场站统计数据
const fetchStationData = async () => {
  try {
    const res = await getStationStatistics()
    if (res.code == 200) {
      const totalCount = res.data.typeStatistics.reduce((sum, item) => sum + item.count, 0)
      stationData.value = res.data.typeStatistics.map(item => ({
        name: item.name,
        percentage: totalCount > 0 ? Math.round((item.count / totalCount) * 100) : 0
      }))
    }
  } catch (error) {
    console.error('获取场站统计数据失败：', error)
  }
}

onMounted(() => {
  fetchStationData()
})
</script>

<style scoped>
.panel-content {
  flex: 1;
  padding: 0px 10px 5px;
  display: flex;
  flex-direction: column;
  gap: 0px;
  /* 当没有数据时，内容居中 */
  justify-content: center;
  align-items: center;
}
.no-data{
  margin-top: 50px;
}
.station-item {
  display: flex;
  flex-direction: column;
}

.station-info {
  display: flex;
  flex-direction: column;
  gap: 0px;
}

.station-name {
  color: #fff;
  font-size: 12px;
  margin-left: 5px;
  opacity: 0.6;
}

.chart-area {
  display: flex;
  align-items: center;
  gap: 5px;
}

.progress-container {
  flex: 1;
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: visible;
  position: relative;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #6DBFEC 0%, #129AFF 100%);
  border-radius: 4px;
  position: relative;
}

.indicator-mark {
  position: absolute;
  width: 2px;
  height: 14px;
  background: #FFFFFF;
  right: 0;
  top: -3px;
}

.percentage {
  min-width: 45px;
  text-align: right;
  color: #fff;
  font-size: 16px;
  font-weight: 500;
}

/* 940px左右高度的屏幕特别优化 */
@media (min-height: 940px) and (max-height: 1055px) {
  .panel-content {
    padding: 5px 10px;
    /* 同样需要在小屏幕下居中 */
    justify-content: center;
    align-items: center;
  }

  .station-item {
    margin-bottom: 5px;
  }

  .station-name {
    font-size: 11px;
    margin-bottom: 1px;
  }

  .chart-area {
    gap: 3px;
  }

  .progress-container {
    height: 6px;
    /* 减小进度条高度 */
  }

  .indicator-mark {
    height: 10px;
    /* 减小指示标记高度 */
    width: 1px;
    top: -2px;
  }

  .percentage {
    min-width: 35px;
    font-size: 14px;
  }
}
</style>