<template>
  <PanelBox title="隐患信息">
    <template #extra>
      <div class="com-select">
        <CommonSelect v-model="timeRange" :options="timeOptions" @change="handleTimeChange" />
      </div>
    </template>
    <div class="panel-content">
      <!-- 上部：隐患总数与整改率图表 -->
      <div class="top-section">
        <div class="chart-container">
          <div ref="rectificationChartRef" class="rectification-chart"></div>
        </div>
        <div class="stats-summary">
          <div class="total-risks">
            <span class="label">隐患总数</span>
            <div class="total-risks-line"></div>
            <span class="value primary-value">{{ statsData.total }}</span>
          </div>
          <div class="risk-categories">
            <div class="category-item">
              <span class="dot-wrapper blue-dot"><span class="dot blue-dot"></span></span>
              <span class="label">已整改</span>
              <span class="value">{{ statsData.rectified }}</span>
            </div>
            <div class="category-item">
              <span class="dot-wrapper orange-dot"><span class="dot orange-dot"></span></span>
              <span class="label">待整改</span>
              <span class="value">{{ statsData.pending }}</span>
            </div>
            <div class="category-item">
              <span class="dot-wrapper teal-dot"><span class="dot teal-dot"></span></span>
              <span class="label">整改中</span>
              <span class="value">{{ statsData.inProgress }}</span>
            </div>
            <div class="category-item">
              <span class="dot-wrapper green-dot"><span class="dot green-dot"></span></span>
              <span class="label">待复查</span>
              <span class="value">{{ statsData.toReview }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 下部：隐患列表 -->
      <div class="list-section">
        <ScrollTable :columns="tableColumns" :data="riskList" :autoScroll="true" :scrollSpeed="3000"
          :tableHeight="tableHeight" :visibleRows="3" :hiddenHeader="true">
          <template #custom="{ row }">
            <div class="risk-item-row">
              <div class="risk-main-info">
                <span class="status-label">整改进度：</span>
                <span :class="['status-value', getStatusClass(row.status)]">{{ row.statusText }}</span>
                <span :class="['type-tag', getRiskTypeClass(row.type)]">{{ row.type }}</span>
              </div>
              <div class="risk-description">
                隐患描述：{{ row.description }}
              </div>
              <div class="risk-location">
                <img src="@/assets/images/screen/common/location.svg" alt="位置" class="location-icon" />
                <span>{{ row.location }}</span>
              </div>
            </div>
          </template>
        </ScrollTable>
      </div>
    </div>
  </PanelBox>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount, nextTick } from 'vue'
import * as echarts from 'echarts/core'
import { PieChart } from 'echarts/charts'
import { TitleComponent, TooltipComponent, LegendComponent, GraphicComponent } from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'
import PanelBox from '@/components/screen/PanelBox.vue'
import CommonSelect from '@/components/screen/CommonSelect.vue'
import ScrollTable from '@/components/screen/common/ScrollTable.vue'
import { useDebounceFn } from '@vueuse/core' // 用于resize防抖

echarts.use([PieChart, TitleComponent, TooltipComponent, LegendComponent, GraphicComponent, CanvasRenderer])

// 时间选择
const timeRange = ref('week')
const timeOptions = [
  { label: '近一周', value: 'week' },
  { label: '近一月', value: 'month' },
  { label: '近一年', value: 'year' }
]

// 模拟统计数据
const statsData = ref({
  total: 2,
  rectified: 1,
  inProgress: 0,
  pending: 0,
  toReview: 1
})

const rectificationRate = ref(0) // 将在 handleTimeChange 中计算

// ECharts 实例
const rectificationChartRef = ref(null)
let chartInstance = null

// 模拟隐患列表数据
const riskList = ref([])

const mockRiskListData = {
  week: [
    { id: 1, status: 'pending', statusText: '待整改', type: '一般隐患', description: '供热管道A段轻微泄漏', location: 'XX路1号热力井' },
    { id: 2, status: 'inProgress', statusText: '整改中', type: '较大隐患', description: '换热站B设备异响', location: 'XX小区中心换热站' },
    { id: 3, status: 'rectified', statusText: '已整改', type: '一般隐患', description: '阀门C外观锈蚀', location: 'XX街22号阀门室' },
    { id: 4, status: 'toReview', statusText: '待复查', type: '一般隐患', description: '压力表D读数异常', location: 'XX工业园3号楼' },
    { id: 5, status: 'pending', statusText: '待整改', type: '重大隐患', description: '主管道E区域沉降风险', location: 'XX大道与YY路交叉口' },
  ],
  month: [
    { id: 6, status: 'pending', statusText: '待整改', type: '一般隐患', description: '管道F保温层破损', location: 'XX新区5号楼外管' },
    { id: 7, status: 'inProgress', statusText: '整改中', type: '较大隐患', description: '调节阀G失灵', location: 'XX商业广场地下管廊' },
    { id: 8, status: 'rectified', statusText: '已整改', type: '一般隐患', description: '巡检记录H缺失', location: 'XX路巡检段' },
  ],
  year: [
    { id: 9, status: 'pending', statusText: '待整改', type: '重大隐患', description: '管网I老化严重', location: '老城区主干管网' },
    { id: 10, status: 'rectified', statusText: '已整改', type: '较大隐患', description: '安全出口J堵塞', location: 'XX工厂锅炉房' },
  ]
}

const tableColumns = [{ title: '隐患详情', dataIndex: 'custom', width: '100%' }]

// 动态计算表格高度，使其填满剩余空间
const tableHeight = ref('100px') // 初始值，会被resize覆盖

const calculateTableHeight = () => {
  // Set tableHeight to 100% to fill the list-section
  // list-section has flex: 1, so it will take available space
  tableHeight.value = '100%';
}


const initChart = () => {
  if (rectificationChartRef.value) {
    chartInstance = echarts.init(rectificationChartRef.value)
    updateChart()
  }
}

const updateChart = () => {
  if (!chartInstance) return

  const rate = rectificationRate.value
  const option = {
    title: {
      text: `${(rate * 100).toFixed(0)}%`,
      subtext: '整改率',
      left: 'center',
      top: '40%', // 调整以垂直居中
      textStyle: {
        color: '#00EDFF',
        fontSize: 18, // 根据实际大小调整
        fontFamily: 'D-DIN-Bold, sans-serif',
      },
      subtextStyle: {
        color: '#FFFFFF',
        fontSize: 12, // 根据实际大小调整
        fontFamily: 'PingFangSC-Regular, sans-serif',
        top: '50%'
      },
      itemGap: 5 // 调整主副标题间距
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {d}%'
    },
    series: [
      {
        name: '整改率',
        type: 'pie',
        radius: ['70%', '90%'], // 环形图内外半径
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: false // 不在中心显示标签，因为title已经显示了
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { value: statsData.value.rectified, name: '已整改', itemStyle: { color: '#0081ED' } },
          { value: statsData.value.pending, name: '待整改', itemStyle: { color: '#FD8800' } },
          { value: statsData.value.inProgress, name: '整改中', itemStyle: { color: '#00FFC6' } },
          { value: statsData.value.toReview, name: '待复查', itemStyle: { color: '#54FF00' } }
        ],
        silent: true // 禁止图表的鼠标事件
      }
    ]
  }
  chartInstance.setOption(option)
}

const handleTimeChange = (value) => {
  console.log('时间范围变更为:', value)
  // 模拟根据时间范围更新数据
  const currentStats = {
    week: { total: 0, rectified: 0, inProgress: 0, pending: 0, toReview: 0 },
    month: { total: 0, rectified: 0, inProgress: 0, pending: 0, toReview: 0 },
    year: { total: 0, rectified: 0, inProgress: 0, pending: 0, toReview: 0 }
  }
  statsData.value = currentStats[value]

  if (statsData.value.total > 0) {
    rectificationRate.value = statsData.value.rectified / statsData.value.total
  } else {
    rectificationRate.value = 0
  }

  riskList.value = mockRiskListData[value] || []

  if (chartInstance) {
    updateChart()
  }
}

const getStatusClass = (status) => {
  return `status-${status}`
}
const getRiskTypeClass = (type) => {
  if (type === '一般隐患') return 'type-general'
  if (type === '较大隐患') return 'type-larger'
  if (type === '重大隐患') return 'type-major'
  return ''
}

const debouncedResize = useDebounceFn(() => {
  if (chartInstance) {
    chartInstance.resize()
  }
  calculateTableHeight()
}, 200)


onMounted(() => {
  handleTimeChange(timeRange.value) // 初始化数据和图表
  nextTick(() => {
    initChart()
    calculateTableHeight()
  })
  window.addEventListener('resize', debouncedResize)
})

onBeforeUnmount(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
  window.removeEventListener('resize', debouncedResize)
})

</script>

<style scoped>
.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 3px;
  /* 各区域间距 */
  overflow: hidden;
  /* 确保内容不溢出 PanelBox */
}

.com-select {
  margin-right: 20px;
  /* 与 LeftBottomPanel.vue 保持一致 */
}

.top-section {
  display: flex;
  gap: 15px;
  /* 统计和图表间距 */
  height: 120px;
  /* 参考 righttop.vue .box_14 height */
  flex-shrink: 0;
  /* 防止被压缩 */
  align-items: center;
  /* 垂直居中对齐 */
}

.stats-summary {
  flex: 1;
  /* 占据更多空间 */
  display: flex;
  flex-direction: column;
  justify-content: center;
  /* 垂直居中内容 */
  color: #fff;
  gap: 15px;
  /* 隐患总数和分类之间的间距 */
}

.total-risks {
  display: flex;
  align-items: center;
  /* 水平居中对齐标签、线和数值 */
  /* margin-bottom: 10px; 由 stats-summary 的 gap 控制 */
}

.total-risks .label {
  font-family: PingFangSC, 'PingFang SC', sans-serif;
  font-weight: 500;
  font-size: 16px;
  color: #FFFFFF;
  line-height: 22px;
  margin-right: 8px;
  /* 与虚线间距 */
}

.total-risks .value {
  font-family: 'D-DIN', 'D-DIN', sans-serif;
  font-weight: bold;
  font-size: 24px;
  color: #00EDFF;
  line-height: 26px;
  /* 保持与设计一致或根据需要调整 */
  margin-left: 8px;
  /* 与虚线间距 */
}

.total-risks-line {
  width: 180px;
  height: 1px;
  border-top: 1px dotted #0156B9;
  /* 使用 border-top 实现虚线效果更佳 */
}

.risk-categories {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  /* 两列布局 */
  gap: 8px 15px;
  /* 行间距 列间距 */
}

.category-item {
  display: flex;
  align-items: center;
}

.dot-wrapper {
  width: 10px;
  height: 10px;
  /* border-radius: 50%; */
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 6px;
}

.category-item .dot {
  width: 4px;
  height: 4px;
  /* border-radius: 50%; */
}

.dot-wrapper.blue-dot {
  border: 1px solid #0081ED;
}

.category-item .dot.blue-dot {
  background-color: #0081ED;
}

/* 已整改 */

.dot-wrapper.orange-dot {
  border: 1px solid #FD8800;
}

.category-item .dot.orange-dot {
  background-color: #FD8800;
}

/* 待整改 */

.dot-wrapper.teal-dot {
  border: 1px solid #00FFC6;
}

.category-item .dot.teal-dot {
  background-color: #00FFC6;
}

/* 整改中 */

.dot-wrapper.green-dot {
  border: 1px solid #54FF00;
}

.category-item .dot.green-dot {
  background-color: #54FF00;
}

/* 待复查 */
:deep(.dark-row) {
  background-color: transparent;
}
:deep(.light-row) {
  background-color: transparent;
}
.category-item .label {
  font-family: PingFangSC, 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 14px;
  color: #FFFFFF;
  margin-right: 8px;
  white-space: nowrap;
}

.category-item .value {
  font-family: 'D-DIN', 'D-DIN', sans-serif;
  font-weight: bold;
  font-size: 20px;
  color: #FFFFFF;
}

.chart-container {
  width: 120px;
  /* 参考 righttop.vue .text-wrapper_14 width */
  height: 120px;
  /* 参考 righttop.vue .text-wrapper_14 height */
  flex-shrink: 0;
  /* 防止被压缩 */
  display: flex;
  align-items: center;
  justify-content: center;
  /* background: url(...) no-repeat; background-size: 100% 100%; */
  /* 可选：如果需要背景图 */
}

.rectification-chart {
  width: 100%;
  height: 100%;
}

.list-section {
  flex: 1;
  /* 占据剩余空间 */
  overflow: hidden;
  /* 配合ScrollTable内部滚动 */
  /* background: url(https://lanhu-oss-2537-2.lanhuapp.com/SketchPng5a460a69625991281d73cba1eb8be19a0052f27cbae6189f03eae2d68efff0ba) 100% no-repeat; */
}

.risk-item-row {
  background-image: url('@/assets/images/screen/heating/row_bg.png');
  background-size: 100% 100%;
  /* Stretch to fit */
  background-repeat: no-repeat;
  height: 92px;
  width: 100%;
  padding: 10px 15px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  /* justify-content: space-around; */
  /* Distribute content vertically */
}

.risk-main-info {
  display: flex;
  align-items: center;
}

.status-label {
  font-family: 'PingFangSC', 'PingFang SC', sans-serif;
  font-weight: 500;
  font-size: 16px;
  color: #DEEDFF;
}

.status-value {
  font-family: 'PingFangSC', 'PingFang SC', sans-serif;
  font-weight: 500;
  font-size: 16px;
  margin-left: 8px;
  /* Space after label */
  /* Base color, overridden by specific status classes */
  color: #DEEDFF;
}

.status-value.status-pending {
  color: #FD8800;
}

/* 待整改 - Orange */
.status-value.status-rectified {
  color: #0081ED;
}

/* 已整改 - Blue */
.status-value.status-inProgress {
  color: #00FFC6;
}

/* 整改中 - Teal */
.status-value.status-toReview {
  color: #54FF00;
}

/* 待复查 - Green */

.type-tag {
  padding: 3px 8px;
  border-radius: 4px;
  font-family: 'PingFangSC', 'PingFang SC', sans-serif;
  font-weight: 500;
  font-size: 12px;
  text-align: center;
  margin-left: auto;
  /* Push to the right */
}

.type-tag.type-general {
  background-color: #FFB02A;
  /* Yellowish-orange */
  color: #000000;
}

.type-tag.type-larger {
  background-color: #FF7D00;
  /* Orange - example */
  color: #FFFFFF;
}

.type-tag.type-major {
  background-color: #D9001B;
  /* Red - example */
  color: #FFFFFF;
}

.risk-description {
  font-family: 'PingFangSC', 'PingFang SC', sans-serif;
  font-weight: 500;
  font-size: 16px;
  color: #DEEDFF;
  opacity: 0.8;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.risk-location {
  display: flex;
  align-items: center;
  font-family: 'PingFangSC', 'PingFang SC', sans-serif;
  font-weight: 500;
  font-size: 16px;
  color: #DEEDFF;
  opacity: 0.8;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.location-icon {
  width: 14px;
  height: 14px;
  margin-right: 6px;
  flex-shrink: 0;
  /* Prevent icon from shrinking */
}

/* background-size: 100% 100%; */
/* 列表区域的背景，如果需要 */
/* padding: 5px; */
/* 内部列表项与边框的间距 */

.risk-item-row {
  padding: 5px 8px;
  /* 参考 .box_21 内边距 */
  margin-bottom: 8px;
  /* 列表项之间的间距 */
  color: #FFFFFF;
  font-size: 14px;
}

.risk-item-row:last-child {
  margin-bottom: 8px;
}

.risk-main-info {
  display: flex;
  align-items: center;
  margin-bottom: 6px;
  /* 参考 .box_22 与 .text-wrapper_17 间距 */
}

.status-label {
  font-size: 16px;
  /* 参考 .text_102 */
  font-family: PingFangSC-Medium, sans-serif;
  color: #DEEDFF;
  margin-right: 5px;
}

.status-value {
  font-size: 16px;
  /* 参考 .text_103 */
  font-family: PingFangSC-Medium, sans-serif;
  margin-right: auto;
  /* 推开类型标签 */
}

.status-pending {
  color: #FFC300;
}

/* 待整改 - 黄色 */
.status-inProgress {
  color: #00C6FF;
}

/* 整改中 - 蓝色 */
.status-rectified {
  color: #32D79F;
}

/* 已整改 - 绿色 */
.status-toReview {
  color: #FF7A00;
}

/* 待复查 - 橙色 */


.type-tag {
  padding: 2px 8px;
  border-radius: 2px;
  font-size: 12px;
  /* 参考 .text_104 */
  color: #FFFFFF;
  border: 1px solid;
}

.type-general {
  background-color: rgba(255, 225, 0, 0.5);
  border-color: #FFE100;
}

/* 一般隐患 - 黄色 */
.type-larger {
  background-color: rgba(255, 160, 17, 0.5);
  border-color: #FFA011;
}

/* 较大隐患 - 橙色 */
.type-major {
  background-color: rgba(255, 70, 70, 0.5);
  border-color: #FF4646;
}

/* 重大隐患 - 红色 */


.risk-description {
  margin-bottom: 6px;
  /* 参考 .text-wrapper_17 与 .box_23 间距 */
  line-height: 1.4;
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  /* 参考 .text_105 */
}

.risk-location {
  display: flex;
  align-items: center;
  font-size: 14px;
  /* 参考 .text_106 */
  color: rgba(255, 255, 255, 0.8);
}

.location-icon {
  width: 14px;
  /* 参考 .thumbnail_10 */
  height: 14px;
  margin-right: 6px;
}

/* 响应式调整，如果需要更细致的控制 */
@media (max-width: 1600px) {
  .total-risks .value {
    font-size: 22px;
  }

  .category-item .value {
    font-size: 16px;
  }

  .status-label,
  .status-value {
    font-size: 15px;
  }
}

@media (max-height: 1050px) {
  .risk-item-row {
    padding: 10px 8px;
    font-size: 12px;
  }
}
</style>