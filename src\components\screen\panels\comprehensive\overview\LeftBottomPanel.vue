<template>
  <PanelBox title="风险隐患">
    <template #extra>
      <div class="tab-buttons">
        <div class="tab-btn" :class="{ active: activeTab === 'risk' }" @click="changeTab('risk')">
          风险
        </div>
        <div class="divider"></div>
        <div class="tab-btn" :class="{ active: activeTab === 'hidden' }" @click="changeTab('hidden')">
          隐患
        </div>
      </div>
    </template>
    <div class="panel-content">
      <!-- 风险面板 -->
      <div v-if="activeTab === 'risk'" class="risk-panel">
        <div class="chart-legend">
          <div class="legend-item" v-for="(item, index) in legendItems" :key="index">
            <div class="legend-color" :style="{ background: item.color }"></div>
            <div class="legend-text">{{ item.name }}</div>
          </div>
        </div>
        <div ref="chartRef" class="chart-container"></div>
      </div>

      <!-- 隐患面板 -->
      <div v-else class="hidden-panel">
        <div class="ring-chart-container">
          <div ref="ringChartRef" class="ring-chart"></div>
          <div class="center-num">{{ centerNum }}</div>
        </div>
        <div class="stats-container">
          <div class="stat-item">
            <div class="stat-icon total-icon"></div>
            <div class="stat-info">
              <div class="stat-value">{{ statsData.total }}</div>
              <div class="stat-label">隐患总数</div>
            </div>
          </div>
          <div class="stat-item">
            <div class="stat-icon completed-icon"></div>
            <div class="stat-info">
              <div class="stat-value">{{ statsData.completed }}</div>
              <div class="stat-label">已整改</div>
            </div>
          </div>
          <div class="stat-item">
            <div class="stat-icon rate-icon"></div>
            <div class="stat-info">
              <div class="stat-value">{{ statsData.completionRate }}%</div>
              <div class="stat-label">整改完成率</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </PanelBox>
</template>

<script setup>
import { ref, onMounted, onUnmounted, reactive, nextTick, watch } from 'vue'
import PanelBox from '@/components/screen/PanelBox.vue'
import * as echarts from 'echarts'

// 综合态势总览左下面板组件

// 当前激活的标签页
const activeTab = ref('risk')

// 切换标签页
const changeTab = (tab) => {
  activeTab.value = tab
  
  // 切换标签页时，延迟触发一次window的resize事件
  // 这样可以让echarts图表重新计算大小
  setTimeout(() => {
    window.dispatchEvent(new Event('resize'))
    
    // 切换到风险标签页时初始化风险图表
    if (tab === 'risk') {
      if (!chartInstance) {
        initChart()
      } else {
        chartInstance.resize()
      }
    } 
    // 切换到隐患标签页时初始化隐患环形图
    else if (tab === 'hidden') {
      if (!ringChartInstance) {
        initRingChart()
      } else {
        ringChartInstance.resize()
      }
    }
  }, 200)
}

// 风险面板图表DOM引用
const chartRef = ref(null)
// 图表实例
let chartInstance = null

// 隐患面板环形图DOM引用
const ringChartRef = ref(null)
// 环形图实例
let ringChartInstance = null

// 环形图中心数字
const centerNum = ref(16)

// 隐患统计数据
const statsData = reactive({
  total: 100,
  completed: 60,
  completionRate: 60
})

// 风险面板图例数据
const legendItems = reactive([
  { name: '重大风险', color: '#DE6970' },
  { name: '较大风险', color: '#FE9150' },
  { name: '一般风险', color: '#D8F115' },
  { name: '低风险', color: '#00E1B9' }
])

// 风险面板数据
const chartData = reactive({
  xAxisData: ['燃气风险', '桥梁风险', '排水风险', '供热风险'],
  series: [
    {
      name: '低风险',
      data: [120, 150, 170, 35],
      color: {
        type: 'linear',
        x: 0, y: 0, x2: 0, y2: 1,
        colorStops: [
          { offset: 0, color: '#00E1B9' },
          { offset: 1, color: 'rgba(0,225,185,0.01)' }
        ]
      }
    },
    {
      name: '一般风险',
      data: [60, 120, 30, 125],
      color: {
        type: 'linear',
        x: 0, y: 0, x2: 0, y2: 1,
        colorStops: [
          { offset: 0, color: '#D8F115' },
          { offset: 1, color: 'rgba(216,241,21,0.01)' }
        ]
      }
    },
    {
      name: '较大风险',
      data: [95, 15, 40, 95],
      color: {
        type: 'linear',
        x: 0, y: 0, x2: 0, y2: 1,
        colorStops: [
          { offset: 0, color: '#FE9150' },
          { offset: 1, color: 'rgba(254,145,80,0.01)' }
        ]
      }
    },
    {
      name: '重大风险',
      data: [35, 48, 75, 25],
      color: {
        type: 'linear',
        x: 0, y: 0, x2: 0, y2: 1,
        colorStops: [
          { offset: 0, color: '#DE6970' },
          { offset: 1, color: 'rgba(222,105,112,0.01)' }
        ]
      }
    }
  ]
})

// 隐患环形图数据
const ringChartData = reactive({
  series: [
    {
      name: '重大隐患',
      value: 30,
      color: '#DE6970'
    },
    {
      name: '二级隐患',
      value: 40,
      color: '#4C95FF'
    },
    {
      name: '一般隐患',
      value: 30,
      color: '#F4CE4A'
    }
  ]
})

// 初始化风险图表
const initChart = async () => {
  await nextTick()
  
  // 添加额外的延迟确保DOM完全渲染
  await new Promise(resolve => setTimeout(resolve, 200))
  
  if (!chartRef.value) {
    console.error('图表DOM引用不存在')
    return
  }

  try {
    // 确保容器有尺寸
    if (chartRef.value.offsetWidth === 0 || chartRef.value.offsetHeight === 0) {
      console.error('图表容器尺寸为0，无法初始化')
      // 强制设置最小尺寸，确保图表能够渲染
      chartRef.value.style.minHeight = '180px'
      chartRef.value.style.width = '100%'
    }
    
    // 如果已经有实例，先销毁
    if (chartInstance) {
      chartInstance.dispose()
    }
    
    // 创建图表实例
    chartInstance = echarts.init(chartRef.value)

    // 设置图表配置项
    updateChart()

    // 监听窗口大小变化，调整图表大小
    window.addEventListener('resize', handleResize)
  } catch (error) {
    console.error('初始化图表失败:', error)
  }
}

// 初始化隐患环形图
const initRingChart = async () => {
  await nextTick()
  
  // 添加额外的延迟确保DOM完全渲染
  await new Promise(resolve => setTimeout(resolve, 200))
  
  if (!ringChartRef.value) {
    console.error('环形图DOM引用不存在')
    return
  }

  try {
    // 确保容器有尺寸
    if (ringChartRef.value.offsetWidth === 0 || ringChartRef.value.offsetHeight === 0) {
      console.error('环形图容器尺寸为0，无法初始化')
      // 强制设置最小尺寸，确保图表能够渲染
      ringChartRef.value.style.minHeight = '180px'
      ringChartRef.value.style.width = '100%'
    }
    
    // 如果已经有实例，先销毁
    if (ringChartInstance) {
      ringChartInstance.dispose()
    }
    
    // 创建图表实例
    ringChartInstance = echarts.init(ringChartRef.value)

    // 设置图表配置项
    updateRingChart()

    // 监听窗口大小变化，调整图表大小
    window.addEventListener('resize', handleRingResize)
  } catch (error) {
    console.error('初始化环形图失败:', error)
  }
}

// 处理窗口大小变化
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

// 处理环形图窗口大小变化
const handleRingResize = () => {
  if (ringChartInstance) {
    ringChartInstance.resize()
  }
}

// 更新风险图表数据
const updateChart = () => {
  if (!chartInstance) {
    console.error('图表实例不存在，无法更新')
    return
  }

  try {
    // 构建图表配置
    const option = {
      backgroundColor: 'transparent',
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      grid: {
        top: '16%',
        left: '8%',
        right: '4%',
        bottom: '10%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: chartData.xAxisData,
        axisLine: {
          lineStyle: {
            color: '#5F5F60'
          }
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: '#FFFFFF',
          fontFamily: 'PingFangSC, PingFang SC',
          fontWeight: 400,
          fontSize: 14,
          interval: 0
        }
      },
      yAxis: {
        type: 'value',
        name: '单位（处）',
        nameTextStyle: {
          color: '#FFFFFF',
          fontFamily: 'PingFangSC, PingFang SC',
          fontWeight: 400,
          fontSize: 14,
          padding: [0, 30, 0, 0]
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.1)',
            type: 'dashed'
          }
        },
        axisLabel: {
          color: '#FFFFFF',
          fontFamily: 'PingFangSC, PingFang SC',
          fontWeight: 400,
          fontSize: 14
        }
      },
      series: chartData.series.map((item) => {
        return {
          name: item.name,
          type: 'bar',
          barWidth: '10px',
          barGap: '30%',
          emphasis: {
            focus: 'series'
          },
          data: item.data,
          itemStyle: {
            color: item.color
          }
        }
      })
    }

    // 设置图表配置
    chartInstance.setOption(option)
    chartInstance.resize() // 强制重新计算大小
  } catch (error) {
    console.error('更新图表失败:', error)
  }
}

// 更新隐患环形图数据
const updateRingChart = () => {
  if (!ringChartInstance) {
    console.error('环形图实例不存在，无法更新')
    return
  }

  try {
    const seriesData = ringChartData.series.map(item => ({
      value: item.value,
      name: item.name,
      itemStyle: {
        color: item.color
      }
    }))

    // 构建环形图配置
    const option = {
      backgroundColor: 'transparent',
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        show: false
      },
      series: [
        {
          name: '隐患类型',
          type: 'pie',
          radius: ['50%', '75%'],
          avoidLabelOverlap: false,
          label: {
            show: true,
            position: 'outside',
            formatter: '{b}: {d}%',
            color: '#FFFFFF',
            fontSize: 12
          },
          labelLine: {
            show: true,
            length: 10,
            length2: 15,
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.3)'
            }
          },
          itemStyle: {
            borderColor: '#0A1830',
            borderWidth: 2
          },
          data: seriesData
        }
      ]
    }

    // 设置环形图配置
    ringChartInstance.setOption(option)
    ringChartInstance.resize() // 强制重新计算大小
  } catch (error) {
    console.error('更新环形图失败:', error)
  }
}

// 监听activeTab变化
watch(activeTab, (newVal) => {
  // 增加延迟，确保DOM已完全渲染
  setTimeout(() => {
    if (newVal === 'risk') {
      initChart()
    } else if (newVal === 'hidden') {
      initRingChart()
    }
  }, 300)
})

// 组件挂载时初始化
onMounted(() => {
  // 使用setTimeout延迟初始化，确保DOM已完全渲染
  setTimeout(() => {
    if (activeTab.value === 'risk') {
      initChart()
    }
  }, 500)
})

// 组件卸载时清理资源
onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
  if (ringChartInstance) {
    ringChartInstance.dispose()
  }
  window.removeEventListener('resize', handleResize)
  window.removeEventListener('resize', handleRingResize)
})
</script>

<style scoped>
.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
}

.tab-buttons {
  display: flex;
  align-items: center;
  margin-right: 30px;
}

.divider {
  width: 1px;
  height: 16px;
  background-color: rgba(255, 255, 255, 0.8);
  margin: 0 10px;
}

.tab-btn {
  cursor: pointer;
  font-family: PingFangSC, 'PingFang SC';
  font-weight: 500;
  font-size: 16px;
  color: rgba(255, 255, 255, 0.3);
  line-height: 22px;
}

.tab-btn.active {
  color: rgba(255, 255, 255, 0.8);
}

/* 风险面板样式 */
.risk-panel {
  display: flex;
  flex-direction: column;
  flex: 1;
  width: 100%;
  min-height: 200px;
}

.chart-legend {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  justify-content: center;
  margin-bottom: 10px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.legend-color {
  width: 12px;
  height: 4px;
}

.legend-text {
  font-family: 'PingFangSC, PingFang SC';
  font-weight: 400;
  font-size: 14px;
  color: #FFFFFF;
}

.chart-container {
  flex: 1;
  min-height: 180px;
  width: 100%;
  position: relative;
}

/* 隐患面板样式 */
.hidden-panel {
  display: flex;
  flex-direction: column;
  flex: 1;
  gap: 3px;
  width: 100%;
  min-height: 200px;
}

.ring-chart-container {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 180px;
  width: 100%;
}

.ring-chart {
  width: 100%;
  height: 100%;
  min-height: 180px;
}

.center-num {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-family: 'D-DIN', sans-serif;
  font-weight: bold;
  font-size: 32px;
  color: #FFFFFF;
}

.stats-container {
  display: flex;
  justify-content: space-around;
  padding: 0 10px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.stat-icon {
  width: 30px;
  height: 30px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.total-icon {
  background: rgba(31, 159, 255, 0.3);
  border-radius: 50%;
  position: relative;
}

.total-icon::after {
  content: "";
  position: absolute;
  width: 20px;
  height: 20px;
  top: 5px;
  left: 5px;
  background: #1F9FFF;
  border-radius: 50%;
}

.completed-icon {
  background: rgba(102, 218, 67, 0.3);
  border-radius: 50%;
  position: relative;
}

.completed-icon::after {
  content: "";
  position: absolute;
  width: 20px;
  height: 20px;
  top: 5px;
  left: 5px;
  background: #66DA43;
  border-radius: 50%;
}

.rate-icon {
  background: rgba(230, 162, 60, 0.3);
  border-radius: 50%;
  position: relative;
}

.rate-icon::after {
  content: "";
  position: absolute;
  width: 20px;
  height: 20px;
  top: 5px;
  left: 5px;
  background: #E6A23C;
  border-radius: 50%;
}

.stat-info {
  display: flex;
  flex-direction: column;
}

.stat-value {
  font-family: 'D-DIN', sans-serif;
  font-weight: bold;
  font-size: 24px;
  color: #FFFFFF;
  line-height: 1.2;
}

.stat-label {
  font-family: 'PingFangSC, PingFang SC';
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
  opacity: 0.8;
}

/* 媒体查询适配不同分辨率 */
@media screen and (min-width: 1920px) and (max-width: 2560px) {
  .chart-container {
    min-height: 180px;
  }
  
  .ring-chart-container {
    height: 180px;
  }
}

@media screen and (max-width: 1919px) {
  .chart-container {
    min-height: 170px;
  }
  
  .ring-chart-container {
    height: 170px;
  }
  
  .center-num {
    font-size: 28px;
  }
  
  .stat-value {
    font-size: 22px;
  }
}

/* 添加910-1050px高度的适配 */
@media screen and (min-height: 910px) and (max-height: 1050px) {
  .panel-content {
    padding: 10px;
  }
  
  .chart-legend {
    gap: 10px;
    margin-bottom: 5px;
  }
  
  .legend-text {
    font-size: 12px;
  }
  
  .chart-container {
    min-height: 150px;
  }
  
  .ring-chart-container {
    height: 150px;
  }
  
  .center-num {
    font-size: 26px;
  }
  
  .stats-container {
    padding: 0 5px;
  }
  .hidden-panel{
    gap: 20px;
  }
  
  .stat-item {
    gap: 6px;
  }
  
  .stat-icon {
    width: 25px;
    height: 25px;
  }
  
  .stat-icon::after {
    width: 15px;
    height: 15px;
    top: 5px;
    left: 5px;
  }
  
  .stat-value {
    font-size: 20px;
  }
  
  .stat-label {
    font-size: 11px;
  }
}

/* 910px高度的屏幕特别优化 */
@media screen and (min-height: 900px) and (max-height: 939px) {
  .panel-content {
    padding: 8px;
  }
  
  .chart-legend {
    gap: 8px;
    margin-bottom: 5px;
  }
  
  .legend-text {
    font-size: 12px;
  }
  
  .chart-container {
    min-height: 140px;
  }
  
  .ring-chart-container {
    height: 140px;
  }
  
  .center-num {
    font-size: 24px;
  }
  
  .stats-container {
    padding: 0;
  }
  
  .stat-item {
    gap: 5px;
  }
  
  .stat-icon {
    width: 22px;
    height: 22px;
  }
  
  .stat-icon::after {
    width: 14px;
    height: 14px;
    top: 4px;
    left: 4px;
  }
  
  .stat-value {
    font-size: 18px;
  }
  
  .stat-label {
    font-size: 10px;
  }
}
</style> 