import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs' // 导入中文语言包
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import 'element-plus/dist/index.css'
import './assets/font/font.css'
import './styles/index.scss'
import App from './App.vue'
import router from './router'
import svgLoader from './plugins/svgLoader'
import store from './stores'

// 引入rem适配相关文件
import './assets/css/rem.css'
import './utils/flexible.js'
import './styles/element-plus-rem.scss'

// 引入全局屏幕适配样式
import './assets/css/screen.css'

// 导入全局样式文件
import '@/assets/css/base.css'

const app = createApp(App)

// 注册所有图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

app.use(createPinia())
app.use(router)
app.use(store)
app.use(ElementPlus, {
  locale: zhCn, // 设置为中文
})
app.use(svgLoader)

app.mount('#app')
