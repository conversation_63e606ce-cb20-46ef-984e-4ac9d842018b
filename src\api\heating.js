import request from '@/utils/request'
import moment from 'moment'

// 获取风险监测设备统计数据
export function getRiskMonitorStatistics() {
  return request({
    url: '/heat/api/v1/situation/monitor/device/statistics',
    method: 'get'
  })
}

// 获取高发报警设备列表
export function getHighFrequencyDevices(params) {
  return request({
    url: '/heat/api/v1/monitor/analysis/device/high-frequency',
    method: 'post',
    params
  })
}

// 获取供热企业分页数据
export function getEnterpriseList(pageNum, pageSize, params) {
  return request({
    url: `/heat/usmBasicEnterprise/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

// 获取供热企业详情
export function getEnterpriseDetail(id) {
  return request({
    url: `/heat/usmBasicEnterprise/${id}`,
    method: 'get'
  })
}

// 删除供热企业
export function deleteEnterprise(id) {
  return request({
    url: `/heat/usmBasicEnterprise/${id}`,
    method: 'delete'
  })
}

// 获取所有企业列表
export function getAllEnterpriseList(params = {}) {
  return request({
    url: '/heat/usmBasicEnterprise/list',
    method: 'post',
    data: params
  })
}

// 新增企业信息
export function saveEnterprise(data) {
  return request({
    url: '/heat/usmBasicEnterprise/save',
    method: 'post',
    data
  })
}

// 更新企业信息
export function updateEnterprise(data) {
  return request({
    url: '/heat/usmBasicEnterprise/update',
    method: 'post',
    data
  })
}


// 获取热源工厂列表
export function getHeatFactoryList(params = {}) {
  return request({
    url: '/heat/usmBasicHeatFactory/list',
    method: 'post',
    data: params
  })
}

// 获取热源工厂分页列表
export function getHeatFactoryPage(pageNum, pageSize, params = {}) {
  return request({
    url: `/heat/usmBasicHeatFactory/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

// 获取热源信息详情
export function getHeatFactoryDetail(id) {
  return request({
    url: `/heat/usmBasicHeatFactory/${id}`,
    method: 'get'
  })
}

// 删除热源信息
export function deleteHeatFactory(id) {
  return request({
    url: `/heat/usmBasicHeatFactory/${id}`,
    method: 'delete'
  })
}

// 新增热源信息
export function saveHeatFactory(data) {
  return request({
    url: '/heat/usmBasicHeatFactory/save',
    method: 'post',
    data
  })
}

// 更新热源信息
export function updateHeatFactory(data) {
  return request({
    url: '/heat/usmBasicHeatFactory/update',
    method: 'post',
    data
  })
}

// 获取换热站分页列表
export function getHeatStationPage(pageNum, pageSize, params = {}) {
  return request({
    url: `/heat/usmBasicHeatStation/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

// 获取换热站信息详情
export function getHeatStationDetail(id) {
  return request({
    url: `/heat/usmBasicHeatStation/${id}`,
    method: 'get'
  })
}
// 获取所有换热站列表
export function getAllHeatStationList(params = {}) {
  return request({
    url: '/heat/usmBasicHeatStation/list',
    method: 'post',
    data: params
  })
}
// 删除换热站信息
export function deleteHeatStation(id) {
  return request({
    url: `/heat/usmBasicHeatStation/${id}`,
    method: 'delete'
  })
}

// 新增换热站信息
export function saveHeatStation(data) {
  return request({
    url: '/heat/usmBasicHeatStation/save',
    method: 'post',
    data
  })
}

// 更新换热站信息
export function updateHeatStation(data) {
  return request({
    url: '/heat/usmBasicHeatStation/update',
    method: 'post',
    data
  })
}

// 获取机组分页列表
export function getUnitPage(pageNum, pageSize, params = {}) {
  return request({
    url: `/heat/usmBasicUnit/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

// 获取机组信息详情
export function getUnitDetail(id) {
  return request({
    url: `/heat/usmBasicUnit/${id}`,
    method: 'get'
  })
}

// 删除机组信息
export function deleteUnit(id) {
  return request({
    url: `/heat/usmBasicUnit/${id}`,
    method: 'delete'
  })
}

// 新增机组信息
export function saveUnit(data) {
  return request({
    url: '/heat/usmBasicUnit/save',
    method: 'post',
    data
  })
}

// 更新机组信息
export function updateUnit(data) {
  return request({
    url: '/heat/usmBasicUnit/update',
    method: 'post',
    data
  })
}

// 获取管线信息分页列表
export function getPipelineInfoPage(pageNum, pageSize, params = {}) {
  return request({
    url: `/heat/usmBasicPipeline/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

// 获取管线信息详情
export function getPipelineInfoDetail(id) {
  return request({
    url: `/heat/usmBasicPipeline/${id}`,
    method: 'get'
  })
}

// 新增管线信息
export function savePipelineInfo(data) {
  return request({
    url: '/heat/usmBasicPipeline/save',
    method: 'post',
    data
  })
}

// 更新管线信息
export function updatePipelineInfo(data) {
  return request({
    url: '/heat/usmBasicPipeline/update',
    method: 'post',
    data
  })
}

// 删除管线信息
export function deletePipelineInfo(id) {
  return request({
    url: `/heat/usmBasicPipeline/${id}`,
    method: 'delete'
  })
}

// 获取管点信息分页列表
export function getPipelineNodePage(pageNum, pageSize, params = {}) {
  return request({
    url: `/heat/usmBasicPoint/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

// 获取管点信息详情
export function getPipelineNodeDetail(id) {
  return request({
    url: `/heat/usmBasicPoint/${id}`,
    method: 'get'
  })
}

// 新增管点信息
export function savePipelineNode(data) {
  return request({
    url: '/heat/usmBasicPoint/save',
    method: 'post',
    data
  })
}

// 更新管点信息
export function updatePipelineNode(data) {
  return request({
    url: '/heat/usmBasicPoint/update',
    method: 'post',
    data
  })
}

// 删除管点信息
export function deletePipelineNode(id) {
  return request({
    url: `/heat/usmBasicPoint/${id}`,
    method: 'delete'
  })
}

// 获取管线维修记录分页列表
export function getPipelineMaintenancePage(pageNum, pageSize, params = {}) {
  return request({
    url: `/heat/usmBasicRepair/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

// 获取管线维修记录详情
export function getPipelineMaintenanceDetail(id) {
  return request({
    url: `/heat/usmBasicRepair/${id}`,
    method: 'get'
  })
}

// 新增管线维修记录
export function savePipelineMaintenance(data) {
  return request({
    url: '/heat/usmBasicRepair/save',
    method: 'post',
    data
  })
}

// 更新管线维修记录
export function updatePipelineMaintenance(data) {
  return request({
    url: '/heat/usmBasicRepair/update',
    method: 'post',
    data
  })
}

// 删除管线维修记录
export function deletePipelineMaintenance(id) {
  return request({
    url: `/heat/usmBasicRepair/${id}`,
    method: 'delete'
  })
}

// 获取监测预警阈值分页列表
export function getAlarmThresholdPage(pageNum, pageSize, params = {}) {
  return request({
    url: `/heat/usmAlarmThreshold/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

// 获取监测预警阈值详情
export function getAlarmThresholdDetail(id) {
  return request({
    url: `/heat/usmAlarmThreshold/${id}`,
    method: 'get'
  })
}
// 获取报警类型
export function getAlarmType() {
  return request({
    url: `/heat/usmMonitorAlarm/getAlarmType`,
    method: 'get'
  })
}

// 新增监测预警阈值
export function saveAlarmThreshold(data) {
  return request({
    url: '/heat/usmAlarmThreshold/save',
    method: 'post',
    data
  })
}

// 更新监测预警阈值
export function updateAlarmThreshold(data) {
  return request({
    url: '/heat/usmAlarmThreshold/update',
    method: 'post',
    data
  })
}

// 删除监测预警阈值
export function deleteAlarmThreshold(id) {
  return request({
    url: `/heat/usmAlarmThreshold/${id}`,
    method: 'delete'
  })
}

// 获取监测设备列表
export function getMonitorDeviceList(params = {}) {
  return request({
    url: '/heat/usmMonitorDevice/list',
    method: 'post',
    data: params
  })
}

// 获取传感器设备分页列表
export function getSensorDevicePage(pageNum, pageSize, params = {}) {
  return request({
    url: `/heat/usmMonitorDevice/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

// 获取传感器设备详情
export function getSensorDeviceDetail(id) {
  return request({
    url: `/heat/usmMonitorDevice/${id}`,
    method: 'get'
  })
}

// 新增传感器设备
export function saveSensorDevice(data) {
  return request({
    url: '/heat/usmMonitorDevice/save',
    method: 'post',
    data
  })
}

// 更新传感器设备
export function updateSensorDevice(data) {
  return request({
    url: '/heat/usmMonitorDevice/update',
    method: 'post',
    data
  })
}

// 删除传感器设备
export function deleteSensorDevice(id) {
  return request({
    url: `/heat/usmMonitorDevice/${id}`,
    method: 'delete'
  })
}

// 获取管线列表
export function getPipelineList(params = {}) {
  return request({
    url: '/heat/usmBasicPipeline/list',
    method: 'post',
    data: params
  })
}

// 获取用户列表
export function getUserList(params = {}) {
  return request({
    url: '/heat/usmBasicUser/list',
    method: 'post',
    data: params
  })
}

// 获取窨井列表
export function getWellList(params = {}) {
  return request({
    url: '/heat/usmBasicWell/list',
    method: 'post',
    data: params
  })
}

// 获取监测指标列表
export function getMonitorIndicatorsList(params = {}) {
  return request({
    url: '/heat/usmMonitorIndicators/list',
    method: 'post',
    data: params
  })
}

// ============ 管网运行监测预警管理相关接口 ============

// 获取监测报警统计数据
export function getHeatingAlarmStatistics(params) {
  return request({
    url: '/heat/usmAlarmSituationAnalysis/statistics',
    method: 'post',
    data: {
      startDate: params.startDate || '',
      endDate: params.endDate || ''
    }
  })
}

// 获取报警等级统计数据
export function getHeatingAlarmLevelStatistics(params) {
  return request({
    url: '/heat/usmAlarmSituationAnalysis/level/statistics',
    method: 'post',
    data: {
      startDate: params.startDate || '',
      endDate: params.endDate || ''
    }
  })
}

// 获取报警信息列表（分页查询）
export function getHeatingAlarmList(page, size, params = {}) {
  return request({
    url: `/heat/usmMonitorAlarm/search/${page}/${size}`,
    method: 'post',
    data: params
  })
}

// 获取报警详情
export function getHeatingAlarmDetail(id) {
  return request({
    url: `/heat/usmMonitorAlarm/${id}`,
    method: 'get'
  })
}

// 报警确认
export function confirmHeatingAlarm(data) {
  return request({
    url: '/heat/usmMonitorAlarm/alarm/confirm',
    method: 'post',
    data
  })
}

// 报警处置新增/编辑
export function handleHeatingAlarm(data) {
  return request({
    url: '/heat/usmMonitorAlarm/alarm/handle',
    method: 'post',
    data
  })
}

// 删除报警处置
export function deleteHeatingAlarmHandle(id) {
  return request({
    url: `/heat/usmMonitorAlarm/alarm/handle/${id}`,
    method: 'delete'
  })
}

// 获取报警处置列表
export function getHeatingAlarmHandleList(id) {
  return request({
    url: `/heat/usmMonitorAlarm/alarm/handleList/${id}`,
    method: 'get'
  })
}

// 获取报警状态记录时间线
export function getHeatingAlarmStatusList(params) {
  return request({
    url: '/heat/usmMonitorAlarmStatus/list',
    method: 'post',
    data: params
  })
}

// 获取报警监测曲线数据
export function getHeatingAlarmMonitorCurve(params) {
  return request({
    url: '/heat/usmMonitorRecord/monitorCurve',
    method: 'post',
    data: params
  })
}

// 获取报警记录
export function getHeatingAlarmRecords(deviceId) {
  return request({
    url: `/heat/usmMonitorAlarm/alarmRecord/${deviceId}`,
    method: 'get'
  })
}

// ============ 供热区域建筑信息管理相关接口 ============

// 获取建筑信息分页列表
export function getBuildingPage(pageNum, pageSize, params = {}) {
  return request({
    url: `/heat/usmBasicBuilding/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

// 获取建筑信息详情
export function getBuildingDetail(id) {
  return request({
    url: `/heat/usmBasicBuilding/${id}`,
    method: 'get'
  })
}

// 新增建筑信息
export function saveBuilding(data) {
  return request({
    url: '/heat/usmBasicBuilding/save',
    method: 'post',
    data
  })
}

// 更新建筑信息
export function updateBuilding(data) {
  return request({
    url: '/heat/usmBasicBuilding/update',
    method: 'post',
    data
  })
}

// 删除建筑信息
export function deleteBuilding(id) {
  return request({
    url: `/heat/usmBasicBuilding/${id}`,
    method: 'delete'
  })
}

// 获取所有机组列表
export function getAllUnitList(params = {}) {
  return request({
    url: '/heat/usmBasicUnit/list',
    method: 'post',
    data: params
  })
}

// ============ 供热用户信息管理相关接口 ============

// 获取用户信息分页列表
export function getUserPage(pageNum, pageSize, params = {}) {
  return request({
    url: `/heat/usmBasicUser/search/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

// 获取用户信息详情
export function getUserDetail(id) {
  return request({
    url: `/heat/usmBasicUser/${id}`,
    method: 'get'
  })
}

// 新增用户信息
export function saveUser(data) {
  return request({
    url: '/heat/usmBasicUser/save',
    method: 'post',
    data
  })
}

// 更新用户信息
export function updateUser(data) {
  return request({
    url: '/heat/usmBasicUser/update',
    method: 'post',
    data
  })
}

// 删除用户信息
export function deleteUser(id) {
  return request({
    url: `/heat/usmBasicUser/${id}`,
    method: 'delete'
  })
}

// 获取所有建筑列表
export function getAllBuildingList(params = {}) {
  return request({
    url: '/heat/usmBasicBuilding/list',
    method: 'post',
    data: params
  })
}

// ============ 供热窨井信息管理相关接口 ============

// 获取窨井信息分页列表
export function getManholeWellPage(pageNum, pageSize, params = {}) {
  return request({
    url: `/heat/usmBasicWell/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

// 获取窨井信息详情
export function getManholeWellDetail(id) {
  return request({
    url: `/heat/usmBasicWell/${id}`,
    method: 'get'
  })
}

// 新增窨井信息
export function saveManholeWell(data) {
  return request({
    url: '/heat/usmBasicWell/save',
    method: 'post',
    data
  })
}

// 更新窨井信息
export function updateManholeWell(data) {
  return request({
    url: '/heat/usmBasicWell/update',
    method: 'post',
    data
  })
}

// 删除窨井信息
export function deleteManholeWell(id) {
  return request({
    url: `/heat/usmBasicWell/${id}`,
    method: 'delete'
  })
}