import request from '@/utils/request'

/**
 * 燃气专项监测设备撒点数据
 */
export function postUsmMonitorDeviceList(data={}) {
    return request({
        url: "/gas/api/v1/mapScatterPoints/devicePoints",
        method: "post",
        data,
    });
}

/**
 * 燃气监测设备详情
 */
export function getUsmMonitorDeviceInfo(id) {
    return request({
        url: `/gas/usmMonitorDevice/${id}`,
        method: "get",
    });
}

/**
 * 燃气监测设备指标枚举
 */
export function getGasUsmMonitorIndicatorsInfo(deviceId) {
    return request({
        url: `/gas/usmMonitorRecord/monitorIndicators/${deviceId}`,
        method: "get",
    });
}

/**
 * 燃气监测设备曲线数据
 */
export function getGasUsmMonitorRecordMonitorCurve(data={}) {
    return request({
        url: "/gas/usmMonitorRecord/monitorCurve",
        method: "post",
        data,
    });
}

/**
 * 燃气监测设备报警信息详情
 */
export function getGasUsmMonitorAlarmInfo(id) {
    return request({
        url: `/gas/usmMonitorAlarm/${id}`,
        method: "get",
    });
}

// 获取燃气监测设备报警状态记录列表
export function postGasAlarmStatusList(data) {
    return request({
        url: '/gas/usmMonitorAlarmStatus/list',
        method: 'post',
        data
    });
}

/**
 * 燃气管点撒点数据
 */
export function postUsmZyGasPointList(data={}) {
    return request({
        url: "/gas/usmZyGasPoint/list",
        method: "post",
        data,
    });
}

/**
 * 燃气管点详情
 */
export function getUsmZyGasPointInfo(id) {
    return request({
        url: `/gas/usmZyGasPoint/${id}`,
        method: "get",
    });
}

/**
 * 燃气场站撒点数据
 */
export function postUsmZyGasStationList(data={}) {
    return request({
        url: "/gas/usmZyGasStation/list",
        method: "post",
        data,
    });
}

/**
 * 燃气场站详情
 */
export function getUsmZyGasStationInfo(id) {
    return request({
        url: `/gas/usmZyGasStation/${id}`,
        method: "get",
    });
}

/**
 * 燃气窨井撒点数据
 */
export function postUsmZyGasWellList(data={}) {
    return request({
        url: "/gas/usmZyGasWell/list",
        method: "post",
        data,
    });
}

/**
 * 燃气窨井详情
 */
export function getUsmZyGasWellInfo(id) {
    return request({
        url: `/gas/usmZyGasWell/${id}`,
        method: "get",
    });
}

/**
 * 燃气危险源撒点数据
 */
export function postUsmZyGasDangerList(data={}) {
    return request({
        url: "/gas/usmZyGasDanger/list",
        method: "post",
        data,
    });
}

/**
 * 燃气危险源详情
 */
export function getUsmZyGasDangerInfo(id) {
    return request({
        url: `/gas/usmZyGasDanger/${id}`,
        method: "get",
    });
}

/**
 * 燃气防护目标撒点数据
 */
export function postUsmZyGasProtectList(data={}) {
    return request({
        url: "/gas/usmZyGasProtect/list",
        method: "post",
        data,
    });
}

/**
 * 燃气防护目标详情
 */
export function getUsmZyGasProtectInfo(id) {
    return request({
        url: `/gas/usmZyGasProtect/${id}`,
        method: "get",
    });
}

/**
 * 排水专项监测设备撒点数据
 */
export function postDrainUsmMonitorDeviceList(data={}) {
    return request({
        url: "/drain/api/v1/mapScatterPoints/devicePoints", //  接口已更换/gas/usmMonitorDevice/list
        method: "post",
        data,
    });
}

/**
 * 排水监测设备详情
 */
export function getDrainUsmMonitorDeviceInfo(id) {
    return request({
        url: `/drain/usmMonitorDevice/${id}`,
        method: "get",
    });
}

/**
 * 排水监测设备指标枚举
 */
export function getDrainUsmMonitorIndicatorsInfo(deviceId) {
    return request({
        url: `/drain/usmMonitorRecord/monitorIndicators/${deviceId}`,
        method: "get",
    });
}

/**
 * 排水监测设备曲线数据
 */
export function getDrainUsmMonitorRecordMonitorCurve(data={}) {
    return request({
        url: "/drain/usmMonitorRecord/monitorCurve",
        method: "post",
        data,
    });
}

/**
 * 排水监测设备报警信息详情
 */
export function getDrainUsmMonitorAlarmInfo(id) {
    return request({
        url: `/drain/usmMonitorAlarm/${id}`,
        method: "get",
    });
}

// 获取排水监测设备报警状态记录列表
export function postDrainAlarmStatusList(data) {
    return request({
        url: '/drain/usmMonitorAlarmStatus/list',
        method: 'post',
        data
    });
}

/**
 * 排水泵站撒点数据
 */
export function postUsmBasicPumpStationList(data={}) {
    return request({
        url: "/drain/usmBasicPumpStation/list",
        method: "post",
        data,
    });
}

/**
 * 排水泵站详情
 */
export function getUsmBasicPumpStationInfo(id) {
    return request({
        url: `/drain/usmBasicPumpStation/${id}`,
        method: "get",
    });
}

/**
 * 排水污水厂撒点数据
 */
export function postUsmBasicSewageFactoryList(data={}) {
    return request({
        url: "/drain/usmBasicSewageFactory/list",
        method: "post",
        data,
    });
}

/**
 * 排水污水厂详情
 */
export function getUsmBasicSewageFactoryInfo(id) {
    return request({
        url: `/drain/usmBasicSewageFactory/${id}`,
        method: "get",
    });
}

/**
 * 排水排水口撒点数据
 */
export function postUsmBasicDrainOutletList(data={}) {
    return request({
        url: "/drain/usmBasicDrainOutlet/list",
        method: "post",
        data,
    });
}

/**
 * 排水排水口详情
 */
export function getUsmBasicDrainOutletInfo(id) {
    return request({
        url: `/drain/usmBasicDrainOutlet/${id}`,
        method: "get",
    });
}

/**
 * 排水管点撒点数据
 */
export function postDrainUsmBasicPointList(data={}) {
    return request({
        url: "/drain/usmBasicPoint/list",
        method: "post",
        data,
    });
}

/**
 * 排水管点详情
 */
export function getDrainUsmBasicPointInfo(id) {
    return request({
        url: `/drain/usmBasicPoint/${id}`,
        method: "get",
    });
}

/**
 * 排水窨井撒点数据
 */
export function postDrainUsmBasicWellList(data={}) {
    return request({
        url: "/drain/usmBasicWell/list",
        method: "post",
        data,
    });
}

/**
 * 排水窨井详情
 */
export function getDrainUsmBasicWellInfo(id) {
    return request({
        url: `/drain/usmBasicWell/${id}`,
        method: "get",
    });
}

/**
 * 排水易涝点撒点数据
 */
export function postDrainUsmBasicFloodPointList(data={}) {
    return request({
        url: "/drain/usmBasicFloodPoint/list",
        method: "post",
        data,
    });
}

/**
 * 排水易涝点详情
 */
export function getDrainUsmBasicFloodPointInfo(id) {
    return request({
        url: `/drain/usmBasicFloodPoint/${id}`,
        method: "get",
    });
}

/**
 * 排水危险源撒点数据
 */
export function postDrainUsmRiskDangerList(data={}) {
    return request({
        url: "/drain/usmRiskDanger/list",
        method: "post",
        data,
    });
}

/**
 * 排水危险源详情
 */
export function getDrainUsmRiskDangerInfo(id) {
    return request({
        url: `/drain/usmRiskDanger/${id}`,
        method: "get",
    });
}

/**
 * 排水防护目标撒点数据
 */
export function postDrainUsmRiskProtectList(data={}) {
    return request({
        url: "/drain/usmRiskProtect/list",
        method: "post",
        data,
    });
}

/**
 * 排水防护目标详情
 */
export function getDrainUsmRiskProtectInfo(id) {
    return request({
        url: `/drain/usmRiskProtect/${id}`,
        method: "get",
    });
}

/**
 * 热力专项监测设备撒点数据
 */
export function postHeatUsmMonitorDeviceList(data={}) {
    return request({
        url: "/heat/api/v1/mapScatterPoints/devicePoints",
        method: "post",
        data,
    });
}

/**
 * 热力监测设备详情
 */
export function getHeatUsmMonitorDeviceInfo(id) {
    return request({
        url: `/heat/usmMonitorDevice/${id}`,
        method: "get",
    });
}

/**
 * 热力监测设备指标枚举
 */
export function getHeatUsmMonitorIndicatorsInfo(deviceId) {
    return request({
        url: `/heat/usmMonitorRecord/monitorIndicators/${deviceId}`,
        method: "get",
    });
}

/**
 * 热力监测设备曲线数据
 */
export function getHeatUsmMonitorRecordMonitorCurve(data={}) {
    return request({
        url: "/heat/usmMonitorRecord/monitorCurve",
        method: "post",
        data,
    });
}

/**
 * 供热监测设备报警信息详情
 */
export function getHeatUsmMonitorAlarmInfo(id) {
    return request({
        url: `/heat/usmMonitorAlarm/${id}`,
        method: "get",
    });
}

// 获取供热监测设备报警状态记录列表
export function postHeatAlarmStatusList(data) {
    return request({
        url: '/heat/usmMonitorAlarmStatus/list',
        method: 'post',
        data
    });
}

/**
 * 供热管点撒点数据
 */
export function postHeatUsmBasicPointList(data={}) {
    return request({
        url: "/heat/usmBasicPoint/list",
        method: "post",
        data,
    });
}

/**
 * 供热管点详情
 */
export function getHeatUsmBasicPointInfo(id) {
    return request({
        url: `/heat/usmBasicPoint/${id}`,
        method: "get",
    });
}
/**
 * 供热窨井撒点数据
 */
export function postHeatUsmBasicWellList(data={}) {
    return request({
        url: "/heat/usmBasicWell/list",
        method: "post",
        data,
    });
}

/**
 * 供热窨井详情
 */
export function getHeatUsmBasicWellInfo(id) {
    return request({
        url: `/heat/usmBasicWell/${id}`,
        method: "get",
    });
}

/**
 * 供热企业撒点数据
 */
export function postHeatUsmBasicEnterpriseList(data={}) {
    return request({
        url: "/heat/usmBasicEnterprise/list",
        method: "post",
        data,
    });
}

/**
 * 供热企业详情
 */
export function getHeatUsmBasicEnterpriseInfo(id) {
    return request({
        url: `/heat/usmBasicEnterprise/${id}`,
        method: "get",
    });
}

/**
 * 供热热源厂撒点数据
 */
export function postHeatUsmBasicHeatFactoryList(data={}) {
    return request({
        url: "/heat/usmBasicHeatFactory/list",
        method: "post",
        data,
    });
}

/**
 * 供热热源厂详情
 */
export function getHeatUsmBasicHeatFactoryInfo(id) {
    return request({
        url: `/heat/usmBasicHeatFactory/${id}`,
        method: "get",
    });
}

/**
 * 供热换热站撒点数据
 */
export function postHeatUsmBasicHeatStationList(data={}) {
    return request({
        url: "/heat/usmBasicHeatStation/list",
        method: "post",
        data,
    });
}

/**
 * 供热换热站详情
 */
export function getHeatUsmBasicHeatStationInfo(id) {
    return request({
        url: `/heat/usmBasicHeatStation/${id}`,
        method: "get",
    });
}

/**
 * 供热用户撒点数据
 */
export function postHeatUsmBasicUserList(data={}) {
    return request({
        url: "/heat/usmBasicUser/list",
        method: "post",
        data,
    });
}

/**
 * 供热用户详情
 */
export function getHeatUsmBasicUserInfo(id) {
    return request({
        url: `/heat/usmBasicUser/${id}`,
        method: "get",
    });
}

/**
 * 供热危险源撒点数据
 */
export function postHeatUsmRiskDangerList(data={}) {
    return request({
        url: "/heat/usmRiskDanger/list",
        method: "post",
        data,
    });
}

/**
 * 供热危险源详情
 */
export function getHeatUsmRiskDangerInfo(id) {
    return request({
        url: `/heat/usmRiskDanger/${id}`,
        method: "get",
    });
}
/**
 * 供热防护目标撒点数据
 */
export function postHeatUsmRiskProtectList(data={}) {
    return request({
        url: "/heat/usmRiskProtect/list",
        method: "post",
        data,
    });
}

/**
 * 供热防护目标详情
 */
export function getHeatUsmRiskProtectInfo(id) {
    return request({
        url: `/heat/usmRiskProtect/${id}`,
        method: "get",
    });
}

/**
 * 桥梁监测设备撒点数据
 * @param data
 * @returns {*}
 */
export function postBridgeMapDevicePointsList(data={}) {
    return request({
        url: "/bridge/api/v1/mapScatterPoints/devicePoints",
        method: "post",
        data,
    });
}

/**
 * 桥梁监测设备详情
 */
export function getBridgeUsmMonitorDeviceInfo(id) {
    return request({
        url: `/bridge/usmMonitorDevice/${id}`,
        method: "get",
    });
}

/**
 * 桥梁监测设备指标枚举
 */
export function getBridgeUsmMonitorIndicatorsInfo(deviceId) {
    return request({
        url: `/bridge/usmMonitorRecord/monitorIndicators/${deviceId}`,
        method: "get",
    });
}

/**
 * 桥梁监测设备曲线数据
 */
export function getBridgeUsmMonitorRecordMonitorCurve(data={}) {
    return request({
        url: "/bridge/usmMonitorRecord/monitorCurve",
        method: "post",
        data,
    });
}

/**
 * 桥梁监测设备报警信息详情
 */
export function getBridgeUsmMonitorAlarmInfo(id) {
    return request({
        url: `/bridge/usmMonitorAlarm/${id}`,
        method: "get",
    });
}

// 获取桥梁监测设备报警状态记录列表
export function postBridgeAlarmStatusList(data) {
    return request({
        url: '/bridge/usmMonitorAlarmStatus/list',
        method: 'post',
        data
    });
}



