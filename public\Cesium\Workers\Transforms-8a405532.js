/**
 *    ████████                   ██                   ██       ██                  ██      ██
 *   ██░░░░░░██                 ░░                   ░██      ░██                 ░██     ░██
 *  ██      ░░   █████  ███████  ██ ██   ██  ██████  ░██   █  ░██  ██████  ██████ ░██     ░██
 * ░██          ██░░░██░░██░░░██░██░██  ░██ ██░░░░   ░██  ███ ░██ ██░░░░██░░██░░█ ░██  ██████
 * ░██    █████░███████ ░██  ░██░██░██  ░██░░█████   ░██ ██░██░██░██   ░██ ░██ ░  ░██ ██░░░██
 * ░░██  ░░░░██░██░░░░  ░██  ░██░██░██  ░██ ░░░░░██  ░████ ░░████░██   ░██ ░██    ░██░██  ░██
 *  ░░████████ ░░██████ ███  ░██░██░░██████ ██████   ░██░   ░░░██░░██████ ░███    ███░░██████
 *   ░░░░░░░░   ░░░░░░ ░░░   ░░ ░░  ░░░░░░ ░░░░░░    ░░       ░░  ░░░░░░  ░░░    ░░░  ░░░░░░
 *
 * @license
 * GeniusWorld Web SDK - http://zydlxx.cn:1234/
 * Version 3.5.0#develop-c1086f5b@202310131719
 *
 *
 * Copyright © 2020 - 2023 正元地理信息集团股份有限公司. All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
define(["require","exports","./Matrix3-5f307711","./defaultValue-c9f7e7c2","./Math-a28f0fcd","./Matrix2-0f368b7a","./combine-ae0d42f8","./RuntimeError-d2c36d11"],(function(e,t,n,r,o,i,s,a){"use strict";function u(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(n){if("default"!==n){var r=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(t,n,r.get?r:{enumerable:!0,get:function(){return e[n]}})}})),t.default=e,Object.freeze(t)}var c="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function l(e){this._ellipsoid=r.defaultValue(e,n.Ellipsoid.WGS84),this._semimajorAxis=this._ellipsoid.maximumRadius,this._oneOverSemimajorAxis=1/this._semimajorAxis}Object.defineProperties(l.prototype,{ellipsoid:{get:function(){return this._ellipsoid}}}),l.prototype.project=function(e,t){const o=this._semimajorAxis,i=e.longitude*o,s=e.latitude*o,a=e.height;return r.defined(t)?(t.x=i,t.y=s,t.z=a,t):new n.Cartesian3(i,s,a)},l.prototype.unproject=function(e,t){const o=this._oneOverSemimajorAxis,i=e.x*o,s=e.y*o,a=e.z;return r.defined(t)?(t.longitude=i,t.latitude=s,t.height=a,t):new n.Cartographic(i,s,a)};var d=Object.freeze({OUTSIDE:-1,INTERSECTING:0,INSIDE:1});function f(e,t){this.start=r.defaultValue(e,0),this.stop=r.defaultValue(t,0)}function p(e,t){this.center=n.Cartesian3.clone(r.defaultValue(e,n.Cartesian3.ZERO)),this.radius=r.defaultValue(t,0)}const h=new n.Cartesian3,m=new n.Cartesian3,g=new n.Cartesian3,y=new n.Cartesian3,v=new n.Cartesian3,w=new n.Cartesian3,C=new n.Cartesian3,_=new n.Cartesian3,b=new n.Cartesian3,x=new n.Cartesian3,S=new n.Cartesian3,A=new n.Cartesian3,E=4/3*o.CesiumMath.PI;p.fromPoints=function(e,t){if(r.defined(t)||(t=new p),!r.defined(e)||0===e.length)return t.center=n.Cartesian3.clone(n.Cartesian3.ZERO,t.center),t.radius=0,t;const o=n.Cartesian3.clone(e[0],C),i=n.Cartesian3.clone(o,h),s=n.Cartesian3.clone(o,m),a=n.Cartesian3.clone(o,g),u=n.Cartesian3.clone(o,y),c=n.Cartesian3.clone(o,v),l=n.Cartesian3.clone(o,w),d=e.length;let f;for(f=1;f<d;f++){n.Cartesian3.clone(e[f],o);const t=o.x,r=o.y,d=o.z;t<i.x&&n.Cartesian3.clone(o,i),t>u.x&&n.Cartesian3.clone(o,u),r<s.y&&n.Cartesian3.clone(o,s),r>c.y&&n.Cartesian3.clone(o,c),d<a.z&&n.Cartesian3.clone(o,a),d>l.z&&n.Cartesian3.clone(o,l)}const E=n.Cartesian3.magnitudeSquared(n.Cartesian3.subtract(u,i,_)),O=n.Cartesian3.magnitudeSquared(n.Cartesian3.subtract(c,s,_)),P=n.Cartesian3.magnitudeSquared(n.Cartesian3.subtract(l,a,_));let I=i,R=u,T=E;O>T&&(T=O,I=s,R=c),P>T&&(T=P,I=a,R=l);const q=b;q.x=.5*(I.x+R.x),q.y=.5*(I.y+R.y),q.z=.5*(I.z+R.z);let z=n.Cartesian3.magnitudeSquared(n.Cartesian3.subtract(R,q,_)),M=Math.sqrt(z);const D=x;D.x=i.x,D.y=s.y,D.z=a.z;const U=S;U.x=u.x,U.y=c.y,U.z=l.z;const k=n.Cartesian3.midpoint(D,U,A);let F=0;for(f=0;f<d;f++){n.Cartesian3.clone(e[f],o);const t=n.Cartesian3.magnitude(n.Cartesian3.subtract(o,k,_));t>F&&(F=t);const r=n.Cartesian3.magnitudeSquared(n.Cartesian3.subtract(o,q,_));if(r>z){const e=Math.sqrt(r);M=.5*(M+e),z=M*M;const t=e-M;q.x=(M*q.x+t*o.x)/e,q.y=(M*q.y+t*o.y)/e,q.z=(M*q.z+t*o.z)/e}}return M<F?(n.Cartesian3.clone(q,t.center),t.radius=M):(n.Cartesian3.clone(k,t.center),t.radius=F),t};const O=new l,P=new n.Cartesian3,I=new n.Cartesian3,R=new n.Cartographic,T=new n.Cartographic;p.fromRectangle2D=function(e,t,n){return p.fromRectangleWithHeights2D(e,t,0,0,n)},p.fromRectangleWithHeights2D=function(e,t,o,s,a){if(r.defined(a)||(a=new p),!r.defined(e))return a.center=n.Cartesian3.clone(n.Cartesian3.ZERO,a.center),a.radius=0,a;t=r.defaultValue(t,O),i.Rectangle.southwest(e,R),R.height=o,i.Rectangle.northeast(e,T),T.height=s;const u=t.project(R,P),c=t.project(T,I),l=c.x-u.x,d=c.y-u.y,f=c.z-u.z;a.radius=.5*Math.sqrt(l*l+d*d+f*f);const h=a.center;return h.x=u.x+.5*l,h.y=u.y+.5*d,h.z=u.z+.5*f,a};const q=[];p.fromRectangle3D=function(e,t,o,s){if(t=r.defaultValue(t,n.Ellipsoid.WGS84),o=r.defaultValue(o,0),r.defined(s)||(s=new p),!r.defined(e))return s.center=n.Cartesian3.clone(n.Cartesian3.ZERO,s.center),s.radius=0,s;const a=i.Rectangle.subsample(e,t,o,q);return p.fromPoints(a,s)},p.fromVertices=function(e,t,o,i){if(r.defined(i)||(i=new p),!r.defined(e)||0===e.length)return i.center=n.Cartesian3.clone(n.Cartesian3.ZERO,i.center),i.radius=0,i;t=r.defaultValue(t,n.Cartesian3.ZERO),o=r.defaultValue(o,3);const s=C;s.x=e[0]+t.x,s.y=e[1]+t.y,s.z=e[2]+t.z;const a=n.Cartesian3.clone(s,h),u=n.Cartesian3.clone(s,m),c=n.Cartesian3.clone(s,g),l=n.Cartesian3.clone(s,y),d=n.Cartesian3.clone(s,v),f=n.Cartesian3.clone(s,w),E=e.length;let O;for(O=0;O<E;O+=o){const r=e[O]+t.x,o=e[O+1]+t.y,i=e[O+2]+t.z;s.x=r,s.y=o,s.z=i,r<a.x&&n.Cartesian3.clone(s,a),r>l.x&&n.Cartesian3.clone(s,l),o<u.y&&n.Cartesian3.clone(s,u),o>d.y&&n.Cartesian3.clone(s,d),i<c.z&&n.Cartesian3.clone(s,c),i>f.z&&n.Cartesian3.clone(s,f)}const P=n.Cartesian3.magnitudeSquared(n.Cartesian3.subtract(l,a,_)),I=n.Cartesian3.magnitudeSquared(n.Cartesian3.subtract(d,u,_)),R=n.Cartesian3.magnitudeSquared(n.Cartesian3.subtract(f,c,_));let T=a,q=l,z=P;I>z&&(z=I,T=u,q=d),R>z&&(z=R,T=c,q=f);const M=b;M.x=.5*(T.x+q.x),M.y=.5*(T.y+q.y),M.z=.5*(T.z+q.z);let D=n.Cartesian3.magnitudeSquared(n.Cartesian3.subtract(q,M,_)),U=Math.sqrt(D);const k=x;k.x=a.x,k.y=u.y,k.z=c.z;const F=S;F.x=l.x,F.y=d.y,F.z=f.z;const N=n.Cartesian3.midpoint(k,F,A);let j=0;for(O=0;O<E;O+=o){s.x=e[O]+t.x,s.y=e[O+1]+t.y,s.z=e[O+2]+t.z;const r=n.Cartesian3.magnitude(n.Cartesian3.subtract(s,N,_));r>j&&(j=r);const o=n.Cartesian3.magnitudeSquared(n.Cartesian3.subtract(s,M,_));if(o>D){const e=Math.sqrt(o);U=.5*(U+e),D=U*U;const t=e-U;M.x=(U*M.x+t*s.x)/e,M.y=(U*M.y+t*s.y)/e,M.z=(U*M.z+t*s.z)/e}}return U<j?(n.Cartesian3.clone(M,i.center),i.radius=U):(n.Cartesian3.clone(N,i.center),i.radius=j),i},p.fromEncodedCartesianVertices=function(e,t,o){if(r.defined(o)||(o=new p),!r.defined(e)||!r.defined(t)||e.length!==t.length||0===e.length)return o.center=n.Cartesian3.clone(n.Cartesian3.ZERO,o.center),o.radius=0,o;const i=C;i.x=e[0]+t[0],i.y=e[1]+t[1],i.z=e[2]+t[2];const s=n.Cartesian3.clone(i,h),a=n.Cartesian3.clone(i,m),u=n.Cartesian3.clone(i,g),c=n.Cartesian3.clone(i,y),l=n.Cartesian3.clone(i,v),d=n.Cartesian3.clone(i,w),f=e.length;let E;for(E=0;E<f;E+=3){const r=e[E]+t[E],o=e[E+1]+t[E+1],f=e[E+2]+t[E+2];i.x=r,i.y=o,i.z=f,r<s.x&&n.Cartesian3.clone(i,s),r>c.x&&n.Cartesian3.clone(i,c),o<a.y&&n.Cartesian3.clone(i,a),o>l.y&&n.Cartesian3.clone(i,l),f<u.z&&n.Cartesian3.clone(i,u),f>d.z&&n.Cartesian3.clone(i,d)}const O=n.Cartesian3.magnitudeSquared(n.Cartesian3.subtract(c,s,_)),P=n.Cartesian3.magnitudeSquared(n.Cartesian3.subtract(l,a,_)),I=n.Cartesian3.magnitudeSquared(n.Cartesian3.subtract(d,u,_));let R=s,T=c,q=O;P>q&&(q=P,R=a,T=l),I>q&&(q=I,R=u,T=d);const z=b;z.x=.5*(R.x+T.x),z.y=.5*(R.y+T.y),z.z=.5*(R.z+T.z);let M=n.Cartesian3.magnitudeSquared(n.Cartesian3.subtract(T,z,_)),D=Math.sqrt(M);const U=x;U.x=s.x,U.y=a.y,U.z=u.z;const k=S;k.x=c.x,k.y=l.y,k.z=d.z;const F=n.Cartesian3.midpoint(U,k,A);let N=0;for(E=0;E<f;E+=3){i.x=e[E]+t[E],i.y=e[E+1]+t[E+1],i.z=e[E+2]+t[E+2];const r=n.Cartesian3.magnitude(n.Cartesian3.subtract(i,F,_));r>N&&(N=r);const o=n.Cartesian3.magnitudeSquared(n.Cartesian3.subtract(i,z,_));if(o>M){const e=Math.sqrt(o);D=.5*(D+e),M=D*D;const t=e-D;z.x=(D*z.x+t*i.x)/e,z.y=(D*z.y+t*i.y)/e,z.z=(D*z.z+t*i.z)/e}}return D<N?(n.Cartesian3.clone(z,o.center),o.radius=D):(n.Cartesian3.clone(F,o.center),o.radius=N),o},p.fromCornerPoints=function(e,t,o){r.defined(o)||(o=new p);const i=n.Cartesian3.midpoint(e,t,o.center);return o.radius=n.Cartesian3.distance(i,t),o},p.fromEllipsoid=function(e,t){return r.defined(t)||(t=new p),n.Cartesian3.clone(n.Cartesian3.ZERO,t.center),t.radius=e.maximumRadius,t};const z=new n.Cartesian3;p.fromBoundingSpheres=function(e,t){if(r.defined(t)||(t=new p),!r.defined(e)||0===e.length)return t.center=n.Cartesian3.clone(n.Cartesian3.ZERO,t.center),t.radius=0,t;const o=e.length;if(1===o)return p.clone(e[0],t);if(2===o)return p.union(e[0],e[1],t);const i=[];let s;for(s=0;s<o;s++)i.push(e[s].center);const a=(t=p.fromPoints(i,t)).center;let u=t.radius;for(s=0;s<o;s++){const t=e[s];u=Math.max(u,n.Cartesian3.distance(a,t.center,z)+t.radius)}return t.radius=u,t};const M=new n.Cartesian3,D=new n.Cartesian3,U=new n.Cartesian3;p.fromOrientedBoundingBox=function(e,t){r.defined(t)||(t=new p);const o=e.halfAxes,i=n.Matrix3.getColumn(o,0,M),s=n.Matrix3.getColumn(o,1,D),a=n.Matrix3.getColumn(o,2,U);return n.Cartesian3.add(i,s,i),n.Cartesian3.add(i,a,i),t.center=n.Cartesian3.clone(e.center,t.center),t.radius=n.Cartesian3.magnitude(i),t};const k=new n.Cartesian3,F=new n.Cartesian3;p.fromTransformation=function(e,t){r.defined(t)||(t=new p);const o=i.Matrix4.getTranslation(e,k),s=i.Matrix4.getScale(e,F),a=.5*n.Cartesian3.magnitude(s);return t.center=n.Cartesian3.clone(o,t.center),t.radius=a,t},p.clone=function(e,t){if(r.defined(e))return r.defined(t)?(t.center=n.Cartesian3.clone(e.center,t.center),t.radius=e.radius,t):new p(e.center,e.radius)},p.packedLength=4,p.pack=function(e,t,n){n=r.defaultValue(n,0);const o=e.center;return t[n++]=o.x,t[n++]=o.y,t[n++]=o.z,t[n]=e.radius,t},p.unpack=function(e,t,n){t=r.defaultValue(t,0),r.defined(n)||(n=new p);const o=n.center;return o.x=e[t++],o.y=e[t++],o.z=e[t++],n.radius=e[t],n};const N=new n.Cartesian3,j=new n.Cartesian3;p.union=function(e,t,o){r.defined(o)||(o=new p);const i=e.center,s=e.radius,a=t.center,u=t.radius,c=n.Cartesian3.subtract(a,i,N),l=n.Cartesian3.magnitude(c);if(s>=l+u)return e.clone(o),o;if(u>=l+s)return t.clone(o),o;const d=.5*(s+l+u),f=n.Cartesian3.multiplyByScalar(c,(-s+d)/l,j);return n.Cartesian3.add(f,i,f),n.Cartesian3.clone(f,o.center),o.radius=d,o};const B=new n.Cartesian3;p.expand=function(e,t,r){r=p.clone(e,r);const o=n.Cartesian3.magnitude(n.Cartesian3.subtract(t,r.center,B));return o>r.radius&&(r.radius=o),r},p.intersectPlane=function(e,t){const r=e.center,o=e.radius,i=t.normal,s=n.Cartesian3.dot(i,r)+t.distance;return s<-o?d.OUTSIDE:s<o?d.INTERSECTING:d.INSIDE},p.transform=function(e,t,n){return r.defined(n)||(n=new p),n.center=i.Matrix4.multiplyByPoint(t,e.center,n.center),n.radius=i.Matrix4.getMaximumScale(t)*e.radius,n};const V=new n.Cartesian3;p.distanceSquaredTo=function(e,t){const r=n.Cartesian3.subtract(e.center,t,V),o=n.Cartesian3.magnitude(r)-e.radius;return o<=0?0:o*o},p.transformWithoutScale=function(e,t,n){return r.defined(n)||(n=new p),n.center=i.Matrix4.multiplyByPoint(t,e.center,n.center),n.radius=e.radius,n};const L=new n.Cartesian3;p.computePlaneDistances=function(e,t,o,i){r.defined(i)||(i=new f);const s=n.Cartesian3.subtract(e.center,t,L),a=n.Cartesian3.dot(o,s);return i.start=a-e.radius,i.stop=a+e.radius,i};const Q=new n.Cartesian3,$=new n.Cartesian3,W=new n.Cartesian3,H=new n.Cartesian3,Y=new n.Cartesian3,Z=new n.Cartographic,J=new Array(8);for(let e=0;e<8;++e)J[e]=new n.Cartesian3;const X=new l;let G;p.projectTo2D=function(e,t,o){const i=(t=r.defaultValue(t,X)).ellipsoid;let s=e.center;const a=e.radius;let u;u=n.Cartesian3.equals(s,n.Cartesian3.ZERO)?n.Cartesian3.clone(n.Cartesian3.UNIT_X,Q):i.geodeticSurfaceNormal(s,Q);const c=n.Cartesian3.cross(n.Cartesian3.UNIT_Z,u,$);n.Cartesian3.normalize(c,c);const l=n.Cartesian3.cross(u,c,W);n.Cartesian3.normalize(l,l),n.Cartesian3.multiplyByScalar(u,a,u),n.Cartesian3.multiplyByScalar(l,a,l),n.Cartesian3.multiplyByScalar(c,a,c);const d=n.Cartesian3.negate(l,Y),f=n.Cartesian3.negate(c,H),h=J;let m=h[0];n.Cartesian3.add(u,l,m),n.Cartesian3.add(m,c,m),m=h[1],n.Cartesian3.add(u,l,m),n.Cartesian3.add(m,f,m),m=h[2],n.Cartesian3.add(u,d,m),n.Cartesian3.add(m,f,m),m=h[3],n.Cartesian3.add(u,d,m),n.Cartesian3.add(m,c,m),n.Cartesian3.negate(u,u),m=h[4],n.Cartesian3.add(u,l,m),n.Cartesian3.add(m,c,m),m=h[5],n.Cartesian3.add(u,l,m),n.Cartesian3.add(m,f,m),m=h[6],n.Cartesian3.add(u,d,m),n.Cartesian3.add(m,f,m),m=h[7],n.Cartesian3.add(u,d,m),n.Cartesian3.add(m,c,m);const g=h.length;for(let e=0;e<g;++e){const r=h[e];n.Cartesian3.add(s,r,r);const o=i.cartesianToCartographic(r,Z);t.project(o,r)}s=(o=p.fromPoints(h,o)).center;const y=s.x,v=s.y,w=s.z;return s.x=w,s.y=y,s.z=v,o},p.isOccluded=function(e,t){return!t.isBoundingSphereVisible(e)},p.equals=function(e,t){return e===t||r.defined(e)&&r.defined(t)&&n.Cartesian3.equals(e.center,t.center)&&e.radius===t.radius},p.prototype.intersectPlane=function(e){return p.intersectPlane(this,e)},p.prototype.distanceSquaredTo=function(e){return p.distanceSquaredTo(this,e)},p.prototype.computePlaneDistances=function(e,t,n){return p.computePlaneDistances(this,e,t,n)},p.prototype.isOccluded=function(e){return p.isOccluded(this,e)},p.prototype.equals=function(e){return p.equals(this,e)},p.prototype.clone=function(e){return p.clone(this,e)},p.prototype.volume=function(){const e=this.radius;return E*e*e*e};const K={requestFullscreen:void 0,exitFullscreen:void 0,fullscreenEnabled:void 0,fullscreenElement:void 0,fullscreenchange:void 0,fullscreenerror:void 0},ee={};Object.defineProperties(ee,{element:{get:function(){if(ee.supportsFullscreen())return document[K.fullscreenElement]}},changeEventName:{get:function(){if(ee.supportsFullscreen())return K.fullscreenchange}},errorEventName:{get:function(){if(ee.supportsFullscreen())return K.fullscreenerror}},enabled:{get:function(){if(ee.supportsFullscreen())return document[K.fullscreenEnabled]}},fullscreen:{get:function(){if(ee.supportsFullscreen())return null!==ee.element}}}),ee.supportsFullscreen=function(){if(r.defined(G))return G;G=!1;const e=document.body;if("function"==typeof e.requestFullscreen)return K.requestFullscreen="requestFullscreen",K.exitFullscreen="exitFullscreen",K.fullscreenEnabled="fullscreenEnabled",K.fullscreenElement="fullscreenElement",K.fullscreenchange="fullscreenchange",K.fullscreenerror="fullscreenerror",G=!0,G;const t=["webkit","moz","o","ms","khtml"];let n;for(let r=0,o=t.length;r<o;++r){const o=t[r];n=`${o}RequestFullscreen`,"function"==typeof e[n]?(K.requestFullscreen=n,G=!0):(n=`${o}RequestFullScreen`,"function"==typeof e[n]&&(K.requestFullscreen=n,G=!0)),n=`${o}ExitFullscreen`,"function"==typeof document[n]?K.exitFullscreen=n:(n=`${o}CancelFullScreen`,"function"==typeof document[n]&&(K.exitFullscreen=n)),n=`${o}FullscreenEnabled`,void 0!==document[n]?K.fullscreenEnabled=n:(n=`${o}FullScreenEnabled`,void 0!==document[n]&&(K.fullscreenEnabled=n)),n=`${o}FullscreenElement`,void 0!==document[n]?K.fullscreenElement=n:(n=`${o}FullScreenElement`,void 0!==document[n]&&(K.fullscreenElement=n)),n=`${o}fullscreenchange`,void 0!==document[`on${n}`]&&("ms"===o&&(n="MSFullscreenChange"),K.fullscreenchange=n),n=`${o}fullscreenerror`,void 0!==document[`on${n}`]&&("ms"===o&&(n="MSFullscreenError"),K.fullscreenerror=n)}return G},ee.requestFullscreen=function(e,t){ee.supportsFullscreen()&&e[K.requestFullscreen]({vrDisplay:t})},ee.exitFullscreen=function(){ee.supportsFullscreen()&&document[K.exitFullscreen]()},ee._names=K;var te=ee;let ne,re,oe,ie,se,ae,ue,ce,le,de,fe,pe,he,me,ge,ye,ve,we;function Ce(e){const t=e.split(".");for(let e=0,n=t.length;e<n;++e)t[e]=parseInt(t[e],10);return t}function _e(){if(!r.defined(re)&&(re=!1,!Ae())){const e=/ Chrome\/([\.0-9]+)/.exec(ne.userAgent);null!==e&&(re=!0,oe=Ce(e[1]))}return re}function be(){if(!r.defined(ie)&&(ie=!1,!_e()&&!Ae()&&/ Safari\/[\.0-9]+/.test(ne.userAgent))){const e=/ Version\/([\.0-9]+)/.exec(ne.userAgent);null!==e&&(ie=!0,se=Ce(e[1]))}return ie}function xe(){if(!r.defined(ae)){ae=!1;const e=/ AppleWebKit\/([\.0-9]+)(\+?)/.exec(ne.userAgent);null!==e&&(ae=!0,ue=Ce(e[1]),ue.isNightly=!!e[2])}return ae}function Se(){if(!r.defined(ce)){let e;ce=!1,"Microsoft Internet Explorer"===ne.appName?(e=/MSIE ([0-9]{1,}[\.0-9]{0,})/.exec(ne.userAgent),null!==e&&(ce=!0,le=Ce(e[1]))):"Netscape"===ne.appName&&(e=/Trident\/.*rv:([0-9]{1,}[\.0-9]{0,})/.exec(ne.userAgent),null!==e&&(ce=!0,le=Ce(e[1])))}return ce}function Ae(){if(!r.defined(de)){de=!1;const e=/ Edg\/([\.0-9]+)/.exec(ne.userAgent);null!==e&&(de=!0,fe=Ce(e[1]))}return de}function Ee(){if(!r.defined(pe)){pe=!1;const e=/Firefox\/([\.0-9]+)/.exec(ne.userAgent);null!==e&&(pe=!0,he=Ce(e[1]))}return pe}function Oe(){if(!r.defined(we)){const e=document.createElement("canvas");e.setAttribute("style","image-rendering: -moz-crisp-edges;image-rendering: pixelated;");const t=e.style.imageRendering;we=r.defined(t)&&""!==t,we&&(ve=t)}return we}function Pe(){return Pe._result}ne="undefined"!=typeof navigator?navigator:{},Pe._promise=void 0,Pe._result=void 0,Pe.initialize=function(){return r.defined(Pe._promise)||(Pe._promise=new Promise((e=>{const t=new Image;t.onload=function(){Pe._result=t.width>0&&t.height>0,e(Pe._result)},t.onerror=function(){Pe._result=!1,e(Pe._result)},t.src="data:image/webp;base64,UklGRiIAAABXRUJQVlA4IBYAAAAwAQCdASoBAAEADsD+JaQAA3AAAAAA"}))),Pe._promise},Object.defineProperties(Pe,{initialized:{get:function(){return r.defined(Pe._result)}}});const Ie=[];"undefined"!=typeof ArrayBuffer&&(Ie.push(Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array),"undefined"!=typeof Uint8ClampedArray&&Ie.push(Uint8ClampedArray),"undefined"!=typeof Uint8ClampedArray&&Ie.push(Uint8ClampedArray),"undefined"!=typeof BigInt64Array&&Ie.push(BigInt64Array),"undefined"!=typeof BigUint64Array&&Ie.push(BigUint64Array));const Re={isChrome:_e,chromeVersion:function(){return _e()&&oe},isSafari:be,safariVersion:function(){return be()&&se},isWebkit:xe,webkitVersion:function(){return xe()&&ue},isInternetExplorer:Se,internetExplorerVersion:function(){return Se()&&le},isEdge:Ae,edgeVersion:function(){return Ae()&&fe},isFirefox:Ee,firefoxVersion:function(){return Ee()&&he},isWindows:function(){return r.defined(me)||(me=/Windows/i.test(ne.appVersion)),me},isIPadOrIOS:function(){return r.defined(ge)||(ge="iPhone"===navigator.platform||"iPod"===navigator.platform||"iPad"===navigator.platform),ge},hardwareConcurrency:r.defaultValue(ne.hardwareConcurrency,3),supportsPointerEvents:function(){return r.defined(ye)||(ye=!Ee()&&"undefined"!=typeof PointerEvent&&(!r.defined(ne.pointerEnabled)||ne.pointerEnabled)),ye},supportsImageRenderingPixelated:Oe,supportsWebP:Pe,imageRenderingValue:function(){return Oe()?ve:void 0},typedArrayTypes:Ie,supportsBasis:function(e){return Re.supportsWebAssembly()&&e.context.supportsBasis},supportsFullscreen:function(){return te.supportsFullscreen()},supportsTypedArrays:function(){return"undefined"!=typeof ArrayBuffer},supportsBigInt64Array:function(){return"undefined"!=typeof BigInt64Array},supportsBigUint64Array:function(){return"undefined"!=typeof BigUint64Array},supportsBigInt:function(){return"undefined"!=typeof BigInt},supportsWebWorkers:function(){return"undefined"!=typeof Worker},supportsWebAssembly:function(){return"undefined"!=typeof WebAssembly}};var Te=Re;function qe(e,t,n,o){this.x=r.defaultValue(e,0),this.y=r.defaultValue(t,0),this.z=r.defaultValue(n,0),this.w=r.defaultValue(o,0)}let ze=new n.Cartesian3;qe.fromAxisAngle=function(e,t,o){const i=t/2,s=Math.sin(i);ze=n.Cartesian3.normalize(e,ze);const a=ze.x*s,u=ze.y*s,c=ze.z*s,l=Math.cos(i);return r.defined(o)?(o.x=a,o.y=u,o.z=c,o.w=l,o):new qe(a,u,c,l)};const Me=[1,2,0],De=new Array(3);qe.fromRotationMatrix=function(e,t){let o,i,s,a,u;const c=e[n.Matrix3.COLUMN0ROW0],l=e[n.Matrix3.COLUMN1ROW1],d=e[n.Matrix3.COLUMN2ROW2],f=c+l+d;if(f>0)o=Math.sqrt(f+1),u=.5*o,o=.5/o,i=(e[n.Matrix3.COLUMN1ROW2]-e[n.Matrix3.COLUMN2ROW1])*o,s=(e[n.Matrix3.COLUMN2ROW0]-e[n.Matrix3.COLUMN0ROW2])*o,a=(e[n.Matrix3.COLUMN0ROW1]-e[n.Matrix3.COLUMN1ROW0])*o;else{const t=Me;let r=0;l>c&&(r=1),d>c&&d>l&&(r=2);const f=t[r],p=t[f];o=Math.sqrt(e[n.Matrix3.getElementIndex(r,r)]-e[n.Matrix3.getElementIndex(f,f)]-e[n.Matrix3.getElementIndex(p,p)]+1);const h=De;h[r]=.5*o,o=.5/o,u=(e[n.Matrix3.getElementIndex(p,f)]-e[n.Matrix3.getElementIndex(f,p)])*o,h[f]=(e[n.Matrix3.getElementIndex(f,r)]+e[n.Matrix3.getElementIndex(r,f)])*o,h[p]=(e[n.Matrix3.getElementIndex(p,r)]+e[n.Matrix3.getElementIndex(r,p)])*o,i=-h[0],s=-h[1],a=-h[2]}return r.defined(t)?(t.x=i,t.y=s,t.z=a,t.w=u,t):new qe(i,s,a,u)};const Ue=new qe;let ke=new qe,Fe=new qe,Ne=new qe;qe.fromHeadingPitchRoll=function(e,t){return Ne=qe.fromAxisAngle(n.Cartesian3.UNIT_X,e.roll,Ue),Fe=qe.fromAxisAngle(n.Cartesian3.UNIT_Y,-e.pitch,t),t=qe.multiply(Fe,Ne,Fe),ke=qe.fromAxisAngle(n.Cartesian3.UNIT_Z,-e.heading,Ue),qe.multiply(ke,t,t)};const je=new n.Cartesian3,Be=new n.Cartesian3,Ve=new qe,Le=new qe,Qe=new qe;qe.packedLength=4,qe.pack=function(e,t,n){return n=r.defaultValue(n,0),t[n++]=e.x,t[n++]=e.y,t[n++]=e.z,t[n]=e.w,t},qe.unpack=function(e,t,n){return t=r.defaultValue(t,0),r.defined(n)||(n=new qe),n.x=e[t],n.y=e[t+1],n.z=e[t+2],n.w=e[t+3],n},qe.packedInterpolationLength=3,qe.convertPackedArrayForInterpolation=function(e,t,n,o){qe.unpack(e,4*n,Qe),qe.conjugate(Qe,Qe);for(let i=0,s=n-t+1;i<s;i++){const n=3*i;qe.unpack(e,4*(t+i),Ve),qe.multiply(Ve,Qe,Ve),Ve.w<0&&qe.negate(Ve,Ve),qe.computeAxis(Ve,je);const s=qe.computeAngle(Ve);r.defined(o)||(o=[]),o[n]=je.x*s,o[n+1]=je.y*s,o[n+2]=je.z*s}},qe.unpackInterpolationResult=function(e,t,o,i,s){r.defined(s)||(s=new qe),n.Cartesian3.fromArray(e,0,Be);const a=n.Cartesian3.magnitude(Be);return qe.unpack(t,4*i,Le),0===a?qe.clone(qe.IDENTITY,Ve):qe.fromAxisAngle(Be,a,Ve),qe.multiply(Ve,Le,s)},qe.clone=function(e,t){if(r.defined(e))return r.defined(t)?(t.x=e.x,t.y=e.y,t.z=e.z,t.w=e.w,t):new qe(e.x,e.y,e.z,e.w)},qe.conjugate=function(e,t){return t.x=-e.x,t.y=-e.y,t.z=-e.z,t.w=e.w,t},qe.magnitudeSquared=function(e){return e.x*e.x+e.y*e.y+e.z*e.z+e.w*e.w},qe.magnitude=function(e){return Math.sqrt(qe.magnitudeSquared(e))},qe.normalize=function(e,t){const n=1/qe.magnitude(e),r=e.x*n,o=e.y*n,i=e.z*n,s=e.w*n;return t.x=r,t.y=o,t.z=i,t.w=s,t},qe.inverse=function(e,t){const n=qe.magnitudeSquared(e);return t=qe.conjugate(e,t),qe.multiplyByScalar(t,1/n,t)},qe.add=function(e,t,n){return n.x=e.x+t.x,n.y=e.y+t.y,n.z=e.z+t.z,n.w=e.w+t.w,n},qe.subtract=function(e,t,n){return n.x=e.x-t.x,n.y=e.y-t.y,n.z=e.z-t.z,n.w=e.w-t.w,n},qe.negate=function(e,t){return t.x=-e.x,t.y=-e.y,t.z=-e.z,t.w=-e.w,t},qe.dot=function(e,t){return e.x*t.x+e.y*t.y+e.z*t.z+e.w*t.w},qe.multiply=function(e,t,n){const r=e.x,o=e.y,i=e.z,s=e.w,a=t.x,u=t.y,c=t.z,l=t.w,d=s*a+r*l+o*c-i*u,f=s*u-r*c+o*l+i*a,p=s*c+r*u-o*a+i*l,h=s*l-r*a-o*u-i*c;return n.x=d,n.y=f,n.z=p,n.w=h,n},qe.multiplyByScalar=function(e,t,n){return n.x=e.x*t,n.y=e.y*t,n.z=e.z*t,n.w=e.w*t,n},qe.divideByScalar=function(e,t,n){return n.x=e.x/t,n.y=e.y/t,n.z=e.z/t,n.w=e.w/t,n},qe.computeAxis=function(e,t){const n=e.w;if(Math.abs(n-1)<o.CesiumMath.EPSILON6)return t.x=t.y=t.z=0,t;const r=1/Math.sqrt(1-n*n);return t.x=e.x*r,t.y=e.y*r,t.z=e.z*r,t},qe.computeAngle=function(e){return Math.abs(e.w-1)<o.CesiumMath.EPSILON6?0:2*Math.acos(e.w)};let $e=new qe;qe.lerp=function(e,t,n,r){return $e=qe.multiplyByScalar(t,n,$e),r=qe.multiplyByScalar(e,1-n,r),qe.add($e,r,r)};let We=new qe,He=new qe,Ye=new qe;qe.slerp=function(e,t,n,r){let i=qe.dot(e,t),s=t;if(i<0&&(i=-i,s=We=qe.negate(t,We)),1-i<o.CesiumMath.EPSILON6)return qe.lerp(e,s,n,r);const a=Math.acos(i);return He=qe.multiplyByScalar(e,Math.sin((1-n)*a),He),Ye=qe.multiplyByScalar(s,Math.sin(n*a),Ye),r=qe.add(He,Ye,r),qe.multiplyByScalar(r,1/Math.sin(a),r)},qe.log=function(e,t){const r=o.CesiumMath.acosClamped(e.w);let i=0;return 0!==r&&(i=r/Math.sin(r)),n.Cartesian3.multiplyByScalar(e,i,t)},qe.exp=function(e,t){const r=n.Cartesian3.magnitude(e);let o=0;return 0!==r&&(o=Math.sin(r)/r),t.x=e.x*o,t.y=e.y*o,t.z=e.z*o,t.w=Math.cos(r),t};const Ze=new n.Cartesian3,Je=new n.Cartesian3,Xe=new qe,Ge=new qe;qe.computeInnerQuadrangle=function(e,t,r,o){const i=qe.conjugate(t,Xe);qe.multiply(i,r,Ge);const s=qe.log(Ge,Ze);qe.multiply(i,e,Ge);const a=qe.log(Ge,Je);return n.Cartesian3.add(s,a,s),n.Cartesian3.multiplyByScalar(s,.25,s),n.Cartesian3.negate(s,s),qe.exp(s,Xe),qe.multiply(t,Xe,o)},qe.squad=function(e,t,n,r,o,i){const s=qe.slerp(e,t,o,Xe),a=qe.slerp(n,r,o,Ge);return qe.slerp(s,a,2*o*(1-o),i)};const Ke=new qe,et=1.9011074535173003,tt=Te.supportsTypedArrays()?new Float32Array(8):[],nt=Te.supportsTypedArrays()?new Float32Array(8):[],rt=Te.supportsTypedArrays()?new Float32Array(8):[],ot=Te.supportsTypedArrays()?new Float32Array(8):[];for(let e=0;e<7;++e){const t=e+1,n=2*t+1;tt[e]=1/(t*n),nt[e]=t/n}function it(e,t,n){let r,o,i=0,s=e.length-1;for(;i<=s;)if(r=~~((i+s)/2),o=n(e[r],t),o<0)i=r+1;else{if(!(o>0))return r;s=r-1}return~(s+1)}function st(e,t,n,r,o){this.xPoleWander=e,this.yPoleWander=t,this.xPoleOffset=n,this.yPoleOffset=r,this.ut1MinusUtc=o}function at(e,t,n,r,o,i,s,a){this.year=e,this.month=t,this.day=n,this.hour=r,this.minute=o,this.second=i,this.millisecond=s,this.isLeapSecond=a}function ut(e){return e%4==0&&e%100!=0||e%400==0}function ct(e,t){this.julianDate=e,this.offset=t}tt[7]=et/136,nt[7]=8*et/17,qe.fastSlerp=function(e,t,n,r){let o,i=qe.dot(e,t);i>=0?o=1:(o=-1,i=-i);const s=i-1,a=1-n,u=n*n,c=a*a;for(let e=7;e>=0;--e)rt[e]=(tt[e]*u-nt[e])*s,ot[e]=(tt[e]*c-nt[e])*s;const l=o*n*(1+rt[0]*(1+rt[1]*(1+rt[2]*(1+rt[3]*(1+rt[4]*(1+rt[5]*(1+rt[6]*(1+rt[7])))))))),d=a*(1+ot[0]*(1+ot[1]*(1+ot[2]*(1+ot[3]*(1+ot[4]*(1+ot[5]*(1+ot[6]*(1+ot[7])))))))),f=qe.multiplyByScalar(e,d,Ke);return qe.multiplyByScalar(t,l,r),qe.add(f,r,r)},qe.fastSquad=function(e,t,n,r,o,i){const s=qe.fastSlerp(e,t,o,Xe),a=qe.fastSlerp(n,r,o,Ge);return qe.fastSlerp(s,a,2*o*(1-o),i)},qe.equals=function(e,t){return e===t||r.defined(e)&&r.defined(t)&&e.x===t.x&&e.y===t.y&&e.z===t.z&&e.w===t.w},qe.equalsEpsilon=function(e,t,n){return n=r.defaultValue(n,0),e===t||r.defined(e)&&r.defined(t)&&Math.abs(e.x-t.x)<=n&&Math.abs(e.y-t.y)<=n&&Math.abs(e.z-t.z)<=n&&Math.abs(e.w-t.w)<=n},qe.ZERO=Object.freeze(new qe(0,0,0,0)),qe.IDENTITY=Object.freeze(new qe(0,0,0,1)),qe.prototype.clone=function(e){return qe.clone(this,e)},qe.prototype.equals=function(e){return qe.equals(this,e)},qe.prototype.equalsEpsilon=function(e,t){return qe.equalsEpsilon(this,e,t)},qe.prototype.toString=function(){return`(${this.x}, ${this.y}, ${this.z}, ${this.w})`};var lt=Object.freeze({SECONDS_PER_MILLISECOND:.001,SECONDS_PER_MINUTE:60,MINUTES_PER_HOUR:60,HOURS_PER_DAY:24,SECONDS_PER_HOUR:3600,MINUTES_PER_DAY:1440,SECONDS_PER_DAY:86400,DAYS_PER_JULIAN_CENTURY:36525,PICOSECOND:1e-9,MODIFIED_JULIAN_DATE_DIFFERENCE:2400000.5});var dt=Object.freeze({UTC:0,TAI:1});const ft=new at,pt=[31,28,31,30,31,30,31,31,30,31,30,31];function ht(e,t){return It.compare(e.julianDate,t.julianDate)}const mt=new ct;function gt(e){mt.julianDate=e;const t=It.leapSeconds;let n=it(t,mt,ht);n<0&&(n=~n),n>=t.length&&(n=t.length-1);let r=t[n].offset;if(n>0){It.secondsDifference(t[n].julianDate,e)>r&&(n--,r=t[n].offset)}It.addSeconds(e,r,e)}function yt(e,t){mt.julianDate=e;const n=It.leapSeconds;let r=it(n,mt,ht);if(r<0&&(r=~r),0===r)return It.addSeconds(e,-n[0].offset,t);if(r>=n.length)return It.addSeconds(e,-n[r-1].offset,t);const o=It.secondsDifference(n[r].julianDate,e);return 0===o?It.addSeconds(e,-n[r].offset,t):o<=1?void 0:It.addSeconds(e,-n[--r].offset,t)}function vt(e,t,n){const r=t/lt.SECONDS_PER_DAY|0;return e+=r,(t-=lt.SECONDS_PER_DAY*r)<0&&(e--,t+=lt.SECONDS_PER_DAY),n.dayNumber=e,n.secondsOfDay=t,n}function wt(e,t,n,r,o,i,s){const a=(t-14)/12|0,u=e+4800+a;let c=(1461*u/4|0)+(367*(t-2-12*a)/12|0)-(3*((u+100)/100|0)/4|0)+n-32075;(r-=12)<0&&(r+=24);const l=i+(r*lt.SECONDS_PER_HOUR+o*lt.SECONDS_PER_MINUTE+s*lt.SECONDS_PER_MILLISECOND);return l>=43200&&(c-=1),[c,l]}const Ct=/^(\d{4})$/,_t=/^(\d{4})-(\d{2})$/,bt=/^(\d{4})-?(\d{3})$/,xt=/^(\d{4})-?W(\d{2})-?(\d{1})?$/,St=/^(\d{4})-?(\d{2})-?(\d{2})$/,At=/([Z+\-])?(\d{2})?:?(\d{2})?$/,Et=/^(\d{2})(\.\d+)?/.source+At.source,Ot=/^(\d{2}):?(\d{2})(\.\d+)?/.source+At.source,Pt=/^(\d{2}):?(\d{2}):?(\d{2})(\.\d+)?/.source+At.source;function It(e,t,n){this.dayNumber=void 0,this.secondsOfDay=void 0,e=r.defaultValue(e,0),t=r.defaultValue(t,0),n=r.defaultValue(n,dt.UTC);const o=0|e;vt(o,t+=(e-o)*lt.SECONDS_PER_DAY,this),n===dt.UTC&&gt(this)}It.fromGregorianDate=function(e,t){const n=wt(e.year,e.month,e.day,e.hour,e.minute,e.second,e.millisecond);return r.defined(t)?(vt(n[0],n[1],t),gt(t),t):new It(n[0],n[1],dt.UTC)},It.fromDate=function(e,t){const n=wt(e.getUTCFullYear(),e.getUTCMonth()+1,e.getUTCDate(),e.getUTCHours(),e.getUTCMinutes(),e.getUTCSeconds(),e.getUTCMilliseconds());return r.defined(t)?(vt(n[0],n[1],t),gt(t),t):new It(n[0],n[1],dt.UTC)},It.fromIso8601=function(e,t){let n,o=(e=e.replace(",",".")).split("T"),i=1,s=1,a=0,u=0,c=0,l=0;const d=o[0],f=o[1];let p,h,m;if(o=d.match(St),null!==o)n=+o[1],i=+o[2],s=+o[3];else if(o=d.match(_t),null!==o)n=+o[1],i=+o[2];else if(o=d.match(Ct),null!==o)n=+o[1];else{let e;if(o=d.match(bt),null!==o)n=+o[1],e=+o[2],h=ut(n);else if(o=d.match(xt),null!==o){n=+o[1];e=7*+o[2]+(+o[3]||0)-new Date(Date.UTC(n,0,4)).getUTCDay()-3}p=new Date(Date.UTC(n,0,1)),p.setUTCDate(e),i=p.getUTCMonth()+1,s=p.getUTCDate()}if(h=ut(n),r.defined(f)){o=f.match(Pt),null!==o?(a=+o[1],u=+o[2],c=+o[3],l=1e3*+(o[4]||0),m=5):(o=f.match(Ot),null!==o?(a=+o[1],u=+o[2],c=60*+(o[3]||0),m=4):(o=f.match(Et),null!==o&&(a=+o[1],u=60*+(o[2]||0),m=3)));const e=o[m],t=+o[m+1],r=+(o[m+2]||0);switch(e){case"+":a-=t,u-=r;break;case"-":a+=t,u+=r;break;case"Z":break;default:u+=new Date(Date.UTC(n,i-1,s,a,u)).getTimezoneOffset()}}const g=60===c;for(g&&c--;u>=60;)u-=60,a++;for(;a>=24;)a-=24,s++;for(p=h&&2===i?29:pt[i-1];s>p;)s-=p,i++,i>12&&(i-=12,n++),p=h&&2===i?29:pt[i-1];for(;u<0;)u+=60,a--;for(;a<0;)a+=24,s--;for(;s<1;)i--,i<1&&(i+=12,n--),p=h&&2===i?29:pt[i-1],s+=p;const y=wt(n,i,s,a,u,c,l);return r.defined(t)?(vt(y[0],y[1],t),gt(t)):t=new It(y[0],y[1],dt.UTC),g&&It.addSeconds(t,1,t),t},It.now=function(e){return It.fromDate(new Date,e)};const Rt=new It(0,0,dt.TAI);It.toGregorianDate=function(e,t){let n=!1,o=yt(e,Rt);r.defined(o)||(It.addSeconds(e,-1,Rt),o=yt(Rt,Rt),n=!0);let i=o.dayNumber;const s=o.secondsOfDay;s>=43200&&(i+=1);let a=i+68569|0;const u=4*a/146097|0;a=a-((146097*u+3)/4|0)|0;const c=4e3*(a+1)/1461001|0;a=a-(1461*c/4|0)+31|0;const l=80*a/2447|0,d=a-(2447*l/80|0)|0;a=l/11|0;const f=l+2-12*a|0,p=100*(u-49)+c+a|0;let h=s/lt.SECONDS_PER_HOUR|0,m=s-h*lt.SECONDS_PER_HOUR;const g=m/lt.SECONDS_PER_MINUTE|0;m-=g*lt.SECONDS_PER_MINUTE;let y=0|m;const v=(m-y)/lt.SECONDS_PER_MILLISECOND;return h+=12,h>23&&(h-=24),n&&(y+=1),r.defined(t)?(t.year=p,t.month=f,t.day=d,t.hour=h,t.minute=g,t.second=y,t.millisecond=v,t.isLeapSecond=n,t):new at(p,f,d,h,g,y,v,n)},It.toDate=function(e){const t=It.toGregorianDate(e,ft);let n=t.second;return t.isLeapSecond&&(n-=1),new Date(Date.UTC(t.year,t.month-1,t.day,t.hour,t.minute,n,t.millisecond))},It.toIso8601=function(e,t){const n=It.toGregorianDate(e,ft);let o=n.year,i=n.month,s=n.day,a=n.hour;const u=n.minute,c=n.second,l=n.millisecond;let d;return 1e4===o&&1===i&&1===s&&0===a&&0===u&&0===c&&0===l&&(o=9999,i=12,s=31,a=24),r.defined(t)||0===l?r.defined(t)&&0!==t?(d=(.01*l).toFixed(t).replace(".","").slice(0,t),`${o.toString().padStart(4,"0")}-${i.toString().padStart(2,"0")}-${s.toString().padStart(2,"0")}T${a.toString().padStart(2,"0")}:${u.toString().padStart(2,"0")}:${c.toString().padStart(2,"0")}.${d}Z`):`${o.toString().padStart(4,"0")}-${i.toString().padStart(2,"0")}-${s.toString().padStart(2,"0")}T${a.toString().padStart(2,"0")}:${u.toString().padStart(2,"0")}:${c.toString().padStart(2,"0")}Z`:(d=(.01*l).toString().replace(".",""),`${o.toString().padStart(4,"0")}-${i.toString().padStart(2,"0")}-${s.toString().padStart(2,"0")}T${a.toString().padStart(2,"0")}:${u.toString().padStart(2,"0")}:${c.toString().padStart(2,"0")}.${d}Z`)},It.clone=function(e,t){if(r.defined(e))return r.defined(t)?(t.dayNumber=e.dayNumber,t.secondsOfDay=e.secondsOfDay,t):new It(e.dayNumber,e.secondsOfDay,dt.TAI)},It.compare=function(e,t){const n=e.dayNumber-t.dayNumber;return 0!==n?n:e.secondsOfDay-t.secondsOfDay},It.equals=function(e,t){return e===t||r.defined(e)&&r.defined(t)&&e.dayNumber===t.dayNumber&&e.secondsOfDay===t.secondsOfDay},It.equalsEpsilon=function(e,t,n){return n=r.defaultValue(n,0),e===t||r.defined(e)&&r.defined(t)&&Math.abs(It.secondsDifference(e,t))<=n},It.totalDays=function(e){return e.dayNumber+e.secondsOfDay/lt.SECONDS_PER_DAY},It.secondsDifference=function(e,t){return(e.dayNumber-t.dayNumber)*lt.SECONDS_PER_DAY+(e.secondsOfDay-t.secondsOfDay)},It.daysDifference=function(e,t){return e.dayNumber-t.dayNumber+(e.secondsOfDay-t.secondsOfDay)/lt.SECONDS_PER_DAY},It.computeTaiMinusUtc=function(e){mt.julianDate=e;const t=It.leapSeconds;let n=it(t,mt,ht);return n<0&&(n=~n,--n,n<0&&(n=0)),t[n].offset},It.addSeconds=function(e,t,n){return vt(e.dayNumber,e.secondsOfDay+t,n)},It.addMinutes=function(e,t,n){const r=e.secondsOfDay+t*lt.SECONDS_PER_MINUTE;return vt(e.dayNumber,r,n)},It.addHours=function(e,t,n){const r=e.secondsOfDay+t*lt.SECONDS_PER_HOUR;return vt(e.dayNumber,r,n)},It.addDays=function(e,t,n){return vt(e.dayNumber+t,e.secondsOfDay,n)},It.lessThan=function(e,t){return It.compare(e,t)<0},It.lessThanOrEquals=function(e,t){return It.compare(e,t)<=0},It.greaterThan=function(e,t){return It.compare(e,t)>0},It.greaterThanOrEquals=function(e,t){return It.compare(e,t)>=0},It.prototype.clone=function(e){return It.clone(this,e)},It.prototype.equals=function(e){return It.equals(this,e)},It.prototype.equalsEpsilon=function(e,t){return It.equalsEpsilon(this,e,t)},It.prototype.toString=function(){return It.toIso8601(this)},It.leapSeconds=[new ct(new It(2441317,43210,dt.TAI),10),new ct(new It(2441499,43211,dt.TAI),11),new ct(new It(2441683,43212,dt.TAI),12),new ct(new It(2442048,43213,dt.TAI),13),new ct(new It(2442413,43214,dt.TAI),14),new ct(new It(2442778,43215,dt.TAI),15),new ct(new It(2443144,43216,dt.TAI),16),new ct(new It(2443509,43217,dt.TAI),17),new ct(new It(2443874,43218,dt.TAI),18),new ct(new It(2444239,43219,dt.TAI),19),new ct(new It(2444786,43220,dt.TAI),20),new ct(new It(2445151,43221,dt.TAI),21),new ct(new It(2445516,43222,dt.TAI),22),new ct(new It(2446247,43223,dt.TAI),23),new ct(new It(2447161,43224,dt.TAI),24),new ct(new It(2447892,43225,dt.TAI),25),new ct(new It(2448257,43226,dt.TAI),26),new ct(new It(2448804,43227,dt.TAI),27),new ct(new It(2449169,43228,dt.TAI),28),new ct(new It(2449534,43229,dt.TAI),29),new ct(new It(2450083,43230,dt.TAI),30),new ct(new It(2450630,43231,dt.TAI),31),new ct(new It(2451179,43232,dt.TAI),32),new ct(new It(2453736,43233,dt.TAI),33),new ct(new It(2454832,43234,dt.TAI),34),new ct(new It(2456109,43235,dt.TAI),35),new ct(new It(2457204,43236,dt.TAI),36),new ct(new It(2457754,43237,dt.TAI),37)];var Tt,qt={exports:{}},zt={exports:{}};var Mt,Dt={exports:{}};
/*!
	 * URI.js - Mutating URLs
	 * IPv6 Support
	 *
	 * Version: 1.19.11
	 *
	 * Author: Rodney Rehm
	 * Web: http://medialize.github.io/URI.js/
	 *
	 * Licensed under
	 *   MIT License http://www.opensource.org/licenses/mit-license
	 *
	 */var Ut,kt,Ft,Nt,jt={exports:{}};
/*!
	 * URI.js - Mutating URLs
	 * Second Level Domain (SLD) Support
	 *
	 * Version: 1.19.11
	 *
	 * Author: Rodney Rehm
	 * Web: http://medialize.github.io/URI.js/
	 *
	 * Licensed under
	 *   MIT License http://www.opensource.org/licenses/mit-license
	 *
	 */Ft=c,Nt=function(e,t,n,r){var o=r&&r.URI;function i(e,t){var n=arguments.length>=1;if(!(this instanceof i))return n?arguments.length>=2?new i(e,t):new i(e):new i;if(void 0===e){if(n)throw new TypeError("undefined is not a valid argument for URI");e="undefined"!=typeof location?location.href+"":""}if(null===e&&n)throw new TypeError("null is not a valid argument for URI");return this.href(e),void 0!==t?this.absoluteTo(t):this}i.version="1.19.11";var s=i.prototype,a=Object.prototype.hasOwnProperty;function u(e){return e.replace(/([.*+?^=!:${}()|[\]\/\\])/g,"\\$1")}function c(e){return void 0===e?"Undefined":String(Object.prototype.toString.call(e)).slice(8,-1)}function l(e){return"Array"===c(e)}function d(e,t){var n,r,o={};if("RegExp"===c(t))o=null;else if(l(t))for(n=0,r=t.length;n<r;n++)o[t[n]]=!0;else o[t]=!0;for(n=0,r=e.length;n<r;n++)(o&&void 0!==o[e[n]]||!o&&t.test(e[n]))&&(e.splice(n,1),r--,n--);return e}function f(e,t){var n,r;if(l(t)){for(n=0,r=t.length;n<r;n++)if(!f(e,t[n]))return!1;return!0}var o=c(t);for(n=0,r=e.length;n<r;n++)if("RegExp"===o){if("string"==typeof e[n]&&e[n].match(t))return!0}else if(e[n]===t)return!0;return!1}function p(e,t){if(!l(e)||!l(t))return!1;if(e.length!==t.length)return!1;e.sort(),t.sort();for(var n=0,r=e.length;n<r;n++)if(e[n]!==t[n])return!1;return!0}function h(e){return e.replace(/^\/+|\/+$/g,"")}function m(e){return escape(e)}function g(e){return encodeURIComponent(e).replace(/[!'()*]/g,m).replace(/\*/g,"%2A")}i._parts=function(){return{protocol:null,username:null,password:null,hostname:null,urn:null,port:null,path:null,query:null,fragment:null,preventInvalidHostname:i.preventInvalidHostname,duplicateQueryParameters:i.duplicateQueryParameters,escapeQuerySpace:i.escapeQuerySpace}},i.preventInvalidHostname=!1,i.duplicateQueryParameters=!1,i.escapeQuerySpace=!0,i.protocol_expression=/^[a-z][a-z0-9.+-]*$/i,i.idn_expression=/[^a-z0-9\._-]/i,i.punycode_expression=/(xn--)/i,i.ip4_expression=/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/,i.ip6_expression=/^\s*((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))(%.+)?\s*$/,i.find_uri_expression=/\b((?:[a-z][\w-]+:(?:\/{1,3}|[a-z0-9%])|www\d{0,3}[.]|[a-z0-9.\-]+[.][a-z]{2,4}\/)(?:[^\s()<>]+|\(([^\s()<>]+|(\([^\s()<>]+\)))*\))+(?:\(([^\s()<>]+|(\([^\s()<>]+\)))*\)|[^\s`!()\[\]{};:'".,<>?«»“”‘’]))/gi,i.findUri={start:/\b(?:([a-z][a-z0-9.+-]*:\/\/)|www\.)/gi,end:/[\s\r\n]|$/,trim:/[`!()\[\]{};:'".,<>?«»“”„‘’]+$/,parens:/(\([^\)]*\)|\[[^\]]*\]|\{[^}]*\}|<[^>]*>)/g},i.leading_whitespace_expression=/^[\x00-\x20\u00a0\u1680\u2000-\u200a\u2028\u2029\u202f\u205f\u3000\ufeff]+/,i.ascii_tab_whitespace=/[\u0009\u000A\u000D]+/g,i.defaultPorts={http:"80",https:"443",ftp:"21",gopher:"70",ws:"80",wss:"443"},i.hostProtocols=["http","https"],i.invalid_hostname_characters=/[^a-zA-Z0-9\.\-:_]/,i.domAttributes={a:"href",blockquote:"cite",link:"href",base:"href",script:"src",form:"action",img:"src",area:"href",iframe:"src",embed:"src",source:"src",track:"src",input:"src",audio:"src",video:"src"},i.getDomAttribute=function(e){if(e&&e.nodeName){var t=e.nodeName.toLowerCase();if("input"!==t||"image"===e.type)return i.domAttributes[t]}},i.encode=g,i.decode=decodeURIComponent,i.iso8859=function(){i.encode=escape,i.decode=unescape},i.unicode=function(){i.encode=g,i.decode=decodeURIComponent},i.characters={pathname:{encode:{expression:/%(24|26|2B|2C|3B|3D|3A|40)/gi,map:{"%24":"$","%26":"&","%2B":"+","%2C":",","%3B":";","%3D":"=","%3A":":","%40":"@"}},decode:{expression:/[\/\?#]/g,map:{"/":"%2F","?":"%3F","#":"%23"}}},reserved:{encode:{expression:/%(21|23|24|26|27|28|29|2A|2B|2C|2F|3A|3B|3D|3F|40|5B|5D)/gi,map:{"%3A":":","%2F":"/","%3F":"?","%23":"#","%5B":"[","%5D":"]","%40":"@","%21":"!","%24":"$","%26":"&","%27":"'","%28":"(","%29":")","%2A":"*","%2B":"+","%2C":",","%3B":";","%3D":"="}}},urnpath:{encode:{expression:/%(21|24|27|28|29|2A|2B|2C|3B|3D|40)/gi,map:{"%21":"!","%24":"$","%27":"'","%28":"(","%29":")","%2A":"*","%2B":"+","%2C":",","%3B":";","%3D":"=","%40":"@"}},decode:{expression:/[\/\?#:]/g,map:{"/":"%2F","?":"%3F","#":"%23",":":"%3A"}}}},i.encodeQuery=function(e,t){var n=i.encode(e+"");return void 0===t&&(t=i.escapeQuerySpace),t?n.replace(/%20/g,"+"):n},i.decodeQuery=function(e,t){e+="",void 0===t&&(t=i.escapeQuerySpace);try{return i.decode(t?e.replace(/\+/g,"%20"):e)}catch(t){return e}};var y,v={encode:"encode",decode:"decode"},w=function(e,t){return function(n){try{return i[t](n+"").replace(i.characters[e][t].expression,(function(n){return i.characters[e][t].map[n]}))}catch(e){return n}}};for(y in v)i[y+"PathSegment"]=w("pathname",v[y]),i[y+"UrnPathSegment"]=w("urnpath",v[y]);var C=function(e,t,n){return function(r){var o;o=n?function(e){return i[t](i[n](e))}:i[t];for(var s=(r+"").split(e),a=0,u=s.length;a<u;a++)s[a]=o(s[a]);return s.join(e)}};function _(e){return function(t,n){return void 0===t?this._parts[e]||"":(this._parts[e]=t||null,this.build(!n),this)}}function b(e,t){return function(n,r){return void 0===n?this._parts[e]||"":(null!==n&&(n+="").charAt(0)===t&&(n=n.substring(1)),this._parts[e]=n,this.build(!r),this)}}i.decodePath=C("/","decodePathSegment"),i.decodeUrnPath=C(":","decodeUrnPathSegment"),i.recodePath=C("/","encodePathSegment","decode"),i.recodeUrnPath=C(":","encodeUrnPathSegment","decode"),i.encodeReserved=w("reserved","encode"),i.parse=function(e,t){var n;return t||(t={preventInvalidHostname:i.preventInvalidHostname}),(n=(e=(e=e.replace(i.leading_whitespace_expression,"")).replace(i.ascii_tab_whitespace,"")).indexOf("#"))>-1&&(t.fragment=e.substring(n+1)||null,e=e.substring(0,n)),(n=e.indexOf("?"))>-1&&(t.query=e.substring(n+1)||null,e=e.substring(0,n)),"//"===(e=(e=e.replace(/^(https?|ftp|wss?)?:+[/\\]*/i,"$1://")).replace(/^[/\\]{2,}/i,"//")).substring(0,2)?(t.protocol=null,e=e.substring(2),e=i.parseAuthority(e,t)):(n=e.indexOf(":"))>-1&&(t.protocol=e.substring(0,n)||null,t.protocol&&!t.protocol.match(i.protocol_expression)?t.protocol=void 0:"//"===e.substring(n+1,n+3).replace(/\\/g,"/")?(e=e.substring(n+3),e=i.parseAuthority(e,t)):(e=e.substring(n+1),t.urn=!0)),t.path=e,t},i.parseHost=function(e,t){e||(e="");var n,r,o=(e=e.replace(/\\/g,"/")).indexOf("/");if(-1===o&&(o=e.length),"["===e.charAt(0))n=e.indexOf("]"),t.hostname=e.substring(1,n)||null,t.port=e.substring(n+2,o)||null,"/"===t.port&&(t.port=null);else{var s=e.indexOf(":"),a=e.indexOf("/"),u=e.indexOf(":",s+1);-1!==u&&(-1===a||u<a)?(t.hostname=e.substring(0,o)||null,t.port=null):(r=e.substring(0,o).split(":"),t.hostname=r[0]||null,t.port=r[1]||null)}return t.hostname&&"/"!==e.substring(o).charAt(0)&&(o++,e="/"+e),t.preventInvalidHostname&&i.ensureValidHostname(t.hostname,t.protocol),t.port&&i.ensureValidPort(t.port),e.substring(o)||"/"},i.parseAuthority=function(e,t){return e=i.parseUserinfo(e,t),i.parseHost(e,t)},i.parseUserinfo=function(e,t){var n=e;-1!==e.indexOf("\\")&&(e=e.replace(/\\/g,"/"));var r,o=e.indexOf("/"),s=e.lastIndexOf("@",o>-1?o:e.length-1);return s>-1&&(-1===o||s<o)?(r=e.substring(0,s).split(":"),t.username=r[0]?i.decode(r[0]):null,r.shift(),t.password=r[0]?i.decode(r.join(":")):null,e=n.substring(s+1)):(t.username=null,t.password=null),e},i.parseQuery=function(e,t){if(!e)return{};if(!(e=e.replace(/&+/g,"&").replace(/^\?*&*|&+$/g,"")))return{};for(var n,r,o,s={},u=e.split("&"),c=u.length,l=0;l<c;l++)n=u[l].split("="),r=i.decodeQuery(n.shift(),t),o=n.length?i.decodeQuery(n.join("="),t):null,"__proto__"!==r&&(a.call(s,r)?("string"!=typeof s[r]&&null!==s[r]||(s[r]=[s[r]]),s[r].push(o)):s[r]=o);return s},i.build=function(e){var t="",n=!1;return e.protocol&&(t+=e.protocol+":"),e.urn||!t&&!e.hostname||(t+="//",n=!0),t+=i.buildAuthority(e)||"","string"==typeof e.path&&("/"!==e.path.charAt(0)&&n&&(t+="/"),t+=e.path),"string"==typeof e.query&&e.query&&(t+="?"+e.query),"string"==typeof e.fragment&&e.fragment&&(t+="#"+e.fragment),t},i.buildHost=function(e){var t="";return e.hostname?(i.ip6_expression.test(e.hostname)?t+="["+e.hostname+"]":t+=e.hostname,e.port&&(t+=":"+e.port),t):""},i.buildAuthority=function(e){return i.buildUserinfo(e)+i.buildHost(e)},i.buildUserinfo=function(e){var t="";return e.username&&(t+=i.encode(e.username)),e.password&&(t+=":"+i.encode(e.password)),t&&(t+="@"),t},i.buildQuery=function(e,t,n){var r,o,s,u,c="";for(o in e)if("__proto__"!==o&&a.call(e,o))if(l(e[o]))for(r={},s=0,u=e[o].length;s<u;s++)void 0!==e[o][s]&&void 0===r[e[o][s]+""]&&(c+="&"+i.buildQueryParameter(o,e[o][s],n),!0!==t&&(r[e[o][s]+""]=!0));else void 0!==e[o]&&(c+="&"+i.buildQueryParameter(o,e[o],n));return c.substring(1)},i.buildQueryParameter=function(e,t,n){return i.encodeQuery(e,n)+(null!==t?"="+i.encodeQuery(t,n):"")},i.addQuery=function(e,t,n){if("object"==typeof t)for(var r in t)a.call(t,r)&&i.addQuery(e,r,t[r]);else{if("string"!=typeof t)throw new TypeError("URI.addQuery() accepts an object, string as the name parameter");if(void 0===e[t])return void(e[t]=n);"string"==typeof e[t]&&(e[t]=[e[t]]),l(n)||(n=[n]),e[t]=(e[t]||[]).concat(n)}},i.setQuery=function(e,t,n){if("object"==typeof t)for(var r in t)a.call(t,r)&&i.setQuery(e,r,t[r]);else{if("string"!=typeof t)throw new TypeError("URI.setQuery() accepts an object, string as the name parameter");e[t]=void 0===n?null:n}},i.removeQuery=function(e,t,n){var r,o,s;if(l(t))for(r=0,o=t.length;r<o;r++)e[t[r]]=void 0;else if("RegExp"===c(t))for(s in e)t.test(s)&&(e[s]=void 0);else if("object"==typeof t)for(s in t)a.call(t,s)&&i.removeQuery(e,s,t[s]);else{if("string"!=typeof t)throw new TypeError("URI.removeQuery() accepts an object, string, RegExp as the first parameter");void 0!==n?"RegExp"===c(n)?!l(e[t])&&n.test(e[t])?e[t]=void 0:e[t]=d(e[t],n):e[t]!==String(n)||l(n)&&1!==n.length?l(e[t])&&(e[t]=d(e[t],n)):e[t]=void 0:e[t]=void 0}},i.hasQuery=function(e,t,n,r){switch(c(t)){case"String":break;case"RegExp":for(var o in e)if(a.call(e,o)&&t.test(o)&&(void 0===n||i.hasQuery(e,o,n)))return!0;return!1;case"Object":for(var s in t)if(a.call(t,s)&&!i.hasQuery(e,s,t[s]))return!1;return!0;default:throw new TypeError("URI.hasQuery() accepts a string, regular expression or object as the name parameter")}switch(c(n)){case"Undefined":return t in e;case"Boolean":return n===Boolean(l(e[t])?e[t].length:e[t]);case"Function":return!!n(e[t],t,e);case"Array":return!!l(e[t])&&(r?f:p)(e[t],n);case"RegExp":return l(e[t])?!!r&&f(e[t],n):Boolean(e[t]&&e[t].match(n));case"Number":n=String(n);case"String":return l(e[t])?!!r&&f(e[t],n):e[t]===n;default:throw new TypeError("URI.hasQuery() accepts undefined, boolean, string, number, RegExp, Function as the value parameter")}},i.joinPaths=function(){for(var e=[],t=[],n=0,r=0;r<arguments.length;r++){var o=new i(arguments[r]);e.push(o);for(var s=o.segment(),a=0;a<s.length;a++)"string"==typeof s[a]&&t.push(s[a]),s[a]&&n++}if(!t.length||!n)return new i("");var u=new i("").segment(t);return""!==e[0].path()&&"/"!==e[0].path().slice(0,1)||u.path("/"+u.path()),u.normalize()},i.commonPath=function(e,t){var n,r=Math.min(e.length,t.length);for(n=0;n<r;n++)if(e.charAt(n)!==t.charAt(n)){n--;break}return n<1?e.charAt(0)===t.charAt(0)&&"/"===e.charAt(0)?"/":"":("/"===e.charAt(n)&&"/"===t.charAt(n)||(n=e.substring(0,n).lastIndexOf("/")),e.substring(0,n+1))},i.withinString=function(e,t,n){n||(n={});var r=n.start||i.findUri.start,o=n.end||i.findUri.end,s=n.trim||i.findUri.trim,a=n.parens||i.findUri.parens,u=/[a-z0-9-]=["']?$/i;for(r.lastIndex=0;;){var c=r.exec(e);if(!c)break;var l=c.index;if(n.ignoreHtml){var d=e.slice(Math.max(l-3,0),l);if(d&&u.test(d))continue}for(var f=l+e.slice(l).search(o),p=e.slice(l,f),h=-1;;){var m=a.exec(p);if(!m)break;var g=m.index+m[0].length;h=Math.max(h,g)}if(!((p=h>-1?p.slice(0,h)+p.slice(h).replace(s,""):p.replace(s,"")).length<=c[0].length||n.ignore&&n.ignore.test(p))){var y=t(p,l,f=l+p.length,e);void 0!==y?(y=String(y),e=e.slice(0,l)+y+e.slice(f),r.lastIndex=l+y.length):r.lastIndex=f}}return r.lastIndex=0,e},i.ensureValidHostname=function(t,n){var r=!!t,o=!1;if(!!n&&(o=f(i.hostProtocols,n)),o&&!r)throw new TypeError("Hostname cannot be empty, if protocol is "+n);if(t&&t.match(i.invalid_hostname_characters)){if(!e)throw new TypeError('Hostname "'+t+'" contains characters other than [A-Z0-9.-:_] and Punycode.js is not available');if(e.toASCII(t).match(i.invalid_hostname_characters))throw new TypeError('Hostname "'+t+'" contains characters other than [A-Z0-9.-:_]')}},i.ensureValidPort=function(e){if(e){var t=Number(e);if(!(/^[0-9]+$/.test(t)&&t>0&&t<65536))throw new TypeError('Port "'+e+'" is not a valid port')}},i.noConflict=function(e){if(e){var t={URI:this.noConflict()};return r.URITemplate&&"function"==typeof r.URITemplate.noConflict&&(t.URITemplate=r.URITemplate.noConflict()),r.IPv6&&"function"==typeof r.IPv6.noConflict&&(t.IPv6=r.IPv6.noConflict()),r.SecondLevelDomains&&"function"==typeof r.SecondLevelDomains.noConflict&&(t.SecondLevelDomains=r.SecondLevelDomains.noConflict()),t}return r.URI===this&&(r.URI=o),this},s.build=function(e){return!0===e?this._deferred_build=!0:(void 0===e||this._deferred_build)&&(this._string=i.build(this._parts),this._deferred_build=!1),this},s.clone=function(){return new i(this)},s.valueOf=s.toString=function(){return this.build(!1)._string},s.protocol=_("protocol"),s.username=_("username"),s.password=_("password"),s.hostname=_("hostname"),s.port=_("port"),s.query=b("query","?"),s.fragment=b("fragment","#"),s.search=function(e,t){var n=this.query(e,t);return"string"==typeof n&&n.length?"?"+n:n},s.hash=function(e,t){var n=this.fragment(e,t);return"string"==typeof n&&n.length?"#"+n:n},s.pathname=function(e,t){if(void 0===e||!0===e){var n=this._parts.path||(this._parts.hostname?"/":"");return e?(this._parts.urn?i.decodeUrnPath:i.decodePath)(n):n}return this._parts.urn?this._parts.path=e?i.recodeUrnPath(e):"":this._parts.path=e?i.recodePath(e):"/",this.build(!t),this},s.path=s.pathname,s.href=function(e,t){var n;if(void 0===e)return this.toString();this._string="",this._parts=i._parts();var r=e instanceof i,o="object"==typeof e&&(e.hostname||e.path||e.pathname);if(e.nodeName&&(e=e[i.getDomAttribute(e)]||"",o=!1),!r&&o&&void 0!==e.pathname&&(e=e.toString()),"string"==typeof e||e instanceof String)this._parts=i.parse(String(e),this._parts);else{if(!r&&!o)throw new TypeError("invalid input");var s=r?e._parts:e;for(n in s)"query"!==n&&a.call(this._parts,n)&&(this._parts[n]=s[n]);s.query&&this.query(s.query,!1)}return this.build(!t),this},s.is=function(e){var t=!1,r=!1,o=!1,s=!1,a=!1,u=!1,c=!1,l=!this._parts.urn;switch(this._parts.hostname&&(l=!1,r=i.ip4_expression.test(this._parts.hostname),o=i.ip6_expression.test(this._parts.hostname),a=(s=!(t=r||o))&&n&&n.has(this._parts.hostname),u=s&&i.idn_expression.test(this._parts.hostname),c=s&&i.punycode_expression.test(this._parts.hostname)),e.toLowerCase()){case"relative":return l;case"absolute":return!l;case"domain":case"name":return s;case"sld":return a;case"ip":return t;case"ip4":case"ipv4":case"inet4":return r;case"ip6":case"ipv6":case"inet6":return o;case"idn":return u;case"url":return!this._parts.urn;case"urn":return!!this._parts.urn;case"punycode":return c}return null};var x=s.protocol,S=s.port,A=s.hostname;s.protocol=function(e,t){if(e&&!(e=e.replace(/:(\/\/)?$/,"")).match(i.protocol_expression))throw new TypeError('Protocol "'+e+"\" contains characters other than [A-Z0-9.+-] or doesn't start with [A-Z]");return x.call(this,e,t)},s.scheme=s.protocol,s.port=function(e,t){return this._parts.urn?void 0===e?"":this:(void 0!==e&&(0===e&&(e=null),e&&(":"===(e+="").charAt(0)&&(e=e.substring(1)),i.ensureValidPort(e))),S.call(this,e,t))},s.hostname=function(e,t){if(this._parts.urn)return void 0===e?"":this;if(void 0!==e){var n={preventInvalidHostname:this._parts.preventInvalidHostname};if("/"!==i.parseHost(e,n))throw new TypeError('Hostname "'+e+'" contains characters other than [A-Z0-9.-]');e=n.hostname,this._parts.preventInvalidHostname&&i.ensureValidHostname(e,this._parts.protocol)}return A.call(this,e,t)},s.origin=function(e,t){if(this._parts.urn)return void 0===e?"":this;if(void 0===e){var n=this.protocol();return this.authority()?(n?n+"://":"")+this.authority():""}var r=i(e);return this.protocol(r.protocol()).authority(r.authority()).build(!t),this},s.host=function(e,t){if(this._parts.urn)return void 0===e?"":this;if(void 0===e)return this._parts.hostname?i.buildHost(this._parts):"";if("/"!==i.parseHost(e,this._parts))throw new TypeError('Hostname "'+e+'" contains characters other than [A-Z0-9.-]');return this.build(!t),this},s.authority=function(e,t){if(this._parts.urn)return void 0===e?"":this;if(void 0===e)return this._parts.hostname?i.buildAuthority(this._parts):"";if("/"!==i.parseAuthority(e,this._parts))throw new TypeError('Hostname "'+e+'" contains characters other than [A-Z0-9.-]');return this.build(!t),this},s.userinfo=function(e,t){if(this._parts.urn)return void 0===e?"":this;if(void 0===e){var n=i.buildUserinfo(this._parts);return n?n.substring(0,n.length-1):n}return"@"!==e[e.length-1]&&(e+="@"),i.parseUserinfo(e,this._parts),this.build(!t),this},s.resource=function(e,t){var n;return void 0===e?this.path()+this.search()+this.hash():(n=i.parse(e),this._parts.path=n.path,this._parts.query=n.query,this._parts.fragment=n.fragment,this.build(!t),this)},s.subdomain=function(e,t){if(this._parts.urn)return void 0===e?"":this;if(void 0===e){if(!this._parts.hostname||this.is("IP"))return"";var n=this._parts.hostname.length-this.domain().length-1;return this._parts.hostname.substring(0,n)||""}var r=this._parts.hostname.length-this.domain().length,o=this._parts.hostname.substring(0,r),s=new RegExp("^"+u(o));if(e&&"."!==e.charAt(e.length-1)&&(e+="."),-1!==e.indexOf(":"))throw new TypeError("Domains cannot contain colons");return e&&i.ensureValidHostname(e,this._parts.protocol),this._parts.hostname=this._parts.hostname.replace(s,e),this.build(!t),this},s.domain=function(e,t){if(this._parts.urn)return void 0===e?"":this;if("boolean"==typeof e&&(t=e,e=void 0),void 0===e){if(!this._parts.hostname||this.is("IP"))return"";var n=this._parts.hostname.match(/\./g);if(n&&n.length<2)return this._parts.hostname;var r=this._parts.hostname.length-this.tld(t).length-1;return r=this._parts.hostname.lastIndexOf(".",r-1)+1,this._parts.hostname.substring(r)||""}if(!e)throw new TypeError("cannot set domain empty");if(-1!==e.indexOf(":"))throw new TypeError("Domains cannot contain colons");if(i.ensureValidHostname(e,this._parts.protocol),!this._parts.hostname||this.is("IP"))this._parts.hostname=e;else{var o=new RegExp(u(this.domain())+"$");this._parts.hostname=this._parts.hostname.replace(o,e)}return this.build(!t),this},s.tld=function(e,t){if(this._parts.urn)return void 0===e?"":this;if("boolean"==typeof e&&(t=e,e=void 0),void 0===e){if(!this._parts.hostname||this.is("IP"))return"";var r=this._parts.hostname.lastIndexOf("."),o=this._parts.hostname.substring(r+1);return!0!==t&&n&&n.list[o.toLowerCase()]&&n.get(this._parts.hostname)||o}var i;if(!e)throw new TypeError("cannot set TLD empty");if(e.match(/[^a-zA-Z0-9-]/)){if(!n||!n.is(e))throw new TypeError('TLD "'+e+'" contains characters other than [A-Z0-9]');i=new RegExp(u(this.tld())+"$"),this._parts.hostname=this._parts.hostname.replace(i,e)}else{if(!this._parts.hostname||this.is("IP"))throw new ReferenceError("cannot set TLD on non-domain host");i=new RegExp(u(this.tld())+"$"),this._parts.hostname=this._parts.hostname.replace(i,e)}return this.build(!t),this},s.directory=function(e,t){if(this._parts.urn)return void 0===e?"":this;if(void 0===e||!0===e){if(!this._parts.path&&!this._parts.hostname)return"";if("/"===this._parts.path)return"/";var n=this._parts.path.length-this.filename().length-1,r=this._parts.path.substring(0,n)||(this._parts.hostname?"/":"");return e?i.decodePath(r):r}var o=this._parts.path.length-this.filename().length,s=this._parts.path.substring(0,o),a=new RegExp("^"+u(s));return this.is("relative")||(e||(e="/"),"/"!==e.charAt(0)&&(e="/"+e)),e&&"/"!==e.charAt(e.length-1)&&(e+="/"),e=i.recodePath(e),this._parts.path=this._parts.path.replace(a,e),this.build(!t),this},s.filename=function(e,t){if(this._parts.urn)return void 0===e?"":this;if("string"!=typeof e){if(!this._parts.path||"/"===this._parts.path)return"";var n=this._parts.path.lastIndexOf("/"),r=this._parts.path.substring(n+1);return e?i.decodePathSegment(r):r}var o=!1;"/"===e.charAt(0)&&(e=e.substring(1)),e.match(/\.?\//)&&(o=!0);var s=new RegExp(u(this.filename())+"$");return e=i.recodePath(e),this._parts.path=this._parts.path.replace(s,e),o?this.normalizePath(t):this.build(!t),this},s.suffix=function(e,t){if(this._parts.urn)return void 0===e?"":this;if(void 0===e||!0===e){if(!this._parts.path||"/"===this._parts.path)return"";var n,r,o=this.filename(),s=o.lastIndexOf(".");return-1===s?"":(n=o.substring(s+1),r=/^[a-z0-9%]+$/i.test(n)?n:"",e?i.decodePathSegment(r):r)}"."===e.charAt(0)&&(e=e.substring(1));var a,c=this.suffix();if(c)a=e?new RegExp(u(c)+"$"):new RegExp(u("."+c)+"$");else{if(!e)return this;this._parts.path+="."+i.recodePath(e)}return a&&(e=i.recodePath(e),this._parts.path=this._parts.path.replace(a,e)),this.build(!t),this},s.segment=function(e,t,n){var r=this._parts.urn?":":"/",o=this.path(),i="/"===o.substring(0,1),s=o.split(r);if(void 0!==e&&"number"!=typeof e&&(n=t,t=e,e=void 0),void 0!==e&&"number"!=typeof e)throw new Error('Bad segment "'+e+'", must be 0-based integer');if(i&&s.shift(),e<0&&(e=Math.max(s.length+e,0)),void 0===t)return void 0===e?s:s[e];if(null===e||void 0===s[e])if(l(t)){s=[];for(var a=0,u=t.length;a<u;a++)(t[a].length||s.length&&s[s.length-1].length)&&(s.length&&!s[s.length-1].length&&s.pop(),s.push(h(t[a])))}else(t||"string"==typeof t)&&(t=h(t),""===s[s.length-1]?s[s.length-1]=t:s.push(t));else t?s[e]=h(t):s.splice(e,1);return i&&s.unshift(""),this.path(s.join(r),n)},s.segmentCoded=function(e,t,n){var r,o,s;if("number"!=typeof e&&(n=t,t=e,e=void 0),void 0===t){if(l(r=this.segment(e,t,n)))for(o=0,s=r.length;o<s;o++)r[o]=i.decode(r[o]);else r=void 0!==r?i.decode(r):void 0;return r}if(l(t))for(o=0,s=t.length;o<s;o++)t[o]=i.encode(t[o]);else t="string"==typeof t||t instanceof String?i.encode(t):t;return this.segment(e,t,n)};var E=s.query;return s.query=function(e,t){if(!0===e)return i.parseQuery(this._parts.query,this._parts.escapeQuerySpace);if("function"==typeof e){var n=i.parseQuery(this._parts.query,this._parts.escapeQuerySpace),r=e.call(this,n);return this._parts.query=i.buildQuery(r||n,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),this.build(!t),this}return void 0!==e&&"string"!=typeof e?(this._parts.query=i.buildQuery(e,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),this.build(!t),this):E.call(this,e,t)},s.setQuery=function(e,t,n){var r=i.parseQuery(this._parts.query,this._parts.escapeQuerySpace);if("string"==typeof e||e instanceof String)r[e]=void 0!==t?t:null;else{if("object"!=typeof e)throw new TypeError("URI.addQuery() accepts an object, string as the name parameter");for(var o in e)a.call(e,o)&&(r[o]=e[o])}return this._parts.query=i.buildQuery(r,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),"string"!=typeof e&&(n=t),this.build(!n),this},s.addQuery=function(e,t,n){var r=i.parseQuery(this._parts.query,this._parts.escapeQuerySpace);return i.addQuery(r,e,void 0===t?null:t),this._parts.query=i.buildQuery(r,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),"string"!=typeof e&&(n=t),this.build(!n),this},s.removeQuery=function(e,t,n){var r=i.parseQuery(this._parts.query,this._parts.escapeQuerySpace);return i.removeQuery(r,e,t),this._parts.query=i.buildQuery(r,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),"string"!=typeof e&&(n=t),this.build(!n),this},s.hasQuery=function(e,t,n){var r=i.parseQuery(this._parts.query,this._parts.escapeQuerySpace);return i.hasQuery(r,e,t,n)},s.setSearch=s.setQuery,s.addSearch=s.addQuery,s.removeSearch=s.removeQuery,s.hasSearch=s.hasQuery,s.normalize=function(){return this._parts.urn?this.normalizeProtocol(!1).normalizePath(!1).normalizeQuery(!1).normalizeFragment(!1).build():this.normalizeProtocol(!1).normalizeHostname(!1).normalizePort(!1).normalizePath(!1).normalizeQuery(!1).normalizeFragment(!1).build()},s.normalizeProtocol=function(e){return"string"==typeof this._parts.protocol&&(this._parts.protocol=this._parts.protocol.toLowerCase(),this.build(!e)),this},s.normalizeHostname=function(n){return this._parts.hostname&&(this.is("IDN")&&e?this._parts.hostname=e.toASCII(this._parts.hostname):this.is("IPv6")&&t&&(this._parts.hostname=t.best(this._parts.hostname)),this._parts.hostname=this._parts.hostname.toLowerCase(),this.build(!n)),this},s.normalizePort=function(e){return"string"==typeof this._parts.protocol&&this._parts.port===i.defaultPorts[this._parts.protocol]&&(this._parts.port=null,this.build(!e)),this},s.normalizePath=function(e){var t,n=this._parts.path;if(!n)return this;if(this._parts.urn)return this._parts.path=i.recodeUrnPath(this._parts.path),this.build(!e),this;if("/"===this._parts.path)return this;var r,o,s="";for("/"!==(n=i.recodePath(n)).charAt(0)&&(t=!0,n="/"+n),"/.."!==n.slice(-3)&&"/."!==n.slice(-2)||(n+="/"),n=n.replace(/(\/(\.\/)+)|(\/\.$)/g,"/").replace(/\/{2,}/g,"/"),t&&(s=n.substring(1).match(/^(\.\.\/)+/)||"")&&(s=s[0]);-1!==(r=n.search(/\/\.\.(\/|$)/));)0!==r?(-1===(o=n.substring(0,r).lastIndexOf("/"))&&(o=r),n=n.substring(0,o)+n.substring(r+3)):n=n.substring(3);return t&&this.is("relative")&&(n=s+n.substring(1)),this._parts.path=n,this.build(!e),this},s.normalizePathname=s.normalizePath,s.normalizeQuery=function(e){return"string"==typeof this._parts.query&&(this._parts.query.length?this.query(i.parseQuery(this._parts.query,this._parts.escapeQuerySpace)):this._parts.query=null,this.build(!e)),this},s.normalizeFragment=function(e){return this._parts.fragment||(this._parts.fragment=null,this.build(!e)),this},s.normalizeSearch=s.normalizeQuery,s.normalizeHash=s.normalizeFragment,s.iso8859=function(){var e=i.encode,t=i.decode;i.encode=escape,i.decode=decodeURIComponent;try{this.normalize()}finally{i.encode=e,i.decode=t}return this},s.unicode=function(){var e=i.encode,t=i.decode;i.encode=g,i.decode=unescape;try{this.normalize()}finally{i.encode=e,i.decode=t}return this},s.readable=function(){var t=this.clone();t.username("").password("").normalize();var n="";if(t._parts.protocol&&(n+=t._parts.protocol+"://"),t._parts.hostname&&(t.is("punycode")&&e?(n+=e.toUnicode(t._parts.hostname),t._parts.port&&(n+=":"+t._parts.port)):n+=t.host()),t._parts.hostname&&t._parts.path&&"/"!==t._parts.path.charAt(0)&&(n+="/"),n+=t.path(!0),t._parts.query){for(var r="",o=0,s=t._parts.query.split("&"),a=s.length;o<a;o++){var u=(s[o]||"").split("=");r+="&"+i.decodeQuery(u[0],this._parts.escapeQuerySpace).replace(/&/g,"%26"),void 0!==u[1]&&(r+="="+i.decodeQuery(u[1],this._parts.escapeQuerySpace).replace(/&/g,"%26"))}n+="?"+r.substring(1)}return n+=i.decodeQuery(t.hash(),!0)},s.absoluteTo=function(e){var t,n,r,o=this.clone(),s=["protocol","username","password","hostname","port"];if(this._parts.urn)throw new Error("URNs do not have any generally defined hierarchical components");if(e instanceof i||(e=new i(e)),o._parts.protocol)return o;if(o._parts.protocol=e._parts.protocol,this._parts.hostname)return o;for(n=0;r=s[n];n++)o._parts[r]=e._parts[r];return o._parts.path?(".."===o._parts.path.substring(-2)&&(o._parts.path+="/"),"/"!==o.path().charAt(0)&&(t=(t=e.directory())||(0===e.path().indexOf("/")?"/":""),o._parts.path=(t?t+"/":"")+o._parts.path,o.normalizePath())):(o._parts.path=e._parts.path,o._parts.query||(o._parts.query=e._parts.query)),o.build(),o},s.relativeTo=function(e){var t,n,r,o,s,a=this.clone().normalize();if(a._parts.urn)throw new Error("URNs do not have any generally defined hierarchical components");if(e=new i(e).normalize(),t=a._parts,n=e._parts,o=a.path(),s=e.path(),"/"!==o.charAt(0))throw new Error("URI is already relative");if("/"!==s.charAt(0))throw new Error("Cannot calculate a URI relative to another relative URI");if(t.protocol===n.protocol&&(t.protocol=null),t.username!==n.username||t.password!==n.password)return a.build();if(null!==t.protocol||null!==t.username||null!==t.password)return a.build();if(t.hostname!==n.hostname||t.port!==n.port)return a.build();if(t.hostname=null,t.port=null,o===s)return t.path="",a.build();if(!(r=i.commonPath(o,s)))return a.build();var u=n.path.substring(r.length).replace(/[^\/]*$/,"").replace(/.*?\//g,"../");return t.path=u+t.path.substring(r.length)||"./",a.build()},s.equals=function(e){var t,n,r,o,s,u=this.clone(),c=new i(e),d={};if(u.normalize(),c.normalize(),u.toString()===c.toString())return!0;if(r=u.query(),o=c.query(),u.query(""),c.query(""),u.toString()!==c.toString())return!1;if(r.length!==o.length)return!1;for(s in t=i.parseQuery(r,this._parts.escapeQuerySpace),n=i.parseQuery(o,this._parts.escapeQuerySpace),t)if(a.call(t,s)){if(l(t[s])){if(!p(t[s],n[s]))return!1}else if(t[s]!==n[s])return!1;d[s]=!0}for(s in n)if(a.call(n,s)&&!d[s])return!1;return!0},s.preventInvalidHostname=function(e){return this._parts.preventInvalidHostname=!!e,this},s.duplicateQueryParameters=function(e){return this._parts.duplicateQueryParameters=!!e,this},s.escapeQuerySpace=function(e){return this._parts.escapeQuerySpace=!!e,this},i},(
/*!
	 * URI.js - Mutating URLs
	 *
	 * Version: 1.19.11
	 *
	 * Author: Rodney Rehm
	 * Web: http://medialize.github.io/URI.js/
	 *
	 * Licensed under
	 *   MIT License http://www.opensource.org/licenses/mit-license
	 *
	 */
kt=qt).exports?kt.exports=Nt((Tt||(Tt=1,function(e,t){!function(n){var r=t&&!t.nodeType&&t,o=e&&!e.nodeType&&e,i="object"==typeof c&&c;i.global!==i&&i.window!==i&&i.self!==i||(n=i);var s,a,u=**********,l=36,d=1,f=26,p=38,h=700,m=72,g=128,y="-",v=/^xn--/,w=/[^\x20-\x7E]/,C=/[\x2E\u3002\uFF0E\uFF61]/g,_={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},b=l-d,x=Math.floor,S=String.fromCharCode;function A(e){throw new RangeError(_[e])}function E(e,t){for(var n=e.length,r=[];n--;)r[n]=t(e[n]);return r}function O(e,t){var n=e.split("@"),r="";return n.length>1&&(r=n[0]+"@",e=n[1]),r+E((e=e.replace(C,".")).split("."),t).join(".")}function P(e){for(var t,n,r=[],o=0,i=e.length;o<i;)(t=e.charCodeAt(o++))>=55296&&t<=56319&&o<i?56320==(64512&(n=e.charCodeAt(o++)))?r.push(((1023&t)<<10)+(1023&n)+65536):(r.push(t),o--):r.push(t);return r}function I(e){return E(e,(function(e){var t="";return e>65535&&(t+=S((e-=65536)>>>10&1023|55296),e=56320|1023&e),t+S(e)})).join("")}function R(e,t){return e+22+75*(e<26)-((0!=t)<<5)}function T(e,t,n){var r=0;for(e=n?x(e/h):e>>1,e+=x(e/t);e>b*f>>1;r+=l)e=x(e/b);return x(r+(b+1)*e/(e+p))}function q(e){var t,n,r,o,i,s,a,c,p,h,v,w=[],C=e.length,_=0,b=g,S=m;for((n=e.lastIndexOf(y))<0&&(n=0),r=0;r<n;++r)e.charCodeAt(r)>=128&&A("not-basic"),w.push(e.charCodeAt(r));for(o=n>0?n+1:0;o<C;){for(i=_,s=1,a=l;o>=C&&A("invalid-input"),((c=(v=e.charCodeAt(o++))-48<10?v-22:v-65<26?v-65:v-97<26?v-97:l)>=l||c>x((u-_)/s))&&A("overflow"),_+=c*s,!(c<(p=a<=S?d:a>=S+f?f:a-S));a+=l)s>x(u/(h=l-p))&&A("overflow"),s*=h;S=T(_-i,t=w.length+1,0==i),x(_/t)>u-b&&A("overflow"),b+=x(_/t),_%=t,w.splice(_++,0,b)}return I(w)}function z(e){var t,n,r,o,i,s,a,c,p,h,v,w,C,_,b,E=[];for(w=(e=P(e)).length,t=g,n=0,i=m,s=0;s<w;++s)(v=e[s])<128&&E.push(S(v));for(r=o=E.length,o&&E.push(y);r<w;){for(a=u,s=0;s<w;++s)(v=e[s])>=t&&v<a&&(a=v);for(a-t>x((u-n)/(C=r+1))&&A("overflow"),n+=(a-t)*C,t=a,s=0;s<w;++s)if((v=e[s])<t&&++n>u&&A("overflow"),v==t){for(c=n,p=l;!(c<(h=p<=i?d:p>=i+f?f:p-i));p+=l)b=c-h,_=l-h,E.push(S(R(h+b%_,0))),c=x(b/_);E.push(S(R(c,0))),i=T(n,C,r==o),n=0,++r}++n,++t}return E.join("")}if(s={version:"1.3.2",ucs2:{decode:P,encode:I},decode:q,encode:z,toASCII:function(e){return O(e,(function(e){return w.test(e)?"xn--"+z(e):e}))},toUnicode:function(e){return O(e,(function(e){return v.test(e)?q(e.slice(4).toLowerCase()):e}))}},r&&o)if(e.exports==r)o.exports=s;else for(a in s)s.hasOwnProperty(a)&&(r[a]=s[a]);else n.punycode=s}(c)}(zt,zt.exports)),zt.exports),function(){return Mt||(Mt=1,t=c,n=function(e){var t=e&&e.IPv6;return{best:function(e){var t,n,r=e.toLowerCase().split(":"),o=r.length,i=8;for(""===r[0]&&""===r[1]&&""===r[2]?(r.shift(),r.shift()):""===r[0]&&""===r[1]?r.shift():""===r[o-1]&&""===r[o-2]&&r.pop(),-1!==r[(o=r.length)-1].indexOf(".")&&(i=7),t=0;t<o&&""!==r[t];t++);if(t<i)for(r.splice(t,1,"0000");r.length<i;)r.splice(t,0,"0000");for(var s=0;s<i;s++){n=r[s].split("");for(var a=0;a<3&&"0"===n[0]&&n.length>1;a++)n.splice(0,1);r[s]=n.join("")}var u=-1,c=0,l=0,d=-1,f=!1;for(s=0;s<i;s++)f?"0"===r[s]?l+=1:(f=!1,l>c&&(u=d,c=l)):"0"===r[s]&&(f=!0,d=s,l=1);l>c&&(u=d,c=l),c>1&&r.splice(u,c,""),o=r.length;var p="";for(""===r[0]&&(p=":"),s=0;s<o&&(p+=r[s],s!==o-1);s++)p+=":";return""===r[o-1]&&(p+=":"),p},noConflict:function(){return e.IPv6===this&&(e.IPv6=t),this}}},(e=Dt).exports?e.exports=n():t.IPv6=n(t)),Dt.exports;var e,t,n}(),function(){return Ut||(Ut=1,t=c,n=function(e){var t=e&&e.SecondLevelDomains,n={list:{ac:" com gov mil net org ",ae:" ac co gov mil name net org pro sch ",af:" com edu gov net org ",al:" com edu gov mil net org ",ao:" co ed gv it og pb ",ar:" com edu gob gov int mil net org tur ",at:" ac co gv or ",au:" asn com csiro edu gov id net org ",ba:" co com edu gov mil net org rs unbi unmo unsa untz unze ",bb:" biz co com edu gov info net org store tv ",bh:" biz cc com edu gov info net org ",bn:" com edu gov net org ",bo:" com edu gob gov int mil net org tv ",br:" adm adv agr am arq art ato b bio blog bmd cim cng cnt com coop ecn edu eng esp etc eti far flog fm fnd fot fst g12 ggf gov imb ind inf jor jus lel mat med mil mus net nom not ntr odo org ppg pro psc psi qsl rec slg srv tmp trd tur tv vet vlog wiki zlg ",bs:" com edu gov net org ",bz:" du et om ov rg ",ca:" ab bc mb nb nf nl ns nt nu on pe qc sk yk ",ck:" biz co edu gen gov info net org ",cn:" ac ah bj com cq edu fj gd gov gs gx gz ha hb he hi hl hn jl js jx ln mil net nm nx org qh sc sd sh sn sx tj tw xj xz yn zj ",co:" com edu gov mil net nom org ",cr:" ac c co ed fi go or sa ",cy:" ac biz com ekloges gov ltd name net org parliament press pro tm ",do:" art com edu gob gov mil net org sld web ",dz:" art asso com edu gov net org pol ",ec:" com edu fin gov info med mil net org pro ",eg:" com edu eun gov mil name net org sci ",er:" com edu gov ind mil net org rochest w ",es:" com edu gob nom org ",et:" biz com edu gov info name net org ",fj:" ac biz com info mil name net org pro ",fk:" ac co gov net nom org ",fr:" asso com f gouv nom prd presse tm ",gg:" co net org ",gh:" com edu gov mil org ",gn:" ac com gov net org ",gr:" com edu gov mil net org ",gt:" com edu gob ind mil net org ",gu:" com edu gov net org ",hk:" com edu gov idv net org ",hu:" 2000 agrar bolt casino city co erotica erotika film forum games hotel info ingatlan jogasz konyvelo lakas media news org priv reklam sex shop sport suli szex tm tozsde utazas video ",id:" ac co go mil net or sch web ",il:" ac co gov idf k12 muni net org ",in:" ac co edu ernet firm gen gov i ind mil net nic org res ",iq:" com edu gov i mil net org ",ir:" ac co dnssec gov i id net org sch ",it:" edu gov ",je:" co net org ",jo:" com edu gov mil name net org sch ",jp:" ac ad co ed go gr lg ne or ",ke:" ac co go info me mobi ne or sc ",kh:" com edu gov mil net org per ",ki:" biz com de edu gov info mob net org tel ",km:" asso com coop edu gouv k medecin mil nom notaires pharmaciens presse tm veterinaire ",kn:" edu gov net org ",kr:" ac busan chungbuk chungnam co daegu daejeon es gangwon go gwangju gyeongbuk gyeonggi gyeongnam hs incheon jeju jeonbuk jeonnam k kg mil ms ne or pe re sc seoul ulsan ",kw:" com edu gov net org ",ky:" com edu gov net org ",kz:" com edu gov mil net org ",lb:" com edu gov net org ",lk:" assn com edu gov grp hotel int ltd net ngo org sch soc web ",lr:" com edu gov net org ",lv:" asn com conf edu gov id mil net org ",ly:" com edu gov id med net org plc sch ",ma:" ac co gov m net org press ",mc:" asso tm ",me:" ac co edu gov its net org priv ",mg:" com edu gov mil nom org prd tm ",mk:" com edu gov inf name net org pro ",ml:" com edu gov net org presse ",mn:" edu gov org ",mo:" com edu gov net org ",mt:" com edu gov net org ",mv:" aero biz com coop edu gov info int mil museum name net org pro ",mw:" ac co com coop edu gov int museum net org ",mx:" com edu gob net org ",my:" com edu gov mil name net org sch ",nf:" arts com firm info net other per rec store web ",ng:" biz com edu gov mil mobi name net org sch ",ni:" ac co com edu gob mil net nom org ",np:" com edu gov mil net org ",nr:" biz com edu gov info net org ",om:" ac biz co com edu gov med mil museum net org pro sch ",pe:" com edu gob mil net nom org sld ",ph:" com edu gov i mil net ngo org ",pk:" biz com edu fam gob gok gon gop gos gov net org web ",pl:" art bialystok biz com edu gda gdansk gorzow gov info katowice krakow lodz lublin mil net ngo olsztyn org poznan pwr radom slupsk szczecin torun warszawa waw wroc wroclaw zgora ",pr:" ac biz com edu est gov info isla name net org pro prof ",ps:" com edu gov net org plo sec ",pw:" belau co ed go ne or ",ro:" arts com firm info nom nt org rec store tm www ",rs:" ac co edu gov in org ",sb:" com edu gov net org ",sc:" com edu gov net org ",sh:" co com edu gov net nom org ",sl:" com edu gov net org ",st:" co com consulado edu embaixada gov mil net org principe saotome store ",sv:" com edu gob org red ",sz:" ac co org ",tr:" av bbs bel biz com dr edu gen gov info k12 name net org pol tel tsk tv web ",tt:" aero biz cat co com coop edu gov info int jobs mil mobi museum name net org pro tel travel ",tw:" club com ebiz edu game gov idv mil net org ",mu:" ac co com gov net or org ",mz:" ac co edu gov org ",na:" co com ",nz:" ac co cri geek gen govt health iwi maori mil net org parliament school ",pa:" abo ac com edu gob ing med net nom org sld ",pt:" com edu gov int net nome org publ ",py:" com edu gov mil net org ",qa:" com edu gov mil net org ",re:" asso com nom ",ru:" ac adygeya altai amur arkhangelsk astrakhan bashkiria belgorod bir bryansk buryatia cbg chel chelyabinsk chita chukotka chuvashia com dagestan e-burg edu gov grozny int irkutsk ivanovo izhevsk jar joshkar-ola kalmykia kaluga kamchatka karelia kazan kchr kemerovo khabarovsk khakassia khv kirov koenig komi kostroma kranoyarsk kuban kurgan kursk lipetsk magadan mari mari-el marine mil mordovia mosreg msk murmansk nalchik net nnov nov novosibirsk nsk omsk orenburg org oryol penza perm pp pskov ptz rnd ryazan sakhalin samara saratov simbirsk smolensk spb stavropol stv surgut tambov tatarstan tom tomsk tsaritsyn tsk tula tuva tver tyumen udm udmurtia ulan-ude vladikavkaz vladimir vladivostok volgograd vologda voronezh vrn vyatka yakutia yamal yekaterinburg yuzhno-sakhalinsk ",rw:" ac co com edu gouv gov int mil net ",sa:" com edu gov med net org pub sch ",sd:" com edu gov info med net org tv ",se:" a ac b bd c d e f g h i k l m n o org p parti pp press r s t tm u w x y z ",sg:" com edu gov idn net org per ",sn:" art com edu gouv org perso univ ",sy:" com edu gov mil net news org ",th:" ac co go in mi net or ",tj:" ac biz co com edu go gov info int mil name net nic org test web ",tn:" agrinet com defense edunet ens fin gov ind info intl mincom nat net org perso rnrt rns rnu tourism ",tz:" ac co go ne or ",ua:" biz cherkassy chernigov chernovtsy ck cn co com crimea cv dn dnepropetrovsk donetsk dp edu gov if in ivano-frankivsk kh kharkov kherson khmelnitskiy kiev kirovograd km kr ks kv lg lugansk lutsk lviv me mk net nikolaev od odessa org pl poltava pp rovno rv sebastopol sumy te ternopil uzhgorod vinnica vn zaporizhzhe zhitomir zp zt ",ug:" ac co go ne or org sc ",uk:" ac bl british-library co cym gov govt icnet jet lea ltd me mil mod national-library-scotland nel net nhs nic nls org orgn parliament plc police sch scot soc ",us:" dni fed isa kids nsn ",uy:" com edu gub mil net org ",ve:" co com edu gob info mil net org web ",vi:" co com k12 net org ",vn:" ac biz com edu gov health info int name net org pro ",ye:" co com gov ltd me net org plc ",yu:" ac co edu gov org ",za:" ac agric alt bourse city co cybernet db edu gov grondar iaccess imt inca landesign law mil net ngo nis nom olivetti org pix school tm web ",zm:" ac co com edu gov net org sch ",com:"ar br cn de eu gb gr hu jpn kr no qc ru sa se uk us uy za ",net:"gb jp se uk ",org:"ae",de:"com "},has:function(e){var t=e.lastIndexOf(".");if(t<=0||t>=e.length-1)return!1;var r=e.lastIndexOf(".",t-1);if(r<=0||r>=t-1)return!1;var o=n.list[e.slice(t+1)];return!!o&&o.indexOf(" "+e.slice(r+1,t)+" ")>=0},is:function(e){var t=e.lastIndexOf(".");if(t<=0||t>=e.length-1)return!1;if(e.lastIndexOf(".",t-1)>=0)return!1;var r=n.list[e.slice(t+1)];return!!r&&r.indexOf(" "+e.slice(0,t)+" ")>=0},get:function(e){var t=e.lastIndexOf(".");if(t<=0||t>=e.length-1)return null;var r=e.lastIndexOf(".",t-1);if(r<=0||r>=t-1)return null;var o=n.list[e.slice(t+1)];return o?o.indexOf(" "+e.slice(r+1,t)+" ")<0?null:e.slice(r+1):null},noConflict:function(){return e.SecondLevelDomains===this&&(e.SecondLevelDomains=t),this}};return n},(e=jt).exports?e.exports=n():t.SecondLevelDomains=n(t)),jt.exports;var e,t,n}()):Ft.URI=Nt(Ft.punycode,Ft.IPv6,Ft.SecondLevelDomains,Ft);var Bt=qt.exports;function Vt(e,t){if(null===e||"object"!=typeof e)return e;t=r.defaultValue(t,!1);const n=new e.constructor;for(const r in e)if(e.hasOwnProperty(r)){let o=e[r];t&&(o=Vt(o,t)),n[r]=o}return n}function Lt(){let e,t;const n=new Promise((function(n,r){e=n,t=r}));return{resolve:e,reject:t,promise:n}}function Qt(e,t){let n;return"undefined"!=typeof document&&(n=document),Qt._implementation(e,t,n)}Qt._implementation=function(e,t,n){if(!r.defined(t)){if(void 0===n)return e;t=r.defaultValue(n.baseURI,n.location.href)}const o=new Bt(e);return""!==o.scheme()?o.toString():o.absoluteTo(t).toString()};const $t={};function Wt(e,t,n){r.defined(t)||(t=e.width),r.defined(n)||(n=e.height);let o=$t[t];r.defined(o)||(o={},$t[t]=o);let i=o[n];if(!r.defined(i)){const e=document.createElement("canvas");e.width=t,e.height=n,i=e.getContext("2d"),i.globalCompositeOperation="copy",o[n]=i}return i.drawImage(e,0,0,t,n),i.getImageData(0,0,t,n).data}const Ht=/^blob:/i;function Yt(e){return Ht.test(e)}let Zt;const Jt=/^data:/i;function Xt(e){return Jt.test(e)}var Gt=Object.freeze({UNISSUED:0,ISSUED:1,ACTIVE:2,RECEIVED:3,CANCELLED:4,FAILED:5});var Kt=Object.freeze({TERRAIN:0,IMAGERY:1,TILES3D:2,OTHER:3});function en(e){e=r.defaultValue(e,r.defaultValue.EMPTY_OBJECT);const t=r.defaultValue(e.throttleByServer,!1),n=r.defaultValue(e.throttle,!1);this.url=e.url,this.requestFunction=e.requestFunction,this.cancelFunction=e.cancelFunction,this.priorityFunction=e.priorityFunction,this.priority=r.defaultValue(e.priority,0),this.throttle=n,this.throttleByServer=t,this.type=r.defaultValue(e.type,Kt.OTHER),this.serverKey=e.serverKey,this.state=Gt.UNISSUED,this.deferred=void 0,this.cancelled=!1}function tn(e,t,n){this.statusCode=e,this.response=t,this.responseHeaders=n,"string"==typeof this.responseHeaders&&(this.responseHeaders=function(e){const t={};if(!e)return t;const n=e.split("\r\n");for(let e=0;e<n.length;++e){const r=n[e],o=r.indexOf(": ");if(o>0){const e=r.substring(0,o),n=r.substring(o+2);t[e]=n}}return t}(this.responseHeaders))}function nn(){this._listeners=[],this._scopes=[],this._toRemove=[],this._insideRaiseEvent=!1}function rn(e,t){return t-e}function on(e){this._comparator=e.comparator,this._array=[],this._length=0,this._maximumLength=void 0}function sn(e,t,n){const r=e[t];e[t]=e[n],e[n]=r}en.prototype.cancel=function(){this.cancelled=!0},en.prototype.clone=function(e){return r.defined(e)?(e.url=this.url,e.requestFunction=this.requestFunction,e.cancelFunction=this.cancelFunction,e.priorityFunction=this.priorityFunction,e.priority=this.priority,e.throttle=this.throttle,e.throttleByServer=this.throttleByServer,e.type=this.type,e.serverKey=this.serverKey,e.state=Gt.UNISSUED,e.deferred=void 0,e.cancelled=!1,e):new en(this)},tn.prototype.toString=function(){let e="Request has failed.";return r.defined(this.statusCode)&&(e+=` Status Code: ${this.statusCode}`),e},Object.defineProperties(nn.prototype,{numberOfListeners:{get:function(){return this._listeners.length-this._toRemove.length}}}),nn.prototype.addEventListener=function(e,t){this._listeners.push(e),this._scopes.push(t);const n=this;return function(){n.removeEventListener(e,t)}},nn.prototype.removeEventListener=function(e,t){const n=this._listeners,r=this._scopes;let o=-1;for(let i=0;i<n.length;i++)if(n[i]===e&&r[i]===t){o=i;break}return-1!==o&&(this._insideRaiseEvent?(this._toRemove.push(o),n[o]=void 0,r[o]=void 0):(n.splice(o,1),r.splice(o,1)),!0)},nn.prototype.raiseEvent=function(){let e;this._insideRaiseEvent=!0;const t=this._listeners,n=this._scopes;let o=t.length;for(e=0;e<o;e++){const o=t[e];r.defined(o)&&t[e].apply(n[e],arguments)}const i=this._toRemove;if(o=i.length,o>0){for(i.sort(rn),e=0;e<o;e++){const r=i[e];t.splice(r,1),n.splice(r,1)}i.length=0}this._insideRaiseEvent=!1},Object.defineProperties(on.prototype,{length:{get:function(){return this._length}},internalArray:{get:function(){return this._array}},maximumLength:{get:function(){return this._maximumLength},set:function(e){const t=this._length;if(e<t){const n=this._array;for(let r=e;r<t;++r)n[r]=void 0;this._length=e,n.length=e}this._maximumLength=e}},comparator:{get:function(){return this._comparator}}}),on.prototype.reserve=function(e){e=r.defaultValue(e,this._length),this._array.length=e},on.prototype.heapify=function(e){e=r.defaultValue(e,0);const t=this._length,n=this._comparator,o=this._array;let i=-1,s=!0;for(;s;){const r=2*(e+1),a=r-1;i=a<t&&n(o[a],o[e])<0?a:e,r<t&&n(o[r],o[i])<0&&(i=r),i!==e?(sn(o,i,e),e=i):s=!1}},on.prototype.resort=function(){const e=this._length;for(let t=Math.ceil(e/2);t>=0;--t)this.heapify(t)},on.prototype.insert=function(e){const t=this._array,n=this._comparator,o=this._maximumLength;let i,s=this._length++;for(s<t.length?t[s]=e:t.push(e);0!==s;){const e=Math.floor((s-1)/2);if(!(n(t[s],t[e])<0))break;sn(t,s,e),s=e}return r.defined(o)&&this._length>o&&(i=t[o],this._length=o),i},on.prototype.pop=function(e){if(e=r.defaultValue(e,0),0===this._length)return;const t=this._array,n=t[e];return sn(t,e,--this._length),this.heapify(e),t[this._length]=void 0,n};const an={numberOfAttemptedRequests:0,numberOfActiveRequests:0,numberOfCancelledRequests:0,numberOfCancelledActiveRequests:0,numberOfFailedRequests:0,numberOfActiveRequestsEver:0,lastNumberOfActiveRequests:0};let un=20;const cn=new on({comparator:function(e,t){return e.priority-t.priority}});cn.maximumLength=un,cn.reserve(un);const ln=[];let dn={};const fn="undefined"!=typeof document?new Bt(document.location.href):new Bt,pn=new nn;function hn(){}function mn(e){r.defined(e.priorityFunction)&&(e.priority=e.priorityFunction())}function gn(e){return e.state===Gt.UNISSUED&&(e.state=Gt.ISSUED,e.deferred=Lt()),e.deferred.promise}function yn(e){const t=gn(e);return e.state=Gt.ACTIVE,ln.push(e),++an.numberOfActiveRequests,++an.numberOfActiveRequestsEver,++dn[e.serverKey],e.requestFunction().then(function(e){return function(t){if(e.state===Gt.CANCELLED)return;const n=e.deferred;--an.numberOfActiveRequests,--dn[e.serverKey],pn.raiseEvent(),e.state=Gt.RECEIVED,e.deferred=void 0,n.resolve(t)}}(e)).catch(function(e){return function(t){e.state!==Gt.CANCELLED&&(++an.numberOfFailedRequests,--an.numberOfActiveRequests,--dn[e.serverKey],pn.raiseEvent(t),e.state=Gt.FAILED,e.deferred.reject(t))}}(e)),t}function vn(e){const t=e.state===Gt.ACTIVE;if(e.state=Gt.CANCELLED,++an.numberOfCancelledRequests,r.defined(e.deferred)){const t=e.deferred;e.deferred=void 0,t.reject()}t&&(--an.numberOfActiveRequests,--dn[e.serverKey],++an.numberOfCancelledActiveRequests),r.defined(e.cancelFunction)&&e.cancelFunction()}hn.maximumRequests=50,hn.maximumRequestsPerServer=6,hn.requestsByServer={"api.cesium.com:443":18,"assets.ion.cesium.com:443":18,"ibasemaps-api.arcgis.com:443":18,"tile.googleapis.com:443":18},hn.throttleRequests=!0,hn.debugShowStatistics=!1,hn.requestCompletedEvent=pn,Object.defineProperties(hn,{statistics:{get:function(){return an}},priorityHeapLength:{get:function(){return un},set:function(e){if(e<un)for(;cn.length>e;){vn(cn.pop())}un=e,cn.maximumLength=e,cn.reserve(e)}}}),hn.serverHasOpenSlots=function(e,t){t=r.defaultValue(t,1);const n=r.defaultValue(hn.requestsByServer[e],hn.maximumRequestsPerServer);return dn[e]+t<=n},hn.heapHasOpenSlots=function(e){return cn.length+e<=un},hn.update=function(){let e,t,n=0;const r=ln.length;for(e=0;e<r;++e)t=ln[e],t.cancelled&&vn(t),t.state===Gt.ACTIVE?n>0&&(ln[e-n]=t):++n;ln.length-=n;const o=cn.internalArray,i=cn.length;for(e=0;e<i;++e)mn(o[e]);cn.resort();const s=Math.max(hn.maximumRequests-ln.length,0);let a=0;for(;a<s&&cn.length>0;)t=cn.pop(),t.cancelled?vn(t):!t.throttleByServer||hn.serverHasOpenSlots(t.serverKey)?(yn(t),++a):vn(t);!function(){if(!hn.debugShowStatistics)return;0===an.numberOfActiveRequests&&an.lastNumberOfActiveRequests>0&&(an.numberOfAttemptedRequests>0&&(console.log(`Number of attempted requests: ${an.numberOfAttemptedRequests}`),an.numberOfAttemptedRequests=0),an.numberOfCancelledRequests>0&&(console.log(`Number of cancelled requests: ${an.numberOfCancelledRequests}`),an.numberOfCancelledRequests=0),an.numberOfCancelledActiveRequests>0&&(console.log(`Number of cancelled active requests: ${an.numberOfCancelledActiveRequests}`),an.numberOfCancelledActiveRequests=0),an.numberOfFailedRequests>0&&(console.log(`Number of failed requests: ${an.numberOfFailedRequests}`),an.numberOfFailedRequests=0));an.lastNumberOfActiveRequests=an.numberOfActiveRequests}()},hn.getServerKey=function(e){let t=new Bt(e);""===t.scheme()&&(t=t.absoluteTo(fn),t.normalize());let n=t.authority();/:/.test(n)||(n=`${n}:${"https"===t.scheme()?"443":"80"}`);const o=dn[n];return r.defined(o)||(dn[n]=0),n},hn.request=function(e){if(Xt(e.url)||Yt(e.url))return pn.raiseEvent(),e.state=Gt.RECEIVED,e.requestFunction();if(++an.numberOfAttemptedRequests,r.defined(e.serverKey)||(e.serverKey=hn.getServerKey(e.url)),hn.throttleRequests&&e.throttleByServer&&!hn.serverHasOpenSlots(e.serverKey))return;if(!hn.throttleRequests||!e.throttle)return yn(e);if(ln.length>=hn.maximumRequests)return;mn(e);const t=cn.insert(e);if(r.defined(t)){if(t===e)return;vn(t)}return gn(e)},hn.clearForSpecs=function(){for(;cn.length>0;){vn(cn.pop())}const e=ln.length;for(let t=0;t<e;++t)vn(ln[t]);ln.length=0,dn={},an.numberOfAttemptedRequests=0,an.numberOfActiveRequests=0,an.numberOfCancelledRequests=0,an.numberOfCancelledActiveRequests=0,an.numberOfFailedRequests=0,an.numberOfActiveRequestsEver=0,an.lastNumberOfActiveRequests=0},hn.numberOfActiveRequestsByServer=function(e){return dn[e]},hn.requestHeap=cn;const wn={};let Cn={};wn.add=function(e,t){const n=`${e.toLowerCase()}:${t}`;r.defined(Cn[n])||(Cn[n]=!0)},wn.remove=function(e,t){const n=`${e.toLowerCase()}:${t}`;r.defined(Cn[n])&&delete Cn[n]},wn.contains=function(e){const t=function(e){const t=new Bt(e);t.normalize();let n=t.authority();if(0!==n.length){if(t.authority(n),-1!==n.indexOf("@")){const e=n.split("@");n=e[1]}if(-1===n.indexOf(":")){let e=t.scheme();if(0===e.length&&(e=window.location.protocol,e=e.substring(0,e.length-1)),"http"===e)n+=":80";else{if("https"!==e)return;n+=":443"}}return n}}(e);return!(!r.defined(t)||!r.defined(Cn[t]))},wn.clear=function(){Cn={}};var _n=wn;const bn=function(){try{const e=new XMLHttpRequest;return e.open("GET","#",!0),e.responseType="blob","blob"===e.responseType}catch(e){return!1}}();function xn(e){"string"==typeof(e=r.defaultValue(e,r.defaultValue.EMPTY_OBJECT))&&(e={url:e}),this._url=void 0,this._templateValues=Sn(e.templateValues,{}),this._queryParameters=Sn(e.queryParameters,{}),this.headers=Sn(e.headers,{}),this.request=r.defaultValue(e.request,new en),this.proxy=e.proxy,this.retryCallback=e.retryCallback,this.retryAttempts=r.defaultValue(e.retryAttempts,0),this._retryCount=0;r.defaultValue(e.parseUrl,!0)?this.parseUrl(e.url,!0,!0):this._url=e.url,this._credits=e.credits}function Sn(e,t){return r.defined(e)?Vt(e):t}let An;function En(e,t,n){if(!n)return s.combine(e,t);const o=Vt(e,!0);for(const e in t)if(t.hasOwnProperty(e)){let n=o[e];const i=t[e];r.defined(n)?(Array.isArray(n)||(n=o[e]=[n]),o[e]=n.concat(i)):o[e]=Array.isArray(i)?i.slice():i}return o}function On(e){const t=e.resource,n=e.flipY,o=e.skipColorSpaceConversion,i=e.preferImageBitmap,s=t.request;s.url=t.url,s.requestFunction=function(){let e=!1;t.isDataUri||t.isBlobUri||(e=t.isCrossOriginUrl);const r=Lt();return xn._Implementations.createImage(s,e,r,n,o,i),r.promise};const a=hn.request(s);if(r.defined(a))return a.catch((function(e){return s.state!==Gt.FAILED?Promise.reject(e):t.retryOnError(e).then((function(r){return r?(s.state=Gt.UNISSUED,s.deferred=void 0,On({resource:t,flipY:n,skipColorSpaceConversion:o,preferImageBitmap:i})):Promise.reject(e)}))}))}function Pn(e,t,n){const o={};o[t]=n,e.setQueryParameters(o);const i=e.request,s=e.url;i.url=s,i.requestFunction=function(){const e=Lt();return window[n]=function(t){e.resolve(t);try{delete window[n]}catch(e){window[n]=void 0}},xn._Implementations.loadAndExecuteScript(s,n,e),e.promise};const a=hn.request(i);if(r.defined(a))return a.catch((function(r){return i.state!==Gt.FAILED?Promise.reject(r):e.retryOnError(r).then((function(o){return o?(i.state=Gt.UNISSUED,i.deferred=void 0,Pn(e,t,n)):Promise.reject(r)}))}))}function In(e){if(e.state===Gt.ISSUED||e.state===Gt.ACTIVE)throw new a.RuntimeError("The Resource is already being fetched.");e.state=Gt.UNISSUED,e.deferred=void 0}xn.createIfNeeded=function(e){return e instanceof xn?e.getDerivedResource({request:e.request}):"string"!=typeof e?e:new xn({url:e})},xn.supportsImageBitmapOptions=function(){if(r.defined(An))return An;if("function"!=typeof createImageBitmap)return An=Promise.resolve(!1),An;return An=xn.fetchBlob({url:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAIAAACQd1PeAAAABGdBTUEAAE4g3rEiDgAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAADElEQVQI12Ng6GAAAAEUAIngE3ZiAAAAAElFTkSuQmCC"}).then((function(e){return Promise.all([createImageBitmap(e,{imageOrientation:"flipY",premultiplyAlpha:"none",colorSpaceConversion:"none"}),createImageBitmap(e)])})).then((function(e){const t=Wt(e[0]),n=Wt(e[1]);return t[1]!==n[1]})).catch((function(){return!1})),An},Object.defineProperties(xn,{isBlobSupported:{get:function(){return bn}}}),Object.defineProperties(xn.prototype,{queryParameters:{get:function(){return this._queryParameters}},templateValues:{get:function(){return this._templateValues}},url:{get:function(){return this.getUrlComponent(!0,!0)},set:function(e){this.parseUrl(e,!1,!1)}},extension:{get:function(){return function(e){const t=new Bt(e);t.normalize();let n=t.path(),r=n.lastIndexOf("/");return-1!==r&&(n=n.substr(r+1)),r=n.lastIndexOf("."),n=-1===r?"":n.substr(r+1),n}(this._url)}},isDataUri:{get:function(){return Xt(this._url)}},isBlobUri:{get:function(){return Yt(this._url)}},isCrossOriginUrl:{get:function(){return function(e){r.defined(Zt)||(Zt=document.createElement("a")),Zt.href=window.location.href;const t=Zt.host,n=Zt.protocol;return Zt.href=e,Zt.href=Zt.href,n!==Zt.protocol||t!==Zt.host}(this._url)}},hasHeaders:{get:function(){return Object.keys(this.headers).length>0}},credits:{get:function(){return this._credits}}}),xn.prototype.toString=function(){return this.getUrlComponent(!0,!0)},xn.prototype.parseUrl=function(e,t,n,o){let i=new Bt(e);const s=function(e){if(0===e.length)return{};if(-1===e.indexOf("="))return{[e]:void 0};return function(e){const t={};if(""===e)return t;const n=e.replace(/\+/g,"%20").split(/[&;]/);for(let e=0,o=n.length;e<o;++e){const o=n[e].split("="),i=decodeURIComponent(o[0]);let s=o[1];s=r.defined(s)?decodeURIComponent(s):"";const a=t[i];"string"==typeof a?t[i]=[a,s]:Array.isArray(a)?a.push(s):t[i]=s}return t}(e)}(i.query());this._queryParameters=t?En(s,this.queryParameters,n):s,i.search(""),i.fragment(""),r.defined(o)&&""===i.scheme()&&(i=i.absoluteTo(Qt(o))),this._url=i.toString()},xn.prototype.getUrlComponent=function(e,t){if(this.isDataUri)return this._url;let n=this._url;e&&(n=`${n}${function(e){const t=Object.keys(e);if(0===t.length)return"";if(1===t.length&&!r.defined(e[t[0]]))return`?${t[0]}`;return`?${function(e){let t="";for(const n in e)if(e.hasOwnProperty(n)){const r=e[n],o=`${encodeURIComponent(n)}=`;if(Array.isArray(r))for(let e=0,n=r.length;e<n;++e)t+=`${o+encodeURIComponent(r[e])}&`;else t+=`${o+encodeURIComponent(r)}&`}return t=t.slice(0,-1),t}(e)}`}(this.queryParameters)}`),n=n.replace(/%7B/g,"{").replace(/%7D/g,"}");const o=this._templateValues;return Object.keys(o).length>0&&(n=n.replace(/{(.*?)}/g,(function(e,t){const n=o[t];return r.defined(n)?encodeURIComponent(n):e}))),t&&r.defined(this.proxy)&&(n=this.proxy.getURL(n)),n},xn.prototype.setQueryParameters=function(e,t){this._queryParameters=t?En(this._queryParameters,e,!1):En(e,this._queryParameters,!1)},xn.prototype.appendQueryParameters=function(e){this._queryParameters=En(e,this._queryParameters,!0)},xn.prototype.setTemplateValues=function(e,t){this._templateValues=t?s.combine(this._templateValues,e):s.combine(e,this._templateValues)},xn.prototype.getDerivedResource=function(e){const t=this.clone();if(t._retryCount=0,r.defined(e.url)){const n=r.defaultValue(e.preserveQueryParameters,!1);t.parseUrl(e.url,!0,n,this._url)}return r.defined(e.queryParameters)&&(t._queryParameters=s.combine(e.queryParameters,t.queryParameters)),r.defined(e.templateValues)&&(t._templateValues=s.combine(e.templateValues,t.templateValues)),r.defined(e.headers)&&(t.headers=s.combine(e.headers,t.headers)),r.defined(e.proxy)&&(t.proxy=e.proxy),r.defined(e.request)&&(t.request=e.request),r.defined(e.retryCallback)&&(t.retryCallback=e.retryCallback),r.defined(e.retryAttempts)&&(t.retryAttempts=e.retryAttempts),t},xn.prototype.retryOnError=function(e){const t=this.retryCallback;if("function"!=typeof t||this._retryCount>=this.retryAttempts)return Promise.resolve(!1);const n=this;return Promise.resolve(t(this,e)).then((function(e){return++n._retryCount,e}))},xn.prototype.clone=function(e){return r.defined(e)?(e._url=this._url,e._queryParameters=Vt(this._queryParameters),e._templateValues=Vt(this._templateValues),e.headers=Vt(this.headers),e.proxy=this.proxy,e.retryCallback=this.retryCallback,e.retryAttempts=this.retryAttempts,e._retryCount=0,e.request=this.request.clone(),e):new xn({url:this._url,queryParameters:this.queryParameters,templateValues:this.templateValues,headers:this.headers,proxy:this.proxy,retryCallback:this.retryCallback,retryAttempts:this.retryAttempts,request:this.request.clone(),parseUrl:!1,credits:r.defined(this.credits)?this.credits.slice():void 0})},xn.prototype.getBaseUri=function(e){return function(e,t){let n="";const r=e.lastIndexOf("/");return-1!==r&&(n=e.substring(0,r+1)),t?(0!==(e=new Bt(e)).query().length&&(n+=`?${e.query()}`),0!==e.fragment().length&&(n+=`#${e.fragment()}`),n):n}(this.getUrlComponent(e),e)},xn.prototype.appendForwardSlash=function(){var e;this._url=(0!==(e=this._url).length&&"/"===e[e.length-1]||(e=`${e}/`),e)},xn.prototype.fetchArrayBuffer=function(){return this.fetch({responseType:"arraybuffer"})},xn.fetchArrayBuffer=function(e){return new xn(e).fetchArrayBuffer()},xn.prototype.fetchBlob=function(){return this.fetch({responseType:"blob"})},xn.fetchBlob=function(e){return new xn(e).fetchBlob()},xn.prototype.fetchImage=function(e){e=r.defaultValue(e,r.defaultValue.EMPTY_OBJECT);const t=r.defaultValue(e.preferImageBitmap,!1),n=r.defaultValue(e.preferBlob,!1),o=r.defaultValue(e.flipY,!1),i=r.defaultValue(e.skipColorSpaceConversion,!1);if(In(this.request),!bn||this.isDataUri||this.isBlobUri||!this.hasHeaders&&!n)return On({resource:this,flipY:o,skipColorSpaceConversion:i,preferImageBitmap:t});const s=this.fetchBlob();if(!r.defined(s))return;let a,u,c,l;return xn.supportsImageBitmapOptions().then((function(e){return a=e,u=a&&t,s})).then((function(e){if(!r.defined(e))return;if(l=e,u)return xn.createImageBitmapFromBlob(e,{flipY:o,premultiplyAlpha:!1,skipColorSpaceConversion:i});const t=window.URL.createObjectURL(e);return c=new xn({url:t}),On({resource:c,flipY:o,skipColorSpaceConversion:i,preferImageBitmap:!1})})).then((function(e){if(r.defined(e))return e.blob=l,u||window.URL.revokeObjectURL(c.url),e})).catch((function(e){return r.defined(c)&&window.URL.revokeObjectURL(c.url),e.blob=l,Promise.reject(e)}))},xn.fetchImage=function(e){return new xn(e).fetchImage({flipY:e.flipY,skipColorSpaceConversion:e.skipColorSpaceConversion,preferBlob:e.preferBlob,preferImageBitmap:e.preferImageBitmap})},xn.prototype.fetchText=function(){return this.fetch({responseType:"text"})},xn.fetchText=function(e){return new xn(e).fetchText()},xn.prototype.fetchJson=function(){const e=this.fetch({responseType:"text",headers:{Accept:"application/json,*/*;q=0.01"}});if(r.defined(e))return e.then((function(e){if(r.defined(e))return JSON.parse(e)}))},xn.fetchJson=function(e){return new xn(e).fetchJson()},xn.prototype.fetchXML=function(){return this.fetch({responseType:"document",overrideMimeType:"text/xml"})},xn.fetchXML=function(e){return new xn(e).fetchXML()},xn.prototype.fetchJsonp=function(e){let t;e=r.defaultValue(e,"callback"),In(this.request);do{t=`loadJsonp${o.CesiumMath.nextRandomNumber().toString().substring(2,8)}`}while(r.defined(window[t]));return Pn(this,e,t)},xn.fetchJsonp=function(e){return new xn(e).fetchJsonp(e.callbackParameterName)},xn.prototype._makeRequest=function(e){const t=this;In(t.request);const n=t.request,o=t.url;n.url=o,n.requestFunction=function(){const i=e.responseType,a=s.combine(e.headers,t.headers),u=e.overrideMimeType,c=e.method,l=e.data,d=Lt(),f=xn._Implementations.loadWithXhr(o,i,c,l,a,d,u);return r.defined(f)&&r.defined(f.abort)&&(n.cancelFunction=function(){f.abort()}),d.promise};const i=hn.request(n);if(r.defined(i))return i.then((function(e){return n.cancelFunction=void 0,e})).catch((function(r){return n.cancelFunction=void 0,n.state!==Gt.FAILED?Promise.reject(r):t.retryOnError(r).then((function(o){return o?(n.state=Gt.UNISSUED,n.deferred=void 0,t.fetch(e)):Promise.reject(r)}))}))};const Rn=/^data:(.*?)(;base64)?,(.*)$/;function Tn(e,t){const n=decodeURIComponent(t);return e?atob(n):n}function qn(e,t){const n=Tn(e,t),r=new ArrayBuffer(n.length),o=new Uint8Array(r);for(let e=0;e<n.length;e++)o[e]=n.charCodeAt(e);return r}function zn(e,t){switch(t){case"text":return e.toString("utf8");case"json":return JSON.parse(e.toString("utf8"));default:return new Uint8Array(e).buffer}}xn.prototype.fetch=function(e){return(e=Sn(e,{})).method="GET",this._makeRequest(e)},xn.fetch=function(e){return new xn(e).fetch({responseType:e.responseType,overrideMimeType:e.overrideMimeType})},xn.prototype.delete=function(e){return(e=Sn(e,{})).method="DELETE",this._makeRequest(e)},xn.delete=function(e){return new xn(e).delete({responseType:e.responseType,overrideMimeType:e.overrideMimeType,data:e.data})},xn.prototype.head=function(e){return(e=Sn(e,{})).method="HEAD",this._makeRequest(e)},xn.head=function(e){return new xn(e).head({responseType:e.responseType,overrideMimeType:e.overrideMimeType})},xn.prototype.options=function(e){return(e=Sn(e,{})).method="OPTIONS",this._makeRequest(e)},xn.options=function(e){return new xn(e).options({responseType:e.responseType,overrideMimeType:e.overrideMimeType})},xn.prototype.post=function(e,t){return i.Check.defined("data",e),(t=Sn(t,{})).method="POST",t.data=e,this._makeRequest(t)},xn.post=function(e){return new xn(e).post(e.data,{responseType:e.responseType,overrideMimeType:e.overrideMimeType})},xn.prototype.put=function(e,t){return i.Check.defined("data",e),(t=Sn(t,{})).method="PUT",t.data=e,this._makeRequest(t)},xn.put=function(e){return new xn(e).put(e.data,{responseType:e.responseType,overrideMimeType:e.overrideMimeType})},xn.prototype.patch=function(e,t){return i.Check.defined("data",e),(t=Sn(t,{})).method="PATCH",t.data=e,this._makeRequest(t)},xn.patch=function(e){return new xn(e).patch(e.data,{responseType:e.responseType,overrideMimeType:e.overrideMimeType})},xn._Implementations={},xn._Implementations.loadImageElement=function(e,t,n){const r=new Image;r.onload=function(){0===r.naturalWidth&&0===r.naturalHeight&&0===r.width&&0===r.height&&(r.width=300,r.height=150),n.resolve(r)},r.onerror=function(e){n.reject(e)},t&&(_n.contains(e)?r.crossOrigin="use-credentials":r.crossOrigin=""),r.src=e},xn._Implementations.createImage=function(e,t,n,o,i,s){const u=e.url;xn.supportsImageBitmapOptions().then((function(c){if(!c||!s)return void xn._Implementations.loadImageElement(u,t,n);const l=Lt(),d=xn._Implementations.loadWithXhr(u,"blob","GET",void 0,void 0,l,void 0,void 0,void 0);return r.defined(d)&&r.defined(d.abort)&&(e.cancelFunction=function(){d.abort()}),l.promise.then((function(e){if(r.defined(e))return xn.createImageBitmapFromBlob(e,{flipY:o,premultiplyAlpha:!1,skipColorSpaceConversion:i});n.reject(new a.RuntimeError(`Successfully retrieved ${u} but it contained no content.`))})).then((function(e){n.resolve(e)}))})).catch((function(e){n.reject(e)}))},xn.createImageBitmapFromBlob=function(e,t){return i.Check.defined("options",t),i.Check.typeOf.bool("options.flipY",t.flipY),i.Check.typeOf.bool("options.premultiplyAlpha",t.premultiplyAlpha),i.Check.typeOf.bool("options.skipColorSpaceConversion",t.skipColorSpaceConversion),createImageBitmap(e,{imageOrientation:t.flipY?"flipY":"none",premultiplyAlpha:t.premultiplyAlpha?"premultiply":"none",colorSpaceConversion:t.skipColorSpaceConversion?"none":"default"})};const Mn="undefined"==typeof XMLHttpRequest;function Dn(e){if(e=r.defaultValue(e,r.defaultValue.EMPTY_OBJECT),this._dates=void 0,this._samples=void 0,this._dateColumn=-1,this._xPoleWanderRadiansColumn=-1,this._yPoleWanderRadiansColumn=-1,this._ut1MinusUtcSecondsColumn=-1,this._xCelestialPoleOffsetRadiansColumn=-1,this._yCelestialPoleOffsetRadiansColumn=-1,this._taiMinusUtcSecondsColumn=-1,this._columnCount=0,this._lastIndex=-1,this._downloadPromise=void 0,this._dataError=void 0,this._addNewLeapSeconds=r.defaultValue(e.addNewLeapSeconds,!0),r.defined(e.data))kn(this,e.data);else if(r.defined(e.url)){const t=xn.createIfNeeded(e.url),n=this;this._downloadPromise=t.fetchJson().then((function(e){kn(n,e)})).catch((function(){n._dataError=`An error occurred while retrieving the EOP data from the URL ${t.url}.`}))}else kn(this,{columnNames:["dateIso8601","modifiedJulianDateUtc","xPoleWanderRadians","yPoleWanderRadians","ut1MinusUtcSeconds","lengthOfDayCorrectionSeconds","xCelestialPoleOffsetRadians","yCelestialPoleOffsetRadians","taiMinusUtcSeconds"],samples:[]})}function Un(e,t){return It.compare(e.julianDate,t)}function kn(e,t){if(!r.defined(t.columnNames))return void(e._dataError="Error in loaded EOP data: The columnNames property is required.");if(!r.defined(t.samples))return void(e._dataError="Error in loaded EOP data: The samples property is required.");const n=t.columnNames.indexOf("modifiedJulianDateUtc"),o=t.columnNames.indexOf("xPoleWanderRadians"),i=t.columnNames.indexOf("yPoleWanderRadians"),s=t.columnNames.indexOf("ut1MinusUtcSeconds"),a=t.columnNames.indexOf("xCelestialPoleOffsetRadians"),u=t.columnNames.indexOf("yCelestialPoleOffsetRadians"),c=t.columnNames.indexOf("taiMinusUtcSeconds");if(n<0||o<0||i<0||s<0||a<0||u<0||c<0)return void(e._dataError="Error in loaded EOP data: The columnNames property must include modifiedJulianDateUtc, xPoleWanderRadians, yPoleWanderRadians, ut1MinusUtcSeconds, xCelestialPoleOffsetRadians, yCelestialPoleOffsetRadians, and taiMinusUtcSeconds columns");const l=e._samples=t.samples,d=e._dates=[];let f;e._dateColumn=n,e._xPoleWanderRadiansColumn=o,e._yPoleWanderRadiansColumn=i,e._ut1MinusUtcSecondsColumn=s,e._xCelestialPoleOffsetRadiansColumn=a,e._yCelestialPoleOffsetRadiansColumn=u,e._taiMinusUtcSecondsColumn=c,e._columnCount=t.columnNames.length,e._lastIndex=void 0;const p=e._addNewLeapSeconds;for(let t=0,o=l.length;t<o;t+=e._columnCount){const e=l[t+n],o=l[t+c],i=new It(e+lt.MODIFIED_JULIAN_DATE_DIFFERENCE,o,dt.TAI);if(d.push(i),p){if(o!==f&&r.defined(f)){const e=It.leapSeconds,t=it(e,i,Un);if(t<0){const n=new ct(i,o);e.splice(~t,0,n)}}f=o}}}function Fn(e,t,n,r,o){const i=n*r;o.xPoleWander=t[i+e._xPoleWanderRadiansColumn],o.yPoleWander=t[i+e._yPoleWanderRadiansColumn],o.xPoleOffset=t[i+e._xCelestialPoleOffsetRadiansColumn],o.yPoleOffset=t[i+e._yCelestialPoleOffsetRadiansColumn],o.ut1MinusUtc=t[i+e._ut1MinusUtcSecondsColumn]}function Nn(e,t,n){return t+e*(n-t)}function jn(e,t,n,r,o,i,s){const a=e._columnCount;if(i>t.length-1)return s.xPoleWander=0,s.yPoleWander=0,s.xPoleOffset=0,s.yPoleOffset=0,s.ut1MinusUtc=0,s;const u=t[o],c=t[i];if(u.equals(c)||r.equals(u))return Fn(e,n,o,a,s),s;if(r.equals(c))return Fn(e,n,i,a,s),s;const l=It.secondsDifference(r,u)/It.secondsDifference(c,u),d=o*a,f=i*a;let p=n[d+e._ut1MinusUtcSecondsColumn],h=n[f+e._ut1MinusUtcSecondsColumn];const m=h-p;if(m>.5||m<-.5){const t=n[d+e._taiMinusUtcSecondsColumn],o=n[f+e._taiMinusUtcSecondsColumn];t!==o&&(c.equals(r)?p=h:h-=o-t)}return s.xPoleWander=Nn(l,n[d+e._xPoleWanderRadiansColumn],n[f+e._xPoleWanderRadiansColumn]),s.yPoleWander=Nn(l,n[d+e._yPoleWanderRadiansColumn],n[f+e._yPoleWanderRadiansColumn]),s.xPoleOffset=Nn(l,n[d+e._xCelestialPoleOffsetRadiansColumn],n[f+e._xCelestialPoleOffsetRadiansColumn]),s.yPoleOffset=Nn(l,n[d+e._yCelestialPoleOffsetRadiansColumn],n[f+e._yCelestialPoleOffsetRadiansColumn]),s.ut1MinusUtc=Nn(l,p,h),s}function Bn(e,t,n){this.heading=r.defaultValue(e,0),this.pitch=r.defaultValue(t,0),this.roll=r.defaultValue(n,0)}xn._Implementations.loadWithXhr=function(t,n,o,i,s,c,l){const d=Rn.exec(t);if(null!==d)return void c.resolve(function(e,t){t=r.defaultValue(t,"");const n=e[1],o=!!e[2],i=e[3];let s,a;switch(t){case"":case"text":return Tn(o,i);case"arraybuffer":return qn(o,i);case"blob":return s=qn(o,i),new Blob([s],{type:n});case"document":return a=new DOMParser,a.parseFromString(Tn(o,i),n);case"json":return JSON.parse(Tn(o,i))}}(d,n));if(Mn)return void function(t,n,r,o,i,s,c){let l,d;Promise.all([new Promise((function(t,n){e(["url"],(function(e){t(u(e))}),n)})),new Promise((function(t,n){e(["zlib"],(function(e){t(u(e))}),n)}))]).then((([n,r])=>(l=n.parse(t),d=r,"https:"===l.protocol?new Promise((function(t,n){e(["https"],(function(e){t(u(e))}),n)})):new Promise((function(t,n){e(["http"],(function(e){t(u(e))}),n)}))))).then((e=>{const t={protocol:l.protocol,hostname:l.hostname,port:l.port,path:l.path,query:l.query,method:r,headers:i};e.request(t).on("response",(function(e){if(e.statusCode<200||e.statusCode>=300)return void s.reject(new tn(e.statusCode,e,e.headers));const t=[];e.on("data",(function(e){t.push(e)})),e.on("end",(function(){const r=Buffer.concat(t);"gzip"===e.headers["content-encoding"]?d.gunzip(r,(function(e,t){e?s.reject(new a.RuntimeError("Error decompressing response.")):s.resolve(zn(t,n))})):s.resolve(zn(r,n))}))})).on("error",(function(e){s.reject(new tn)})).end()}))}(t,n,o,0,s,c);const f=new XMLHttpRequest;if(_n.contains(t)&&(f.withCredentials=!0),t=(t=t.replace(/%5C/g,"/")).replace(/{/g,"%7B").replace(/}/g,"%7D"),f.open(o,t,!0),r.defined(l)&&r.defined(f.overrideMimeType)&&f.overrideMimeType(l),r.defined(s))for(const e in s)s.hasOwnProperty(e)&&f.setRequestHeader(e,s[e]);r.defined(n)&&(f.responseType=n);let p=!1;return"string"==typeof t&&(p=0===t.indexOf("file://")||"undefined"!=typeof window&&"file://"===window.location.origin),f.onload=function(){if((f.status<200||f.status>=300)&&(!p||0!==f.status))return void c.reject(new tn(f.status,f.response,f.getAllResponseHeaders()));const e=f.response,t=f.responseType;if("HEAD"===o||"OPTIONS"===o){const e=f.getAllResponseHeaders().trim().split(/[\r\n]+/),t={};return e.forEach((function(e){const n=e.split(": "),r=n.shift();t[r]=n.join(": ")})),void c.resolve(t)}if(204===f.status)c.resolve();else if(!r.defined(e)||r.defined(n)&&t!==n)if("json"===n&&"string"==typeof e)try{c.resolve(JSON.parse(e))}catch(e){c.reject(e)}else(""===t||"document"===t)&&r.defined(f.responseXML)&&f.responseXML.hasChildNodes()?c.resolve(f.responseXML):""!==t&&"text"!==t||!r.defined(f.responseText)?c.reject(new a.RuntimeError("Invalid XMLHttpRequest response type.")):c.resolve(f.responseText);else c.resolve(e)},f.onerror=function(e){c.reject(new tn)},f.send(i),f},xn._Implementations.loadAndExecuteScript=function(e,t,n){return function(e){const t=document.createElement("script");return t.async=!0,t.src=e,new Promise(((e,n)=>{window.crossOriginIsolated&&t.setAttribute("crossorigin","anonymous");const r=document.getElementsByTagName("head")[0];t.onload=function(){t.onload=void 0,r.removeChild(t),e()},t.onerror=function(e){n(e)},r.appendChild(t)}))}(e).catch((function(e){n.reject(e)}))},xn._DefaultImplementations={},xn._DefaultImplementations.createImage=xn._Implementations.createImage,xn._DefaultImplementations.loadWithXhr=xn._Implementations.loadWithXhr,xn._DefaultImplementations.loadAndExecuteScript=xn._Implementations.loadAndExecuteScript,xn.DEFAULT=Object.freeze(new xn({url:"undefined"==typeof document?"":document.location.href.split("?")[0]})),Dn.NONE=Object.freeze({getPromiseToLoad:function(){return Promise.resolve()},compute:function(e,t){return r.defined(t)?(t.xPoleWander=0,t.yPoleWander=0,t.xPoleOffset=0,t.yPoleOffset=0,t.ut1MinusUtc=0):t=new st(0,0,0,0,0),t}}),Dn.prototype.getPromiseToLoad=function(){return Promise.resolve(this._downloadPromise)},Dn.prototype.compute=function(e,t){if(!r.defined(this._samples)){if(r.defined(this._dataError))throw new a.RuntimeError(this._dataError);return}if(r.defined(t)||(t=new st(0,0,0,0,0)),0===this._samples.length)return t.xPoleWander=0,t.yPoleWander=0,t.xPoleOffset=0,t.yPoleOffset=0,t.ut1MinusUtc=0,t;const n=this._dates,o=this._lastIndex;let i=0,s=0;if(r.defined(o)){const a=n[o],u=n[o+1],c=It.lessThanOrEquals(a,e),l=!r.defined(u),d=l||It.greaterThanOrEquals(u,e);if(c&&d)return i=o,!l&&u.equals(e)&&++i,s=i+1,jn(this,n,this._samples,e,i,s,t),t}let u=it(n,e,It.compare,this._dateColumn);return u>=0?(u<n.length-1&&n[u+1].equals(e)&&++u,i=u,s=u):(s=~u,i=s-1,i<0&&(i=0)),this._lastIndex=i,jn(this,n,this._samples,e,i,s,t),t},Bn.fromQuaternion=function(e,t){r.defined(t)||(t=new Bn);const n=2*(e.w*e.y-e.z*e.x),i=1-2*(e.x*e.x+e.y*e.y),s=2*(e.w*e.x+e.y*e.z),a=1-2*(e.y*e.y+e.z*e.z),u=2*(e.w*e.z+e.x*e.y);return t.heading=-Math.atan2(u,a),t.roll=Math.atan2(s,i),t.pitch=-o.CesiumMath.asinClamped(n),t},Bn.fromDegrees=function(e,t,n,i){return r.defined(i)||(i=new Bn),i.heading=e*o.CesiumMath.RADIANS_PER_DEGREE,i.pitch=t*o.CesiumMath.RADIANS_PER_DEGREE,i.roll=n*o.CesiumMath.RADIANS_PER_DEGREE,i},Bn.clone=function(e,t){if(r.defined(e))return r.defined(t)?(t.heading=e.heading,t.pitch=e.pitch,t.roll=e.roll,t):new Bn(e.heading,e.pitch,e.roll)},Bn.equals=function(e,t){return e===t||r.defined(e)&&r.defined(t)&&e.heading===t.heading&&e.pitch===t.pitch&&e.roll===t.roll},Bn.equalsEpsilon=function(e,t,n,i){return e===t||r.defined(e)&&r.defined(t)&&o.CesiumMath.equalsEpsilon(e.heading,t.heading,n,i)&&o.CesiumMath.equalsEpsilon(e.pitch,t.pitch,n,i)&&o.CesiumMath.equalsEpsilon(e.roll,t.roll,n,i)},Bn.prototype.clone=function(e){return Bn.clone(this,e)},Bn.prototype.equals=function(e){return Bn.equals(this,e)},Bn.prototype.equalsEpsilon=function(e,t,n){return Bn.equalsEpsilon(this,e,t,n)},Bn.prototype.toString=function(){return`(${this.heading}, ${this.pitch}, ${this.roll})`};const Vn=/((?:.*\/)|^)Cesium\.js(?:\?|\#|$)/;let Ln,Qn,$n;function Wn(e){return"undefined"==typeof document?e:(r.defined(Ln)||(Ln=document.createElement("a")),Ln.href=e,Ln.href=Ln.href,Ln.href)}function Hn(){if(r.defined(Qn))return Qn;let t;return t="undefined"!=typeof CESIUM_BASE_URL?CESIUM_BASE_URL:"object"==typeof define&&r.defined(define.amd)&&!define.amd.toUrlUndefined&&r.defined(e.toUrl)?Qt("..",Jn("Core/buildModuleUrl.js")):function(){const e=document.getElementsByTagName("script");for(let t=0,n=e.length;t<n;++t){const n=e[t].getAttribute("src"),r=Vn.exec(n);if(null!==r)return r[1]}}(),Qn=new xn({url:Wn(t)}),Qn.appendForwardSlash(),Qn}function Yn(t){return Wn(e.toUrl(`../${t}`))}function Zn(e){return Hn().getDerivedResource({url:e}).url}function Jn(t){r.defined($n)||($n="object"==typeof define&&r.defined(define.amd)&&!define.amd.toUrlUndefined&&r.defined(e.toUrl)?Yn:Zn);return $n(t)}function Xn(e,t,n){this.x=e,this.y=t,this.s=n}function Gn(e){e=r.defaultValue(e,r.defaultValue.EMPTY_OBJECT),this._xysFileUrlTemplate=xn.createIfNeeded(e.xysFileUrlTemplate),this._interpolationOrder=r.defaultValue(e.interpolationOrder,9),this._sampleZeroJulianEphemerisDate=r.defaultValue(e.sampleZeroJulianEphemerisDate,2442396.5),this._sampleZeroDateTT=new It(this._sampleZeroJulianEphemerisDate,0,dt.TAI),this._stepSizeDays=r.defaultValue(e.stepSizeDays,1),this._samplesPerXysFile=r.defaultValue(e.samplesPerXysFile,1e3),this._totalSamples=r.defaultValue(e.totalSamples,27426),this._samples=new Array(3*this._totalSamples),this._chunkDownloadsInProgress=[];const t=this._interpolationOrder,n=this._denominators=new Array(t+1),o=this._xTable=new Array(t+1),i=Math.pow(this._stepSizeDays,t);for(let e=0;e<=t;++e){n[e]=i,o[e]=e*this._stepSizeDays;for(let r=0;r<=t;++r)r!==e&&(n[e]*=e-r);n[e]=1/n[e]}this._work=new Array(t+1),this._coef=new Array(t+1)}Jn._cesiumScriptRegex=Vn,Jn._buildModuleUrlFromBaseUrl=Zn,Jn._clearBaseResource=function(){Qn=void 0},Jn.setBaseUrl=function(e){Qn=xn.DEFAULT.getDerivedResource({url:e})},Jn.getCesiumBaseUrl=Hn;const Kn=new It(0,0,dt.TAI);function er(e,t,n){const r=Kn;return r.dayNumber=t,r.secondsOfDay=n,It.daysDifference(r,e._sampleZeroDateTT)}function tr(e,t){if(e._chunkDownloadsInProgress[t])return e._chunkDownloadsInProgress[t];let n;const o=e._xysFileUrlTemplate;n=r.defined(o)?o.getDerivedResource({templateValues:{0:t}}):new xn({url:Jn(`Assets/IAU2006_XYS/IAU2006_XYS_${t}.json`)});const i=n.fetchJson().then((function(n){e._chunkDownloadsInProgress[t]=!1;const r=e._samples,o=n.samples,i=t*e._samplesPerXysFile*3;for(let e=0,t=o.length;e<t;++e)r[i+e]=o[e]}));return e._chunkDownloadsInProgress[t]=i,i}Gn.prototype.preload=function(e,t,n,r){const o=er(this,e,t),i=er(this,n,r);let s=o/this._stepSizeDays-this._interpolationOrder/2|0;s<0&&(s=0);let a=i/this._stepSizeDays-this._interpolationOrder/2|0+this._interpolationOrder;a>=this._totalSamples&&(a=this._totalSamples-1);const u=s/this._samplesPerXysFile|0,c=a/this._samplesPerXysFile|0,l=[];for(let e=u;e<=c;++e)l.push(tr(this,e));return Promise.all(l)},Gn.prototype.computeXysRadians=function(e,t,n){const o=er(this,e,t);if(o<0)return;const i=o/this._stepSizeDays|0;if(i>=this._totalSamples)return;const s=this._interpolationOrder;let a=i-(s/2|0);a<0&&(a=0);let u=a+s;u>=this._totalSamples&&(u=this._totalSamples-1,a=u-s,a<0&&(a=0));let c=!1;const l=this._samples;if(r.defined(l[3*a])||(tr(this,a/this._samplesPerXysFile|0),c=!0),r.defined(l[3*u])||(tr(this,u/this._samplesPerXysFile|0),c=!0),c)return;r.defined(n)?(n.x=0,n.y=0,n.s=0):n=new Xn(0,0,0);const d=o-a*this._stepSizeDays,f=this._work,p=this._denominators,h=this._coef,m=this._xTable;let g,y;for(g=0;g<=s;++g)f[g]=d-m[g];for(g=0;g<=s;++g){for(h[g]=1,y=0;y<=s;++y)y!==g&&(h[g]*=f[y]);h[g]*=p[g];let e=3*(a+g);n.x+=h[g]*l[e++],n.y+=h[g]*l[e++],n.s+=h[g]*l[e]}return n};const nr={},rr={up:{south:"east",north:"west",west:"south",east:"north"},down:{south:"west",north:"east",west:"north",east:"south"},south:{up:"west",down:"east",west:"down",east:"up"},north:{up:"east",down:"west",west:"up",east:"down"},west:{up:"north",down:"south",north:"down",south:"up"},east:{up:"south",down:"north",north:"up",south:"down"}},or={north:[-1,0,0],east:[0,1,0],up:[0,0,1],south:[1,0,0],west:[0,-1,0],down:[0,0,-1]},ir={},sr={east:new n.Cartesian3,north:new n.Cartesian3,up:new n.Cartesian3,west:new n.Cartesian3,south:new n.Cartesian3,down:new n.Cartesian3};let ar=new n.Cartesian3,ur=new n.Cartesian3,cr=new n.Cartesian3;nr.localFrameToFixedFrameGenerator=function(e,t){if(!rr.hasOwnProperty(e)||!rr[e].hasOwnProperty(t))throw new i.DeveloperError("firstAxis and secondAxis must be east, north, up, west, south or down.");const s=rr[e][t];let a;const u=e+t;return r.defined(ir[u])?a=ir[u]:(a=function(a,u,c){if(r.defined(c)||(c=new i.Matrix4),n.Cartesian3.equalsEpsilon(a,n.Cartesian3.ZERO,o.CesiumMath.EPSILON14))n.Cartesian3.unpack(or[e],0,ar),n.Cartesian3.unpack(or[t],0,ur),n.Cartesian3.unpack(or[s],0,cr);else if(o.CesiumMath.equalsEpsilon(a.x,0,o.CesiumMath.EPSILON14)&&o.CesiumMath.equalsEpsilon(a.y,0,o.CesiumMath.EPSILON14)){const r=o.CesiumMath.sign(a.z);n.Cartesian3.unpack(or[e],0,ar),"east"!==e&&"west"!==e&&n.Cartesian3.multiplyByScalar(ar,r,ar),n.Cartesian3.unpack(or[t],0,ur),"east"!==t&&"west"!==t&&n.Cartesian3.multiplyByScalar(ur,r,ur),n.Cartesian3.unpack(or[s],0,cr),"east"!==s&&"west"!==s&&n.Cartesian3.multiplyByScalar(cr,r,cr)}else{(u=r.defaultValue(u,n.Ellipsoid.WGS84)).geodeticSurfaceNormal(a,sr.up);const o=sr.up,i=sr.east;i.x=-a.y,i.y=a.x,i.z=0,n.Cartesian3.normalize(i,sr.east),n.Cartesian3.cross(o,i,sr.north),n.Cartesian3.multiplyByScalar(sr.up,-1,sr.down),n.Cartesian3.multiplyByScalar(sr.east,-1,sr.west),n.Cartesian3.multiplyByScalar(sr.north,-1,sr.south),ar=sr[e],ur=sr[t],cr=sr[s]}return c[0]=ar.x,c[1]=ar.y,c[2]=ar.z,c[3]=0,c[4]=ur.x,c[5]=ur.y,c[6]=ur.z,c[7]=0,c[8]=cr.x,c[9]=cr.y,c[10]=cr.z,c[11]=0,c[12]=a.x,c[13]=a.y,c[14]=a.z,c[15]=1,c},ir[u]=a),a},nr.eastNorthUpToFixedFrame=nr.localFrameToFixedFrameGenerator("east","north"),nr.northEastDownToFixedFrame=nr.localFrameToFixedFrameGenerator("north","east"),nr.northUpEastToFixedFrame=nr.localFrameToFixedFrameGenerator("north","up"),nr.northWestUpToFixedFrame=nr.localFrameToFixedFrameGenerator("north","west");const lr=new qe,dr=new n.Cartesian3(1,1,1),fr=new i.Matrix4;nr.headingPitchRollToFixedFrame=function(e,t,o,s,a){s=r.defaultValue(s,nr.eastNorthUpToFixedFrame);const u=qe.fromHeadingPitchRoll(t,lr),c=i.Matrix4.fromTranslationQuaternionRotationScale(n.Cartesian3.ZERO,u,dr,fr);return a=s(e,o,a),i.Matrix4.multiply(a,c,a)};const pr=new i.Matrix4,hr=new n.Matrix3;nr.headingPitchRollQuaternion=function(e,t,n,r,o){const s=nr.headingPitchRollToFixedFrame(e,t,n,r,pr),a=i.Matrix4.getMatrix3(s,hr);return qe.fromRotationMatrix(a,o)};const mr=new n.Cartesian3(1,1,1),gr=new n.Cartesian3,yr=new i.Matrix4,vr=new i.Matrix4,wr=new n.Matrix3,Cr=new qe;nr.fixedFrameToHeadingPitchRoll=function(e,t,o,s){t=r.defaultValue(t,n.Ellipsoid.WGS84),o=r.defaultValue(o,nr.eastNorthUpToFixedFrame),r.defined(s)||(s=new Bn);const a=i.Matrix4.getTranslation(e,gr);if(n.Cartesian3.equals(a,n.Cartesian3.ZERO))return s.heading=0,s.pitch=0,s.roll=0,s;let u=i.Matrix4.inverseTransformation(o(a,t,yr),yr),c=i.Matrix4.setScale(e,mr,vr);c=i.Matrix4.setTranslation(c,n.Cartesian3.ZERO,c),u=i.Matrix4.multiply(u,c,u);let l=qe.fromRotationMatrix(i.Matrix4.getMatrix3(u,wr),Cr);return l=qe.normalize(l,l),Bn.fromQuaternion(l,s)};const _r=o.CesiumMath.TWO_PI/86400;let br=new It;nr.computeTemeToPseudoFixedMatrix=function(e,t){br=It.addSeconds(e,-It.computeTaiMinusUtc(e),br);const i=br.dayNumber,s=br.secondsOfDay;let a;const u=i-2451545;a=s>=43200?(u+.5)/lt.DAYS_PER_JULIAN_CENTURY:(u-.5)/lt.DAYS_PER_JULIAN_CENTURY;const c=(24110.54841+a*(8640184.812866+a*(.093104+-62e-7*a)))*_r%o.CesiumMath.TWO_PI+(72921158553e-15+11772758384668e-32*(i-2451545.5))*((s+.5*lt.SECONDS_PER_DAY)%lt.SECONDS_PER_DAY),l=Math.cos(c),d=Math.sin(c);return r.defined(t)?(t[0]=l,t[1]=-d,t[2]=0,t[3]=d,t[4]=l,t[5]=0,t[6]=0,t[7]=0,t[8]=1,t):new n.Matrix3(l,d,0,-d,l,0,0,0,1)},nr.iau2006XysData=new Gn,nr.earthOrientationParameters=Dn.NONE;const xr=32.184;nr.preloadIcrfFixed=function(e){const t=e.start.dayNumber,n=e.start.secondsOfDay+xr,r=e.stop.dayNumber,o=e.stop.secondsOfDay+xr,i=nr.iau2006XysData.preload(t,n,r,o),s=nr.earthOrientationParameters.getPromiseToLoad();return Promise.all([i,s])},nr.computeIcrfToFixedMatrix=function(e,t){r.defined(t)||(t=new n.Matrix3);const o=nr.computeFixedToIcrfMatrix(e,t);if(r.defined(o))return n.Matrix3.transpose(o,t)};const Sr=new Xn(0,0,0),Ar=new st(0,0,0,0,0),Er=new n.Matrix3,Or=new n.Matrix3;nr.computeFixedToIcrfMatrix=function(e,t){r.defined(t)||(t=new n.Matrix3);const i=nr.earthOrientationParameters.compute(e,Ar);if(!r.defined(i))return;const s=e.dayNumber,a=e.secondsOfDay+xr,u=nr.iau2006XysData.computeXysRadians(s,a,Sr);if(!r.defined(u))return;const c=u.x+i.xPoleOffset,l=u.y+i.yPoleOffset,d=1/(1+Math.sqrt(1-c*c-l*l)),f=Er;f[0]=1-d*c*c,f[3]=-d*c*l,f[6]=c,f[1]=-d*c*l,f[4]=1-d*l*l,f[7]=l,f[2]=-c,f[5]=-l,f[8]=1-d*(c*c+l*l);const p=n.Matrix3.fromRotationZ(-u.s,Or),h=n.Matrix3.multiply(f,p,Er),m=e.dayNumber-2451545,g=(e.secondsOfDay-It.computeTaiMinusUtc(e)+i.ut1MinusUtc)/lt.SECONDS_PER_DAY;let y=.779057273264+g+.00273781191135448*(m+g);y=y%1*o.CesiumMath.TWO_PI;const v=n.Matrix3.fromRotationZ(y,Or),w=n.Matrix3.multiply(h,v,Er),C=Math.cos(i.xPoleWander),_=Math.cos(i.yPoleWander),b=Math.sin(i.xPoleWander),x=Math.sin(i.yPoleWander);let S=s-2451545+a/lt.SECONDS_PER_DAY;S/=36525;const A=-47e-6*S*o.CesiumMath.RADIANS_PER_DEGREE/3600,E=Math.cos(A),O=Math.sin(A),P=Or;return P[0]=C*E,P[1]=C*O,P[2]=b,P[3]=-_*O+x*b*E,P[4]=_*E+x*b*O,P[5]=-x*C,P[6]=-x*O-_*b*E,P[7]=x*E-_*b*O,P[8]=_*C,n.Matrix3.multiply(w,P,t)};const Pr=new i.Cartesian4;nr.pointToWindowCoordinates=function(e,t,n,r){return(r=nr.pointToGLWindowCoordinates(e,t,n,r)).y=2*t[5]-r.y,r},nr.pointToGLWindowCoordinates=function(e,t,n,o){r.defined(o)||(o=new i.Cartesian2);const s=Pr;return i.Matrix4.multiplyByVector(e,i.Cartesian4.fromElements(n.x,n.y,n.z,1,s),s),i.Cartesian4.multiplyByScalar(s,1/s.w,s),i.Matrix4.multiplyByVector(t,s,s),i.Cartesian2.fromCartesian4(s,o)};const Ir=new n.Cartesian3,Rr=new n.Cartesian3,Tr=new n.Cartesian3;nr.rotationMatrixFromPositionVelocity=function(e,t,i,s){const a=r.defaultValue(i,n.Ellipsoid.WGS84).geodeticSurfaceNormal(e,Ir);let u=n.Cartesian3.cross(t,a,Rr);n.Cartesian3.equalsEpsilon(u,n.Cartesian3.ZERO,o.CesiumMath.EPSILON6)&&(u=n.Cartesian3.clone(n.Cartesian3.UNIT_X,u));const c=n.Cartesian3.cross(u,t,Tr);return n.Cartesian3.normalize(c,c),n.Cartesian3.cross(t,c,u),n.Cartesian3.negate(u,u),n.Cartesian3.normalize(u,u),r.defined(s)||(s=new n.Matrix3),s[0]=t.x,s[1]=t.y,s[2]=t.z,s[3]=u.x,s[4]=u.y,s[5]=u.z,s[6]=c.x,s[7]=c.y,s[8]=c.z,s};const qr=new i.Matrix4(0,0,1,0,1,0,0,0,0,1,0,0,0,0,0,1),zr=new n.Cartographic,Mr=new n.Cartesian3,Dr=new n.Cartesian3,Ur=new n.Matrix3,kr=new i.Matrix4,Fr=new i.Matrix4;nr.basisTo2D=function(e,t,r){const o=i.Matrix4.getTranslation(t,Dr),s=e.ellipsoid,a=s.cartesianToCartographic(o,zr),u=e.project(a,Mr);n.Cartesian3.fromElements(u.z,u.x,u.y,u);const c=nr.eastNorthUpToFixedFrame(o,s,kr),l=i.Matrix4.inverseTransformation(c,Fr),d=i.Matrix4.getMatrix3(t,Ur),f=i.Matrix4.multiplyByMatrix3(l,d,r);return i.Matrix4.multiply(qr,f,r),i.Matrix4.setTranslation(r,u,r),r},nr.wgs84To2DModelMatrix=function(e,t,r){const o=e.ellipsoid,s=nr.eastNorthUpToFixedFrame(t,o,kr),a=i.Matrix4.inverseTransformation(s,Fr),u=o.cartesianToCartographic(t,zr),c=e.project(u,Mr);n.Cartesian3.fromElements(c.z,c.x,c.y,c);const l=i.Matrix4.fromTranslation(c,kr);return i.Matrix4.multiply(qr,a,r),i.Matrix4.multiply(l,r,r),r};var Nr=nr;t.BoundingSphere=p,t.FeatureDetection=Te,t.GeographicProjection=l,t.Intersect=d,t.Interval=f,t.Quaternion=qe,t.Resource=xn,t.Transforms=Nr,t.buildModuleUrl=Jn}));
