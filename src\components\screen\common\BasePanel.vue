<template>
  <PanelBox :title="title" class="base-panel">
    <slot></slot>
  </PanelBox>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import PanelBox from '@/components/screen/PanelBox.vue';

defineProps({
  title: {
    type: String,
    required: true
  }
});
</script>

<style>
/* 低高度屏幕适配 - 全局样式 */
@media screen and (max-height: 940px) {
  /* 视频播放器适配 */
  .video-player-container {
    max-height: 130px;
  }
  
  /* 图表容器适配 */
  .echarts-container {
    height: 200px !important;
  }
  
  /* 列表适配 */
  .table-container {
    max-height: 200px !important;
  }
  
  .data-list {
    max-height: 180px !important;
  }
  
  /* 数据卡片适配 */
  .data-card {
    padding: 8px !important;
  }
  
  /* 内容间距适配 */
  .panel-content-wrapper {
    gap: 8px !important;
    padding: 5px !important;
  }
  
  /* 文字大小适配 */
  .panel-title-text {
    font-size: 14px !important;
  }
  
  .panel-subtitle {
    font-size: 12px !important;
  }
  
  /* 图标大小适配 */
  .icon-container {
    transform: scale(0.9) !important;
  }
}
</style> 