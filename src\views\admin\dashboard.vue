<template>
  <div>
    <h2 class="text-2xl font-bold mb-6">仪表盘</h2>
    
    <!-- 数据卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
      <el-card shadow="hover">
        <template #header>
          <div class="flex items-center">
            <el-icon class="text-blue-500 mr-2"><user /></el-icon>
            <span>总用户数</span>
          </div>
        </template>
        <div class="text-3xl font-bold">1,234</div>
        <div class="text-sm text-gray-500 mt-2">较上月增长 12%</div>
      </el-card>
      
      <el-card shadow="hover">
        <template #header>
          <div class="flex items-center">
            <el-icon class="text-green-500 mr-2"><data-line /></el-icon>
            <span>今日访问</span>
          </div>
        </template>
        <div class="text-3xl font-bold">256</div>
        <div class="text-sm text-gray-500 mt-2">较昨日增长 8%</div>
      </el-card>
      
      <el-card shadow="hover">
        <template #header>
          <div class="flex items-center">
            <el-icon class="text-yellow-500 mr-2"><warning /></el-icon>
            <span>告警数量</span>
          </div>
        </template>
        <div class="text-3xl font-bold">12</div>
        <div class="text-sm text-gray-500 mt-2">较昨日减少 3</div>
      </el-card>
      
      <el-card shadow="hover">
        <template #header>
          <div class="flex items-center">
            <el-icon class="text-purple-500 mr-2"><timer /></el-icon>
            <span>平均响应时间</span>
          </div>
        </template>
        <div class="text-3xl font-bold">128ms</div>
        <div class="text-sm text-gray-500 mt-2">较昨日减少 5ms</div>
      </el-card>
    </div>

    <!-- 图表区域 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <el-card shadow="hover">
        <template #header>
          <div class="flex items-center">
            <el-icon class="text-blue-500 mr-2"><trend-charts /></el-icon>
            <span>访问趋势</span>
          </div>
        </template>
        <div ref="visitChartRef" class="h-80"></div>
      </el-card>
      
      <el-card shadow="hover">
        <template #header>
          <div class="flex items-center">
            <el-icon class="text-green-500 mr-2"><pie-chart /></el-icon>
            <span>用户分布</span>
          </div>
        </template>
        <div ref="userChartRef" class="h-80"></div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useECharts } from '@/utils/echarts'
import { User, DataLine, Warning, Timer, TrendCharts, PieChart } from '@element-plus/icons-vue'

const visitChartRef = ref(null)
const userChartRef = ref(null)

onMounted(() => {
  // 访问趋势图表
  const visitChart = useECharts()
  visitChart.initChart(visitChartRef.value)
  visitChart.updateChart({
    tooltip: { trigger: 'axis' },
    xAxis: {
      type: 'category',
      data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
    },
    yAxis: { type: 'value' },
    series: [{
      data: [820, 932, 901, 934, 1290, 1330, 1320],
      type: 'line',
      smooth: true
    }]
  })

  // 用户分布图表
  const userChart = useECharts()
  userChart.initChart(userChartRef.value)
  userChart.updateChart({
    tooltip: { trigger: 'item' },
    series: [{
      type: 'pie',
      radius: '50%',
      data: [
        { value: 1048, name: '移动端' },
        { value: 735, name: 'PC端' },
        { value: 580, name: '平板' },
        { value: 484, name: '其他' }
      ]
    }]
  })
  
  // 监听窗口大小变化，更新图表
  window.addEventListener('resize', () => {
    visitChart.resizeChart()
    userChart.resizeChart()
  })
})
</script> 