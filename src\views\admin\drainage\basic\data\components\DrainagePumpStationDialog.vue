<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="1000px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="drainage-pump-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="140px"
      :disabled="mode === 'view'"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="泵站编码" prop="stationCode">
            <el-input v-model="formData.stationCode" placeholder="请输入泵站编码" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="泵站名称" prop="stationName">
            <el-input v-model="formData.stationName" placeholder="请输入泵站名称" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="泵站类型" prop="stationType">
            <el-select v-model="formData.stationType" placeholder="请选择" class="w-full">
              <el-option v-for="item in pumpStationTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="拥有泵数量" prop="pumpNum">
            <el-input-number v-model="formData.pumpNum" :min="0" :precision="0" class="w-full" placeholder="请输入泵数量" />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="设计雨水排水能力" prop="drainRainCapacity">
            <div class="input-with-unit">
              <el-input-number 
                v-model="formData.drainRainCapacity" 
                :min="0" 
                :precision="2" 
                class="w-90" 
                placeholder="请输入雨水排水能力" 
              />
              <span class="unit">m³/s</span>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="设计污水排水能力" prop="drainSewageCapacity">
            <div class="input-with-unit">
              <el-input-number 
                v-model="formData.drainSewageCapacity" 
                :min="0" 
                :precision="2" 
                class="w-90" 
                placeholder="请输入污水排水能力" 
              />
              <span class="unit">m³/s</span>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="服务范围" prop="serverRange">
            <el-input v-model="formData.serverRange" placeholder="请输入服务范围" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="服务面积" prop="serverArea">
            <div class="input-with-unit">
              <el-input-number 
                v-model="formData.serverArea" 
                :min="0" 
                :precision="2" 
                class="w-full" 
                placeholder="请输入服务面积" 
              />
              <span class="unit">km²</span>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="运行时间" prop="workTime">
            <el-date-picker
              v-model="formData.workTime"
              type="date"
              placeholder="请选择运行时间"
              class="w-full"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="建设单位" prop="constructionUnit">
            <el-input v-model="formData.constructionUnitName" placeholder="请输入建设单位" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="联系人" prop="contactUser">
            <el-input v-model="formData.contactUser" placeholder="请输入联系人" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系方式" prop="contactInfo">
            <el-input v-model="formData.contactInfo" placeholder="请输入联系方式" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="位置" prop="address">
            <div class="flex items-center">
              <el-cascader
                v-model="areaValues"
                :options="areaOptions"
                :props="{
                  value: 'code',
                  label: 'name',
                  children: 'children'
                }"
                placeholder="请选择所属区域"
                class="w-50"
                @change="handleAreaChange"
              />
              <el-input v-model="formData.address" placeholder="请输入详细地址" class="ml-2" style="width: calc(100% - 250px);" />
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="经纬度" prop="coordinates">
            <div class="flex items-center">
              <el-input v-model="formData.longitude" placeholder="经度" style="width: 180px;" />
              <el-input v-model="formData.latitude" placeholder="纬度" class="ml-2" style="width: 180px;" />
              <el-button type="primary" class="ml-2" icon="Location" circle @click="openMapPicker"></el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="位置坐标" prop="locationCoord">
            <el-input v-model="formData.locationCoord" placeholder="请输入位置坐标" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="使用状态" prop="usageStatus">
            <el-select v-model="formData.usageStatus" placeholder="请选择" class="w-full">
              <el-option v-for="item in usageStatusOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="备注">
            <el-input
              v-model="formData.remarks"
              type="textarea"
              :rows="4"
              placeholder="请输入备注"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="mode !== 'view'">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted, onUnmounted, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import { savePumpStation, updatePumpStation } from '@/api/drainage';
import { PUMP_STATION_TYPE_OPTIONS, USE_TYPE } from '@/constants/drainage';
import { AREA_OPTIONS } from '@/constants/gas';
import { collectShow } from "@/hooks/gishooks";
import bus from '@/utils/mitt';
import moment from 'moment';

// 过滤掉选项中的"全部"选项
const pumpStationTypeOptions = PUMP_STATION_TYPE_OPTIONS.filter(item => item.value !== '');
const usageStatusOptions = USE_TYPE;

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add', // 'add', 'edit', 'view'
    validator: (value) => ['add', 'edit', 'view'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'success']);

// 表单引用
const formRef = ref(null);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 对话框标题
const dialogTitle = computed(() => {
  const titles = {
    add: '新增泵站',
    edit: '编辑泵站',
    view: '泵站详情'
  };
  return titles[props.mode] || '泵站信息';
});

// 行政区划选项和选中值
const areaOptions = ref(AREA_OPTIONS);
const areaValues = ref([]);

// 表单数据
const formData = reactive({
  id: '',
  stationCode: '',
  stationName: '',
  stationType: '',
  stationTypeName: '',
  pumpNum: '',
  drainRainCapacity: '',
  drainSewageCapacity: '',
  serverRange: '',
  serverArea: '',
  workTime: null,
  workTimeStr: '',
  constructionUnit: '',
  constructionUnitName: '',
  contactUser: '',
  contactInfo: '',
  address: '',
  longitude: '',
  latitude: '',
  locationCoord: '', // 用于显示经纬度的组合值
  usageStatus: '',
  usageStatusName: '',
  city: '',
  county: '371728',
  countyName: '东明县',
  town: '',
  townName: '',
  remarks: ''
});

// 表单验证规则
const formRules = {
  stationCode: [{ required: true, message: '请输入泵站编码', trigger: 'blur' }],
  stationName: [{ required: true, message: '请输入泵站名称', trigger: 'blur' }],
  stationType: [{ required: true, message: '请选择泵站类型', trigger: 'change' }],
  pumpNum: [{ required: true, message: '请输入泵数量', trigger: 'blur' }],
  drainRainCapacity: [{ required: true, message: '请输入雨水排水能力', trigger: 'blur' }],
  drainSewageCapacity: [{ required: true, message: '请输入污水排水能力', trigger: 'blur' }],
  usageStatus: [{ required: true, message: '请选择使用状态', trigger: 'change' }]
};

// 重置表单
const resetForm = () => {
  // 重置表单数据
  Object.keys(formData).forEach(key => {
    formData[key] = '';
  });
  
  // 保留默认值
  formData.county = '371728';
  formData.countyName = '东明县';
  areaValues.value = [];
};

// 更新各字段的名称，基于选中的值
const updateNamesByValues = () => {
  // 泵站类型
  const selectedStationType = pumpStationTypeOptions.find(item => item.value === formData.stationType);
  if (selectedStationType) {
    formData.stationTypeName = selectedStationType.label;
  }
  
  // 使用状态
  const selectedUsageStatus = usageStatusOptions.find(item => item.value === formData.usageStatus);
  if (selectedUsageStatus) {
    formData.usageStatusName = selectedUsageStatus.label;
  }
};

// 更新经纬度组合显示
const updateLocationCoord = () => {
  if (formData.longitude && formData.latitude) {
    formData.locationCoord = `${formData.longitude},${formData.latitude}`;
  } else {
    formData.locationCoord = '';
  }
};

// 监听props.data变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    // 复制数据到表单
    Object.keys(formData).forEach(key => {
      if (newVal[key] !== undefined) {
        formData[key] = newVal[key];
      }
    });
    
    // 处理日期格式
    if (newVal.workTime) {
      if (typeof newVal.workTime === 'object' && newVal.workTime.time) {
        formData.workTime = new Date(newVal.workTime.time);
      } else if (typeof newVal.workTime === 'string') {
        formData.workTime = new Date(newVal.workTime);
      }
    }
    
    // 处理area级联选择器
    if (newVal.town) {
      // 在实际情况中，你可能需要反向查找完整的路径
      // 这里简化处理，只保存最终值
      areaValues.value = [newVal.town];
    }
    
    // 更新经纬度显示
    updateLocationCoord();
  } else if (props.mode === 'add') {
    // 新增模式清空表单，保留默认值
    resetForm();
  }
}, { immediate: true, deep: true });

// 监听经纬度变化，自动更新组合显示
watch([() => formData.longitude, () => formData.latitude], () => {
  updateLocationCoord();
});

// 监听下拉框值变化，自动更新对应的名称
watch(() => formData.stationType, (val) => {
  if (val) {
    const selected = pumpStationTypeOptions.find(item => item.value === val);
    if (selected) {
      formData.stationTypeName = selected.label;
    }
  }
});

watch(() => formData.usageStatus, (val) => {
  if (val) {
    const selected = usageStatusOptions.find(item => item.value === val);
    if (selected) {
      formData.usageStatusName = selected.label;
    }
  }
});

// 处理区域选择变化
const handleAreaChange = (value) => {
  if (value && value.length > 0) {
    formData.town = value[value.length - 1];
    // 更新区域名称
    const selectedArea = findAreaByCode(areaOptions.value, formData.town);
    if (selectedArea) {
      formData.townName = selectedArea.name;
    }
  }
};

// 根据区域代码查找区域信息
const findAreaByCode = (areas, code) => {
  for (const area of areas) {
    if (area.code === code) {
      return area;
    }
    if (area.children) {
      const found = findAreaByCode(area.children, code);
      if (found) return found;
    }
  }
  return null;
};

// 地图选点事件处理函数
const handleCollectLocation = (params) => {
  nextTick(() => {
    formData.longitude = params.longitude;
    formData.latitude = params.latitude;
    updateLocationCoord();
  });
};

// 打开地图选点
const openMapPicker = () => {
  collectShow.value = true; // 激活采集点位窗口
  // 先移除可能存在的旧监听器
  bus.off("getCollectLocation", handleCollectLocation);
  // 添加新的监听器
  bus.on("getCollectLocation", handleCollectLocation);
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  // 重置表单
  if (formRef.value) {
    formRef.value.resetFields();
  }
  resetForm();
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;
  
  try {
    await formRef.value.validate();
    
    // 准备提交数据
    const submitData = { ...formData };
    
    // 处理工作时间
    if (submitData.workTime) {
      submitData.workTime = moment(submitData.workTime).format('YYYY-MM-DD HH:mm:ss');
    }
    
    // 设置枚举值对应的名称
    updateNamesByValues();
    
    // 提交数据
    let res;
    if (props.mode === 'add') {
      res = await savePumpStation(submitData);
    } else if (props.mode === 'edit') {
      res = await updatePumpStation(submitData);
    }
    
    if (res && res.code === 200) {
      ElMessage.success(props.mode === 'add' ? '新增成功' : '更新成功');
      emit('success');
      handleClose();
    } else {
      ElMessage.error(res?.msg || (props.mode === 'add' ? '新增失败' : '更新失败'));
    }
  } catch (error) {
    console.error('表单验证失败', error);
  }
};

// 组件卸载时清理事件监听
onUnmounted(() => {
  bus.off("getCollectLocation", handleCollectLocation);
});

// 组件挂载时初始化
onMounted(() => {
  // 可以在这里添加其他初始化操作
});
</script>

<style scoped>
.drainage-pump-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-form-item__label::before) {
  content: '*';
  color: #ff4d4f;
  margin-right: 4px;
}

:deep(.el-form-item:not([required]) .el-form-item__label::before) {
  content: '';
  margin-right: 0;
}

:deep(.el-input__wrapper),
:deep(.el-select__input) {
  border-radius: 2px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  background-color: #0277FD;
  border-color: #0277FD;
}

:deep(.el-button--primary:hover) {
  background-color: #1890ff;
  border-color: #1890ff;
}

.w-full {
  width: 100%;
}

.w-70 {
  width: 70%;
}

.w-25 {
  width: 25%;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.ml-2 {
  margin-left: 8px;
}

.position-container {
  display: flex;
  align-items: center;
}

.input-with-unit {
  display: flex;
  align-items: center;
}

.unit {
  margin-left: 8px;
  color: #606266;
}

.w-90 {
  width: 90%;
}

.w-50 {
  width: 50%;
}
</style>  