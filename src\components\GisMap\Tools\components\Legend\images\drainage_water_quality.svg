<?xml version="1.0" encoding="UTF-8"?>
<svg width="18px" height="18px" viewBox="0 0 18 18" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>水质检测仪</title>
    <defs>
        <circle id="path-1" cx="7" cy="7" r="7"></circle>
        <filter x="-21.4%" y="-21.4%" width="142.9%" height="142.9%" filterUnits="objectBoundingBox" id="filter-2">
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="1" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.162200418   0 0 0 0 0.568546468   0 0 0 0 0.902202219  0 0 0 0.5 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="27.9288816%" y1="8.11919591%" x2="71.2992224%" y2="91.9872084%" id="linearGradient-3">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#A5E2FF" offset="16.835118%"></stop>
            <stop stop-color="#83BEFF" offset="71.3150675%"></stop>
            <stop stop-color="#C6E3FF" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="图层&amp;图例" transform="translate(-705.000000, -904.000000)">
            <g id="编组-13" transform="translate(683.000000, 30.000000)">
                <g id="编组-45备份" transform="translate(0.000000, 65.000000)">
                    <g id="编组-2备份-2" transform="translate(24.000000, 745.000000)">
                        <g id="水质检测仪" transform="translate(0.000000, 66.000000)">
                            <g id="正常备份-2">
                                <g id="椭圆形">
                                    <use fill="black" fill-opacity="1" filter="url(#filter-2)" xlink:href="#path-1"></use>
                                    <use fill="#0099FF" fill-rule="evenodd" xlink:href="#path-1"></use>
                                </g>
                                <circle id="椭圆形" stroke="url(#linearGradient-3)" stroke-width="0.823529412" cx="7" cy="7" r="6.58823529"></circle>
                            </g>
                            <g id="编组" transform="translate(3.000000, 3.000000)" fill="#FFFFFF" fill-rule="nonzero">
                                <path d="M3.36842105,4.8004 C3.36842105,3.4736 5.68421053,1.2 5.68421053,1.2 C5.68421053,1.2 8,3.4752 8,4.8 C8,6.1252 6.96294737,7.2 5.68421053,7.2 C4.40547368,7.2 3.36842105,6.1252 3.36842105,4.8 L3.36842105,4.8004 Z" id="路径" fill-opacity="0.715362762"></path>
                                <path d="M3.15789474,0 C3.15789474,0 6.31578947,3.344 6.31578947,5 C6.31578947,6.65685425 4.90195184,8 3.15789474,8 C1.41383763,8 0,6.65685425 0,5 C0,3.3432 3.15789474,0 3.15789474,0 Z M2.93442872,4.83085213 C1.99816793,4.32749136 1.06610526,3.91946667 1.06610526,4.9528 C1.06610526,6.0688 2.00252632,6.9736 3.15873684,6.9736 C4.31410526,6.9736 5.25136842,6.0688 5.25136842,4.9528 C5.25136842,3.8368 4.168,5.506 3.15873684,4.9528 L3.15873684,4.9528 Z" id="形状结合"></path>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>