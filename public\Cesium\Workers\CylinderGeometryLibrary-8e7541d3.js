/**
 *    ████████                   ██                   ██       ██                  ██      ██
 *   ██░░░░░░██                 ░░                   ░██      ░██                 ░██     ░██
 *  ██      ░░   █████  ███████  ██ ██   ██  ██████  ░██   █  ░██  ██████  ██████ ░██     ░██
 * ░██          ██░░░██░░██░░░██░██░██  ░██ ██░░░░   ░██  ███ ░██ ██░░░░██░░██░░█ ░██  ██████
 * ░██    █████░███████ ░██  ░██░██░██  ░██░░█████   ░██ ██░██░██░██   ░██ ░██ ░  ░██ ██░░░██
 * ░░██  ░░░░██░██░░░░  ░██  ░██░██░██  ░██ ░░░░░██  ░████ ░░████░██   ░██ ░██    ░██░██  ░██
 *  ░░████████ ░░██████ ███  ░██░██░░██████ ██████   ░██░   ░░░██░░██████ ░███    ███░░██████
 *   ░░░░░░░░   ░░░░░░ ░░░   ░░ ░░  ░░░░░░ ░░░░░░    ░░       ░░  ░░░░░░  ░░░    ░░░  ░░░░░░
 *
 * @license
 * GeniusWorld Web SDK - http://zydlxx.cn:1234/
 * Version 3.5.0#develop-c1086f5b@202310131719
 *
 *
 * Copyright © 2020 - 2023 正元地理信息集团股份有限公司. All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
define(["exports","./Math-a28f0fcd"],(function(t,n){"use strict";const o={computePositions:function(t,o,e,r,s){const c=.5*t,i=-c,a=r+r,f=new Float64Array(3*(s?2*a:a));let u,h=0,y=0;const M=s?3*a:0,d=s?3*(a+r):3*r;for(u=0;u<r;u++){const t=u/r*n.CesiumMath.TWO_PI,a=Math.cos(t),l=Math.sin(t),m=a*e,p=l*e,C=a*o,P=l*o;f[y+M]=m,f[y+M+1]=p,f[y+M+2]=i,f[y+d]=C,f[y+d+1]=P,f[y+d+2]=c,y+=3,s&&(f[h++]=m,f[h++]=p,f[h++]=i,f[h++]=C,f[h++]=P,f[h++]=c)}return f}};var e=o;t.CylinderGeometryLibrary=e}));
