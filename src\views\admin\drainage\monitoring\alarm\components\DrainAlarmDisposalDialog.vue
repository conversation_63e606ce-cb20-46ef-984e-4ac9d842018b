<template>
  <el-dialog
    v-model="dialogVisible"
    title="报警处置"
    width="1200px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="drain-alarm-disposal-dialog"
  >
    <div class="disposal-content">
      <!-- 新增处置按钮 -->
      <div class="action-bar">
        <el-button type="primary" @click="handleAddHandle">新增处置</el-button>
      </div>

      <!-- 处置记录列表 -->
      <div class="disposal-section">
        <h3 class="section-title">处置记录</h3>
        <el-table
          :data="handleList"
          style="width: 100%"
          :header-cell-style="headerCellStyle"
          height="200"
        >
          <el-table-column label="序号" width="60" align="center">
            <template #default="{ $index }">
              {{ $index + 1 }}
            </template>
          </el-table-column>
          <el-table-column prop="handleStatusName" label="处置状态" width="100" align="center" />
          <el-table-column prop="description" label="处置描述" min-width="150" align="center" />
          <el-table-column prop="handleUser" label="处置人员" width="100" align="center" />
          <el-table-column prop="unit" label="处置单位" width="120" align="center" />
          <el-table-column prop="createTime" label="处置时间" width="150" align="center" />
          <el-table-column label="处置图片" width="100" align="center">
            <template #default="scope">
              <span v-if="scope.row.picUrls" class="preview-btn" @click="handlePreviewPhotos(scope.row)">查看</span>
              <span v-else class="no-photo">无</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120" align="center">
            <template #default="scope">
              <div class="operation-btns">
                <span class="operation-btn-text" @click="handleViewHandle(scope.row)">查看</span>
                <span class="operation-divider">|</span>
                <span class="operation-btn-text" @click="handleEditHandle(scope.row)">编辑</span>
                <span class="operation-divider">|</span>
                <span class="operation-btn-text delete-btn" @click="handleDeleteHandle(scope.row)">删除</span>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>

    <!-- 处置表单弹窗 -->
    <DrainAlarmHandleFormDialog
      v-model:visible="handleFormVisible"
      :mode="handleFormMode"
      :alarm-data="alarmData"
      :handle-data="currentHandleData"
      @success="handleFormSuccess"
    />

    <!-- 照片预览弹窗 -->
    <el-dialog v-model="photoPreviewVisible" title="照片预览" width="80%">
      <div class="photo-preview-container">
        <el-image
          v-for="(photo, index) in previewPhotos"
          :key="index"
          :src="photo"
          fit="contain"
          class="preview-image"
          :preview-src-list="previewPhotos"
          :initial-index="index"
        />
      </div>
    </el-dialog>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getDrainAlarmHandleList, deleteDrainAlarmHandle } from '@/api/drainage';
import { DRAIN_HANDLE_STATUS_MAP } from '@/constants/drainage';
import DrainAlarmHandleFormDialog from './DrainAlarmHandleFormDialog.vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  alarmData: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'success']);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 数据列表
const handleList = ref([]);

// 处置表单弹窗相关
const handleFormVisible = ref(false);
const handleFormMode = ref('add');
const currentHandleData = ref({});

// 照片预览相关
const photoPreviewVisible = ref(false);
const previewPhotos = ref([]);

// 表头样式
const headerCellStyle = {
  background: '#F4F4F4',
  fontFamily: 'PingFangSC, PingFang SC',
  fontWeight: '500',
  fontSize: '14px',
  color: '#282828',
  height: '48px'
};

// 获取处置相关数据
const fetchDisposalData = async () => {
  if (!props.alarmData.id) return;
  
  try {
    const res = await getDrainAlarmHandleList(props.alarmData.id);
    if (res && res.code === 200 && res.data) {
      // 处理处置记录列表，添加状态名称
      const dataList = res.data.usmMonitorAlarmStatusDtos || res.data || [];
      handleList.value = (Array.isArray(dataList) ? dataList : [dataList]).map(item => ({
        ...item,
        handleStatusName: DRAIN_HANDLE_STATUS_MAP[item.handleStatus] || item.handleStatusName || '未知'
      }));
    }
  } catch (error) {
    console.error('获取处置数据失败:', error);
    ElMessage.error('获取处置数据失败');
  }
};

// 新增处置
const handleAddHandle = () => {
  handleFormMode.value = 'add';
  currentHandleData.value = {};
  handleFormVisible.value = true;
};

// 查看处置
const handleViewHandle = (row) => {
  handleFormMode.value = 'view';
  currentHandleData.value = row;
  handleFormVisible.value = true;
};

// 编辑处置
const handleEditHandle = (row) => {
  handleFormMode.value = 'edit';
  currentHandleData.value = row;
  handleFormVisible.value = true;
};

// 删除处置
const handleDeleteHandle = async (row) => {
  try {
    await ElMessageBox.confirm('确定要删除这条处置记录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });

    const res = await deleteDrainAlarmHandle(row.id);
    if (res.code === 200) {
      ElMessage.success('删除成功');
      fetchDisposalData(); // 重新获取数据
      emit('success'); // 通知父组件刷新
    } else {
      ElMessage.error(res.message || '删除失败');
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除处置记录失败:', error);
      ElMessage.error('删除失败');
    }
  }
};

// 预览照片
const handlePreviewPhotos = (row) => {
  if (row.picUrls) {
    previewPhotos.value = row.picUrls.split(',').filter(url => url.trim());
    photoPreviewVisible.value = true;
  }
};

// 处置表单操作成功回调
const handleFormSuccess = () => {
  fetchDisposalData(); // 重新获取数据
  emit('success'); // 通知父组件刷新
};

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false;
  // 清空数据
  handleList.value = [];
};

// 监听弹窗显示，加载数据
watch(() => props.visible, (visible) => {
  if (visible && props.alarmData.id) {
    fetchDisposalData();
  }
});
</script>

<style scoped>
.drain-alarm-disposal-dialog {
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "Helvetica Neue", Arial, sans-serif;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
  max-height: 80vh;
  overflow-y: auto;
}

.disposal-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.action-bar {
  display: flex;
  justify-content: flex-end;
  padding-bottom: 10px;
  border-bottom: 1px solid #f0f0f0;
}

.disposal-section {
  display: flex;
  flex-direction: column;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin: 0 0 10px 0;
  padding-left: 10px;
  border-left: 4px solid #0277FD;
}

:deep(.el-table) {
  --el-table-border-color: #EBEEF5;
  --el-table-header-bg-color: #F5F8FA;
}

:deep(.el-table th) {
  background-color: #F5F8FA;
  color: #333333;
  font-weight: bold;
  padding: 0;
  border-bottom: 1px solid #EBEEF5;
}

:deep(.el-table td) {
  padding: 8px 0;
}

.operation-btns {
  display: flex;
  justify-content: center;
  align-items: center;
}

.operation-btn-text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #0086FF;
  cursor: pointer;
}

.operation-btn-text.delete-btn {
  color: #F56C6C;
}

.operation-divider {
  margin: 0 8px;
  color: #CED3DA;
}

.preview-btn {
  color: #0086FF;
  cursor: pointer;
  font-size: 14px;
}

.no-photo {
  color: #999;
  font-size: 14px;
}

.photo-preview-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  justify-content: center;
}

.preview-image {
  max-width: 200px;
  max-height: 200px;
  cursor: pointer;
}

.dialog-footer {
  text-align: right;
  padding-top: 20px;
}
</style> 