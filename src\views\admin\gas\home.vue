<template>
  <div class="gas-home">
    <!-- 顶部卡片统计 -->
    <div class="top-section">
      <!-- 左侧管网信息 -->
      <div class="network-cards">
        <!-- 燃气管网 -->
        <div class="network-card" style="background: #F1F8FF;">
          <div class="icon-box">
            <img src="@/assets/images/mis/gas/ranqiguanwang.png" alt="燃气管网">
          </div>
          <div class="content">
            <div class="title">燃气管网</div>
            <div class="data">
              <span class="value">{{ pipelineStats.totalLength }}</span>
              <span class="unit">km</span>
            </div>
          </div>
          <div class="chart-img">
            <img src="@/assets/images/mis/gas/rani.png" alt="燃气管网图表">
          </div>
        </div>

        <!-- 高压管网 -->
        <div class="network-card" style="background: #FFF3F1;">
          <div class="icon-box">
            <img src="@/assets/images/mis/gas/gaoyaguanwang.png" alt="高压管网">
          </div>
          <div class="content">
            <div class="title">高压管网</div>
            <div class="data">
              <span class="value">{{ pipelineStats.highLength }}</span>
              <span class="unit">km</span>
            </div>
          </div>
          <div class="chart-img">
            <img src="@/assets/images/mis/gas/gaoya.png" alt="高压管网图表">
          </div>
        </div>

        <!-- 中压管网 -->
        <div class="network-card" style="background: #FFF8F0;">
          <div class="icon-box">
            <img src="@/assets/images/mis/gas/zhongyaguanwang.png" alt="中压管网">
          </div>
          <div class="content">
            <div class="title">中压管网</div>
            <div class="data">
              <span class="value">{{ pipelineStats.mediumLength }}</span>
              <span class="unit">km</span>
            </div>
          </div>
          <div class="chart-img">
            <img src="@/assets/images/mis/gas/zhongya.png" alt="中压管网图表">
          </div>
        </div>

        <!-- 低压管网 -->
        <div class="network-card" style="background: #F1F5FF;">
          <div class="icon-box">
            <img src="@/assets/images/mis/gas/diyaguanwang.png" alt="低压管网">
          </div>
          <div class="content">
            <div class="title">低压管网</div>
            <div class="data">
              <span class="value">{{ pipelineStats.lowLength }}</span>
              <span class="unit">km</span>
            </div>
          </div>
          <div class="chart-img">
            <img src="@/assets/images/mis/gas/diya.png" alt="低压管网图表">
          </div>
        </div>

        <!-- 管网维修 -->
        <div class="network-card" style="background: #E7FCFF;">
          <div class="icon-box">
            <img src="@/assets/images/mis/gas/guanwnagweixiu.png" alt="管网维修">
          </div>
          <div class="content">
            <div class="title">管网维修</div>
            <div class="data">
              <span class="value">{{ pipelineStats.repairCount }}</span>
              <span class="unit">次</span>
            </div>
          </div>
          <div class="chart-img">
            <img src="@/assets/images/mis/gas/guanwang.png" alt="管网维修图表">
          </div>
        </div>
      </div>

      <!-- 右侧报警信息 -->
      <div class="alarm-info">
        <div class="alarm-row">
          <div class="alarm-title">今日报警</div>
          <div class="alarm-value alarm-today">{{ alarmCount.todayCount }}</div>
        </div>
        <div class="alarm-row">
          <div class="alarm-title">本月报警</div>
          <div class="alarm-value alarm-month">{{ alarmCount.monthCount }}</div>
        </div>
      </div>
    </div>

    <!-- 第二行：待处理报警和报警列表 -->
    <div class="second-row">
      <!-- 左侧：待处理报警区域 -->
      <div class="pending-alarm-section">
        <div class="section-header">
          <div class="section-title">待处理报警</div>
          <!-- <div class="view-all">查看全部</div> -->
        </div>

        <!-- 报警分级统计 -->
        <div class="alarm-levels">
          <div class="level-card level-one">
            <div class="level-name">一级报警</div>
            <div class="level-value">{{ alarmLevelStats.level1count }}</div>
          </div>
          <div class="level-card level-two">
            <div class="level-name">二级报警</div>
            <div class="level-value">{{ alarmLevelStats.level2count }}</div>
          </div>
          <div class="level-card level-three">
            <div class="level-name">三级报警</div>
            <div class="level-value">{{ alarmLevelStats.level3count }}</div>
          </div>
        </div>

        <!-- 报警列表 -->
        <div class="alarm-list">
          <div v-for="(alarm, index) in (shouldTruncate ? formattedAlarmList : alarmList)" :key="index" class="alarm-item">
            <div class="alarm-info-detail">
              <div class="alarm-title">{{ alarm.deviceName }}报警</div>
              <div class="alarm-location-time">
                <div class="alarm-location">
                  <el-icon>
                    <Location />
                  </el-icon>
                  <span>{{ alarm.address }}</span>
                </div>
                <div class="alarm-time">
                  <el-icon>
                    <Clock />
                  </el-icon>
                  <span>{{ alarm.alarmTime }}</span>
                </div>
              </div>
            </div>
            <div class="alarm-level-tag" :class="`level-${alarm.alarmLevel}-tag`">
              {{ alarm.alarmLevelName }}
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：报警列表 -->
      <div class="alarm-table-section">
        <div class="section-header">
          <div class="section-title">报警列表</div>
        </div>

        <div class="alarm-table-container">
          <el-table :data="alarmTableData" style="width: 100%"
            :header-cell-style="{ background: '#EEF5FF', color: '#0E1D33', fontWeight: '600' }"
            :row-class-name="tableRowClassName" highlight-current-row>
            <el-table-column prop="index" label="序号" width="70" align="center"></el-table-column>
            <el-table-column prop="deviceName" label="设备名称" min-width="180"></el-table-column>
            <el-table-column prop="location" label="位置" min-width="300"></el-table-column>
            <el-table-column prop="alarmCount" label="报警次数" width="100" align="center"></el-table-column>
          </el-table>
        </div>
      </div>
    </div>

    <!-- 底部统计和地图 -->
    <div class="bottom-section">
      <div class="statistics-charts">
        <div class="section-header">
          <div class="section-title">报警统计</div>
          <div class="action">
            <el-radio-group v-model="timeRange" size="small">
              <el-radio-button label="近7日" value="7"></el-radio-button>
              <el-radio-button label="近30日" value="30"></el-radio-button>
            </el-radio-group>
          </div>
        </div>

        <div class="statistics-data">
          <div class="stat-item">
            <div class="stat-value">{{ alarmStatistics.alarmCount }}</div>
            <div class="stat-label">全部报警</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ alarmStatistics.handleCount }}</div>
            <div class="stat-label">已处理</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ alarmStatistics.handleRate }}%</div>
            <div class="stat-label">处理完成率</div>
          </div>
        </div>

        <div ref="trendChartRef" class="trend-chart-container"></div>
      </div>

      <div class="map-container">
        <div class="section-header">
          <div class="section-title">管网泄漏监测设备分布</div>
        </div>
        <div class="map-content">
            <MapGisMis />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive, onBeforeUnmount, computed, watch } from 'vue'
import * as echarts from 'echarts'
import { Location, Clock } from '@element-plus/icons-vue'
import MapGisMis from '@/components/GisMap/components/MapGisMis/index.vue' // 地图组件
import { 
  getHomePagePipelineStatistics,
  getHomePageAlarmCount,
  getHomePageAlarmUnhandleStatistics,
  getHomePageAlarmRank,
  getHomePageAlarmStatistics
} from '@/api/gas'

// 窗口尺寸状态
const windowSize = ref({
  width: window.innerWidth,
  height: window.innerHeight
})

// 根据窗口大小计算是否应该截断长文本
const shouldTruncate = computed(() => {
  return windowSize.value.width < 1440
})

// 格式化超长文本的辅助函数
const formatText = (text, maxLength = 20) => {
  if (!shouldTruncate.value || !text) return text
  return text.length > maxLength ? text.substring(0, maxLength) + '...' : text
}

// 管网统计数据
const pipelineStats = ref({
  totalLength: 0,
  lowLength: 0,
  mediumLength: 0,
  highLength: 0,
  repairCount: 0
})

// 报警数量统计
const alarmCount = ref({
  todayCount: 0,
  monthCount: 0
})

// 未处理报警统计
const alarmLevelStats = ref({
  level1count: 0,
  level2count: 0,
  level3count: 0
})

// 报警列表数据
const alarmList = ref([])

// 格式化后的报警列表，用于在小屏幕上显示
const formattedAlarmList = computed(() => {
  return alarmList.value.map(alarm => ({
    ...alarm,
    title: formatText(alarm.alarmCode + ' ' + alarm.deviceTypeName, 30),
    location: formatText(alarm.address, 25),
    time: formatText(alarm.alarmTime ? alarm.alarmTime.substring(5, 16) : '', 16),
    level: alarm.alarmLevel === '9101' ? 'one' : alarm.alarmLevel === '9102' ? 'two' : 'three',
    levelText: alarm.alarmLevelName || '三级报警',
    highlighted: false
  }))
})

// 报警表格数据
const alarmTableData = ref([])

// 时间范围选择
const timeRange = ref('7')

// 报警统计数据
const alarmStatistics = ref({
  alarmCount: 0,
  handleCount: 0,
  handleRate: 0,
  alarmTrendStatistics: []
})

// 图表引用
const trendChartRef = ref(null)
const mapRef = ref(null)
let trendChart = null

// 初始化图表
onMounted(() => {
  // 监听窗口大小变化，调整图表大小
  window.addEventListener('resize', handleResize)

  // 初始化数据
  fetchAllData()
})

onBeforeUnmount(() => {
  // 移除监听器
  window.removeEventListener('resize', handleResize)

  // 释放图表实例
  if (trendChart) {
    trendChart.dispose()
    trendChart = null
  }
})

// 监听时间范围变化
watch(timeRange, (newVal) => {
  fetchAlarmStatistics()
})

// 处理窗口大小变化
const handleResize = () => {
  windowSize.value = {
    width: window.innerWidth,
    height: window.innerHeight
  }

  if (trendChart) {
    trendChart.resize()
  }
}

// 初始化趋势图表
const initTrendChart = () => {
  if (trendChartRef.value) {
    if (trendChart) {
      trendChart.dispose()
    }
    
    trendChart = echarts.init(trendChartRef.value)

    // 处理图表数据
    const dates = alarmStatistics.value.alarmTrendStatistics.map(item => item.date.substring(5)) // 只显示月-日
    const counts = alarmStatistics.value.alarmTrendStatistics.map(item => item.totalCount)

    const option = {
      tooltip: {
        trigger: 'axis'
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: dates.length > 0 ? dates : ['无数据']
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          formatter: '{value}'
        }
      },
      series: [
        {
          name: '报警数',
          type: 'line',
          smooth: true,
          data: counts.length > 0 ? counts : [0],
          markPoint: {
            data: [
              { type: 'max', name: '最大值' },
              { type: 'min', name: '最小值' }
            ]
          },
          itemStyle: {
            color: '#409EFF'
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: 'rgba(64, 158, 255, 0.8)'
                },
                {
                  offset: 1,
                  color: 'rgba(64, 158, 255, 0.1)'
                }
              ]
            }
          }
        }
      ]
    }

    trendChart.setOption(option)
  }
}

// 获取所有数据
const fetchAllData = async () => {
  try {
    await Promise.all([
      fetchPipelineStatistics(),
      fetchAlarmCount(),
      fetchAlarmUnhandleStatistics(),
      fetchAlarmRank(),
      fetchAlarmStatistics()
    ])
  } catch (error) {
    console.error('获取首页数据失败', error)
  }
}

// 获取管网统计数据
const fetchPipelineStatistics = async () => {
  try {
    const response = await getHomePagePipelineStatistics()
    if (response.code === 200 && response.data) {
      pipelineStats.value = response.data
    }
  } catch (error) {
    console.error('获取管网统计数据失败', error)
  }
}

// 获取报警数量统计
const fetchAlarmCount = async () => {
  try {
    const response = await getHomePageAlarmCount()
    if (response.code === 200 && response.data) {
      alarmCount.value = response.data
    }
  } catch (error) {
    console.error('获取报警数量统计失败', error)
  }
}

// 获取未处理报警统计
const fetchAlarmUnhandleStatistics = async () => {
  try {
    const response = await getHomePageAlarmUnhandleStatistics()
    if (response.code === 200 && response.data) {
      alarmLevelStats.value = {
        level1count: response.data.level1count || 0,
        level2count: response.data.level2count || 0,
        level3count: response.data.level3count || 0
      }

      // 更新报警列表
      if (response.data.alarmInfoPage && response.data.alarmInfoPage.records) {
        alarmList.value = response.data.alarmInfoPage.records
      }
    }
  } catch (error) {
    console.error('获取未处理报警统计失败', error)
  }
}

// 获取报警排名
const fetchAlarmRank = async () => {
  try {
    const response = await getHomePageAlarmRank()
    if (response.code === 200 && response.data && response.data.records) {
      // 添加序号
      alarmTableData.value = response.data.records.map((item, index) => ({
        ...item,
        index: index + 1
      }))
    }
  } catch (error) {
    console.error('获取报警排名失败', error)
  }
}

// 获取报警统计数据
const fetchAlarmStatistics = async () => {
  try {
    const response = await getHomePageAlarmStatistics(Number(timeRange.value))
    if (response.code === 200 && response.data) {
      alarmStatistics.value = response.data
      // 初始化或更新趋势图表
      initTrendChart()
    }
  } catch (error) {
    console.error('获取报警统计数据失败', error)
  }
}

// 表格行的类名 - 用于实现斑马纹效果
const tableRowClassName = ({ rowIndex }) => {
  return rowIndex % 2 === 1 ? 'striped-row' : '';
}
</script>

<style scoped>
.gas-home {
  padding: 1px;
  height: 99%;
  overflow: auto;
}

/* 顶部卡片统计样式 */
.top-section {
  display: flex;
  gap: 16px;
  margin-bottom: 20px;
}

.network-cards {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  width: 1556px;
  height: 140px;
  background: #FFFFFF;
  padding: 15px;
  border-radius: 4px;
}

.network-card {
  display: flex;
  width: 292px;
  height: 110px;
  border-radius: 4px;
  padding: 12px;
  box-sizing: border-box;
}

.icon-box {
  width: 56px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.icon-box img {
  width: 56px;
  height: 56px;
}

.content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.title {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 18px;
  color: #000000;
  margin-bottom: 8px;
}

.data {
  display: flex;
  align-items: baseline;
}

.value {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 24px;
  color: #000000;
}

.unit {
  font-size: 14px;
  color: #909399;
  margin-left: 4px;
}

.chart-img {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.chart-img img {
  max-width: 84px;
  max-height: 64px;
}

/* 为不同的图表设置不同的大小 */
.network-card:nth-child(1) .chart-img img {
  width: 84px;
  height: 64px;
}

.network-card:nth-child(2) .chart-img img,
.network-card:nth-child(4) .chart-img img {
  width: 78px;
  height: 51px;
}

.network-card:nth-child(3) .chart-img img {
  width: 64px;
  height: 64px;
}

.network-card:nth-child(5) .chart-img img {
  width: 84px;
  height: 41px;
}

/* 右侧报警信息样式 */
.alarm-info {
  width: 300px;
  height: 140px;
  background: linear-gradient(180deg, #FFE9E9 0%, #FFF7F7 100%);
  border-radius: 4px;
  border: 1px solid #EFF0F2;
  display: flex;
  gap: 78px;
  padding: 39px 47px;
}

.alarm-row {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 12px;
}

.alarm-row:last-child {
  margin-bottom: 0;
}

.alarm-title {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 16px;
  color: #0E1D33;
  margin-bottom: 4px;
  white-space: nowrap;
}

.alarm-value {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 32px;
}

.alarm-today {
  color: #FF1414;
}

.alarm-month {
  color: #333333;
}

/* 第二行布局样式 */
.second-row {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

/* 左侧：待处理报警区域样式 */
.pending-alarm-section {
  flex: 1;
  height: 378px;
  background: #FFFFFF;
  border: 1px solid #EFF0F2;
  padding: 20px;
  box-sizing: border-box;
  border-radius: 4px;
}

/* 右侧：报警列表区域样式 */
.alarm-table-section {
  flex: 1;
  height: 378px;
  background: #FFFFFF;
  border: 1px solid #EFF0F2;
  padding: 20px;
  box-sizing: border-box;
  border-radius: 4px;
}

/* 区域标题样式 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-title {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 18px;
  color: #222222;
}

.view-all {
  width: 56px;
  height: 20px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 14px;
  color: #2D7EFF;
  cursor: pointer;
}

/* 报警分级卡片样式 */
.alarm-levels {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.level-card {
  width: 278px;
  height: 75px;
  border-radius: 4px;
  border: 1px solid #F3F3F3;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 12px;
  box-sizing: border-box;
}

.level-one {
  background: linear-gradient(315deg, #FFFAFA 0%, #FF6565 100%);
}

.level-two {
  background: linear-gradient(135deg, #FFA149 0%, #FFFDFB 100%);
}

.level-three {
  background: linear-gradient(135deg, #8FBAFF 0%, #F3F8FF 100%);
}

.level-name {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 16px;
  color: #0E1D33;
  margin-bottom: 8px;
}

.level-value {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 24px;
  color: #303133;
}

/* 报警列表样式 */
.alarm-list {
  max-height: 180px;
  overflow-y: auto;
  padding-right: 8px;
}

.alarm-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 16px;
  background-color: #FFFFFF;
  margin-bottom: 12px;
  border-radius: 4px;
  border-left: 4px solid rgba(0, 0, 0, 0.04);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.04);
}

.alarm-item.level-one {
  border-left-color: #FF1414;
}

.alarm-item.level-two {
  border-left-color: #FF7C00;
}

.alarm-item.level-three {
  border-left-color: #2D7EFF;
  background-color: #F3F8FF;
}

.alarm-info-detail {
  display: flex;
  flex-direction: column;
}

.alarm-title {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 16px;
  color: #0E1D33;
  margin-bottom: 8px;
}

.alarm-location-time {
  display: flex;
  align-items: center;
  gap: 24px;
  margin-top: 8px;
}

.alarm-location,
.alarm-time {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #647688;
  display: flex;
  align-items: center;
}

.alarm-location :deep(svg),
.alarm-time :deep(svg) {
  margin-right: 4px;
  font-size: 16px;
  color: #909399;
}

.alarm-level-tag {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 14px;
  padding: 0 0 0 12px;
  position: relative;
  top: 4px;
}

.level-9101-tag {
  color: #FF1414;
}

.level-9102-tag {
  color: #FF7C00;
}

.level-9103-tag {
  color: #2D7EFF;
}
.level-9104-tag {
  color: #50ff2d;
}

/* 表格样式 */
.alarm-table-container {
  height: calc(100% - 40px);
}

:deep(.el-table) {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-table th) {
  height: 40px;
  background: #EEF5FF;
  font-family: PingFangSC, PingFang SC;
  font-weight: 600;
  font-size: 14px;
  color: #0E1D33;
}

:deep(.el-table td) {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #222222;
}

:deep(.striped-row) {
  background: #F8FAFD;
}

:deep(.el-table__row.current-row) {
  background: #EBF6FF;
}

/* 底部区域样式 */
.bottom-section {
  display: flex;
  gap: 16px;
}

.statistics-charts {
  flex: 1;
  background-color: #fff;
  border-radius: 4px;
  padding: 16px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.map-container {
  flex: 1;
  background-color: #fff;
  border-radius: 4px;
  padding: 16px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.statistics-data {
  display: flex;
  margin-bottom: 16px;
  background: linear-gradient(180deg, #F6FAFF 0%, #F0F6FF 100%);
  border-radius: 4px;
  border: 1px solid #F3F3F3;
}

.stat-item {
  flex: 1;
  text-align: center;
  border-right: 1px solid #ebeef5;
  padding: 10px 0;
}

.stat-item:last-child {
  border-right: none;
}

.stat-value {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 24px;
  color: #F54040;
}

.stat-label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #0E1D33;
}

.trend-chart-container {
  height: 211px;
}

.map-content {
  height: 280px;
  background-color: #f5f7fa;
}

/* 响应式适配 */
@media (min-height: 900px) and (max-height: 940px) {
  .gas-home {
    height: 76%;
    overflow: auto;
  }
}
@media screen and (max-width: 1680px) {
  .network-cards {
    width: 1300px;
    padding: 12px;
  }

  .network-card {
    width: 245px;
    height: 100px;
  }

  .alarm-info {
    width: 280px;
    padding: 34px 42px;
  }

  .pending-alarm-section,
  .alarm-table-section {
    width: 800px;
  }

  .level-card {
    width: 242px;
  }

  .bottom-section {
    gap: 16px;
  }

  .trend-chart-container {
    height: 180px;
  }

  .map-content {
    height: 240px;
  }
}

@media screen and (max-width: 1440px) {
  .gas-home {
    padding: 16px;
  }

  .top-section {
    gap: 12px;
  }

  .network-cards {
    width: 1100px;
    padding: 10px;
  }

  .network-card {
    width: 208px;
    height: 90px;
    padding: 10px;
  }

  .icon-box {
    width: 48px;
    height: 48px;
    margin-right: 8px;
  }

  .icon-box img {
    width: 48px;
    height: 48px;
  }

  .title {
    font-size: 16px;
    margin-bottom: 6px;
  }

  .value {
    font-size: 22px;
  }

  .alarm-info {
    width: 250px;
    padding: 30px 35px;
  }

  .second-row {
    gap: 16px;
  }

  .pending-alarm-section,
  .alarm-table-section {
    width: 680px;
    height: 350px;
    padding: 16px;
  }

  .level-card {
    width: 205px;
    height: 65px;
  }

  .alarm-list {
    max-height: 160px;
  }

  .alarm-item {
    padding: 12px;
    margin-bottom: 10px;
  }

  .alarm-title {
    font-size: 15px;
  }

  .alarm-location-time {
    gap: 16px;
  }

  .trend-chart-container {
    height: 160px;
  }

  .map-content {
    height: 220px;
  }
}

@media screen and (max-width: 1366px) {
  .gas-home {
    padding: 12px;
  }

  .top-section {
    gap: 10px;
    flex-direction: column;
  }

  .network-cards {
    width: 100%;
    height: auto;
    padding: 10px;
    margin-bottom: 10px;
  }

  .network-card {
    width: calc(20% - 8px);
    height: 90px;
    padding: 8px;
  }

  .icon-box {
    width: 40px;
    height: 40px;
  }

  .icon-box img {
    width: 40px;
    height: 40px;
  }

  .alarm-info {
    width: 100%;
    height: auto;
    flex-direction: row;
    padding: 16px;
    gap: 20px;
    justify-content: center;
  }

  .second-row {
    flex-direction: column;
    gap: 12px;
  }

  .pending-alarm-section,
  .alarm-table-section {
    width: 100%;
    height: auto;
    min-height: 300px;
  }

  .level-card {
    width: calc(33.33% - 11px);
    height: 60px;
  }

  .level-value {
    font-size: 20px;
  }

  .bottom-section {
    flex-direction: column;
    gap: 12px;
  }

  .trend-chart-container {
    height: 180px;
  }

  .map-content {
    height: 250px;
  }
}

/* 针对超大屏幕的适配 */
@media screen and (min-width: 2560px) {
  .gas-home {
    padding: 30px;
    max-width: 2400px;
    margin: 0 auto;
  }

  .network-cards {
    width: 1800px;
    height: 160px;
  }

  .network-card {
    width: 340px;
    height: 130px;
  }

  .icon-box {
    width: 64px;
    height: 64px;
  }

  .icon-box img {
    width: 64px;
    height: 64px;
  }

  .title {
    font-size: 20px;
  }

  .value {
    font-size: 28px;
  }

  .alarm-info {
    width: 360px;
    height: 160px;
  }

  .pending-alarm-section,
  .alarm-table-section {
    width: 1140px;
    height: 420px;
    padding: 24px;
  }

  .level-card {
    width: 340px;
    height: 85px;
  }

  .alarm-list {
    max-height: 220px;
  }

  .trend-chart-container {
    height: 260px;
  }

  .map-content {
    height: 340px;
  }
}

</style>