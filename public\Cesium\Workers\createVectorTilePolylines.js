/**
 *    ████████                   ██                   ██       ██                  ██      ██
 *   ██░░░░░░██                 ░░                   ░██      ░██                 ░██     ░██
 *  ██      ░░   █████  ███████  ██ ██   ██  ██████  ░██   █  ░██  ██████  ██████ ░██     ░██
 * ░██          ██░░░██░░██░░░██░██░██  ░██ ██░░░░   ░██  ███ ░██ ██░░░░██░░██░░█ ░██  ██████
 * ░██    █████░███████ ░██  ░██░██░██  ░██░░█████   ░██ ██░██░██░██   ░██ ░██ ░  ░██ ██░░░██
 * ░░██  ░░░░██░██░░░░  ░██  ░██░██░██  ░██ ░░░░░██  ░████ ░░████░██   ░██ ░██    ░██░██  ░██
 *  ░░████████ ░░██████ ███  ░██░██░░██████ ██████   ░██░   ░░░██░░██████ ░███    ███░░██████
 *   ░░░░░░░░   ░░░░░░ ░░░   ░░ ░░  ░░░░░░ ░░░░░░    ░░       ░░  ░░░░░░  ░░░    ░░░  ░░░░░░
 *
 * @license
 * GeniusWorld Web SDK - http://zydlxx.cn:1234/
 * Version 4.0.0.B#develop-d3ff2330@202405231107
 *
 *
 * Copyright © 2020 - 2023 正元地理信息集团股份有限公司. All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
define(["./Matrix3-79d15570","./combine-bc3d0d90","./AttributeCompression-aa106b76","./Math-6acd1674","./IndexDatatype-7c192505","./Matrix2-d550732e","./createTaskProcessorWorker","./defaultValue-7b61670d","./ComponentDatatype-e95dda25","./WebGLConstants-68839929","./RuntimeError-7dc4ea5a"],(function(e,a,t,n,r,s,i,o,c,u,f){"use strict";const d=32767,p=new e.Cartographic,l=new e.Cartesian3;const b=new s.Rectangle,C=new e.Ellipsoid,w=new e.Cartesian3,h={min:void 0,max:void 0};const y=new e.Cartesian3,k=new e.Cartesian3,m=new e.Cartesian3,A=new e.Cartesian3,g=new e.Cartesian3;return i((function(i,o){const c=new Uint16Array(i.positions),u=new Uint16Array(i.widths),f=new Uint32Array(i.counts),x=new Uint32Array(i.batchIds);!function(a){a=new Float64Array(a);let t=0;h.min=a[t++],h.max=a[t++],s.Rectangle.unpack(a,t,b),t+=s.Rectangle.packedLength,e.Ellipsoid.unpack(a,t,C),t+=e.Ellipsoid.packedLength,e.Cartesian3.unpack(a,t,w)}(i.packedBuffer);const D=C,E=w,I=function(a,r,s,i,o){const c=a.length/3,u=a.subarray(0,c),f=a.subarray(c,2*c),b=a.subarray(2*c,3*c);t.AttributeCompression.zigZagDeltaDecode(u,f,b);const C=new Float64Array(a.length);for(let a=0;a<c;++a){const t=u[a],c=f[a],w=b[a],h=n.CesiumMath.lerp(r.west,r.east,t/d),y=n.CesiumMath.lerp(r.south,r.north,c/d),k=n.CesiumMath.lerp(s,i,w/d),m=e.Cartographic.fromRadians(h,y,k,p),A=o.cartographicToCartesian(m,l);e.Cartesian3.pack(A,C,3*a)}return C}(c,b,h.min,h.max,D),M=I.length/3,P=4*M-4,U=new Float32Array(3*P),F=new Float32Array(3*P),R=new Float32Array(3*P),T=new Float32Array(2*P),N=new Uint32Array(P),L=new Float32Array(2*P);let S,_=0,v=0,G=0,W=0,B=f.length,O=0;for(S=0;S<B;++S){const a=f[S],t=u[S],n=x[S];for(let r=0;r<a;++r){let s;if(0===r){const a=e.Cartesian3.unpack(I,3*W,y),t=e.Cartesian3.unpack(I,3*(W+1),k);s=e.Cartesian3.subtract(a,t,m),e.Cartesian3.add(a,s,s)}else s=e.Cartesian3.unpack(I,3*(W+r-1),m);const i=e.Cartesian3.unpack(I,3*(W+r),A);let o;if(r===a-1){const t=e.Cartesian3.unpack(I,3*(W+a-1),y),n=e.Cartesian3.unpack(I,3*(W+a-2),k);o=e.Cartesian3.subtract(t,n,g),e.Cartesian3.add(t,o,o)}else o=e.Cartesian3.unpack(I,3*(W+r+1),g);e.Cartesian3.subtract(s,E,s),e.Cartesian3.subtract(i,E,i),e.Cartesian3.subtract(o,E,o);const c=r===a-1?2:4;for(let u=0===r?2:0;u<c;++u){e.Cartesian3.pack(i,U,_),e.Cartesian3.pack(s,F,_),e.Cartesian3.pack(o,R,_),_+=3;const c=u-2<0?-1:1;T[v++]=u%2*2-1,T[v++]=c*t,N[G++]=n,L[O++]=r/(a-1),L[O++]=Math.max(T[v-2],0)}}W+=a}const z=r.IndexDatatype.createTypedArray(P,6*M-6);let H=0,V=0;for(B=M-1,S=0;S<B;++S)z[V++]=H,z[V++]=H+2,z[V++]=H+1,z[V++]=H+1,z[V++]=H+2,z[V++]=H+3,H+=4;o.push(U.buffer,F.buffer,R.buffer),o.push(T.buffer,N.buffer,z.buffer,L.buffer);let Y={indexDatatype:2===z.BYTES_PER_ELEMENT?r.IndexDatatype.UNSIGNED_SHORT:r.IndexDatatype.UNSIGNED_INT,currentPositions:U.buffer,previousPositions:F.buffer,nextPositions:R.buffer,expandAndWidth:T.buffer,batchIds:N.buffer,indices:z.buffer,st:L.buffer};if(i.keepDecodedPositions){const e=function(e){const a=e.length,t=new Uint32Array(a+1);let n=0;for(let r=0;r<a;++r)t[r]=n,n+=e[r];return t[a]=n,t}(f);o.push(I.buffer,e.buffer),Y=a.combine(Y,{decodedPositions:I.buffer,decodedPositionOffsets:e.buffer})}return Y}))}));
