<template>
  <div class="gas-leak-disposal-search">
    <div class="search-form">
      <!-- 第一行查询条件 -->
      <div class="form-row">
        <div class="form-item">
          <span class="label">报警来源:</span>
          <el-select v-model="formData.alarmSource" class="form-input" placeholder="请选择">
            <el-option label="全部" value="all" />
            <el-option v-for="item in alarmSources" :key="item.value" :label="item.label" :value="item.label" />
          </el-select>
        </div>
        <div class="form-item">
          <span class="label">报警等级:</span>
          <el-select v-model="formData.alarmLevel" class="form-input" placeholder="请选择">
            <el-option label="全部" value="all" />
            <el-option v-for="(value, key) in alarmLevelMap" :key="key" :label="value" :value="key" />
          </el-select>
        </div>
        <div class="form-item">
          <span class="label">报警类型:</span>
          <el-select v-model="formData.alarmType" class="form-input" placeholder="请选择">
            <el-option v-for="item in alarmTypes" :key="item.alarmType" :label="item.alarmTypeName" :value="item.alarmType" />
          </el-select>
        </div>
        <div class="form-item">
          <span class="label">报警时间:</span>
          <el-date-picker
            v-model="formData.timeRange"
            type="datetimerange"
            class="form-input time-range-input"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
          />
        </div>
      </div>
      
      <!-- 第二行查询条件 -->
      <div class="form-row">
        <div class="form-item">
          <el-input v-model="formData.code" class="form-input" placeholder="请输入报警编码/设备编码" />
        </div>
        <div class="form-item" style="margin-left: auto;">
          <el-button type="primary" class="search-btn" @click="handleSearch">查询</el-button>
          <el-button class="reset-btn" @click="handleReset">重置</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { ElSelect, ElOption, ElInput, ElButton, ElDatePicker } from 'element-plus';
import { ALARM_LEVEL_MAP, ALARM_SOURCES, ALARM_TYPES } from '@/constants/gas';
import { getAlarmType } from '@/api/gas';

const emit = defineEmits(['search', 'reset']);

// 常量数据
const alarmSources = ALARM_SOURCES;
const alarmLevelMap = ALARM_LEVEL_MAP;

// 表单数据
const formData = ref({
  alarmSource: 'all',
  alarmLevel: 'all',
  alarmType: '',
  timeRange: [],
  code: ''
});
const alarmTypes = ref([]);
// 获取报警类型 
const getAlarmTypes = async () => {
  const res = await getAlarmType();
  if (res.code === 200 && res.data) {
    alarmTypes.value = res.data;
  }
};
getAlarmTypes()
// 处理查询
const handleSearch = () => {
  emit('search', formData.value);
};

// 处理重置
const handleReset = () => {
  formData.value = {
    alarmSource: 'all',
    alarmLevel: 'all',
    alarmType: '',
    timeRange: [],
    code: ''
  };
  emit('reset');
};
</script>

<style scoped>
.gas-leak-disposal-search {
  width: 100%;
}

.search-form {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.form-row {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin-bottom: 16px;
}

.form-item {
  display: flex;
  align-items: center;
  margin-right: 16px;
  margin-bottom: 8px;
}

.label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #282828;
  margin-right: 8px;
  white-space: nowrap;
}

.form-input {
  width: 180px;
  height: 32px;
}

:deep(.el-input__wrapper) {
  background: #FFFFFF;
  border-radius: 2px;
  border: 1px solid #CED3DA;
  height: 32px;
  box-shadow: none !important;
}

:deep(.el-select .el-input__wrapper) {
  background: #FFFFFF;
  border-radius: 2px;
  border: 1px solid #CED3DA;
  height: 32px;
  box-shadow: none !important;
}

:deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px #0277FD inset !important;
}

:deep(.el-select .el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px #0277FD inset !important;
}

:deep(.el-date-editor.el-input__wrapper) {
  width: 180px;
  height: 32px;
}

.time-range-input {
  width: 360px !important;
}

.search-btn {
  width: 60px;
  height: 32px;
  background: #0277FD;
  border-radius: 2px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #FFFFFF;
  padding: 0;
  margin-right: 8px;
}

.reset-btn {
  width: 60px;
  height: 32px;
  background: #FFFFFF;
  border-radius: 2px;
  border: 1px solid #CED3DA;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #647688;
  padding: 0;
}
</style>