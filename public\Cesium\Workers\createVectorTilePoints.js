/**
 *    ████████                   ██                   ██       ██                  ██      ██
 *   ██░░░░░░██                 ░░                   ░██      ░██                 ░██     ░██
 *  ██      ░░   █████  ███████  ██ ██   ██  ██████  ░██   █  ░██  ██████  ██████ ░██     ░██
 * ░██          ██░░░██░░██░░░██░██░██  ░██ ██░░░░   ░██  ███ ░██ ██░░░░██░░██░░█ ░██  ██████
 * ░██    █████░███████ ░██  ░██░██░██  ░██░░█████   ░██ ██░██░██░██   ░██ ░██ ░  ░██ ██░░░██
 * ░░██  ░░░░██░██░░░░  ░██  ░██░██░██  ░██ ░░░░░██  ░████ ░░████░██   ░██ ░██    ░██░██  ░██
 *  ░░████████ ░░██████ ███  ░██░██░░██████ ██████   ░██░   ░░░██░░██████ ░███    ███░░██████
 *   ░░░░░░░░   ░░░░░░ ░░░   ░░ ░░  ░░░░░░ ░░░░░░    ░░       ░░  ░░░░░░  ░░░    ░░░  ░░░░░░
 *
 * @license
 * GeniusWorld Web SDK - http://zydlxx.cn:1234/
 * Version 4.0.0.B#develop-d3ff2330@202405231107
 *
 *
 * Copyright © 2020 - 2023 正元地理信息集团股份有限公司. All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
define(["./AttributeCompression-aa106b76","./Matrix3-79d15570","./Math-6acd1674","./Matrix2-d550732e","./createTaskProcessorWorker","./ComponentDatatype-e95dda25","./defaultValue-7b61670d","./WebGLConstants-68839929","./RuntimeError-7dc4ea5a"],(function(e,a,t,r,n,o,i,s,c){"use strict";const u=32767,p=new a.Cartographic,l=new a.Cartesian3,d=new r.Rectangle,m=new a.Ellipsoid,f={min:void 0,max:void 0};return n((function(n,o){const i=new Uint16Array(n.positions);!function(e){e=new Float64Array(e);let t=0;f.min=e[t++],f.max=e[t++],r.Rectangle.unpack(e,t,d),t+=r.Rectangle.packedLength,a.Ellipsoid.unpack(e,t,m)}(n.packedBuffer);const s=d,c=m,h=f.min,C=f.max,g=i.length/3,b=i.subarray(0,g),w=i.subarray(g,2*g),k=i.subarray(2*g,3*g);e.AttributeCompression.zigZagDeltaDecode(b,w,k);const y=new Float64Array(i.length);for(let e=0;e<g;++e){const r=b[e],n=w[e],o=k[e],i=t.CesiumMath.lerp(s.west,s.east,r/u),d=t.CesiumMath.lerp(s.south,s.north,n/u),m=t.CesiumMath.lerp(h,C,o/u),f=a.Cartographic.fromRadians(i,d,m,p),g=c.cartographicToCartesian(f,l);a.Cartesian3.pack(g,y,3*e)}return o.push(y.buffer),{positions:y.buffer}}))}));
