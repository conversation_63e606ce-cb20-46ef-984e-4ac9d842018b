<template>
  <PanelBox title="隐患整改">
    <div class="panel-content">
      <div class="stats-row">
        <div class="stat-item">
          <span class="stat-dot"><span class="stat-dot-inner"></span></span>
          <span class="stat-label">隐患总数</span>
          <span class="stat-value-red">{{ statsData.totalIssues }}</span>
          <span class="stat-unit">个</span>
        </div>
        <div class="stat-item">
          <span class="stat-dot"><span class="stat-dot-inner"></span></span>
          <span class="stat-label">已整改</span>
          <span class="stat-value-highlight">{{ statsData.fixedIssues }}</span>
          <span class="stat-unit">个</span>
        </div>
        <div class="stat-item">
          <span class="stat-dot"><span class="stat-dot-inner"></span></span>
          <span class="stat-label">整改完成率</span>
          <span class="stat-value-gradient">{{ statsData.fixRate }}</span>
        </div>
      </div>
      <div class="charts-content">
        <div class="chart-wrapper">
          <div class="chart-container" ref="chartContainer"></div>
          <div class="chart-bg"></div>

          <!-- 图例 -->
          <div class="normal-risk-legend">
            <div class="legend-box" style="border-color: #00E1B9;">
              <div class="legend-dot" style="background: #00E1B9;"></div>
            </div>
            <div class="legend-text">一般隐患</div>
          </div>

          <div class="high-risk-legend">
            <div class="legend-box" style="border-color: #FE9150;">
              <div class="legend-dot" style="background: #FE9150;"></div>
            </div>
            <div class="legend-text">较大隐患</div>
          </div>

          <div class="severe-risk-legend">
            <div class="legend-box" style="border-color: #DE6970;">
              <div class="legend-dot" style="background: #DE6970;"></div>
            </div>
            <div class="legend-text">重大隐患</div>
          </div>
        </div>
      </div>
    </div>
  </PanelBox>
</template>

<script setup>
import { reactive,onMounted, onBeforeUnmount, ref, nextTick } from 'vue'
import PanelBox from '@/components/screen/PanelBox.vue'
import * as echarts from 'echarts'
// 综合态势总览右上面板组件
// 统计数据
const statsData = reactive({
  totalIssues: 0,
  fixedIssues: 0,
  fixRate: '0%',
})

// 图表实例
let chartInstance = null
const chartContainer = ref(null)

// 模拟数据
const mockData = [
  { name: '一般隐患', value: 0, distance: '0KM', color: 'rgba(0, 225, 185, 1)' },
  { name: '较大隐患', value: 0, distance: '0KM', color: 'rgba(254, 145, 80, 1)' },
  { name: '重大隐患', value: 0, distance: '0KM', color: 'rgba(222, 105, 112, 1)' }
]

// 创建图表
const initChart = async () => {
  if (!chartContainer.value) {
    console.warn('图表容器未找到，无法初始化图表')
    return
  }

  try {
    // 等待DOM渲染完成
    await nextTick()

    // 销毁之前的图表实例
    if (chartInstance) {
      chartInstance.dispose()
    }

    // 创建新的图表实例
    chartInstance = echarts.init(chartContainer.value)
    
    // 确保实例创建成功再更新图表
    if (chartInstance) {
      updateChart()
      // 监听窗口大小变化，重新绘制图表
      window.addEventListener('resize', handleResize)
    } else {
      console.warn('图表实例创建失败')
    }
  } catch (error) {
    console.error('初始化图表错误:', error)
  }
}

// 处理窗口大小变化
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

// 更新图表数据
const updateChart = () => {
  if (!chartInstance) {
    console.warn('图表实例不存在，无法更新')
    return
  }

  try {
    const data = JSON.parse(JSON.stringify(mockData)) // 创建数据的深拷贝避免引用问题
    
    if (!data || !Array.isArray(data) || data.length === 0) {
      console.warn('图表数据无效')
      return
    }
    
    const option = {
      backgroundColor: 'transparent',
      title: {
        text: '隐患',
        left: '40%',
        top: 'center',
        textStyle: {
          fontSize: 16,
          color: '#FFFFFF',
          fontFamily: 'PingFangSC, PingFang SC',
          fontWeight: 500
        },
        textAlign: 'center'
      },
      series: [
        {
          name: '外环',
          type: 'pie',
          radius: ['57px', '75px'],
          center: ['43%', '48%'],
          startAngle: 90,
          clockwise: false,
          avoidLabelOverlap: false,
          label: {
            show: true,
            position: 'outside',
            formatter: (params) => {
              const targetData = data.find(item => item.name === params.name)
              if (!targetData) return ''
              
              if (params.name === '重大隐患') {
                return `{risk4|重大隐患:}\n{value|${params.value}% | ${targetData.distance}}`;
              } else if (params.name === '较大隐患') {
                return `{risk3|较大隐患:}\n{value|${params.value}% | ${targetData.distance}}`;
              } else if (params.name === '一般隐患') {
                return `{risk2|一般隐患:}\n{value|${params.value}% | ${targetData.distance}}`;
              }
              return `${params.name}:\n${params.value}% | ${targetData.distance || ''}`;
            },
            rich: {
              risk1: {
                fontSize: 12,
                fontFamily: 'PingFangSC, PingFang SC',
                fontWeight: 400,
                color: '#FFFFFF',
                padding: [0, 0, 4, 0]
              },
              risk2: {
                fontSize: 12,
                fontFamily: 'PingFangSC, PingFang SC',
                fontWeight: 400,
                color: '#FFFFFF',
                padding: [0, 0, 4, 0]
              },
              risk3: {
                fontSize: 12,
                fontFamily: 'PingFangSC, PingFang SC',
                fontWeight: 400,
                color: '#FFFFFF',
                padding: [0, 0, 4, 0]
              },
              risk4: {
                fontSize: 12,
                fontFamily: 'PingFangSC, PingFang SC',
                fontWeight: 400,
                color: '#FFFFFF',
                padding: [0, 0, 4, 0]
              },
              value: {
                fontSize: 12,
                fontFamily: 'PingFangSC, PingFang SC',
                fontWeight: 400,
                color: '#FFFFFF'
              }
            },
            alignTo: 'none',
            edgeDistance: '10%'
          },
          labelLine: {
            show: true,
            length: 10,
            length2: 20,
            smooth: true,
            lineStyle: {
              width: 1,
              type: 'dashed',
              color: 'inherit'
            }
          },
          itemStyle: {
            borderWidth: 1,
            borderColor: '#0A2E5B'
          },
          data: data.map(item => {
            // 为每个扇区设置特定的属性
            let itemConfig = {
              value: item.value,
              name: item.name,
              itemStyle: {
                color: item.color,
                opacity: 0.4
              },
              label: {
                show: true,
                opacity: 0.9,
              },
              labelLine: {
                show: true
              }
            };

            // 根据隐患类型设置特定的标签和标签线属性
        if (item.name === '一般隐患') {
              itemConfig.label.position = ['0%', '30%'];
              itemConfig.labelLine = {
                show: true,
                length: 8,
                length2: 15,
                smooth: true,
                lineStyle: {
                  width: 1,
                  type: 'solid',
                  color: item.color
                }
              };
            } else if (item.name === '较大隐患') {
              itemConfig.label.position = ['0%', '-30%'];
              itemConfig.labelLine = {
                show: true,
                length: 8,
                length2: 15,
                smooth: true,
                lineStyle: {
                  width: 1,
                  type: 'solid',
                  color: item.color
                }
              };
            } else if (item.name === '重大隐患') {
              itemConfig.label.position = ['0%', '-30%'];
              itemConfig.labelLine = {
                show: true,
                length: 8,
                length2: 15,
                smooth: true,
                lineStyle: {
                  width: 1,
                  type: 'solid',
                  color: item.color
                }
              };
            }

            return itemConfig;
          })
        },
        {
          name: '内环',
          type: 'pie',
          radius: ['42px', '57px'],
          center: ['43%', '48%'],
          startAngle: 90,
          clockwise: false,
          avoidLabelOverlap: false,
          label: {
            show: false
          },
          labelLine: {
            show: false
          },
          itemStyle: {
            borderWidth: 1,
            borderColor: '#0A2E5B'
          },
          emphasis: {
            scale: false,
            disabled: true
          },
          data: data.map(item => ({
            value: item.value,
            name: item.name,
            itemStyle: {
              color: item.color
            }
          }))
        }
      ]
    }

    // 添加四周虚线边框的图形元素
    option.graphic = [];

    chartInstance.setOption(option, true)
  } catch (error) {
    console.error('更新图表数据错误:', error)
  }
}
// 强制重新计算图表尺寸的方法
const resizeChart = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

// 在视图挂载后初始化图表
onMounted(() => {
  // 延迟初始化，确保DOM已完全渲染
  setTimeout(() => {
    if (chartContainer.value) {
      initChart().then(() => {
        // 初始化后再次强制resize以确保正确渲染
        setTimeout(resizeChart, 300)
      }).catch(err => {
        console.error('图表初始化失败:', err)
      })
    } else {
      console.warn('图表容器未找到，无法初始化')
    }
  }, 200)
})

// 在视图销毁前清理图表实例
onBeforeUnmount(() => {
  if (chartInstance) {
    window.removeEventListener('resize', handleResize)
    chartInstance.dispose()
    chartInstance = null
  }
})

</script>

<style scoped>
.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.stats-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.stat-dot {
  width: 9px;
  height: 9px;
  background: rgba(215, 48, 48, 0.4);
  border-radius: 50%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-dot-inner {
  width: 5px;
  height: 5px;
  background: #D73030;
  border-radius: 50%;
  position: absolute;
}

.stat-label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
}

.stat-value-red {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 24px;
  line-height: 26px;
  text-align: left;
  font-style: normal;
  background: linear-gradient(90deg, #FFFFFF 0%, #DB2D05 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-value-highlight {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 24px;
  color: #3CF3FF;
  line-height: 26px;
}

.stat-value-gradient {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 24px;
  line-height: 26px;
  text-align: left;
  font-style: normal;
  background: linear-gradient(90deg, #FFFFFF 0%, #36F281 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-unit {
  color: rgba(255, 255, 255, 0.4);
  font-size: 12px;
}
.charts-content{
  flex: 1;
}
.chart-wrapper {
  width: 410px;
  height: 184px;
  position: relative;
  margin: 0 auto;
  display: flex;
  justify-content: center;
  align-items: center;
}

.chart-container {
  width: 100%;
  height: 100%;
  position: relative;
  z-index: 2;
}

.chart-bg {
  position: absolute;
  width: 158px;
  height: 158px;
  left: 43%;
  top: 48%;
  transform: translate(-50%, -50%);
  background-image: url('@/assets/images/screen/drainage/ring_bg.png');
  background-position: center;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  z-index: 1;
}


/* 图例样式 */
.chart-wrapper::after {
  content: '';
  position: absolute;
  right: 30px;
  top: 50%;
  transform: translateY(-50%);
  width: 60px;
  z-index: 2;
}

/* 隐患级别图例 - 分别放在四个角落 */
.chart-wrapper::before {
  content: '';
  position: absolute;
  right: 30px;
  top: 40px;
  color: #FFFFFF;
  font-size: 12px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  z-index: 2;
}

.normal-risk-legend,
.high-risk-legend,
.severe-risk-legend {
  position: absolute;
  z-index: 3;
  display: flex;
  align-items: center;
}


.normal-risk-legend {
  right: -5%;
  bottom: 20%;
}

.high-risk-legend {
  right: -5%;
  bottom: 30%;
}

.severe-risk-legend {
  right: -5%;
  bottom: 40%;
}

.legend-box {
  width: 10px;
  height: 10px;
  border: 1px solid;
  position: relative;
  margin-right: 5px;
}

.legend-dot {
  position: absolute;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.legend-text {
  color: #FFFFFF;
  font-size: 12px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  white-space: nowrap;
}

/* 响应式布局 */
@media screen and (max-width: 768px) {
  .panel-content {
    min-height: 219px;
    padding: 0 20px 10px;
  }

  .chart-wrapper {
    width: 350px;
    height: 219px;
  }

  .chart-bg {
    width: 170px;
    height: 170px;
  }
}

@media screen and (min-width: 1921px) {
  .chart-wrapper {
    width: 450px;
    height: 219px;
  }

  .chart-bg {
    width: 210px;
    height: 210px;
  }
}

/* 更小屏幕高度（小于900px）的特别优化 */
@media (max-height: 899px) {
  .panel-content {
    padding: 10px;
    gap: 10px;
  }

  .chart-wrapper {
    width: 350px;
    height: 140px;
  }

  .chart-bg {
    width: 140px;
    height: 140px;
  }
  
  .stat-value-red,
  .stat-value-highlight,
  .stat-value-gradient {
    font-size: 18px;
    line-height: 20px;
  }
}

/* 910-940px高度的特别优化 */
@media (min-height: 910px) and (max-height: 940px) {
  .panel-content {
    padding: 12px;
    gap: 15px;
  }

  .chart-wrapper {
    width: 380px;
    height: 160px;
  }

  .chart-bg {
    width: 150px;
    height: 150px;
  }
  
  .stat-value-red,
  .stat-value-highlight,
  .stat-value-gradient {
    font-size: 22px;
    line-height: 24px;
  }
  


  .normal-risk-legend {
    right: -5%;
    bottom: 12%;
  }

  .high-risk-legend {
    right: -5%;
    bottom: 22%;
  }

  .severe-risk-legend {
    right: -5%;
    bottom: 32%;
  }
}
</style>