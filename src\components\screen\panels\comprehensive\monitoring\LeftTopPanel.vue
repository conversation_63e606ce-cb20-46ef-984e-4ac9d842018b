<template>
  <PanelBox title="设备监测">
    <div class="panel-content">
      <!-- 上部设备信息区域 -->
      <div class="stats-row">
        <div class="stat-item">
          <span class="stat-dot"><span class="stat-dot-inner"></span></span>
          <span class="stat-label">设备总数</span>
          <span class="stat-value-blue">511</span>
        </div>
        <div class="stat-item">
          <span class="stat-dot"><span class="stat-dot-inner"></span></span>
          <span class="stat-label">在线设备</span>
          <span class="stat-value-highlight">27</span>
        </div>
        <div class="stat-item">
          <span class="stat-dot"><span class="stat-dot-inner"></span></span>
          <span class="stat-label">在线率</span>
          <span class="stat-value-gradient">91%</span>
        </div>
      </div>

      <!-- 下部设备类型区域 -->
      <div class="device-type-section">
        <!-- 设备类型标题和筛选按钮 -->
        <div class="type-header">
          <div class="type-title">设备类型</div>
          <div class="type-filters">
            <div class="filter-item" 
                 :class="{ active: activeType === 'gas' }" 
                 @click="changeDeviceType('gas')">
              <span class="filter-dot"></span>
              <span class="filter-label">燃气</span>
            </div>
            <div class="filter-item" 
                 :class="{ active: activeType === 'water' }" 
                 @click="changeDeviceType('water')">
              <span class="filter-dot"></span>
              <span class="filter-label">排水</span>
            </div>
            <div class="filter-item" 
                 :class="{ active: activeType === 'heat' }" 
                 @click="changeDeviceType('heat')">
              <span class="filter-dot"></span>
              <span class="filter-label">供热</span>
            </div>
            <div class="filter-item" 
                 :class="{ active: activeType === 'bridge' }" 
                 @click="changeDeviceType('bridge')">
              <span class="filter-dot"></span>
              <span class="filter-label">桥梁</span>
            </div>
          </div>
        </div>

        <!-- 设备数量信息展示 -->
        <div class="device-stats-container">
          <div class="device-stats-row">
            <div class="device-stat-card">
              <div class="stat-card-content">
                <div class="stat-card-label">压力监测</div>
                <div class="stat-card-value">{{ deviceData.pressure }}</div>
                <div class="stat-card-icon">
                  <img src="@/assets/images/screen/comprehensive/yali.png" alt="压力监测">
                </div>
              </div>
            </div>
            <div class="device-stat-card">
              <div class="stat-card-content">
                <div class="stat-card-label">流量监测</div>
                <div class="stat-card-value">{{ deviceData.flow }}</div>
                <div class="stat-card-icon">
                  <img src="@/assets/images/screen/comprehensive/liuliang.png" alt="流量监测">
                </div>
              </div>
            </div>
            <div class="device-stat-card">
              <div class="stat-card-content">
                <div class="stat-card-label">可燃气体监测</div>
                <div class="stat-card-value">{{ deviceData.gas }}</div>
                <div class="stat-card-icon">
                  <img src="@/assets/images/screen/comprehensive/qita.png" alt="可燃气体监测">
                </div>
              </div>
            </div>
            <div class="device-stat-card">
              <div class="stat-card-content">
                <div class="stat-card-label">井盖传感器</div>
                <div class="stat-card-value">{{ deviceData.manhole }}</div>
                <div class="stat-card-icon">
                  <img src="@/assets/images/screen/comprehensive/qita.png" alt="井盖传感器">
                </div>
              </div>
            </div>
            <div class="device-stat-card">
              <div class="stat-card-content">
                <div class="stat-card-label">视频监控</div>
                <div class="stat-card-value">{{ deviceData.video }}</div>
                <div class="stat-card-icon">
                  <img src="@/assets/images/screen/comprehensive/qita.png" alt="视频监控">
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </PanelBox>
</template>

<script setup>
import { ref, reactive } from 'vue'
import PanelBox from '@/components/screen/PanelBox.vue'

// 当前选中的设备类型
const activeType = ref('gas')

// 设备数据
const deviceData = reactive({
  pressure: 511,
  flow: 128,
  gas: 50,
  manhole: 1267,
  video: 5
})

// 不同类型设备的数据
const deviceTypes = {
  gas: {
    pressure: 511,
    flow: 128,
    gas: 50,
    manhole: 1267,
    video: 5
  },
  water: {
    pressure: 320,
    flow: 95,
    gas: 0,
    manhole: 1200,
    video: 12
  },
  heat: {
    pressure: 245,
    flow: 110,
    gas: 25,
    manhole: 650,
    video: 8
  },
  bridge: {
    pressure: 180,
    flow: 60,
    gas: 15,
    manhole: 420,
    video: 15
  }
}

// 切换设备类型
const changeDeviceType = (type) => {
  activeType.value = type
  // 更新数据
  Object.assign(deviceData, deviceTypes[type])
}
</script>

<style scoped>
.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

/* 上部设备信息区域 - 复制自RightMiddlePanel */
.stats-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.stat-dot {
  width: 9px;
  height: 9px;
  background: rgba(5, 90, 219, 0.4);
  border-radius: 50%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-dot-inner {
  width: 5px;
  height: 5px;
  background: #055ADB;
  border-radius: 50%;
  position: absolute;
}

.stat-label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
}

.stat-value-blue {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 24px;
  line-height: 26px;
  text-align: left;
  font-style: normal;
  background: linear-gradient(90deg, #FFFFFF 0%, #055ADB 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-value-highlight {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 24px;
  color: #3CF3FF;
  line-height: 26px;
}

.stat-value-gradient {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 24px;
  line-height: 26px;
  text-align: left;
  font-style: normal;
  background: linear-gradient(90deg, #FFFFFF 0%, #36F281 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 下部设备类型区域 */
.device-type-section {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

/* 设备类型标题和筛选按钮 */
.type-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.type-title {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 16px;
  line-height: 22px;
  text-align: left;
  font-style: normal;
  background: linear-gradient(90deg, #8EE8FF 0%, #13A9FF 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.type-filters {
  display: flex;
  gap: 15px;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 5px;
  cursor: pointer;
}

.filter-dot {
  width: 6px;
  height: 6px;
  background: #FFFFFF;
  border-radius: 2px;
  opacity: 0.6;
}

.filter-label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #FFFFFF;
  opacity: 0.6;
}

.filter-item.active .filter-dot {
  background: #23CAFF;
  opacity: 1;
}

.filter-item.active .filter-label {
  opacity: 1;
}

/* 设备数量信息展示 - 修改为横向布局 */
.device-stats-container {
  width: 100%;
}

.device-stats-row {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 10px;
}

.device-stat-card {
  width: calc(20% - 8px);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-card-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 5px;
}

.stat-card-label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
}

.stat-card-value {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 20px;
  color: #FFFFFF;
}

.stat-card-icon img {
  width: 66px;
  height: 51px;
  object-fit: contain;
}

/* 响应式布局适配 */
@media (min-height: 940px) and (max-height: 1055px) {
  .panel-content {
    padding: 10px;
    gap: 10px;
  }
  
  .stat-item {
    gap: 3px;
  }
  
  .stat-dot {
    width: 7px;
    height: 7px;
  }
  
  .stat-dot-inner {
    width: 3px;
    height: 3px;
  }
  
  .stat-label {
    font-size: 11px;
  }
  
  .stat-value-blue,
  .stat-value-highlight,
  .stat-value-gradient {
    font-size: 18px;
    line-height: 22px;
  }
  
  .stat-card-value {
    font-size: 18px;
  }
  
  .stat-card-icon img {
    width: 60px;
    height: 46px;
  }
}

@media (max-height: 780px) {
  .panel-content {
    padding: 10px;
    gap: 8px;
  }
  
  .stat-label {
    font-size: 10px;
  }
  
  .stat-value-blue,
  .stat-value-highlight,
  .stat-value-gradient {
    font-size: 16px;
    line-height: 20px;
  }
  
  .type-title {
    font-size: 14px;
  }
  
  .filter-label {
    font-size: 12px;
  }
  
  .stat-card-label {
    font-size: 12px;
  }
  
  .stat-card-value {
    font-size: 16px;
  }
  
  .stat-card-icon img {
    width: 50px;
    height: 38px;
  }
}
</style>