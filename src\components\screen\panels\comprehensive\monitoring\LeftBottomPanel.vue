<template>
  <PanelBox title="报警信息">
    <template #extra>
      <div class="extra-container">
        <div class="com-select">
          <CommonSelect v-model="selectedType" :options="typeOptions" @change="handleTypeChange" />
        </div>
        <div class="com-select">
          <CommonSelect v-model="selectedTime" :options="timeOptions" @change="handleTimeChange" />
        </div>
      </div>
    </template>
    <div class="panel-content">
      <!-- 报警数量统计区域 -->
      <div class="alarm-stats-container">
        <div class="stats-row">
          <div class="stat-item">
            <div class="stat-value red-gradient">{{ alarmStats.total }}</div>
            <div class="stat-info">
              <span class="stat-dot"><span class="stat-dot-inner red"></span></span>
              <span class="stat-label">报警总数</span>
            </div>
          </div>
          <div class="stat-item">
            <div class="stat-value green-gradient">{{ alarmStats.handled }}</div>
            <div class="stat-info">
              <span class="stat-dot"><span class="stat-dot-inner green"></span></span>
              <span class="stat-label">已处置</span>
            </div>
          </div>
          <div class="stat-item">
            <div class="stat-value yellow-gradient">{{ alarmStats.handling }}</div>
            <div class="stat-info">
              <span class="stat-dot"><span class="stat-dot-inner yellow"></span></span>
              <span class="stat-label">处置中</span>
            </div>
          </div>
          <div class="stat-item">
            <div class="stat-value orange-gradient">{{ alarmStats.unhandled }}</div>
            <div class="stat-info">
              <span class="stat-dot"><span class="stat-dot-inner orange"></span></span>
              <span class="stat-label">未处置</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 报警列表区域 -->
      <div class="alarm-list">
        <ScrollTable 
          :columns="tableColumns" 
          :data="alarmList" 
          :autoScroll="true" 
          :scrollSpeed="3000"
          :tableHeight="tableHeight" 
          :visibleRows="5"
        >
          <!-- 状态列的自定义渲染 -->
          <template #status="{ row }">
            <span :class="getStatusClass(row.status)">{{ row.status }}</span>
          </template>
          <!-- 位置列的自定义渲染 -->
          <template #location="{ row }">
            <div class="location-cell">
              <div class="location-icon"></div>
              <span>{{ row.location }}</span>
            </div>
          </template>
        </ScrollTable>
        <!-- 更多按钮 -->
        <div class="more-btn-container">
          <div class="more-btn" @click="handleMoreClick">更多</div>
        </div>
      </div>
    </div>
  </PanelBox>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import PanelBox from '@/components/screen/PanelBox.vue'
import CommonSelect from '@/components/screen/CommonSelect.vue'
import ScrollTable from '@/components/screen/common/ScrollTable.vue'

// 综合运行监测左下面板组件

// 专项类型选择
const selectedType = ref('all')
const typeOptions = [
  { label: '全部', value: 'all' },
  { label: '燃气', value: 'gas' },
  { label: '排水', value: 'water' },
  { label: '桥梁', value: 'bridge' },
  { label: '供热', value: 'heating' }
]

// 时间选择
const selectedTime = ref('week')
const timeOptions = [
  { label: '近一周', value: 'week' },
  { label: '近一月', value: 'month' },
  { label: '近一年', value: 'year' }
]

// 报警统计数据
const alarmStats = reactive({
  total: 360,
  handled: 270,
  handling: 90,
  unhandled: 20
})

// 表格列配置
const tableColumns = [
  { title: '报警名称', dataIndex: 'name', width: '25%', fontSize: '13px' },
  { title: '报警等级', dataIndex: 'level', width: '20%', fontSize: '13px' },
  { title: '报警时间', dataIndex: 'reportTime', width: '35%', fontSize: '13px' },
  { title: '处置状态', dataIndex: 'status', width: '20%', fontSize: '13px', slot: 'status' }
]

// 模拟报警数据
const mockAlarmData = [
  { name: '燃气泄漏', level: '一级报警', reportTime: '2025-04-01 12:02:37', status: '待处置', location: 'XX市XX区XX路' },
  { name: '水压异常', level: '二级报警', reportTime: '2025-04-01 12:02:37', status: '待处置', location: 'XX市XX区XX路' },
  { name: '温度过高', level: '三级报警', reportTime: '2025-04-01 12:02:37', status: '已处置', location: 'XX市XX区XX路' },
  { name: '燃气压力异常', level: '三级报警', reportTime: '2025-04-01 12:02:37', status: '已处置', location: 'XX市XX区XX路' },
  { name: '水位异常', level: '二级报警', reportTime: '2025-04-01 11:35:22', status: '处置中', location: 'XX市XX区XX路' },
  { name: '燃气泄漏', level: '一级报警', reportTime: '2025-04-01 10:45:17', status: '已处置', location: 'XX市XX区XX路' }
]

// 表格数据
const alarmList = ref([...mockAlarmData])

// 表格高度
const tableHeight = computed(() => {
  // 根据不同分辨率动态调整表格高度
  if (window.innerHeight >= 900 && window.innerHeight <= 940) {
    return '200px' // 较小屏幕
  } else if (window.innerHeight >= 940 && window.innerHeight <= 1055) {
    return '220px' // 中等屏幕
  } else if (window.innerHeight >= 1056) {
    return '260px' // 较大屏幕
  } else {
    return '220px' // 默认高度
  }
})

// 根据状态获取对应的样式类
const getStatusClass = (status) => {
  switch (status) {
    case '待处置':
      return 'status-pending'
    case '已处置':
      return 'status-handled'
    case '处置中':
      return 'status-in-progress'
    default:
      return 'status-default'
  }
}

// 处理专项类型变化
const handleTypeChange = (value) => {
  console.log('专项类型变更为:', value)
  fetchAlarmData(value, selectedTime.value)
}

// 处理时间范围变化
const handleTimeChange = (value) => {
  console.log('时间范围变更为:', value)
  fetchAlarmData(selectedType.value, value)
}

// 点击更多按钮
const handleMoreClick = () => {
  console.log('查看更多报警')
  // TODO: 实现查看更多报警的逻辑
}

// 从后端获取数据的方法
const fetchAlarmData = async (type, time) => {
  try {
    // 目前使用模拟数据
    console.log(`获取${type}类型，${time}时间范围的报警数据`)
    
    // 模拟数据变化
    if (type === 'gas') {
      alarmStats.total = 150
      alarmStats.handled = 100
    } else if (type === 'water') {
      alarmStats.total = 120
      alarmStats.handled = 80
    } else if (type === 'heating') {
      alarmStats.total = 90
      alarmStats.handled = 60
    } else if (type === 'bridge') {
      alarmStats.total = 60
      alarmStats.handled = 40
    } else {
      alarmStats.total = 360
      alarmStats.handled = 270
    }
    
    // 计算其他统计数据
    alarmStats.handling = Math.floor(alarmStats.total * 0.2)
    alarmStats.unhandled = alarmStats.total - alarmStats.handled - alarmStats.handling
  } catch (error) {
    console.error('获取报警数据失败:', error)
  }
}

onMounted(() => {
  // 初始化时获取数据
  fetchAlarmData(selectedType.value, selectedTime.value)
})
</script>

<style scoped>
.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.extra-container {
  display: flex;
  gap: 15px;
}

.com-select {
  margin-right: 5px;
}

/* 报警统计样式 */
.alarm-stats-container {
  margin: 5px 0 5px;
}

.stats-row {
  display: flex;
  justify-content: space-between;
  padding: 0 10px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
}

.stat-info {
  display: flex;
  align-items: center;
  gap: 5px;
}

.stat-dot {
  position: relative;
  width: 9px;
  height: 9px;
  border-radius: 50%;
}

.stat-dot-inner {
  width: 5px;
  height: 5px;
  border-radius: 50%;
  position: absolute;
  top: 2px;
  left: 2px;
}

.stat-dot-inner.red { 
  background: #FC4949;
}

.stat-dot-inner.orange { 
  background: #FF6D28;
}

.stat-dot-inner.yellow { 
  background: #FFC75A;
}

.stat-dot-inner.green { 
  background: #3FD87C;
}

.stat-dot:has(.stat-dot-inner.red) {
  background: rgba(252, 73, 73, 0.4);
}

.stat-dot:has(.stat-dot-inner.orange) {
  background: rgba(255, 109, 40, 0.4);
}

.stat-dot:has(.stat-dot-inner.yellow) {
  background: rgba(255, 199, 90, 0.4);
}

.stat-dot:has(.stat-dot-inner.green) {
  background: rgba(63, 216, 124, 0.4);
}

.stat-label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
}

.stat-value {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 24px;
  line-height: 26px;
  font-style: normal;
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  text-align: center;
}

.red-gradient {
  background: linear-gradient(90deg, #FB3737 0%, #FEA6A6 100%);
  -webkit-background-clip: text;
}

.orange-gradient {
  background: linear-gradient(90deg, #FF5717 0%, #FFCD72 100%);
  -webkit-background-clip: text;
}

.yellow-gradient {
  background: linear-gradient(90deg, #FFC24C 0%, #FEDFA6 100%);
  -webkit-background-clip: text;
}

.green-gradient {
  background: linear-gradient(90deg, #43DF81 0%, #A6FED0 100%);
  -webkit-background-clip: text;
}

/* 报警列表样式 */
.alarm-list {
  flex: 1;
  position: relative;
}

/* 状态样式 */
.status-pending {
  color: #FFC400;
}

.status-handled {
  color: #3CF3FF;
}

.status-in-progress {
  color: #36F281;
}

/* 位置单元格样式 */
.location-cell {
  display: flex;
  align-items: center;
  gap: 5px;
}

.location-icon {
  width: 14px;
  height: 14px;
  background-image: url('@/assets/images/screen/common/location.svg');
  background-size: contain;
  background-repeat: no-repeat;
}

/* 更多按钮容器样式 */
.more-btn-container {
  position: relative;
  width: 100%;
  height: 0;
}

/* 更多按钮样式 */
.more-btn {
  position: absolute;
  right: 10px;
  bottom: 10px;
  font-family: PingFangSC, 'PingFang SC';
  font-size: 12px;
  color: #3AA1FF;
  cursor: pointer;
  text-decoration: underline;
  z-index: 10;
}

.more-btn:hover {
  color: #66B8FF;
}

/* 点击行样式 */
:deep(.scroll-table tr) {
  cursor: pointer;
  transition: background-color 0.2s;
}

:deep(.scroll-table tr:hover) {
  background-color: rgba(0, 163, 255, 0.2) !important;
}

/* 响应式布局 */
@media screen and (min-height: 900px) and (max-height: 940px) {
  .panel-content {
    padding: 8px;
    gap: 5px;
  }
  
  .stat-value {
    font-size: 20px;
    line-height: 22px;
  }
  
  .stat-label {
    font-size: 11px;
  }
  
  .more-btn {
    bottom: 8px;
    right: 8px;
  }
  :deep(.scroll-table td) {
    font-size: 12px;
    height: 24px;
    line-height: 24px;
  }
}

@media screen and (min-height: 940px) and (max-height: 1055px) {
  .panel-content {
    padding: 10px;
    gap: 10px;
  }
  
  .stat-value {
    font-size: 22px;
    line-height: 24px;
  }
}

@media screen and (min-height: 1056px) {
  .panel-content {
    padding: 15px;
    gap: 0px;
  }
  
  .stat-value {
    font-size: 26px;
    line-height: 28px;
  }
  
  .stat-label {
    font-size: 13px;
  }
  :deep(.scroll-table td) {
    font-size: 12px;
    height: 28px;
    line-height: 28px;
  }
}
</style> 