<template>
  <div class="side-menu-container">
    <el-menu
      :default-active="activeMenu"
      class="el-menu-vertical"
      :collapse="isCollapse"
      :unique-opened="false"
      :router="true"
      :default-openeds="defaultOpeneds"
      @open="handleOpen"
      @close="handleClose"
    >
      <template v-for="(item, index) in menuList" :key="index">
        <!-- 这些是3级菜单项 -->
        <el-sub-menu 
          :index="item.path" 
          class="third-level-menu"
          :data-path="item.path"
        >
          <template #title>
            <el-icon v-if="item.meta && item.meta.icon">
              <component :is="item.meta.icon" />
            </el-icon>
            <span>{{ item.meta ? item.meta.title : '未命名菜单' }}</span>
          </template>

          <!-- 第四级菜单 -->
          <template v-for="(fourthItem, fourthIndex) in item.children" :key="fourthIndex">
            <el-menu-item :index="fourthItem.path" class="fourth-level-menu">
              <el-icon v-if="fourthItem.meta && fourthItem.meta.icon">
                <component :is="fourthItem.meta.icon" />
              </el-icon>
              <template #title>
                <span>{{ fourthItem.meta ? fourthItem.meta.title : '未命名菜单' }}</span>
              </template>
            </el-menu-item>
          </template>
        </el-sub-menu>
      </template>
    </el-menu>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'

// 接收父组件传递的属性
const props = defineProps({
  // 菜单列表
  menuList: {
    type: Array,
    default: () => []
  },
  // 是否折叠菜单
  isCollapse: {
    type: Boolean,
    default: false
  }
})

const route = useRoute()
const router = useRouter()

// 存储用户手动关闭的菜单
const closedMenus = ref(getClosedMenusFromStorage())

// 计算当前激活的菜单
const activeMenu = computed(() => {
  return route.path
})

// 从localStorage获取已关闭的菜单
function getClosedMenusFromStorage() {
  try {
    const stored = localStorage.getItem('closedMenus')
    return stored ? JSON.parse(stored) : []
  } catch (e) {
    console.error('读取已关闭菜单状态失败:', e)
    return []
  }
}

// 保存已关闭的菜单到localStorage
function saveClosedMenusToStorage() {
  try {
    localStorage.setItem('closedMenus', JSON.stringify(closedMenus.value))
  } catch (e) {
    console.error('保存已关闭菜单状态失败:', e)
  }
}

// 处理菜单打开事件
function handleOpen(index) {
  // 从已关闭菜单列表中移除
  const idx = closedMenus.value.indexOf(index)
  if (idx !== -1) {
    closedMenus.value.splice(idx, 1)
    saveClosedMenusToStorage()
  }
}

// 处理菜单关闭事件
function handleClose(index) {
  // 添加到已关闭菜单列表
  if (!closedMenus.value.includes(index)) {
    closedMenus.value.push(index)
    saveClosedMenusToStorage()
  }
}

// 收集所有可能的菜单路径（包括三级和四级）
const getAllMenuPaths = () => {
  const paths = []
  
  props.menuList.forEach(item => {
    if (item && typeof item.path === 'string') {
      paths.push(item.path)
      
      // 添加四级菜单路径
      if (item.children && Array.isArray(item.children)) {
        item.children.forEach(child => {
          if (child && typeof child.path === 'string') {
            paths.push(child.path)
          }
        })
      }
    }
  })
  
  return paths
}

// 用于获取应该默认展开的菜单路径
const defaultOpeneds = computed(() => {
  try {
    // 获取所有菜单路径
    const allPaths = getAllMenuPaths()
    
    // 过滤掉用户手动关闭的菜单
    const openPaths = allPaths.filter(path => !closedMenus.value.includes(path))
    
    console.log("默认展开菜单路径:", openPaths)
    return openPaths
  } catch (e) {
    console.error('获取默认展开菜单路径出错:', e)
    return []
  }
})

// 确保组件挂载后菜单正确展开
onMounted(() => {
  // 如果菜单不处于折叠状态，延迟执行一次强制更新菜单状态
  if (!props.isCollapse) {
    nextTick(() => {
      // 触发计算属性更新
      const paths = defaultOpeneds.value
      console.log('组件挂载后确保菜单展开:', paths)
    })
  }
})

// 监听折叠状态变化
watch(
  () => props.isCollapse,
  (newVal) => {
    if (!newVal) {
      // 从折叠状态展开时，确保菜单正确展开
      nextTick(() => {
        const paths = defaultOpeneds.value
        console.log('折叠状态变化后确保菜单展开:', paths)
      })
    }
  }
)

// 监听路由变化
watch(
  () => route.path,
  (newPath) => {
    // 路由变化时，确保菜单状态正确
    nextTick(() => {
      // 触发默认展开菜单的重新计算
      const paths = defaultOpeneds.value
      console.log('路由变化后确保菜单展开:', paths)
    })
  }
)
</script>

<style scoped>
.side-menu-container {
  height: 100%;
  border-right: 1px solid #e6e6e6;
  width: 240px; /* 修改侧边栏宽度为240px */
}

.el-menu-vertical:not(.el-menu--collapse) {
  width: 240px; /* 修改菜单宽度为240px */
  min-height: 400px;
}

.el-menu-vertical {
  height: 100%;
  border-right: none;
}

/* 三级菜单样式 */
:deep(.third-level-menu .el-sub-menu__title) {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 16px;
  line-height: 22px;
  color: #000000;
  padding-left: 16px !important;
  height: 40px;
  line-height: 40px;
}

/* 三级菜单选中状态 */
:deep(.third-level-menu.is-active .el-sub-menu__title) {
  color: #0277FD !important;
}

/* 确保三级菜单和四级菜单之间的间距统一为16px */
:deep(.el-menu--inline) {
  padding-top: 16px !important;
  padding-bottom: 0 !important;
}

/* 四级菜单样式 */
:deep(.fourth-level-menu) {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #000000;
  height: 40px;
  line-height: 40px;
  padding-left: 37px !important;
  margin-bottom: 16px; /* 添加四级菜单之间的间距 */
}

/* 最后一个四级菜单不需要底部间距 */
:deep(.el-menu--inline > .fourth-level-menu:last-child) {
  margin-bottom: 0;
}

/* 四级菜单选中状态 */
:deep(.fourth-level-menu.is-active) {
  color: #0277FD !important;
  position: relative;
  background: linear-gradient(270deg, rgba(2,119,253,0) 0%, rgba(2,119,253,0.16) 100%) !important;
  border-radius: 24px 0px 0px 24px !important;
  margin-left: 16px !important;
  margin-right: 16px !important;
  width: calc(100% - 32px) !important;
  padding-left: 21px !important; /* 37px - 16px = 21px, 确保文字位置不变 */
}

/* 重置默认的选中背景色 */
:deep(.el-menu-item.is-active) {
  background-color: transparent;
}

/* 覆盖 ElementPlus 默认的选中颜色 */
:deep(.el-menu-item.is-active),
:deep(.el-sub-menu.is-active .el-sub-menu__title) {
  color: #0277FD !important;
}

/* 覆盖折叠状态的宽度 */
:deep(.el-menu--collapse) {
  width: 64px;
}

/* 确保菜单项文本不会被截断 */
:deep(.el-menu-item span), 
:deep(.el-sub-menu__title span) {
  white-space: normal;
  word-break: break-word;
  line-height: 1.4;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  max-width: 170px; /* 对应减少文本区域宽度 */
}

/* 覆盖ElementPlus的原生样式，确保间距一致 */
:deep(.el-sub-menu__icon-arrow) {
  right: 16px;
}

/* 强制重置菜单底部间距 */
:deep(.el-sub-menu.is-opened) {
  margin-bottom: 0 !important;
}

/* 确保菜单展开箭头不会影响间距 */
:deep(.el-sub-menu__title) {
  margin-bottom: 0 !important;
}
</style>