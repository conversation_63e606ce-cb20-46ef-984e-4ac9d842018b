# 多路RTSP播放器组件

## 简介

`MultiRtspPlayer.vue` 是一个基于 JSMpeg 的多路 RTSP 视频播放器组件，支持通过 WebSocket 接收服务端返回的多路视频流数据。该组件是在 `SimpleRtspPlayer.vue` 基础上改进的版本，主要特性是支持多设备共用一个 WebSocket 服务。

## 主要特性

- ✅ **多路视频支持**: 支持多个设备共用一个 WebSocket 连接
- ✅ **结构化数据处理**: 自动解析服务端返回的 JSON 格式视频流数据
- ✅ **视频ID匹配**: 根据视频ID过滤对应的视频流
- ✅ **页面可见性检测**: 自动处理页面切换时的连接管理
- ✅ **错误处理**: 完善的错误处理和状态提示
- ✅ **响应式设计**: 支持不同尺寸的播放器
- ✅ **事件系统**: 完整的播放事件回调

## 数据格式

### 服务端返回格式

服务端通过 WebSocket 返回的实际数据格式为：

```json
{
  "type": 110,
  "data": {
    "id": "视频ID",
    "message": ["Base64编码的视频流数据块1", "Base64编码的视频流数据块2", ...],
    "limit": 3272,
    "position": 0
  }
}
```

**字段说明：**
- `type`: 消息类型，固定为 110
- `data.id`: 视频设备ID，用于多路视频识别
- `data.message`: 视频流数据数组，包含一个或多个Base64编码的数据块
- `data.limit`: 数据限制大小
- `data.position`: 数据位置偏移

### 数据处理逻辑

组件会自动识别并处理多种数据格式：

1. **数组格式**：`message` 字段直接为数组
   ```json
   {
     "type": "110",
     "data": {
       "id": "c1003",
       "message": ["OFi14RfuO/SXiOdtwy...", "另一个数据块..."],
       "limit": 3272,
       "position": 0
     }
   }
   ```

2. **对象格式**：`message` 字段为包含Base64字符串的对象
   ```json
   {
     "type": "110",
     "data": {
       "id": "c1003",
       "message": {
         "array": "ROEAHgAAAgAAATDACJEAZWlhEORlIUUEAAAAfAANf...",
         "limit": 4090,
         "position": 0
       }
     }
   }
   ```

3. **字符串格式**（向后兼容）：`message` 字段为字符串
   ```json
   {
     "type": "110",
     "data": {
       "id": "c1003", 
       "message": "Base64编码的视频流数据"
     }
   }
   ```

**格式识别逻辑：**
- 支持 `type` 字段为数字 `110` 或字符串 `"110"`
- 自动检测 `message` 字段的类型（数组/对象/字符串）
- 对于对象格式，自动搜索其中的数组数据字段
- 支持常见的数组字段名：`array`、`data`、`chunks`、`blocks`、`content`

### 播放流程

1. 客户端连接 WebSocket 服务：`ws://**************:32021/basic/jsmpeg2`
2. 发送播放请求：`{"type": 110, "data": "视频ID"}`
3. 服务端返回二进制流数据
4. 组件将二进制数据转换为字符串并解析JSON
5. 验证数据格式和视频ID匹配
6. 根据 `message` 字段类型选择处理方式：
   - **数组格式**：直接处理数组中的数据块
   - **对象格式**：搜索对象中的数组字段并处理
   - **字符串格式**：直接解码字符串数据
7. 将所有数据块解码并合并为连续的二进制流
8. 传递给 JSMpeg 播放器进行播放

## 使用方法

### 基本用法

```vue
<template>
  <MultiRtspPlayer
    video-id="c1002"
    :width="640"
    :height="360"
    :autoplay="true"
    @play="onPlay"
    @stop="onStop"
    @error="onError"
  />
</template>

<script setup>
import MultiRtspPlayer from '@/components/rtsp/MultiRtspPlayer.vue'

const onPlay = () => {
  console.log('开始播放')
}

const onStop = () => {
  console.log('停止播放')
}

const onError = (error) => {
  console.error('播放错误:', error)
}
</script>
```

### 多设备播放

```vue
<template>
  <div class="multi-device-container">
    <MultiRtspPlayer
      v-for="device in devices"
      :key="device.id"
      :video-id="device.id"
      :width="320"
      :height="180"
      :show-controls="true"
      @play="() => onDevicePlay(device.id)"
      @error="(error) => onDeviceError(device.id, error)"
    />
  </div>
</template>

<script setup>
import MultiRtspPlayer from '@/components/rtsp/MultiRtspPlayer.vue'

const devices = ref([
  { id: 'c1001', name: '设备1' },
  { id: 'c1002', name: '设备2' },
  { id: 'c1003', name: '设备3' }
])

const onDevicePlay = (deviceId) => {
  console.log(`设备 ${deviceId} 开始播放`)
}

const onDeviceError = (deviceId, error) => {
  console.error(`设备 ${deviceId} 错误:`, error)
}
</script>
```

## 组件属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `videoId` | String | - | **必需**，视频ID，对应RTSP流标识 |
| `wsUrl` | String | `ws://**************:32021/basic/jsmpeg2` | WebSocket服务器地址 |
| `rtspUrl` | String | `''` | RTSP流地址（备用） |
| `width` | Number | `320` | 播放器宽度 |
| `height` | Number | `180` | 播放器高度 |
| `showControls` | Boolean | `false` | 是否显示控制按钮 |
| `autoplay` | Boolean | `true` | 是否自动播放 |

## 组件事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `play` | - | 开始播放时触发 |
| `stop` | - | 停止播放时触发 |
| `error` | `error` | 发生错误时触发 |

## 暴露的方法

通过 `ref` 可以调用以下方法：

```vue
<template>
  <MultiRtspPlayer ref="playerRef" video-id="c1002" />
</template>

<script setup>
import { ref } from 'vue'

const playerRef = ref()

// 开始播放
const play = () => {
  playerRef.value.play()
}

// 停止播放
const stop = () => {
  playerRef.value.stop()
}

// 销毁播放器
const destroy = () => {
  playerRef.value.destroy()
}
</script>
```

## 与简单版本的区别

| 特性 | SimpleRtspPlayer | MultiRtspPlayer |
|------|------------------|-----------------|
| WebSocket端点 | `/basic/jsmpeg` | `/basic/jsmpeg2` |
| 数据处理 | 直接处理二进制流 | 解析JSON包装的数据 |
| 多设备支持 | ❌ | ✅ |
| 视频ID过滤 | ❌ | ✅ |
| 结构化数据 | ❌ | ✅ |

## 示例页面

项目包含一个完整的示例页面 `MultiRtspPlayerExample.vue`，展示了：

- 单个播放器的配置和控制
- 多设备同时播放
- 状态监控和错误处理
- 响应式布局

## 技术实现

### 数据流转换

```javascript
// 1. 接收二进制数据
ws.onmessage = async (event) => {
  if (event.data instanceof ArrayBuffer || event.data instanceof Blob) {
    // 2. 转换为字符串
    const stringData = binaryToString(buffer)
    
    // 3. 解析JSON
    const parsedData = JSON.parse(stringData)
    
    // 4. 验证格式和视频ID
    if (parsedData.type === 110 && parsedData.data.id === props.videoId) {
      // 5. 根据message类型选择处理方式
      let videoData;
      if (Array.isArray(parsedData.data.message)) {
        // 处理数组格式：合并多个数据块
        videoData = convertArrayVideoDataToBinary(parsedData.data.message);
      } else {
        // 处理字符串格式（向后兼容）
        videoData = convertVideoDataToBinary(parsedData.data.message);
      }
      
      // 6. 传递给播放器
      if (videoData) {
        player.value.write(videoData);
      }
    }
  }
}

// 数组数据处理函数
const convertArrayVideoDataToBinary = (videoDataArray) => {
  const processedChunks = [];
  let totalLength = 0;
  
  // 处理数组中的每个数据块
  for (const chunk of videoDataArray) {
    if (typeof chunk === 'string') {
      const binaryChunk = convertVideoDataToBinary(chunk);
      if (binaryChunk) {
        processedChunks.push(binaryChunk);
        totalLength += binaryChunk.length;
      }
    }
  }
  
  // 合并所有数据块
  const mergedData = new Uint8Array(totalLength);
  let offset = 0;
  for (const chunk of processedChunks) {
    mergedData.set(chunk, offset);
    offset += chunk.length;
  }
  
  return mergedData;
};
```

### 错误处理

组件包含多层错误处理：

1. **连接错误**: WebSocket连接失败
2. **数据解析错误**: JSON解析失败时回退到原始数据处理
3. **播放错误**: JSMpeg播放器错误
4. **格式错误**: 数据格式不符合预期

## 注意事项

1. **JSMpeg依赖**: 组件依赖 JSMpeg 库，会自动从CDN加载
2. **浏览器兼容性**: 需要支持 WebSocket 和 Canvas 的现代浏览器
3. **视频格式**: 服务端需要提供 MPEG1 格式的视频流
4. **网络延迟**: WebSocket连接可能存在网络延迟，组件会自动重连
5. **页面可见性**: 页面不可见时会自动断开连接以节省资源

## 故障排除

### 常见问题

1. **无法连接WebSocket**
   - 检查服务端地址是否正确
   - 确认服务端是否运行正常
   - 检查网络连接

2. **无法播放视频**
   - 确认视频ID是否正确
   - 检查服务端是否返回正确格式的数据
   - 查看浏览器控制台错误信息

3. **视频画面卡顿**
   - 检查网络带宽
   - 考虑降低视频分辨率
   - 检查服务端性能

### 调试模式

组件包含详细的控制台日志，可以通过浏览器开发者工具查看数据流和错误信息。 