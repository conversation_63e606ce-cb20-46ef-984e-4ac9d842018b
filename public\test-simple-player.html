<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>简化 RTSP 播放器测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-group { margin-bottom: 30px; border: 1px solid #ccc; padding: 15px; }
        .input-group { margin-bottom: 10px; }
        label { min-width: 120px; display: inline-block; }
        input { width: 400px; padding: 5px; }
        button { margin: 5px; padding: 8px 15px; cursor: pointer; }
        canvas { border: 1px solid #000; margin: 10px 0; }
        .status { margin: 10px 0; padding: 5px; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
    </style>
</head>
<body>
    <h1>简化 RTSP 播放器测试</h1>
    
    <!-- 测试1：使用videoId参数 -->
    <div class="test-group">
        <h2>测试1：使用 videoId 参数</h2>
        <div class="input-group">
            <label for="wsUrl1">WebSocket地址：</label>
            <input type="text" id="wsUrl1" value="ws://**************:32021/basic/jsmpeg">
        </div>
        <div class="input-group">
            <label for="videoId1">视频ID：</label>
            <input type="text" id="videoId1" value="c1002">
        </div>
        <button onclick="testMethod1()">开始测试1</button>
        <button onclick="stopTest1()">停止测试1</button>
        <div id="status1" class="status"></div>
        <canvas id="canvas1" width="320" height="180"></canvas>
    </div>
    
    <!-- 测试2：使用RTSP URL参数 -->
    <div class="test-group">
        <h2>测试2：使用 RTSP URL 参数</h2>
        <div class="input-group">
            <label for="wsUrl2">WebSocket地址：</label>
            <input type="text" id="wsUrl2" value="ws://**************:32021/basic/jsmpeg">
        </div>
        <div class="input-group">
            <label for="rtspUrl2">RTSP地址：</label>
            <input type="text" id="rtspUrl2" value="rtsp://admin:123456@10.195.214.12:554/unicast/c1002/s0/live/forward">
        </div>
        <button onclick="testMethod2()">开始测试2</button>
        <button onclick="stopTest2()">停止测试2</button>
        <div id="status2" class="status"></div>
        <canvas id="canvas2" width="320" height="180"></canvas>
    </div>
    
    <!-- 测试3：发送JSON消息 -->
    <div class="test-group">
        <h2>测试3：发送 JSON 消息</h2>
        <div class="input-group">
            <label for="wsUrl3">WebSocket地址：</label>
            <input type="text" id="wsUrl3" value="ws://**************:32021/basic/jsmpeg">
        </div>
        <div class="input-group">
            <label for="videoId3">视频ID：</label>
            <input type="text" id="videoId3" value="c1002">
        </div>
        <button onclick="testMethod3()">开始测试3</button>
        <button onclick="stopTest3()">停止测试3</button>
        <div id="status3" class="status"></div>
        <canvas id="canvas3" width="320" height="180"></canvas>
    </div>

    <script src="https://cdn.jsdelivr.net/gh/phoboslab/jsmpeg@master/jsmpeg.min.js"></script>
    <script>
        let players = [null, null, null];
        
        function updateStatus(testNum, message, isError = false) {
            const statusDiv = document.getElementById(`status${testNum}`);
            statusDiv.textContent = message;
            statusDiv.className = `status ${isError ? 'error' : 'success'}`;
            console.log(`测试${testNum}: ${message}`);
        }
        
        // 测试1：使用videoId参数
        function testMethod1() {
            const wsUrl = document.getElementById('wsUrl1').value;
            const videoId = document.getElementById('videoId1').value;
            const canvas = document.getElementById('canvas1');
            
            if (players[0]) {
                players[0].destroy();
            }
            
            const fullUrl = `${wsUrl}?videoId=${videoId}`;
            updateStatus(1, `连接到: ${fullUrl}`);
            
            try {
                players[0] = new JSMpeg.Player(fullUrl, {
                    canvas: canvas,
                    autoplay: true,
                    audio: false,
                    onPlay: () => updateStatus(1, '播放中'),
                    onStalled: () => updateStatus(1, '卡顿中'),
                    onSourceEstablished: () => updateStatus(1, '连接建立'),
                    onSourceCompleted: () => updateStatus(1, '数据流结束')
                });
            } catch (error) {
                updateStatus(1, `错误: ${error.message}`, true);
            }
        }
        
        function stopTest1() {
            if (players[0]) {
                players[0].destroy();
                players[0] = null;
                updateStatus(1, '已停止');
            }
        }
        
        // 测试2：使用RTSP URL参数
        function testMethod2() {
            const wsUrl = document.getElementById('wsUrl2').value;
            const rtspUrl = document.getElementById('rtspUrl2').value;
            const canvas = document.getElementById('canvas2');
            
            if (players[1]) {
                players[1].destroy();
            }
            
            const fullUrl = `${wsUrl}?rtsp=${encodeURIComponent(rtspUrl)}`;
            updateStatus(2, `连接到: ${fullUrl}`);
            
            try {
                players[1] = new JSMpeg.Player(fullUrl, {
                    canvas: canvas,
                    autoplay: true,
                    audio: false,
                    onPlay: () => updateStatus(2, '播放中'),
                    onStalled: () => updateStatus(2, '卡顿中'),
                    onSourceEstablished: () => updateStatus(2, '连接建立'),
                    onSourceCompleted: () => updateStatus(2, '数据流结束')
                });
            } catch (error) {
                updateStatus(2, `错误: ${error.message}`, true);
            }
        }
        
        function stopTest2() {
            if (players[1]) {
                players[1].destroy();
                players[1] = null;
                updateStatus(2, '已停止');
            }
        }
        
        // 测试3：发送JSON消息
        function testMethod3() {
            const wsUrl = document.getElementById('wsUrl3').value;
            const videoId = document.getElementById('videoId3').value;
            const canvas = document.getElementById('canvas3');
            
            if (players[2]) {
                players[2].destroy();
            }
            
            // 先建立WebSocket连接
            updateStatus(3, `连接到: ${wsUrl}`);
            
            const ws = new WebSocket(wsUrl);
            ws.binaryType = 'arraybuffer';
            
            ws.onopen = () => {
                updateStatus(3, 'WebSocket连接成功，发送播放请求');
                
                // 发送JSON播放请求
                const message = {
                    type: 110,
                    data: videoId
                };
                ws.send(JSON.stringify(message));
                
                // 创建一个模拟的播放器URL来处理二进制数据
                const dummyUrl = `ws://localhost:0/${videoId}`;
                
                try {
                    players[2] = new JSMpeg.Player(dummyUrl, {
                        canvas: canvas,
                        autoplay: true,
                        audio: false,
                        onPlay: () => updateStatus(3, '播放中'),
                        onStalled: () => updateStatus(3, '卡顿中'),
                        onSourceEstablished: () => updateStatus(3, '连接建立'),
                        onSourceCompleted: () => updateStatus(3, '数据流结束'),
                        // 拦截WebSocket连接，使用我们自己的WebSocket
                        onConnectionCreate: () => {
                            return {
                                onopen: null,
                                onclose: null,
                                onmessage: null,
                                send: () => {},
                                close: () => {},
                                readyState: 1
                            };
                        }
                    });
                    
                    // 模拟source已建立
                    if (players[2] && players[2].source) {
                        players[2].source.established = true;
                    }
                    
                } catch (error) {
                    updateStatus(3, `创建播放器错误: ${error.message}`, true);
                }
            };
            
            ws.onmessage = (event) => {
                if (event.data instanceof ArrayBuffer) {
                    // 处理二进制视频数据
                    if (players[2] && players[2].source) {
                        try {
                            const buffer = new Uint8Array(event.data);
                            updateStatus(3, `收到 ${buffer.length} 字节数据`);
                            
                            // 将数据写入播放器
                            if (players[2].source.write) {
                                players[2].source.write(buffer);
                            }
                        } catch (error) {
                            updateStatus(3, `处理数据错误: ${error.message}`, true);
                        }
                    }
                } else {
                    updateStatus(3, `收到文本消息: ${event.data}`);
                }
            };
            
            ws.onerror = (error) => {
                updateStatus(3, `WebSocket错误: ${error.message}`, true);
            };
            
            ws.onclose = () => {
                updateStatus(3, 'WebSocket连接关闭');
            };
        }
        
        function stopTest3() {
            if (players[2]) {
                players[2].destroy();
                players[2] = null;
                updateStatus(3, '已停止');
            }
        }
        
        // 页面卸载时清理资源
        window.onbeforeunload = () => {
            players.forEach(player => {
                if (player) {
                    player.destroy();
                }
            });
        };
    </script>
</body>
</html> 