<template>
  <div class="table-config-step">
    <div class="step-header">
      <h3>表格列配置</h3>
      <el-button type="primary" size="small" @click="addColumn">添加列</el-button>
    </div>
    
    <div class="columns-container">
      <el-empty v-if="!config.table.columns.length" description="暂无表格列配置，请添加" />
      
      <el-card v-for="(column, index) in config.table.columns" :key="index" class="column-card">
        <template #header>
          <div class="column-header">
            <span>列 {{ index + 1 }}: {{ column.label || '未命名' }}</span>
            <div class="column-actions">
              <el-button type="danger" size="small" circle @click="removeColumn(index)">
                <el-icon><Delete /></el-icon>
              </el-button>
            </div>
          </div>
        </template>
        
        <el-form :model="column" label-width="100px">
          <el-form-item label="列标签" required>
            <el-input v-model="column.label" placeholder="请输入列标签" />
          </el-form-item>
          
          <el-form-item label="属性名" required>
            <el-input v-model="column.prop" placeholder="请输入属性名" />
          </el-form-item>
          
          <el-form-item label="列宽">
            <el-input v-model="column.width" placeholder="请输入列宽，如：100px" />
          </el-form-item>
          
          <el-form-item label="对齐方式">
            <el-select v-model="column.align" placeholder="请选择对齐方式">
              <el-option label="左对齐" value="left" />
              <el-option label="居中" value="center" />
              <el-option label="右对齐" value="right" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="是否排序">
            <el-switch v-model="column.sortable" />
          </el-form-item>
          
          <el-form-item label="是否固定">
            <el-select v-model="column.fixed" placeholder="请选择是否固定">
              <el-option label="不固定" value="" />
              <el-option label="左固定" value="left" />
              <el-option label="右固定" value="right" />
            </el-select>
          </el-form-item>
        </el-form>
      </el-card>
    </div>
    
    <div class="step-actions">
      <el-button @click="handlePrev">上一步</el-button>
      <el-button type="primary" @click="handleNext">下一步</el-button>
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue'
import { Delete } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  config: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update:config', 'next', 'prev'])

// 添加列
const addColumn = () => {
  if (!props.config.table.columns) {
    props.config.table.columns = []
  }
  
  props.config.table.columns.push({
    label: '',
    prop: '',
    width: '',
    align: 'left',
    sortable: false,
    fixed: ''
  })
}

// 移除列
const removeColumn = (index) => {
  props.config.table.columns.splice(index, 1)
}

// 上一步
const handlePrev = () => {
  emit('prev')
}

// 下一步
const handleNext = () => {
  // 验证表格列配置
  if (!props.config.table.columns.length) {
    ElMessage.warning('请至少添加一列表格配置')
    return
  }
  
  // 验证每一列的必填项
  for (let i = 0; i < props.config.table.columns.length; i++) {
    const column = props.config.table.columns[i]
    if (!column.label) {
      ElMessage.warning(`第 ${i + 1} 列的列标签不能为空`)
      return
    }
    if (!column.prop) {
      ElMessage.warning(`第 ${i + 1} 列的属性名不能为空`)
      return
    }
  }
  
  emit('next')
}
</script>

<style scoped>
.table-config-step {
  padding: 0 20px;
}

.step-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.columns-container {
  margin-bottom: 20px;
  max-height: calc(100vh - 400px);
  overflow-y: auto;
}

.column-card {
  margin-bottom: 16px;
}

.column-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.column-actions {
  display: flex;
  gap: 8px;
}

.step-actions {
  margin-top: 30px;
  display: flex;
  justify-content: space-between;
}
</style>
