/**
 * 文件下载工具类
 */
import { saveAs } from 'file-saver'
import JSZip from 'jszip'
import { generateCode } from './templateEngine'

/**
 * 下载单个文件
 * @param {String} content - 文件内容
 * @param {String} filename - 文件名
 */
export function downloadFile(content, filename) {
  const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })
  saveAs(blob, filename)
}

/**
 * 生成并下载所有代码文件
 * @param {Object} config - 配置数据
 */
export async function downloadAllFiles(config) {
  try {
    // 创建一个新的 JSZip 实例
    const zip = new JSZip()
    
    // 创建模块文件夹
    const moduleName = config.basic.moduleName
    const moduleFolder = zip.folder(moduleName)
    
    // 创建组件文件夹
    const componentsFolder = moduleFolder.folder('components')
    
    // 生成主页面代码
    const mainCode = await generateCode(config, 'main')
    moduleFolder.file('index.vue', mainCode)
    
    // 生成搜索组件代码（如果有搜索字段）
    if (config.search.fields && config.search.fields.length > 0) {
      const searchCode = await generateCode(config, 'search')
      componentsFolder.file('SearchForm.vue', searchCode)
    }
    
    // 生成表单组件代码（如果有表单字段）
    if (config.form.fields && config.form.fields.length > 0) {
      const formCode = await generateCode(config, 'form')
      componentsFolder.file('EditForm.vue', formCode)
    }
    
    // 创建 API 文件夹
    const apiFolder = zip.folder('api')
    
    // 生成 API 代码
    const apiCode = await generateCode(config, 'api')
    apiFolder.file(`${moduleName}.js`, apiCode)
    
    // 生成 ZIP 文件并下载
    const zipContent = await zip.generateAsync({ type: 'blob' })
    saveAs(zipContent, `${moduleName}-code.zip`)
    
    return true
  } catch (error) {
    console.error('生成并下载文件失败', error)
    throw error
  }
}
