<template>
  <PanelBox title="预警列表">
    <template #extra>
      <div class="more-btn" @click="openWarningListModal">
        更多>>
      </div>
    </template>
    <div class="panel-content">
      <!-- 查询条件区域 -->
      <div class="filter-area">
        <div class="filter-row">
          <div class="filter-item">
            <CommonSelect v-model="timeRange" :options="timeOptions" />
          </div>
          <div class="filter-item">
            <CommonSelect v-model="warningType" :options="typeOptions" />
          </div>
        </div>
        <div class="filter-row">
          <div class="filter-item">
            <CommonSelect v-model="warningLevel" :options="levelOptions" />
          </div>
          <div class="filter-item">
            <CommonSelect v-model="processStatus" :options="statusOptions" />
          </div>
        </div>
      </div>

      <!-- 列表区域 -->
      <div class="warning-list">
        <ScrollTable :columns="columns" :data="warningList" :auto-scroll="true" :scroll-speed="3000"
          table-height="calc(100% - 10px)" :visible-rows="4" :hidden-header="true" @row-click="handleRowClick">
          <template #warningContent="{ row }">
            <div class="warning-item">
              <!-- 第一行 -->
              <div class="warning-row warning-header">
                <div class="warning-type" :class="getTypeClass(row.type)">{{ row.type }}</div>
                <div class="warning-title">{{ row.title }}</div>
                <div class="warning-level" :class="getLevelClass(row.level)">{{ row.level }}</div>
              </div>

              <!-- 第二行 -->
              <div class="warning-row warning-info">
                <div class="location-info">
                  <span class="location-icon"></span>
                  <span class="location-text">{{ row.location }}</span>
                </div>
                <div class="process-status" :class="getStatusClass(row.status)">{{ row.status }}</div>
              </div>

              <!-- 第三行 -->
              <div class="warning-row warning-time">
                <div class="time-info">
                  <span class="time-icon"></span>
                  <span class="time-text">{{ row.time }}</span>
                </div>
                <div class="detail-icon"></div>
              </div>
            </div>
          </template>
        </ScrollTable>
      </div>
    </div>
  </PanelBox>
</template>

<script setup>
import { ref } from 'vue'
import PanelBox from '@/components/screen/PanelBox.vue'
import CommonSelect from '@/components/screen/CommonSelect.vue'
import ScrollTable from '@/components/screen/common/ScrollTable.vue'

// 查询条件选项
const timeRange = ref('today')
const timeOptions = [
  { label: '今日', value: 'today' },
  { label: '近3天', value: '3days' },
  { label: '近一周', value: 'week' }
]

const warningType = ref('all')
const typeOptions = [
  { label: '全部', value: 'all' },
  { label: '燃气', value: 'gas' },
  { label: '供热', value: 'heating' },
  { label: '排水', value: 'drainage' },
  { label: '桥梁', value: 'bridge' }
]

const warningLevel = ref('all')
const levelOptions = [
  { label: '预警级别', value: 'all' },
  { label: '一级预警', value: 'level1' },
  { label: '二级预警', value: 'level2' },
  { label: '三级预警', value: 'level3' }
]

const processStatus = ref('all')
const statusOptions = [
  { label: '处置状态', value: 'all' },
  { label: '未处置', value: 'unprocessed' },
  { label: '处置中', value: 'processing' },
  { label: '已处置', value: 'processed' }
]

// 表格列配置
const columns = [
  {
    title: '预警内容',
    dataIndex: 'warningContent',
    width: '100%'
  }
]

// 模拟预警数据
const warningList = ref([
  {
    id: 1,
    type: '供热',
    title: 'XX路供热管网泄露',
    level: '二级',
    location: '某天大道XX路XX号',
    status: '未处置',
    time: '2023-11-16 10:00:00'
  },
  {
    id: 2,
    type: '供热',
    title: 'XX路供热管网泄露',
    level: '三级',
    location: '某天大道XX路XX号',
    status: '处置中',
    time: '2023-11-16 10:00:00'
  },
  {
    id: 3,
    type: '排水',
    title: 'XX路排水管网泄露',
    level: '二级',
    location: '某天大道XX路XX号',
    status: '未处置',
    time: '2023-11-16 10:00:00'
  },
  {
    id: 4,
    type: '排水',
    title: 'XX路排水管网泄露',
    level: '三级',
    location: '某天大道XX路XX号',
    status: '处置中',
    time: '2023-11-16 10:00:00'
  },
  {
    id: 5,
    type: '排水',
    title: 'XX路排水管网泄流',
    level: '三级',
    location: '某天大道XX路XX号',
    status: '处置中',
    time: '2023-11-16 10:00:00'
  },
  {
    id: 6,
    type: '排水',
    title: 'XX路排水管网泄流',
    level: '一级',
    location: '某天大道XX路XX号',
    status: '处置中',
    time: '2023-11-16 10:00:00'
  },
  {
    id: 7,
    type: '排水',
    title: 'XX路排水管网泄流',
    level: '一级',
    location: '某天大道XX路XX号',
    status: '未处置',
    time: '2023-11-16 10:00:00'
  },
  {
    id: 8,
    type: '桥梁',
    title: 'XX桥梁发生沉降',
    level: '三级',
    location: '某天大道XX路XX号',
    status: '已处置',
    time: '2023-11-16 10:00:00'
  },
  {
    id: 9,
    type: '燃气',
    title: 'XX路燃气管道泄漏',
    level: '一级',
    location: '某天大道XX路XX号',
    status: '处置中',
    time: '2023-11-16 10:00:00'
  }
])

// 弹窗显示控制
const openWarningListModal = () => {
  console.log('打开预警列表弹窗')
}

// 处理行点击事件
const handleRowClick = (row) => {
  console.log('点击了行', row)
}

// 获取专项类型的样式类
const getTypeClass = (type) => {
  const classMap = {
    '供热': 'type-heating',
    '排水': 'type-drainage',
    '桥梁': 'type-bridge',
    '燃气': 'type-gas'
  }
  return classMap[type] || 'type-heating'
}

// 获取预警级别的样式类
const getLevelClass = (level) => {
  const classMap = {
    '一级': 'level-one',
    '二级': 'level-two',
    '三级': 'level-three'
  }
  return classMap[level] || 'level-one'
}

// 获取处置状态的样式类
const getStatusClass = (status) => {
  const classMap = {
    '未处置': 'status-unprocessed',
    '处置中': 'status-processing',
    '已处置': 'status-processed'
  }
  return classMap[status] || 'status-unprocessed'
}
</script>

<style scoped>
.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

/* 更多按钮样式 */
.more-btn {
  font-family: PingFangSC, 'PingFang SC';
  font-size: 12px;
  color: #3AA1FF;
  cursor: pointer;
  margin-right: 30px;
}

.more-btn:hover {
  color: #66B8FF;
}

/* 筛选区域样式 */
.filter-area {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.filter-row {
  display: flex;
  justify-content: space-between;
  gap: 10px;
}

.filter-item {
  flex: 1;
}

/* 预警列表区域 */
.warning-list {
  flex: 1;
  overflow: hidden;
}
:deep(.dark-row) {
  background-color: transparent;
}
:deep(.light-row) {
  background-color: transparent;
}
/* 预警项样式 */
.warning-item {
  display: flex;
  flex-direction: column;
  gap: 6px;
  padding: 10px 15px;
  background: url('@/assets/images/screen/comprehensive/row_bg.png') no-repeat;
  background-size: 100% 100%;
  margin-bottom: 0.5rem;
}

/* 行样式 */
.warning-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 预警头部样式 */
.warning-header {
  margin-bottom: 6px;
}

/* 专项类型标签 */
.warning-type {
  width: 40px;
  height: 20px;
  background: rgba(42, 185, 255, 0.3);
  border-radius: 4px;
  border: 1px solid #2AB9FF;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #DEEDFF;
  line-height: 17px;
}

/* 不同专项的颜色 */
.type-heating {
  background: rgba(42, 185, 255, 0.3);
  border: 1px solid #2AB9FF;
}

.type-drainage {
  background: rgba(31, 203, 161, 0.3);
  border: 1px solid #1FCBA1;
}

.type-bridge {
  background: rgba(0, 121, 255, 0.3);
  border: 1px solid #0079FF;
}

.type-gas {
  background: rgba(255, 104, 23, 0.3);
  border: 1px solid #FF6817;
}

/* 预警标题 */
.warning-title {
  flex: 1;
  margin: 0 10px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 16px;
  color: #DEEDFF;
  line-height: 22px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 预警级别 */
.warning-level {
  width: 60px;
  height: 20px;
  background: rgba(255, 57, 0, 0.5);
  border-radius: 2px;
  border: 1px solid #FF3900;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
}

/* 不同级别的样式 */
.level-one {
  background: rgba(255, 57, 0, 0.5);
  border: 1px solid #FF3900;
}

.level-two {
  background: rgba(255, 104, 23, 0.5);
  border: 1px solid #FF6817;
}

.level-three {
  background: rgba(255, 211, 46, 0.5);
  border: 1px solid #FFD32E;
}

/* 地址信息 */
.location-info {
  display: flex;
  align-items: center;
  gap: 5px;
}

.location-icon {
  width: 14px;
  height: 14px;
  background: url('~@/assets/images/screen/common/location.svg') no-repeat center/cover;
}

.location-text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
}

/* 处置状态 */
.process-status {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #18B5FF;
}

.status-unprocessed {
  color: #FF3900;
}

.status-processing {
  color: #18B5FF;
}

.status-processed {
  color: #1FCBA1;
}

/* 时间信息 */
.time-info {
  display: flex;
  align-items: center;
  gap: 5px;
}

.time-icon {
  width: 14px;
  height: 14px;
  background: url('@/assets/images/screen/common/location.svg') no-repeat center/cover;
  transform: rotate(45deg);
}

.time-text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #FFFFFF;
}

/* 详情图标 */
.detail-icon {
  width: 20px;
  height: 20px;
  background: url('@/assets/images/screen/arrow-down.png') no-repeat center/cover;
  transform: rotate(-90deg);
  cursor: pointer;
}

/* 响应式布局 */
@media screen and (max-width: 1919px) {
  .panel-content {
    padding: 12px;
    gap: 10px;
  }

  .warning-item {
    padding: 8px 12px;
    gap: 4px;
  }

  .warning-title {
    font-size: 14px;
  }
}

@media screen and (min-width: 1920px) and (max-width: 2560px) {
  .panel-content {
    padding: 15px;
  }
}

@media screen and (min-width: 2561px) {
  .panel-content {
    padding: 18px;
  }

  .warning-item {
    padding: 12px 18px;
    gap: 8px;
  }

  .warning-title {
    font-size: 18px;
  }

  .warning-type {
    width: 45px;
    height: 24px;
    font-size: 14px;
  }

  .warning-level {
    width: 70px;
    height: 24px;
    font-size: 14px;
  }
}

/* 添加全屏模式(1080px高度)的适配 */
@media screen and (min-height: 1056px) and (max-height: 1080px) {
  .panel-content {
    padding: 15px;
  }
}

@media screen and (min-height: 940px) and (max-height: 1055px) {
  .panel-content {
    padding: 10px;
    gap: 10px;
  }
}

@media screen and (min-height: 900px) and (max-height: 940px) {
  .panel-content {
    padding: 8px;
    gap: 8px;
  }
}
</style>