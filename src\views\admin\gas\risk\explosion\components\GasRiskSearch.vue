<template>
  <div class="gas-risk-search">
    <div class="search-form">
      <div class="form-item">
        <span class="label">压力级别:</span>
        <el-select v-model="formData.pressureLevel" class="form-input" placeholder="请选择">
          <el-option label="全部" value="" />
          <el-option 
            v-for="item in pressureLevelOptions" 
            :key="item.value" 
            :label="item.label" 
            :value="item.value" 
          />
        </el-select>
      </div>
      <div class="form-item">
        <span class="label">风险等级:</span>
        <el-select v-model="formData.riskLevel" class="form-input" placeholder="请选择">
          <el-option label="全部" value="" />
          <el-option 
            v-for="item in riskLevelOptions" 
            :key="item.value" 
            :label="item.label" 
            :value="item.value" 
          />
        </el-select>
      </div>
      <div class="form-item">
        <span class="label">管控状态:</span>
        <el-select v-model="formData.pipelineStatus" class="form-input" placeholder="请选择">
          <el-option label="全部" value="" />
          <el-option 
            v-for="item in controlStatusOptions" 
            :key="item.value" 
            :label="item.label" 
            :value="item.value" 
          />
        </el-select>
      </div>
      <div class="form-item">
        <el-input v-model="formData.riskCode" class="form-input" placeholder="请输入风险编码" />
      </div>
      <div class="form-item" style="margin-left: auto;">
        <el-button type="primary" class="search-btn" @click="handleSearch">查询</el-button>
        <el-button class="reset-btn" @click="handleReset">重置</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { ElSelect, ElOption, ElInput, ElButton } from 'element-plus';
import { PRESSURE_LEVELS } from '@/constants/gas';
import { RISK_LEVEL, CONTROL_STATUS ,RISK_LEVEL_MAP} from '@/constants/gas';

const emit = defineEmits(['search', 'reset']);

// 定义选项
const pressureLevelOptions = PRESSURE_LEVELS;
const riskLevelOptions = Object.entries(RISK_LEVEL).map(([key, value]) => ({
  label: RISK_LEVEL_MAP[value],
  value
}));
const controlStatusOptions = Object.entries(CONTROL_STATUS).map(([key, value]) => ({
  label: key === 'NO_CONTROL_NEEDED' ? '无需管控' : key === 'UNCONTROLLED' ? '未管控' : '已管控',
  value
}));

// 表单数据
const formData = ref({
  pressureLevel: '',
  riskLevel: '',
  pipelineStatus: '',
  riskCode: ''
});

// 处理查询
const handleSearch = () => {
  emit('search', formData.value);
};

// 处理重置
const handleReset = () => {
  formData.value = {
    pressureLevel: '',
    riskLevel: '',
    pipelineStatus: '',
    riskCode: ''
  };
  emit('reset');
};
</script>

<style scoped>
.gas-risk-search {
  width: 100%;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.form-item {
  display: flex;
  align-items: center;
  margin-right: 16px;
  margin-bottom: 16px;
}

.label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #282828;
  margin-right: 8px;
  white-space: nowrap;
}

.form-input {
  width: 180px;
  height: 32px;
}

:deep(.el-input__wrapper) {
  background: #FFFFFF;
  border-radius: 2px;
  border: 1px solid #CED3DA;
  height: 32px;
  box-shadow: none !important;
}

:deep(.el-select .el-input__wrapper) {
  background: #FFFFFF;
  border-radius: 2px;
  border: 1px solid #CED3DA;
  height: 32px;
  box-shadow: none !important;
}

:deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px #0277FD inset !important;
}

:deep(.el-select .el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px #0277FD inset !important;
}

.search-btn {
  width: 60px;
  height: 32px;
  background: #0277FD;
  border-radius: 2px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #FFFFFF;
  padding: 0;
  margin-right: 8px;
}

.reset-btn {
  width: 60px;
  height: 32px;
  background: #FFFFFF;
  border-radius: 2px;
  border: 1px solid #CED3DA;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #647688;
  padding: 0;
}
</style> 