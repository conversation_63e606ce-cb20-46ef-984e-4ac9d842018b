<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="1000px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="gas-monitor-sensor-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      :disabled="mode === 'view'"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="设备名称" prop="deviceName">
            <el-input v-model="formData.deviceName" placeholder="请输入设备名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="设备编码" prop="indexCode">
            <el-input v-model="formData.indexCode" placeholder="请输入设备编码" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="设备类型" prop="deviceType">
            <el-select v-model="formData.deviceType" placeholder="请选择" class="w-full">
              <el-option
                v-for="item in deviceTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="监测指标" prop="monitorIndex">
            <el-select v-model="formData.monitorIndex" placeholder="请选择" class="w-full">
              <el-option
                v-for="item in monitorIndexOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="采集频率" prop="collectFrequency">
            <div class="flex items-center">
              <el-input-number v-model="formData.collectFrequency" :min="0" :precision="0" />
              <span class="ml-2">分钟1次</span>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="上传频率" prop="uploadFrequency">
            <div class="flex items-center">
              <el-input-number v-model="formData.uploadFrequency" :min="0" :precision="0" />
              <span class="ml-2">分钟1次</span>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="量程">
            <div class="flex items-center">
              <el-input-number v-model="formData.measureRangeLow" :min="0" :precision="0" placeholder="下限" />
              <span class="mx-2">-</span>
              <el-input-number v-model="formData.measureRangeUp" :min="0" :precision="0" placeholder="上限" />
              <el-input v-model="formData.measureUnit" placeholder="单位" class="ml-2 w-20" />
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="监测对象" prop="monitorObjectId">
            <el-select v-model="formData.monitorObjectId" placeholder="请选择" class="w-full" @change="handleMonitorObjectChange">
              <el-option
                v-for="item in monitorObjectOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="对象选择" prop="monitorTarget">
            <el-select v-model="formData.monitorTarget" placeholder="请选择" class="w-full" :disabled="!formData.monitorObjectId">
              <el-option 
                v-for="option in monitorTargetOptions" 
                :key="option.value" 
                :label="option.label" 
                :value="option.value" 
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="设备状态" prop="onlineStatus">
            <el-select v-model="formData.onlineStatus" placeholder="请选择" class="w-full">
              <el-option
                v-for="item in deviceOnlineStatusOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="所属区域" prop="regionPath">
            <div class="flex items-center">
              <el-cascader
                v-model="formData.regionCode"
                :options="areaOptions"
                :props="{
                  value: 'code',
                  label: 'name',
                  children: 'children'
                }"
                placeholder="请选择所属区域"
                class="w-full"
                @change="handleAreaChange"
              />
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="设备位置" prop="address">
            <el-input v-model="formData.address" placeholder="输入设备安装位置" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="定位" prop="location">
            <div class="flex items-center">
              <el-input v-model="formData.longitude" placeholder="输入选择经度" class="mr-2" />
              <el-input v-model="formData.latitude" placeholder="输入选择纬度" />
              <el-button type="primary" class="ml-2" icon="Location" circle @click="openMapPicker" :disabled="mode === 'view'"></el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="mode !== 'view'">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted, onUnmounted, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import { saveSensorDevice, updateSensorDevice, getSensorDeviceDetail } from '@/api/gas';
import { DEVICE_TYPES, DEVICE_TYPE_MAP, MONITOR_INDEXES, MONITOR_INDEX_MAP, MONITOR_OBJECT_TYPES, MONITOR_OBJECT_TYPE_MAP, DEVICE_ONLINE_STATUS, AREA_OPTIONS } from '@/constants/gas';
import { collectShow } from '@/hooks/gishooks';
import bus from '@/utils/mitt';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add', // 'add', 'edit', 'view'
    validator: (value) => ['add', 'edit', 'view'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'success']);

// 表单引用
const formRef = ref(null);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 对话框标题
const dialogTitle = computed(() => {
  const titles = {
    add: '新增设备',
    edit: '编辑设备',
    view: '设备详情'
  };
  return titles[props.mode] || '设备信息';
});

// 表单数据
const formData = reactive({
  id: '',
  deviceName: '',
  indexCode: '',
  deviceType: '',
  deviceTypeName: '',
  monitorIndex: '',
  monitorIndexName: '',
  collectFrequency: 0,
  uploadFrequency: 0,
  measureRangeLow: '',
  measureRangeUp: '',
  measureUnit: '',
  monitorObjectId: '',
  monitorObjectName: '',
  monitorTarget: '',
  monitorTargetName: '',
  onlineStatus: 0,
  address: '',
  longitude: '',
  latitude: '',
  isVss: false,
  regionCode: '',
  regionName: '',
  regionPath: '@root000000000353d13ccb1eb45e786f6c709318b6d31@',
  regionPathName: '',
  geomText: '',
  monitorType: '',
  monitorTypeName: '',
  picUrls: ''
});

// 表单验证规则
const formRules = {
  deviceName: [{ required: true, message: '请输入设备名称', trigger: 'blur' }],
  indexCode: [{ required: true, message: '请输入设备编码', trigger: 'blur' }],
  deviceType: [{ required: true, message: '请选择设备类型', trigger: 'change' }],
  monitorIndex: [{ required: true, message: '请选择监测指标', trigger: 'change' }],
  collectFrequency: [{ required: true, message: '请输入采集频率', trigger: 'blur' }],
  uploadFrequency: [{ required: true, message: '请输入上传频率', trigger: 'blur' }],
  monitorObjectId: [{ required: true, message: '请选择监测对象', trigger: 'change' }],
  monitorTarget: [{ required: true, message: '请选择对象', trigger: 'change' }],
  onlineStatus: [{ required: true, message: '请选择设备状态', trigger: 'change' }],
  address: [{ required: true, message: '请输入设备位置', trigger: 'blur' }],
  regionCode: [{ required: true, message: '请选择所属区域', trigger: 'change' }]
};

// 监测对象选项
const monitorTargetOptions = ref([]);

// 设备类型选项
const deviceTypeOptions = DEVICE_TYPES;

// 监测指标选项
const monitorIndexOptions = MONITOR_INDEXES;

// 监测对象类型选项
const monitorObjectOptions = MONITOR_OBJECT_TYPES;

// 设备状态选项
const deviceOnlineStatusOptions = DEVICE_ONLINE_STATUS;

// 行政区划选项
const areaOptions = ref(AREA_OPTIONS);

// 处理区域选择变化
const handleAreaChange = (value) => {
  if (value && value.length > 0) {
    formData.regionCode = value[value.length - 1];
    // 更新区域名称
    const selectedArea = findAreaByCode(areaOptions.value, formData.regionCode);
    if (selectedArea) {
      formData.regionName = selectedArea.name;
      
      // 构建区域路径名称
      let pathName = '';
      areaOptions.value.forEach(area => {
        if (area.code === value[0]) {
          pathName = area.name;
          if (area.children) {
            area.children.forEach(town => {
              if (town.code === formData.regionCode) {
                pathName += '/' + town.name;
              }
            });
          }
        }
      });
      formData.regionPathName = pathName;
    }
  }
};

// 根据区域代码查找区域信息
const findAreaByCode = (areas, code) => {
  for (const area of areas) {
    if (area.code === code) {
      return area;
    }
    if (area.children) {
      const found = findAreaByCode(area.children, code);
      if (found) return found;
    }
  }
  return null;
};

// 地图选点事件处理函数
const handleCollectLocation = (params) => {
  nextTick(() => {
    formData.longitude = params.longitude;
    formData.latitude = params.latitude;
  });
};

// 打开地图选点
const openMapPicker = () => {
  collectShow.value = true; // 激活采集点位窗口
  // 先移除可能存在的旧监听器
  bus.off("getCollectLocation", handleCollectLocation);
  // 添加新的监听器
  bus.on("getCollectLocation", handleCollectLocation);
};

// 组件卸载时清理事件监听
onUnmounted(() => {
  bus.off("getCollectLocation", handleCollectLocation);
});

// 监听设备类型变化
watch(() => formData.deviceType, (newVal) => {
  if (newVal) {
    formData.deviceTypeName = DEVICE_TYPE_MAP[newVal] || '';
  } else {
    formData.deviceTypeName = '';
  }
}, { immediate: true });

// 监听监测指标变化
watch(() => formData.monitorIndex, (newVal) => {
  if (newVal) {
    formData.monitorIndexName = MONITOR_INDEX_MAP[newVal] || '';
  } else {
    formData.monitorIndexName = '';
  }
}, { immediate: true });

// 监听监测对象变化
watch(() => formData.monitorObjectId, (newVal) => {
  if (newVal) {
    formData.monitorObjectName = MONITOR_OBJECT_TYPE_MAP[newVal] || '';
  } else {
    formData.monitorObjectName = '';
  }
}, { immediate: true });

// 监听监测对象变化
const handleMonitorObjectChange = (value) => {
  formData.monitorTarget = ''; // 清空对象选择
  
  // 根据监测对象类型加载对应的选项
  if (value === '11001') { // 管网
    monitorTargetOptions.value = [
      { label: '管网1', value: 'pipeline1' },
      { label: '管网2', value: 'pipeline2' }
    ];
  } else if (value === '12002') { // 场站
    monitorTargetOptions.value = [
      { label: '场站1', value: 'station1' },
      { label: '场站2', value: 'station2' }
    ];
  } else if (value === '13003') { // 窨井
    monitorTargetOptions.value = [
      { label: '窨井1', value: 'manhole1' },
      { label: '窨井2', value: 'manhole2' }
    ];
  } else {
    monitorTargetOptions.value = [];
  }
};

// 获取设备详情
const fetchDeviceDetail = async (id) => {
  try {
    const res = await getSensorDeviceDetail(id);
    if (res && (res.code === 200 || res.code === 0)) {
      const data = res.data;
      
      // 将详情数据映射到表单
      Object.keys(formData).forEach(key => {
        if (data[key] !== undefined) {
          formData[key] = data[key];
        }
      });
      
      // 如果有监测对象ID，加载对应的选项
      if (formData.monitorObjectId) {
        handleMonitorObjectChange(formData.monitorObjectId);
      }
    } else {
      ElMessage.error(res?.message || '获取设备详情失败');
    }
  } catch (error) {
    console.error('获取设备详情失败:', error);
    ElMessage.error('获取设备详情失败');
  }
};

// 监听props.data变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    if (props.mode === 'edit' || props.mode === 'view') {
      // 编辑模式或查看模式，需要获取详情
      if (newVal.id) {
        fetchDeviceDetail(newVal.id);
      }
    } else {
      // 新增模式，直接使用传入的默认数据
      Object.keys(formData).forEach(key => {
        if (newVal[key] !== undefined) {
          formData[key] = newVal[key];
        }
      });
    }
    
    // 如果有监测对象ID，加载对应的选项
    if (formData.monitorObjectId) {
      handleMonitorObjectChange(formData.monitorObjectId);
    }
  }
}, { immediate: true, deep: true });

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  // 重置表单
  if (formRef.value) {
    formRef.value.resetFields();
  }
  // 重置表单数据
  Object.keys(formData).forEach(key => {
    if (key === 'onlineStatus') {
      formData[key] = 0;
    } else if (key === 'isVss') {
      formData[key] = false;
    } else if (key === 'collectFrequency' || key === 'uploadFrequency') {
      formData[key] = 0;
    } else if (key === 'regionPath') {
      formData[key] = '@root000000000353d13ccb1eb45e786f6c709318b6d31@';
    } else {
      formData[key] = '';
    }
  });
  monitorTargetOptions.value = [];
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;
  
  try {
    await formRef.value.validate();
    
    // 准备提交数据
    const submitData = { ...formData };
    
    // 确保数值类型字段为数值
    if (submitData.collectFrequency) submitData.collectFrequency = Number(submitData.collectFrequency);
    if (submitData.uploadFrequency) submitData.uploadFrequency = Number(submitData.uploadFrequency);
    if (submitData.measureRangeLow) submitData.measureRangeLow = Number(submitData.measureRangeLow);
    if (submitData.measureRangeUp) submitData.measureRangeUp = Number(submitData.measureRangeUp);
    if (submitData.longitude) submitData.longitude = Number(submitData.longitude);
    if (submitData.latitude) submitData.latitude = Number(submitData.latitude);
    
    // 确保必填参数都存在
    if (!submitData.regionPath) {
      submitData.regionPath = '@root000000000353d13ccb1eb45e786f6c709318b6d31@';
    }
    
    // 处理监测对象目标名称
    const targetOption = monitorTargetOptions.value.find(option => option.value === formData.monitorTarget);
    if (targetOption) {
      submitData.monitorTargetName = targetOption.label;
    }
    
    // 提交数据
    let res;
    if (props.mode === 'add') {
      res = await saveSensorDevice(submitData);
    } else if (props.mode === 'edit') {
      res = await updateSensorDevice(submitData);
    }
    
    if (res && (res.code === 200 || res.code === 0)) {
      ElMessage.success(props.mode === 'add' ? '新增成功' : '更新成功');
      emit('success');
      handleClose();
    } else {
      ElMessage.error(res?.message || (props.mode === 'add' ? '新增失败' : '更新失败'));
    }
  } catch (error) {
    console.error('表单验证失败', error);
  }
};
</script>

<style scoped>
.gas-monitor-sensor-dialog {
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "Helvetica Neue", Arial, sans-serif;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-form-item__label::before) {
  content: '*';
  color: #ff4d4f;
  margin-right: 4px;
}

:deep(.el-form-item:not([required]) .el-form-item__label::before) {
  content: '';
  margin-right: 0;
}

:deep(.el-input__inner),
:deep(.el-select__input) {
  border-radius: 6px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 6px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  background-color: #0277FD;
  border-color: #0277FD;
}

:deep(.el-button--primary:hover) {
  background-color: #1890ff;
  border-color: #1890ff;
}

.w-full {
  width: 100%;
}

.w-20 {
  width: 80px;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.ml-2 {
  margin-left: 8px;
}

.mr-2 {
  margin-right: 8px;
}

.mx-2 {
  margin-left: 8px;
  margin-right: 8px;
}
</style>
