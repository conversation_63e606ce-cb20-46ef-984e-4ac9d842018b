<?xml version="1.0" encoding="UTF-8"?>
<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组</title>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="一张图总览（鼠标点击交互）" transform="translate(-1361, -144)">
            <g id="编组-8备份" transform="translate(1357, 140)">
                <g id="编组" transform="translate(4, 4)">
                    <rect id="矩形备份-28" x="0" y="0" width="24" height="24" rx="4"></rect>
                    <polygon id="路径" stroke="#FFFFFF" stroke-width="1.33333333" stroke-linejoin="round" points="5 8.30476667 11.6666667 10.6666667 18.3333333 8.30476667 11.6666667 6"></polygon>
                    <polyline id="路径" stroke="#FFFFFF" stroke-width="1.33333333" stroke-linecap="round" stroke-linejoin="round" points="5 12.3333333 11.6666667 14.6666667 18.3333333 12.3333333"></polyline>
                    <polyline id="路径" stroke="#FFFFFF" stroke-width="1.33333333" stroke-linecap="round" stroke-linejoin="round" points="5 16.3333333 11.6666667 18.6666667 18.3333333 16.3333333"></polyline>
                </g>
            </g>
        </g>
    </g>
</svg>