import * as echarts from 'echarts'

// 自定义echart自适应指令
const HANDLER = '_vue_echarts_resize_handler'
function unbind(el) {
  window.removeEventListener('resize', el[HANDLER])
  delete el[HANDLER]
}
function bind(el) {
  // binding
  unbind(el)
  el[HANDLER] = function () {
    const chart = echarts.getInstanceByDom(el)
    if (!chart) {
      return
    }
    chart.resize()
  }
  window.addEventListener('resize', el[HANDLER])
}

export default (app) => {
  app.directive('echartResize', {
    mounted(el) {
      bind(el)
    },
    unmounted(el) {
      unbind(el)
    }
  })
}
