<template>
  <div class="protection-search">
    <div class="search-form">
      <div class="form-item">
        <span class="label">建筑类型:</span>
        <el-select v-model="formData.buildingType" class="form-input" placeholder="请选择">
          <el-option label="全部" :value="null" />
          <el-option v-for="item in buildingTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
      <div class="form-item">
        <span class="label">所属单位:</span>
        <el-select v-model="formData.managementUnit" class="form-input" placeholder="请选择">
          <el-option label="全部" :value="null" />
          <el-option v-for="item in managementUnitOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
      <div class="form-item">
        <el-input v-model="formData.nameOrCode" class="form-input" placeholder="请输入防护目标名称/编码" />
      </div>
      <div class="form-item" style="margin-left: auto;">
        <el-button type="primary" class="search-btn" @click="handleSearch">查询</el-button>
        <el-button class="reset-btn" @click="handleReset">重置</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { ElSelect, ElOption, ElInput, ElButton } from 'element-plus';
import { getManagementUnits } from '@/api/gas';
import { BUILDING_TYPE_OPTIONS } from '@/constants/gas';


const emit = defineEmits(['search', 'reset']);

// 建筑类型选项
const buildingTypeOptions = ref(BUILDING_TYPE_OPTIONS);

// 管理单位选项
const managementUnitOptions = ref([]);

// 表单数据
const formData = ref({
  buildingType: null,
  managementUnit: null,
  nameOrCode: ''
});

// 获取管理单位列表
const fetchManagementUnits = async () => {
  try {
    const res = await getManagementUnits();
    if (res.data) {
      managementUnitOptions.value = res.data.map(item => ({
        label: item.enterpriseName,
        value: item.id
      }));
    }
  } catch (error) {
    console.error('获取管理单位失败:', error);
  }
};

// 处理查询
const handleSearch = () => {
  emit('search', formData.value);
};

// 处理重置
const handleReset = () => {
  formData.value = {
    buildingType: null,
    managementUnit: null,
    nameOrCode: ''
  };
  emit('reset');
};

onMounted(() => {
  fetchManagementUnits();
});
</script>

<style scoped>
.protection-search {
  width: 100%;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.form-item {
  display: flex;
  align-items: center;
  margin-right: 16px;
  margin-bottom: 16px;
}

.label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #282828;
  margin-right: 8px;
  white-space: nowrap;
}

.form-input {
  width: 180px;
  height: 32px;
}

:deep(.el-input__wrapper) {
  background: #FFFFFF;
  border-radius: 2px;
  border: 1px solid #CED3DA;
  height: 32px;
  box-shadow: none !important;
}

:deep(.el-select .el-input__wrapper) {
  background: #FFFFFF;
  border-radius: 2px;
  border: 1px solid #CED3DA;
  height: 32px;
  box-shadow: none !important;
}

:deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px #0277FD inset !important;
}

:deep(.el-select .el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px #0277FD inset !important;
}

.search-btn {
  width: 60px;
  height: 32px;
  background: #0277FD;
  border-radius: 2px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #FFFFFF;
  padding: 0;
  margin-right: 8px;
}

.reset-btn {
  width: 60px;
  height: 32px;
  background: #FFFFFF;
  border-radius: 2px;
  border: 1px solid #CED3DA;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #647688;
  padding: 0;
}
</style>