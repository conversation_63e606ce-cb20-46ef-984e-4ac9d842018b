/**
 * px转rem工具函数
 * 基于1920设计稿，1rem = 16px
 */

// 基准值：1rem = 16px (1920px设计稿)
const baseFontSize = 16;

/**
 * px转rem
 * @param {number} px - 像素值
 * @returns {string} - 转换后的rem值，带单位
 */
export function pxToRem(px) {
  if (typeof px !== 'number') {
    console.warn('pxToRem: 参数必须是数字');
    return '0rem';
  }
  return (px / baseFontSize) + 'rem';
}

/**
 * 生成rem值对象
 * @param {object} pxObject - 包含px值的对象
 * @returns {object} - 转换后的rem值对象
 * 
 * 示例:
 * const pxValues = { width: 100, height: 200, fontSize: 16 };
 * const remValues = objectPxToRem(pxValues);
 * // 结果: { width: '6.25rem', height: '12.5rem', fontSize: '1rem' }
 */
export function objectPxToRem(pxObject) {
  if (typeof pxObject !== 'object' || pxObject === null) {
    console.warn('objectPxToRem: 参数必须是对象');
    return {};
  }
  
  const remObject = {};
  for (const key in pxObject) {
    if (typeof pxObject[key] === 'number') {
      remObject[key] = pxToRem(pxObject[key]);
    } else {
      remObject[key] = pxObject[key];
    }
  }
  
  return remObject;
}

/**
 * 生成包含常用尺寸的rem对象
 * @returns {object} - 常用尺寸的rem值对象
 */
export function commonRemSizes() {
  const sizes = {};
  // 常用字体大小
  for (let i = 12; i <= 36; i += 2) {
    sizes[`fontSize${i}`] = pxToRem(i);
  }
  
  // 常用间距
  for (let i = 4; i <= 40; i += 4) {
    sizes[`spacing${i}`] = pxToRem(i);
  }
  
  // 常用宽度
  [50, 100, 150, 200, 250, 300, 400, 500, 600, 800, 1000, 1200, 1400, 1600, 1800, 1920].forEach(width => {
    sizes[`width${width}`] = pxToRem(width);
  });
  
  // 常用高度
  [40, 50, 60, 80, 100, 120, 150, 200, 300, 400, 500, 600, 800, 1000, 1080].forEach(height => {
    sizes[`height${height}`] = pxToRem(height);
  });
  
  return sizes;
}

export default {
  pxToRem,
  objectPxToRem,
  commonRemSizes
}; 