// import * as Cesium from "cesium";
import { createApp, h } from "vue";
// 导入element-plus
import installElementPlus from "@/plugins/element.js";
import { setupElIcons } from '@/plugins/icons.js';
//导入echarts并绑定自适应
import installEcharts from "@/plugins/echarts.js";

export default class gisDialog {
  constructor(opts) {
    const { viewer, offset, position, ...rest } = opts;
    this.viewer = viewer;
    this.position = position;
    this.offset = offset || [0, 0];
    const { vmInstance } = createDialog({
      viewer,
      position,
      ...rest,
      closeEvent: this.windowClose.bind(this),
    });
    if (this.vmInstance) {
      this.windowClose.bind(this);
    } else {
      this.vmInstance = vmInstance;
    }
    viewer.cesiumWidget.container.appendChild(vmInstance.$el);
    this.addPostRender();
  }

  addPostRender() {
    this.viewer.scene.postRender.addEventListener(this.postRender, this);
  }

  postRender = () => {
    if (!this.vmInstance.$el || !this.vmInstance.$el.style) return;
    const canvasHeight = this.viewer.scene.canvas.height;
    const canvasWidth = this.viewer.scene.canvas.width;
    const windowPosition = new Cesium.Cartesian2();
    Cesium.SceneTransforms.wgs84ToWindowCoordinates(
      this.viewer.scene,
      this.position,
      windowPosition
    );

    // debugger;

    // Calculate dynamic offsets based on window size
    const baseOffsetY = canvasHeight * 0.13; // 8% of canvas height
    const baseOffsetX = canvasWidth * 0; // 0% of canvas width

    // Apply the dynamic offsets
    const offsetY = this.offset[1] ? baseOffsetY + canvasHeight * this.offset[1] : baseOffsetY;
    const offsetX = this.offset[0] ? baseOffsetX + this.offset[0] : baseOffsetX;
    const elWidth = this.vmInstance.$el.offsetWidth; // 获取弹窗的宽度
    const elHeight = this.vmInstance.$el.offsetHeight; // 获取弹窗的高度
    // this.vmInstance.$el.style.bottom = canvasHeight - windowPosition.y + offsetY + "px";
    // this.vmInstance.$el.style.left = windowPosition.x - elWidth / 2 + offsetX + "px";
    this.vmInstance.$el.style.bottom = windowPosition.y + "px";
    this.vmInstance.$el.style.left = windowPosition.x - elWidth / 2 + "px";

    const camerPosition = this.viewer.camera.position;
    let height = this.viewer.scene.globe.ellipsoid.cartesianToCartographic(camerPosition).height;
    height += this.viewer.scene.globe.ellipsoid.maximumRadius;
    if (
      !(Cesium.Cartesian3.distance(camerPosition, this.position) > height) &&
      this.viewer.camera.positionCartographic.height < 50000000
    ) {
      this.vmInstance.$el.style.display = "block";
    } else {
      this.vmInstance.$el.style.display = "none";
    }
  };

  windowClose() {
    if (this.vmInstance) {
      this.vmInstance.$el.remove();
    }
    this.viewer.scene.postRender.removeEventListener(this.postRender, this);
  }
}

let parentNode = null;

const createDialog = (opts) => {
  if (parentNode) {
    document.body.removeChild(parentNode);
    parentNode = null;
  }
  const app = createApp({
    render() {
      return h(
        opts.gisPopup,
        {
          ...opts,
        },
        {
          data: opts.data,
        }
      );
    },
  });

  if (opts.useElement) {
    installElementPlus(app);
    setupElIcons(app);
  }
  if (opts.useEcharts) {
    installEcharts(app);
  }

  parentNode = document.createElement("div");
  const instance = app.mount(parentNode);
  document.body.appendChild(parentNode);

  return {
    vmInstance: instance,
  };
};
