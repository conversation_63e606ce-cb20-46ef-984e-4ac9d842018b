<template>
  <el-dialog v-model="dialogVisible" title="报警处置" width="80%" :close-on-click-modal="false" :destroy-on-close="true"
    class="heating-alarm-disposal-dialog">
    <!-- 新增处置按钮 -->
    <div class="disposal-header">
      <el-button type="primary" @click="handleAdd">新增处置</el-button>
    </div>

    <!-- 处置列表 -->
    <div class="disposal-list">
      <el-table :data="disposalList" style="width: 100%">
        <el-table-column prop="index" label="序号" width="60" align="center">
          <template #default="{ $index }">
            {{ $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="handleStatusName" label="处置状态" width="100" align="center" />
        <el-table-column prop="description" label="处置描述" min-width="120" align="center" />
        <el-table-column prop="handleUser" label="处置人员" width="150" align="center" />
        <el-table-column prop="unit" label="处置单位" width="120" align="center" />
        <el-table-column prop="createTime" label="处置时间" width="180" align="center">
          <template #default="scope">
            {{ formatTime(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="picUrls" label="处置照片" width="150" align="center">
          <template #default="scope">
            <span v-if="scope.row.picUrls" class="preview-btn" @click="handlePreviewPhotos(scope.row)">查看</span>
            <span v-else class="no-photo">无</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center">
          <template #default="scope">
            <el-button type="text" @click="handleView(scope.row)">查看</el-button>
            <el-button type="text" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button type="text" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 处置表单弹窗 -->
    <el-dialog v-model="formDialogVisible" :title="getDialogTitle()" width="600px" :close-on-click-modal="false"
      append-to-body>
      <el-form :model="disposalForm" :rules="rules" ref="formRef" label-width="100px"
        :disabled="currentMode === 'view'">
        <el-form-item label="处置状态:" prop="handleStatus" required>
          <el-radio-group v-model="disposalForm.handleStatus">
            <el-radio :value="2002101">处置中</el-radio>
            <el-radio :value="2002102">处置完成</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="处置描述:" prop="description">
          <el-input v-model="disposalForm.description" type="textarea" :rows="4" placeholder="输入处置描述" />
        </el-form-item>

        <el-form-item label="处置照片:" prop="picUrls">
          <template v-if="currentMode !== 'view'">
            <el-upload class="upload-demo" :http-request="handlePhotoUpload" :file-list="photoFileList"
              :on-error="handleUploadError" :on-remove="handlePhotoRemove" :limit="3" list-type="picture-card"
              :auto-upload="true">
              <el-icon>
                <Plus />
              </el-icon>
              <template #tip>
                <div class="el-upload__tip">最多上传3张照片，支持jpg、png格式，单张不超过5MB</div>
              </template>
            </el-upload>
          </template>
          <template v-else>
            <div class="photo-preview">
              <el-image v-for="(photo, index) in photoPreviewList" :key="index" :src="photo"
                :preview-src-list="photoPreviewList" style="width: 100px; height: 100px; margin-right: 10px"
                fit="cover" />
            </div>
          </template>
        </el-form-item>

        <el-form-item label="处置时间:" prop="createTime" required>
          <el-date-picker v-model="disposalForm.createTime" type="datetime" placeholder="选择处置时间"
            format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss" style="width: 100%" />
        </el-form-item>

        <el-form-item label="处置人员:" prop="handleUser" required>
          <el-input v-model="disposalForm.handleUser" placeholder="输入处置人员" />
        </el-form-item>

        <el-form-item label="处置人员单位:" prop="unit">
          <el-input v-model="disposalForm.unit" placeholder="输入处置人员单位" />
        </el-form-item>

        <el-form-item label="备注:" prop="remarks">
          <el-input v-model="disposalForm.remarks" type="textarea" :rows="3" placeholder="输入备注信息" />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleFormCancel">取消</el-button>
          <el-button v-if="currentMode !== 'view'" type="primary" @click="handleFormConfirm" :loading="formLoading">
            {{ currentMode === 'edit' ? '更新' : '确定' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
      </div>
    </template>
    <!-- 照片预览弹窗 -->
    <el-dialog v-model="photoPreviewVisible" title="照片预览" width="80%">
      <div class="photo-preview-container">
        <el-image v-for="(photo, index) in previewPhotos" :key="index" :src="photo" fit="contain" class="preview-image"
          :preview-src-list="previewPhotos" :initial-index="index" />
      </div>
    </el-dialog>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, onMounted } from 'vue';
import { ElDialog, ElButton, ElTable, ElTableColumn, ElForm, ElFormItem, ElRadioGroup, ElRadio, ElInput, ElDatePicker, ElUpload, ElIcon, ElImage, ElMessage, ElMessageBox } from 'element-plus';
import { Picture, Plus } from '@element-plus/icons-vue';
import { getHeatingAlarmHandleList, handleHeatingAlarm, deleteHeatingAlarmHandle } from '@/api/heating';
import { uploadFile } from '@/api/upload';

// 接收父组件传递的参数
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  alarmData: {
    type: Object,
    default: () => ({})
  }
});

// 向父组件发送事件
const emit = defineEmits(['update:visible', 'success']);

// 弹窗显示状态
const dialogVisible = ref(false);
const formDialogVisible = ref(false);
const formLoading = ref(false);
const formRef = ref(null);
const currentMode = ref('add'); // add, edit, view
// 照片预览相关
const photoPreviewVisible = ref(false);
const previewPhotos = ref([]);

// 监听visible属性变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal;
  if (newVal && props.alarmData.id) {
    fetchDisposalList();
  }
});

// 监听弹窗显示状态变化
watch(() => dialogVisible.value, (newVal) => {
  emit('update:visible', newVal);
});

// 处置列表数据
const disposalList = ref([]);

// 处置表单数据
const disposalForm = reactive({
  id: '',
  handleStatus: 2002101,
  description: '',
  picUrls: '',
  createTime: '',
  handleUser: '',
  unit: '',
  remarks: ''
});

// 照片文件列表
const photoFileList = ref([]);
const photoPreviewList = ref([]);

// 表单验证规则
const rules = {
  handleStatus: [
    { required: true, message: '请选择处置状态', trigger: 'change' }
  ],
  createTime: [
    { required: true, message: '请选择处置时间', trigger: 'change' }
  ],
  handleUser: [
    { required: true, message: '请输入处置人员', trigger: 'blur' }
  ]
};

// 获取弹窗标题
const getDialogTitle = () => {
  const titleMap = {
    add: '新增处置',
    edit: '编辑处置',
    view: '查看处置'
  };
  return titleMap[currentMode.value] || '处置记录';
};

// 格式化时间
const formatTime = (timeObj) => {
  if (!timeObj) return '';
  if (typeof timeObj === 'object' && timeObj.time) {
    return new Date(timeObj.time).toLocaleString('zh-CN');
  }
  if (typeof timeObj === 'string') {
    return timeObj;
  }
  return '';
};
// 预览照片
const handlePreviewPhotos = (row) => {
  if (row.picUrls) {
    previewPhotos.value = row.picUrls.split(',').filter(url => url.trim());
    photoPreviewVisible.value = true;
  }
};
// 获取处置列表
const fetchDisposalList = async () => {
  try {
    const res = await getHeatingAlarmHandleList(props.alarmData.id);
    if (res.code === 200 && res.data && res.data.usmMonitorAlarmStatusDtos) {
      disposalList.value = res.data.usmMonitorAlarmStatusDtos;
    }
  } catch (error) {
    console.error('获取处置列表失败:', error);
    // 使用模拟数据
    disposalList.value = [
      {
        id: '1',
        handleStatus: 2002101,
        handleStatusName: '处置中',
        description: '及时对现场门',
        handleUser: '张三',
        unit: '华源燃气',
        createTime: '2023-5-24 16:00',
        picUrls: 'pic1,pic2,pic3',
        remarks: ''
      }
    ];
  }
};

// 重置表单
const resetForm = () => {
  disposalForm.id = '';
  disposalForm.handleStatus = 2002101;
  disposalForm.description = '';
  disposalForm.picUrls = '';
  disposalForm.createTime = '';
  disposalForm.handleUser = '';
  disposalForm.unit = '';
  disposalForm.remarks = '';
  photoFileList.value = [];
  photoPreviewList.value = [];
  if (formRef.value) {
    formRef.value.clearValidate();
  }
};

// 处理新增
const handleAdd = () => {
  currentMode.value = 'add';
  resetForm();
  // 设置默认处置时间为当前时间
  const now = new Date();
  disposalForm.createTime = now.toISOString().slice(0, 19).replace('T', ' ');
  formDialogVisible.value = true;
};

// 处理查看
const handleView = (row) => {
  currentMode.value = 'view';
  fillFormData(row);
  formDialogVisible.value = true;
};

// 处理编辑
const handleEdit = (row) => {
  currentMode.value = 'edit';
  fillFormData(row);
  formDialogVisible.value = true;
};

// 填充表单数据
const fillFormData = (row) => {
  Object.keys(disposalForm).forEach(key => {
    if (row[key] !== undefined) {
      disposalForm[key] = row[key];
    }
  });

  // 处理时间字段映射
  if (row.createTime && !disposalForm.createTime) {
    disposalForm.createTime = row.createTime;
  }

  // 处理照片显示
  if (row.picUrls) {
    const urls = row.picUrls.split(',').filter(Boolean);
    photoPreviewList.value = urls;

    // 编辑模式下需要设置文件列表用于上传组件显示
    if (currentMode.value === 'edit') {
      photoFileList.value = urls.map((url, index) => ({
        name: `照片${index + 1}`,
        url: url,
        status: 'success'
      }));
    } else {
      // 查看模式下清空文件列表
      photoFileList.value = [];
    }
  } else {
    photoFileList.value = [];
    photoPreviewList.value = [];
  }
};

// 照片上传处理
const handlePhotoUpload = async (options) => {
  try {
    const res = await uploadFile(options.file);
    if (res.status === 200) {
      // 添加到照片列表
      const currentUrls = disposalForm.picUrls ? disposalForm.picUrls.split(',').filter(Boolean) : [];
      currentUrls.push(res.data.url);
      disposalForm.picUrls = currentUrls.join(',');

      // 更新文件列表显示
      photoFileList.value.push({
        name: options.file.name,
        url: res.data.url,
        status: 'success'
      });

      ElMessage.success('上传成功');
    } else {
      ElMessage.error(res.message || '上传失败');
    }
  } catch (error) {
    console.error('上传照片失败:', error);
    ElMessage.error('上传失败');
  }
};

// 照片删除处理
const handlePhotoRemove = (file) => {
  const currentUrls = disposalForm.picUrls ? disposalForm.picUrls.split(',').filter(Boolean) : [];
  const index = currentUrls.indexOf(file.url);
  if (index > -1) {
    currentUrls.splice(index, 1);
    disposalForm.picUrls = currentUrls.join(',');
  }
};

// 上传错误处理
const handleUploadError = () => {
  ElMessage.error('上传失败');
};

// 处理删除
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm('确定要删除这条处置记录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });

    const res = await deleteHeatingAlarmHandle(row.id);
    if (res.code === 200) {
      ElMessage.success('删除成功');
      fetchDisposalList();
      emit('success');
    } else {
      ElMessage.error(res.message || '删除失败');
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除处置记录失败:', error);
      ElMessage.error('删除失败');
    }
  }
};

// 处理表单确认
const handleFormConfirm = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    formLoading.value = true;

    const params = {
      ...disposalForm,
      alarmId: props.alarmData.id
    };

    const res = await handleHeatingAlarm(params);
    if (res.code === 200) {
      ElMessage.success(currentMode.value === 'edit' ? '编辑成功' : '新增成功');
      fetchDisposalList();
      emit('success');
      handleFormCancel();
    } else {
      ElMessage.error(res.message || '操作失败');
    }
  } catch (error) {
    console.error('处置操作失败:', error);
    ElMessage.error('操作失败');
  } finally {
    formLoading.value = false;
  }
};

// 处理表单取消
const handleFormCancel = () => {
  formDialogVisible.value = false;
  resetForm();
};

// 处理取消
const handleCancel = () => {
  dialogVisible.value = false;
};

onMounted(() => {
  // 组件挂载时的初始化操作
});
</script>

<style scoped>
.heating-alarm-disposal-dialog {
  :deep(.el-dialog__body) {
    padding: 20px;
    max-height: calc(90vh - 120px);
    overflow-y: auto;
  }
}

.disposal-header {
  margin-bottom: 20px;
}

.disposal-list {
  margin-bottom: 20px;
}

.photo-preview {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.photo-icon {
  width: 16px;
  height: 16px;
  color: #409EFF;
}
.preview-btn {
  color: #0086FF;
  cursor: pointer;
  font-size: 14px;
}

:deep(.el-form-item__label) {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #606266;
}

:deep(.el-radio__label) {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #606266;
}

:deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px #CED3DA inset;
}

:deep(.el-textarea__inner) {
  box-shadow: 0 0 0 1px #CED3DA inset;
}

:deep(.el-button--primary) {
  background-color: #0086FF;
  border-color: #0086FF;
}

:deep(.el-button--primary:hover) {
  background-color: #40A9FF;
  border-color: #40A9FF;
}

:deep(.el-button--text) {
  color: #0086FF;
}

:deep(.el-button--text:hover) {
  color: #40A9FF;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-upload__tip) {
  font-size: 12px;
  color: #999;
  margin-top: 8px;
}

.upload-demo {
  :deep(.el-upload-list) {
    width: 100%;
  }
}

:deep(.el-upload--picture-card) {
  width: 100px;
  height: 100px;
}

:deep(.el-upload-list--picture-card .el-upload-list__item) {
  width: 100px;
  height: 100px;
}
</style>