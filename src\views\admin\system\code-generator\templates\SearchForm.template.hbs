<template>
  <div class="search-form-container">
    <el-form :model="form" inline @keyup.enter="handleSearch">
      {{#each searchFields}}
      <el-form-item label="{{ label }}">
        {{#if_eq type "input"}}
        <el-input 
          v-model="form.{{ prop }}" 
          placeholder="{{ placeholder }}"
          clearable
        />
        {{/if_eq}}
        
        {{#if_eq type "select"}}
        <el-select 
          v-model="form.{{ prop }}" 
          placeholder="{{ placeholder }}"
          clearable
        >
          {{#each options}}
          <el-option label="{{ label }}" value="{{ value }}" />
          {{/each}}
        </el-select>
        {{/if_eq}}
        
        {{#if_eq type "date"}}
        <el-date-picker
          v-model="form.{{ prop }}"
          type="date"
          placeholder="{{ placeholder }}"
          value-format="YYYY-MM-DD"
          clearable
        />
        {{/if_eq}}
        
        {{#if_eq type "daterange"}}
        <el-date-picker
          v-model="form.{{ prop }}"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="YYYY-MM-DD"
          clearable
        />
        {{/if_eq}}
        
        {{#if_eq type "number"}}
        <el-input-number 
          v-model="form.{{ prop }}" 
          :min="0"
          :precision="0"
          :step="1"
          :controls="false"
          placeholder="{{ placeholder }}"
        />
        {{/if_eq}}
      </el-form-item>
      {{/each}}
      
      <el-form-item>
        <el-button type="primary" @click="handleSearch">查询</el-button>
        <el-button @click="handleReset">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { ref, defineEmits } from 'vue'

const emit = defineEmits(['search', 'reset'])

// 表单数据
const form = ref({
  {{#each searchFields}}
  {{ prop }}: '',
  {{/each}}
})

// 处理查询
const handleSearch = () => {
  {{#if hasDateRangeField}}
  // 处理日期范围字段
  const params = { ...form.value }
  {{#each searchFields}}
  {{#if_eq type "daterange"}}
  if (params.{{ prop }} && params.{{ prop }}.length === 2) {
    params.{{ prop }}Start = params.{{ prop }}[0]
    params.{{ prop }}End = params.{{ prop }}[1]
    delete params.{{ prop }}
  }
  {{/if_eq}}
  {{/each}}
  emit('search', params)
  {{else}}
  emit('search', form.value)
  {{/if}}
}

// 处理重置
const handleReset = () => {
  // 重置表单
  form.value = {
    {{#each searchFields}}
    {{ prop }}: '',
    {{/each}}
  }
  emit('reset')
}
</script>

<style scoped>
.search-form-container {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}
</style>
