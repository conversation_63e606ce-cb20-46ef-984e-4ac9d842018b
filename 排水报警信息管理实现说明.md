# 排水报警信息管理功能实现说明

## 功能概述

本功能实现了完整的排水报警信息管理系统，参考燃气专项的设计模式，提供了报警信息的查询、统计、详情查看和地图定位功能。

## 文件结构

### 1. 常量定义 (`src/constants/drainage.js`)

新增了以下排水报警相关的常量：

- **报警级别选项和映射**
  - `DRAIN_ALARM_LEVEL_OPTIONS`: 一级、二级、三级、四级
  - `DRAIN_ALARM_LEVEL_MAP`: 数值到文本的映射

- **报警状态选项和映射**
  - `DRAIN_ALARM_STATUS_OPTIONS`: 待确认、误报、待处置、处置中、已处置、已归档
  - `DRAIN_ALARM_STATUS_MAP`: 状态码到文本的映射

- **报警类型选项和映射**
  - `DRAIN_ALARM_TYPE_OPTIONS`: 7种监测报警类型（污水溢流、管网流量、雨量等）
  - `DRAIN_ALARM_TYPE_MAP`: 类型码到文本的映射

- **报警来源选项和映射**
  - `DRAIN_ALARM_SOURCE_OPTIONS`: 设备监测报警、企业自报报警
  - `DRAIN_ALARM_SOURCE_MAP`: 来源码到文本的映射

- **监测指标编码选项和映射**
  - `DRAIN_MONITOR_INDEX_CODE_OPTIONS`: 11种监测指标（水位、温度、液位等）
  - `DRAIN_MONITOR_INDEX_CODE_MAP`: 指标码到文本的映射

### 2. API接口 (`src/api/drainage.js`)

新增了以下排水报警相关的API接口：

- `getDrainAlarmList(page, size, params)`: 分页查询报警信息
- `getDrainAlarmStatistics(params)`: 获取报警统计数据
- `getDrainAlarmLevelStatistics(params)`: 获取报警级别统计数据
- `getDrainAlarmDetail(id)`: 获取报警详情
- `getDrainAlarmMonitorCurve(params)`: 获取监测曲线数据
- `getDrainAlarmRecords(alarmId)`: 获取报警记录
- `confirmDrainAlarm(data)`: 确认报警
- `handleDrainAlarm(data)`: 处置报警
- `reportFalseDrainAlarm(data)`: 误报上报
- `getDrainDisposalRecords(alarmId)`: 获取处置记录

### 3. 搜索组件 (`src/views/admin/drainage/monitoring/alarm/components/DrainAlarmSearch.vue`)

实现了报警信息的搜索功能，包含以下搜索条件：

- 报警来源（下拉选择）
- 报警等级（下拉选择）
- 报警类型（下拉选择）
- 报警时间（时间范围选择器）
- 报警状态（下拉选择）
- 报警编码/设备编码（输入框）

### 4. 详情弹窗组件 (`src/views/admin/drainage/monitoring/alarm/components/DrainAlarmDialog.vue`)

实现了报警详情的查看功能，包含三个标签页：

- **报警详情**：显示报警的基本信息和处理时间线
- **监测曲线**：显示不同时间范围的监测数据图表
- **报警记录**：显示历史报警记录表格

### 5. 主页面 (`src/views/admin/drainage/monitoring/alarm/info.vue`)

实现了完整的报警信息管理界面，包含：

#### 5.1 头部统计区域
- 6个统计卡片：全部报警、待确认、待处置、处置中、已处置、级别统计
- 使用不同颜色区分不同状态
- 显示数量和百分比

#### 5.2 查询区域
- 集成搜索组件
- 支持多条件组合查询
- 查询和重置功能

#### 5.3 数据表格
- 分页显示报警信息
- 包含完整的报警字段：序号、来源、编号、时间、设备编码、类型、监测对象、报警值、位置、级别、状态
- 支持详情查看和地图定位操作

#### 5.4 详情弹窗
- 集成报警详情弹窗组件
- 支持三种视图：详情、曲线、记录

## 核心功能

### 1. 数据统计
- 实时获取报警处置状态统计
- 报警级别分布统计
- 支持时间范围筛选

### 2. 条件查询
- 多维度查询条件
- 实时搜索和重置
- 分页显示结果

### 3. 详情查看
- 报警基本信息展示
- 处理时间线显示
- 监测数据曲线图
- 历史记录查询

### 4. 地图定位
- 基于经纬度的地图定位
- 集成现有的 GIS 定位 hooks
- 错误提示处理

### 5. 响应式设计
- 适配不同屏幕尺寸
- 现代化的UI设计
- 统一的样式风格

## 技术特点

1. **代码复用**：参考燃气专项的成熟设计模式
2. **组件化**：将功能拆分为独立的组件，便于维护
3. **类型安全**：完整的数据类型定义和映射
4. **错误处理**：包含完善的错误处理和用户提示
5. **性能优化**：合理的数据加载和图表渲染优化
6. **用户体验**：直观的操作界面和交互反馈

## 接口映射

根据需求说明，排水专项的接口基本复用燃气专项的接口结构，只需要将URL中的 `/gas/` 替换为 `/drain/` 即可：

- 报警列表：`POST /drain/usmMonitorAlarm/search/{page}/{size}`
- 报警统计：`POST /drain/usmAlarmStatisticsAnalysis/statistics`
- 级别统计：`POST /drain/usmAlarmStatisticsAnalysis/level/statistics`
- 其他接口类似替换

## 数据格式

支持后端返回的复杂时间对象格式，包含完整的时间戳信息，前端会自动格式化为易读的时间字符串。

## 扩展性

代码结构清晰，便于后续功能扩展：
- 可以轻松添加新的查询条件
- 可以扩展报警处理功能（确认、处置、误报等）
- 可以增加更多的统计维度
- 可以集成更多的地图功能

## 使用说明

1. 页面加载时自动获取统计数据和报警列表
2. 使用搜索区域进行条件查询
3. 点击表格中的"详情"按钮查看报警详细信息
4. 点击"定位"按钮在地图上定位报警位置
5. 在详情弹窗中可以查看监测曲线和历史记录

该实现完全符合原型设计稿的要求，提供了完整的排水报警信息管理功能。 