<template>
  <PanelBox title="报警处置监管">
    <template #extra>
      <div class="com-select">
        <CommonSelect v-model="selectedType" :options="typeOptions" @change="handleTypeChange" />
      </div>
    </template>
    <div class="panel-content">
      <div class="stats-row">
        <div class="stat-item">
          <span class="stat-dot"><span class="stat-dot-inner"></span></span>
          <span class="stat-label">报警总数</span>
          <span class="stat-value-blue">{{ statsData.totalAlarms }}</span>
          <span class="stat-unit">个</span>
        </div>
        <div class="stat-item">
          <span class="stat-dot"><span class="stat-dot-inner"></span></span>
          <span class="stat-label">今日报警</span>
          <span class="stat-value-sky">{{ statsData.todayAlarms }}</span>
          <span class="stat-unit">个</span>
        </div>
        <div class="stat-item">
          <span class="stat-dot"><span class="stat-dot-inner"></span></span>
          <span class="stat-label">本月报警</span>
          <span class="stat-value-gradient">{{ statsData.monthAlarms }}</span>
          <span class="stat-unit">个</span>
        </div>
      </div>

      <div class="alarm-stats-container">
        <div class="alarm-stats-box">
          <div class="alarm-item">
            <div class="alarm-label">一级报警</div>
            <div class="alarm-value-red">{{ alarmData.level1 }}</div>
          </div>
          <div class="alarm-item">
            <div class="alarm-label">二级报警</div>
            <div class="alarm-value-orange">{{ alarmData.level2 }}</div>
          </div>
          <div class="alarm-item">
            <div class="alarm-label">三级报警</div>
            <div class="alarm-value-yellow">{{ alarmData.level3 }}</div>
          </div>
          <div class="alarm-item">
            <div class="alarm-label">已处置</div>
            <div class="alarm-value-blue">{{ alarmData.handled }}</div>
          </div>
          <div class="alarm-item">
            <div class="alarm-label">处置完成率</div>
            <div class="alarm-value-green">{{ alarmData.handleRate }}</div>
          </div>
        </div>
      </div>
      
      <div class="chart-wrapper" ref="chartRef"></div>
    </div>
  </PanelBox>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import PanelBox from '@/components/screen/PanelBox.vue'
import CommonSelect from '@/components/screen/CommonSelect.vue'

// 类型选择
const selectedType = ref('all')
const typeOptions = [
  { label: '全部', value: 'all' },
  { label: '燃气', value: 'gas' },
  { label: '排水', value: 'water' },
  { label: '供热', value: 'heating' },
  { label: '桥梁', value: 'bridge' }
]

// 统计数据
const statsData = reactive({
  totalAlarms: 485,
  todayAlarms: 2,
  monthAlarms: 100
})

// 报警数据
const alarmData = reactive({
  level1: 20,
  level2: 24,
  level3: 441,
  handled: 392,
  handleRate: '80%'
})

// 图表数据
const chartData = {
  xAxis: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'],
  values: [90, 80, 85, 90, 85, 80, 75, 70, 65, 70, 75, 80]
}

// 图表实例
const chartRef = ref(null)
let chartInstance = null

// 处理类型变化
const handleTypeChange = (value) => {
  console.log('类型变化为:', value)
  // 预留后端数据请求
  // fetchData(value)
}

// 从后端获取数据 - 预留接口方法待后续接入
const fetchData = async (type) => {
  try {
    // 预留实际API调用
    console.log(`获取${type}报警数据`)
  } catch (error) {
    console.error('获取报警处置数据失败:', error)
  }
}

// 创建图表配置
const createChartOption = () => {
  return {
    backgroundColor: 'transparent',
    grid: {
      top: '15%',
      left: '5%',
      right: '4%',
      bottom: '45%',
      containLabel: true
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0, 19, 40, 0.8)',
      borderColor: 'rgba(0, 109, 232, 0.2)',
      textStyle: {
        color: '#fff',
        fontSize: 12,
        fontFamily: 'PingFangSC, PingFang SC'
      },
      formatter: function(params) {
        const param = params[0]
        return `${param.name}<br/>${param.marker}报警数量：${param.value}`
      }
    },
    xAxis: {
      type: 'category',
      data: chartData.xAxis,
      axisLine: {
        lineStyle: {
          color: '#5F5F60',
          width: 1
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        fontFamily: 'PingFangSC, PingFang SC',
        fontSize: 12,
        color: '#FFFFFF',
        margin: 12
      }
    },
    yAxis: {
      type: 'value',
      name: '单位（个）',
      nameTextStyle: {
        color: "rgba(255, 255, 255, 0.6)",
        fontSize: 12,
        padding: [0, 30, 0, 0]
      },
      splitLine: {
        show: false,
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)',
          type: 'dashed'
        }
      },
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        fontFamily: 'PingFangSC, PingFang SC',
        fontSize: 12,
        color: '#FFFFFF',
        margin: 12
      }
    },
    series: [
      {
        type: 'line',
        data: chartData.values,
        smooth: false,
        symbol: 'none',
        symbolSize: 6,
        itemStyle: {
          color: '#D9E7FF'
        },
        lineStyle: {
          color: '#D9E7FF',
          width: 2,
          shadowColor: 'rgba(0, 92, 228, 0.4)',
          shadowBlur: 6
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(217, 231, 255, 0.45)' },
              { offset: 1, color: 'rgba(217, 231, 255, 0.01)' }
            ]
          }
        }
      }
    ]
  }
}

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return
  
  chartInstance = echarts.init(chartRef.value)
  const option = createChartOption()
  chartInstance.setOption(option)
  
  // 响应式调整图表大小
  window.addEventListener('resize', () => {
    chartInstance && chartInstance.resize()
  })
}

// 初始化
onMounted(async () => {
  await nextTick()
  initChart()
  // 获取初始数据
  // fetchData(selectedType.value)
})
</script>

<style scoped>
.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.com-select {
  margin-right: 20px;
}

.stats-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
  padding: 0px 10px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.stat-dot {
  width: 9px;
  height: 9px;
  background: rgba(5, 90, 219, 0.4);
  border-radius: 50%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-dot-inner {
  width: 5px;
  height: 5px;
  background: #055ADB;
  border-radius: 50%;
  position: absolute;
}

.stat-label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
}

.stat-value-blue {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 24px;
  line-height: 26px;
  text-align: left;
  font-style: normal;
  background: linear-gradient(90deg, #FFFFFF 0%, #055ADB 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-value-sky {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 24px;
  color: #3CF3FF;
  line-height: 26px;
  text-align: left;
  font-style: normal;
}

.stat-value-gradient {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 24px;
  line-height: 26px;
  text-align: left;
  font-style: normal;
  background: linear-gradient(90deg, #FFFFFF 0%, #36F281 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-unit {
  color: rgba(255, 255, 255, 0.4);
  font-size: 12px;
}

.alarm-stats-container {
  display: flex;
  justify-content: center;
  margin: 5px 0;
}

.alarm-stats-box {
  width: 424px;
  height: 55px;
  border: 1px solid;
  border-image: radial-gradient(circle, rgba(0, 170, 255, 1), rgba(0, 141, 255, 0.5)) 1 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 15px;
}

.alarm-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 3px;
}

.alarm-label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #FFFFFF;
}

.alarm-value-red {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 18px;
  line-height: 20px;
  text-align: left;
  font-style: normal;
  background: linear-gradient(90deg, #FFFFFF 0%, #FF0000 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.alarm-value-orange {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 18px;
  line-height: 20px;
  text-align: left;
  font-style: normal;
  background: linear-gradient(90deg, #FFFFFF 0%, #FF7B00 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.alarm-value-yellow {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 18px;
  line-height: 20px;
  text-align: left;
  font-style: normal;
  background: linear-gradient(90deg, #FFFFFF 0%, #FFC400 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.alarm-value-blue {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 18px;
  line-height: 20px;
  text-align: left;
  font-style: normal;
  background: linear-gradient(90deg, #FFFFFF 0%, #3CF3FF 98%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.alarm-value-green {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 18px;
  line-height: 20px;
  text-align: left;
  font-style: normal;
  background: linear-gradient(90deg, #FFFFFF 0%, #36F281 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.chart-wrapper {
  flex: 1;
  width: 100%;
  min-height: 200px;
}


@media (min-height: 910px) and (max-height: 940px) {
  .panel-content {
    padding: 8px;
    gap: 5px;
  }
  
  .stats-row {
    margin-bottom: 0;
  }
  
  .stat-item {
    gap: 2px;
  }
  
  .stat-dot {
    width: 6px;
    height: 6px;
  }
  
  .stat-dot-inner {
    width: 3px;
    height: 3px;
  }
  
  .stat-label {
    font-size: 10px;
  }
  
  .stat-value-blue,
  .stat-value-sky,
  .stat-value-gradient {
    font-size: 16px;
    line-height: 18px;
  }
  
  .stat-unit {
    font-size: 9px;
  }
  
  .alarm-stats-box {
    height: 42px;
    padding: 0 8px;
  }
  
  .alarm-label {
    font-size: 11px;
  }
  
  .alarm-value-red,
  .alarm-value-orange,
  .alarm-value-yellow,
  .alarm-value-blue,
  .alarm-value-green {
    font-size: 14px;
    line-height: 16px;
  }
  
  .chart-wrapper {
    min-height: 220px;
  }
}
</style> 