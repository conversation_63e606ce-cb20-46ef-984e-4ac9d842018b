<template>
  <el-dialog 
    v-model="dialogVisible" 
    :title="dialogTitle" 
    width="1000px" 
    :close-on-click-modal="false"
    :before-close="handleClose" 
    class="threshold-dialog"
  >
    <el-form 
      ref="formRef" 
      :model="formData" 
      :rules="formRules" 
      label-width="120px" 
      :disabled="mode === 'view'"
    >
      <!-- 规则信息 -->
      <div class="section-title">规则信息</div>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="规则名称:" prop="ruleName">
            <el-input v-model="formData.ruleName" placeholder="请输入规则名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="规则描述:" prop="ruleDesc">
            <el-input v-model="formData.ruleDesc" placeholder="请输入规则描述" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="是否生效:" prop="isEnabled">
            <el-select v-model="formData.isEnabled" placeholder="是" class="w-full">
              <el-option 
                v-for="item in enabledStatusOptions" 
                :key="item.value" 
                :label="item.label" 
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 设置内容 -->
      <div class="section-title">设置内容</div>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="设备类型:" prop="deviceType">
            <el-select v-model="formData.deviceType" placeholder="请选择" class="w-full">
              <el-option 
                v-for="item in deviceTypeOptions" 
                :key="item.value" 
                :label="item.label" 
                :value="item.value" 
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="监测指标:" prop="monitorIndex">
            <el-select 
              v-model="formData.monitorIndex" 
              placeholder="请选择" 
              class="w-full" 
              :disabled="!formData.deviceType"
            >
              <el-option 
                v-for="item in monitorIndexOptions" 
                :key="item.value" 
                :label="item.label" 
                :value="item.value" 
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="设备选择:" prop="deviceIds">
            <el-select 
              v-model="formData.deviceIds" 
              multiple 
              placeholder="请选择设备" 
              class="w-full" 
              :disabled="!formData.deviceType"
            >
              <el-option 
                v-for="item in deviceOptions" 
                :key="item.id" 
                :label="item.deviceName" 
                :value="item.id" 
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 一级报警 -->
      <div class="alert-level">一级报警:</div>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="阈值下限:" prop="thresholdLevel1Min">
            <el-input 
              v-model.number="formData.thresholdLevel1Min" 
              type="number" 
              placeholder="请输入阈值下限" 
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="阈值上限:" prop="thresholdLevel1Max">
            <el-input 
              v-model.number="formData.thresholdLevel1Max" 
              type="number" 
              placeholder="请输入阈值上限" 
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="通知人:" class="notification-item">
            <div class="checkbox-group">
              <div class="notification-row">
                <el-checkbox v-model="formData.notifyRightsDept1" label="权属单位" />
                <el-select 
                  v-if="formData.notifyRightsDept1" 
                  v-model="formData.notifyRightsDeptIds1" 
                  multiple 
                  class="select-unit" 
                  placeholder="请选择权属单位"
                >
                  <el-option 
                    v-for="unit in rightsUnits" 
                    :key="unit.value" 
                    :label="unit.label" 
                    :value="unit.value" 
                  />
                </el-select>
              </div>
              <div class="notification-row">
                <el-checkbox v-model="formData.notifySuperviseDept1" label="监管部门" />
                <el-select 
                  v-if="formData.notifySuperviseDept1" 
                  v-model="formData.notifySuperviseDeptIds1" 
                  multiple 
                  class="select-unit" 
                  placeholder="请选择监管部门"
                >
                  <el-option 
                    v-for="dept in superviseDepartments" 
                    :key="dept.value" 
                    :label="dept.label" 
                    :value="dept.value" 
                  />
                </el-select>
              </div>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 二级报警 -->
      <div class="alert-level">二级报警:</div>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="阈值下限:" prop="thresholdLevel2Min">
            <el-input 
              v-model.number="formData.thresholdLevel2Min" 
              type="number" 
              placeholder="请输入阈值下限" 
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="阈值上限:" prop="thresholdLevel2Max">
            <el-input 
              v-model.number="formData.thresholdLevel2Max" 
              type="number" 
              placeholder="请输入阈值上限" 
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="通知人:" class="notification-item">
            <div class="checkbox-group">
              <div class="notification-row">
                <el-checkbox v-model="formData.notifyRightsDept2" label="权属单位" />
                <el-select 
                  v-if="formData.notifyRightsDept2" 
                  v-model="formData.notifyRightsDeptIds2" 
                  multiple 
                  class="select-unit" 
                  placeholder="请选择权属单位"
                >
                  <el-option 
                    v-for="unit in rightsUnits" 
                    :key="unit.value" 
                    :label="unit.label" 
                    :value="unit.value" 
                  />
                </el-select>
              </div>
              <div class="notification-row">
                <el-checkbox v-model="formData.notifySuperviseDept2" label="监管部门" />
                <el-select 
                  v-if="formData.notifySuperviseDept2" 
                  v-model="formData.notifySuperviseDeptIds2" 
                  multiple 
                  class="select-unit" 
                  placeholder="请选择监管部门"
                >
                  <el-option 
                    v-for="dept in superviseDepartments" 
                    :key="dept.value" 
                    :label="dept.label" 
                    :value="dept.value" 
                  />
                </el-select>
              </div>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 三级报警 -->
      <div class="alert-level">三级报警:</div>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="阈值下限:" prop="thresholdLevel3Min">
            <el-input 
              v-model.number="formData.thresholdLevel3Min" 
              type="number" 
              placeholder="请输入阈值下限" 
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="阈值上限:" prop="thresholdLevel3Max">
            <el-input 
              v-model.number="formData.thresholdLevel3Max" 
              type="number" 
              placeholder="请输入阈值上限" 
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="通知人:" class="notification-item">
            <div class="checkbox-group">
              <div class="notification-row">
                <el-checkbox v-model="formData.notifyRightsDept3" label="权属单位" />
                <el-select 
                  v-if="formData.notifyRightsDept3" 
                  v-model="formData.notifyRightsDeptIds3" 
                  multiple 
                  class="select-unit" 
                  placeholder="请选择权属单位"
                >
                  <el-option 
                    v-for="unit in rightsUnits" 
                    :key="unit.value" 
                    :label="unit.label" 
                    :value="unit.value" 
                  />
                </el-select>
              </div>
              <div class="notification-row">
                <el-checkbox v-model="formData.notifySuperviseDept3" label="监管部门" />
                <el-select 
                  v-if="formData.notifySuperviseDept3" 
                  v-model="formData.notifySuperviseDeptIds3" 
                  multiple 
                  class="select-unit" 
                  placeholder="请选择监管部门"
                >
                  <el-option 
                    v-for="dept in superviseDepartments" 
                    :key="dept.value" 
                    :label="dept.label" 
                    :value="dept.value" 
                  />
                </el-select>
              </div>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 通知方式 -->
      <div class="section-title">通知方式:</div>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="通知方式:">
            <el-checkbox v-model="formData.notifySystem" label="系统通知" />
            <el-checkbox v-model="formData.notifySms" label="短信通知" />
            <el-checkbox v-model="formData.notifyEmail" label="邮件通知" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="mode !== 'view'">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  getMaintenanceEnterpriseList,
  getPipelineInfoList,
  getMonitorIndicatorsList,
  saveAlarmThreshold, 
  updateAlarmThreshold, 
  getAlarmThresholdDetail
} from '@/api/bridge'
import { 
  ENABLED_STATUS_OPTIONS, 
  DEVICE_TYPE_OPTIONS 
} from '@/constants/bridge'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add', // 'add', 'edit', 'view'
    validator: (value) => ['add', 'edit', 'view'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:visible', 'success'])

// 表单引用
const formRef = ref(null)

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

// 对话框标题
const dialogTitle = computed(() => {
  const titles = {
    add: '新增',
    edit: '编辑',
    view: '详情'
  }
  return titles[props.mode] || '阈值配置'
})

// 表单数据
const formData = reactive({
  id: '',
  ruleName: '',
  ruleDesc: '',
  isEnabled: true,
  deviceType: '',
  monitorIndex: '',
  deviceIds: [],
  thresholdLevel1Min: '',
  thresholdLevel1Max: '',
  thresholdLevel2Min: '',
  thresholdLevel2Max: '',
  thresholdLevel3Min: '',
  thresholdLevel3Max: '',
  notifyRightsDept1: false,
  notifySuperviseDept1: false,
  notifyRightsDeptIds1: [],
  notifySuperviseDeptIds1: [],
  notifyRightsDept2: false,
  notifySuperviseDept2: false,
  notifyRightsDeptIds2: [],
  notifySuperviseDeptIds2: [],
  notifyRightsDept3: false,
  notifySuperviseDept3: false,
  notifyRightsDeptIds3: [],
  notifySuperviseDeptIds3: [],
  notifySystem: true,
  notifySms: false,
  notifyEmail: false
})

// 表单验证规则
const formRules = {
  ruleName: [{ required: true, message: '请输入规则名称', trigger: 'blur' }],
  deviceType: [{ required: true, message: '请选择设备类型', trigger: 'change' }],
  monitorIndex: [{ required: true, message: '请选择监测指标', trigger: 'change' }],
  thresholdLevel1Min: [{ required: true, message: '请输入一级阈值下限', trigger: 'blur' }],
  thresholdLevel1Max: [{ required: true, message: '请输入一级阈值上限', trigger: 'blur' }],
  thresholdLevel2Min: [{ required: true, message: '请输入二级阈值下限', trigger: 'blur' }],
  thresholdLevel2Max: [{ required: true, message: '请输入二级阈值上限', trigger: 'blur' }],
  thresholdLevel3Min: [{ required: true, message: '请输入三级阈值下限', trigger: 'blur' }],
  thresholdLevel3Max: [{ required: true, message: '请输入三级阈值上限', trigger: 'blur' }]
}

// 生效状态选项
const enabledStatusOptions = ENABLED_STATUS_OPTIONS

// 设备类型选项
const deviceTypeOptions = DEVICE_TYPE_OPTIONS

// 监测指标选项
const monitorIndexOptions = ref([])

// 监管部门选项
const superviseDepartments = ref([])

// 权属单位选项
const rightsUnits = ref([])

// 设备选项
const deviceOptions = ref([])

// 获取权属单位列表
const fetchRightsUnits = async () => {
  try {
    const res = await getMaintenanceEnterpriseList({ enterpriseType: '5001002' })
    if (res && res.code === 200) {
      rightsUnits.value = (res.data || []).map(item => ({
        label: item.enterpriseName,
        value: item.id
      }))
    }
  } catch (error) {
    console.error('获取权属单位列表失败', error)
  }
}

// 获取监管部门列表
const fetchSuperviseDepartments = async () => {
  try {
    const res = await getMaintenanceEnterpriseList({ enterpriseType: '5001001' })
    if (res && res.code === 200) {
      superviseDepartments.value = (res.data || []).map(item => ({
        label: item.enterpriseName,
        value: item.id
      }))
    }
  } catch (error) {
    console.error('获取监管部门列表失败', error)
  }
}

// 获取监测指标列表
const fetchMonitorIndexes = async (deviceType) => {
  if (!deviceType) {
    monitorIndexOptions.value = []
    return
  }
  
  try {
    const res = await getMonitorIndicatorsList({})
    if (res && res.code === 200) {
      monitorIndexOptions.value = (res.data || []).map(item => ({
        label: item.monitorIndexName,
        value: item.monitorIndex
      }))
    }
  } catch (error) {
    console.error('获取监测指标列表失败', error)
  }
}

// 获取设备列表
const fetchDeviceList = async (deviceType) => {
  const targetDeviceType = deviceType || formData.deviceType
  if (!targetDeviceType) {
    deviceOptions.value = []
    return
  }
  
  try {
    const res = await getPipelineInfoList({ deviceType: targetDeviceType })
    if (res && res.code === 200) {
      deviceOptions.value = res.data || []
    }
  } catch (error) {
    console.error('获取设备列表失败', error)
  }
}

// 获取阈值详情
const fetchThresholdDetail = async (id) => {
  try {
    const res = await getAlarmThresholdDetail(id)
    if (res && res.code === 200) {
      const data = res.data
      
      // 如果有设备类型，先加载对应的监测指标和设备列表
      if (data.deviceType) {
        await fetchMonitorIndexes(data.deviceType)
        await fetchDeviceList(data.deviceType)
      }
      
      // 等待下一个事件循环，确保选项已经更新
      await new Promise(resolve => setTimeout(resolve, 100))
      
      // 复制数据到表单
      Object.keys(formData).forEach(key => {
        if (data[key] !== undefined) {
          formData[key] = data[key]
        }
      })
      
      // 处理字符串形式的数组数据
      if (typeof data.deviceIds === 'string' && data.deviceIds) {
        formData.deviceIds = data.deviceIds.split(',').map(id => id.trim()).filter(id => id)
      } else if (Array.isArray(data.deviceIds)) {
        formData.deviceIds = data.deviceIds
      }
      
      // 处理通知部门ID字段
      ['notifyRightsDeptIds1', 'notifyRightsDeptIds2', 'notifyRightsDeptIds3',
       'notifySuperviseDeptIds1', 'notifySuperviseDeptIds2', 'notifySuperviseDeptIds3'].forEach(field => {
        if (typeof data[field] === 'string' && data[field]) {
          formData[field] = data[field].split(',').map(id => id.trim()).filter(id => id)
        } else if (Array.isArray(data[field])) {
          formData[field] = data[field]
        }
      })
    }
  } catch (error) {
    console.error('获取阈值详情失败', error)
  }
}

// 监听props.data变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    if (props.mode === 'edit' || props.mode === 'view') {
      if (newVal.id) {
        fetchThresholdDetail(newVal.id)
      }
    } else if (props.mode === 'add') {
      // 新增模式：重置表单并复制传入的数据
      Object.keys(formData).forEach(key => {
        if (newVal[key] !== undefined) {
          formData[key] = newVal[key]
        }
      })
    }
  }
}, { immediate: true, deep: true })

// 监听设备类型变化
watch(() => formData.deviceType, (newVal, oldVal) => {
  if (newVal && newVal !== oldVal) {
    fetchMonitorIndexes(newVal)
    fetchDeviceList(newVal)
    
    // 只在新增模式或者确实是用户手动改变设备类型时才清空
    if (props.mode === 'add' || (oldVal && oldVal !== newVal)) {
      formData.monitorIndex = ''
      formData.deviceIds = []
    }
  } else if (!newVal) {
    monitorIndexOptions.value = []
    deviceOptions.value = []
  }
})

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  // 重置表单
  if (formRef.value) {
    formRef.value.resetFields()
  }
  // 重置表单数据
  Object.keys(formData).forEach(key => {
    if (key === 'isEnabled') {
      formData[key] = true
    } else if (key === 'notifySystem') {
      formData[key] = true
    } else if (key === 'notifySms' || key === 'notifyEmail' || 
               key === 'notifyRightsDept1' || key === 'notifySuperviseDept1' || 
               key === 'notifyRightsDept2' || key === 'notifySuperviseDept2' || 
               key === 'notifyRightsDept3' || key === 'notifySuperviseDept3') {
      formData[key] = false
    } else if (Array.isArray(formData[key])) {
      formData[key] = []
    } else {
      formData[key] = ''
    }
  })
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    // 准备提交数据
    const submitData = { ...formData }

    // 确保deviceIds是数组格式
    if (typeof submitData.deviceIds === 'string' && submitData.deviceIds) {
      submitData.deviceIds = submitData.deviceIds.split(',').map(id => id.trim()).filter(id => id)
    } else if (!Array.isArray(submitData.deviceIds)) {
      submitData.deviceIds = []
    }
    
    // 处理通知部门IDs字段 - 转换为字符串格式
    ['notifyRightsDeptIds1', 'notifyRightsDeptIds2', 'notifyRightsDeptIds3',
     'notifySuperviseDeptIds1', 'notifySuperviseDeptIds2', 'notifySuperviseDeptIds3'].forEach(field => {
      if (Array.isArray(submitData[field])) {
        submitData[field] = submitData[field].join(',')
      }
    })

    // 提交数据
    let res
    if (props.mode === 'add') {
      res = await saveAlarmThreshold(submitData)
    } else if (props.mode === 'edit') {
      res = await updateAlarmThreshold(submitData)
    }

    if (res && res.code === 200) {
      ElMessage.success(props.mode === 'add' ? '新增成功' : '更新成功')
      emit('success')
      handleClose()
    } else {
      ElMessage.error(res?.message || (props.mode === 'add' ? '新增失败' : '更新失败'))
    }
  } catch (error) {
    console.error('表单验证失败', error)
  }
}

// 组件挂载时获取数据
onMounted(() => {
  fetchRightsUnits()
  fetchSuperviseDepartments()
  fetchMonitorIndexes()
})
</script>

<style scoped>
.threshold-dialog {
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "Helvetica Neue", Arial, sans-serif;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-input__inner),
:deep(.el-select__input) {
  border-radius: 6px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 6px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  background-color: #0277FD;
  border-color: #0277FD;
}

:deep(.el-button--primary:hover) {
  background-color: #1890ff;
  border-color: #1890ff;
}

.w-full {
  width: 100%;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin: 20px 0 10px;
  padding-left: 10px;
  border-left: 3px solid #0277FD;
}

.alert-level {
  font-size: 14px;
  font-weight: 500;
  color: #666;
  margin: 10px 0;
  padding: 5px 10px;
  background-color: #f8f8f8;
  border-radius: 4px;
}

.notification-item {
  margin-bottom: 20px;
}

.checkbox-group {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
}

.notification-row {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  width: 100%;
}

.notification-row .el-checkbox {
  margin-right: 10px;
  min-width: 80px;
}

.select-unit {
  width: 300px;
  margin-left: 10px;
}

:deep(.el-checkbox) {
  margin-right: 15px;
}
</style> 