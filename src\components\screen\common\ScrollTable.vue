<template>
  <div class="scroll-table-container" :style="{ height: tableHeight }">
    <table class="scroll-table">
      <thead v-if="!hiddenHeader">
        <tr>
          <th v-for="(column, index) in columns" :key="index" :style="{ 
            width: column.width || 'auto',
            fontSize: column.fontSize || '14px'
          }">
            {{ column.title }}
          </th>
        </tr>
      </thead>
      <tbody ref="tableBody">
        <tr v-for="(item, index) in visibleData" :key="index" :class="index % 2 === 0 ? 'light-row' : 'dark-row'" @click="handleRowClick(item)">
          <td v-for="(column, colIndex) in columns" :key="colIndex" :class="column.className || ''">
            <slot :name="column.dataIndex" :row="item" :index="index">
              {{ item[column.dataIndex] }}
            </slot>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount, watch } from 'vue'

const props = defineProps({
  // 表格列配置
  columns: {
    type: Array,
    required: true
  },
  // 表格数据
  data: {
    type: Array,
    required: true
  },
  // 是否启用自动滚动
  autoScroll: {
    type: Boolean,
    default: true
  },
  // 滚动速度(ms)
  scrollSpeed: {
    type: Number,
    default: 3000
  },
  // 表格容器高度
  tableHeight: {
    type: String,
    default: '200px'
  },
  // 一次显示的行数
  visibleRows: {
    type: Number,
    default: 4
  },
  // 是否隐藏表头
  hiddenHeader: {
    type: Boolean,
    default: false
  }
})

// 定义事件
const emit = defineEmits(['row-click'])

// 行点击事件处理
const handleRowClick = (row) => {
  emit('row-click', row)
}

// 显示的数据
const visibleData = ref([])
const tableBody = ref(null)
let scrollInterval = null
let currentIndex = 0

// 如果数据不足以滚动，直接显示所有数据
const shouldScroll = computed(() => {
  return props.autoScroll && props.data.length > props.visibleRows
})

// 初始化显示的数据
const initVisibleData = () => {
  currentIndex = 0
  if (shouldScroll.value) {
    visibleData.value = props.data.slice(0, props.visibleRows)
  } else {
    visibleData.value = [...props.data]
  }
}

// 滚动到下一页数据
const scrollToNext = () => {
  if (!shouldScroll.value) return
  
  currentIndex = (currentIndex + 1) % props.data.length
  const endIndex = currentIndex + props.visibleRows
  
  // 如果到达末尾需要循环取值
  if (endIndex <= props.data.length) {
    visibleData.value = props.data.slice(currentIndex, endIndex)
  } else {
    // 需要从头开始取值
    const remainCount = endIndex - props.data.length
    const firstPart = props.data.slice(currentIndex)
    const secondPart = props.data.slice(0, remainCount)
    visibleData.value = [...firstPart, ...secondPart]
  }
}

// 启动自动滚动
const startAutoScroll = () => {
  if (!shouldScroll.value) return
  
  stopAutoScroll()
  scrollInterval = setInterval(() => {
    scrollToNext()
  }, props.scrollSpeed)
}

// 停止自动滚动
const stopAutoScroll = () => {
  if (scrollInterval) {
    clearInterval(scrollInterval)
    scrollInterval = null
  }
}

// 监听数据变化，重新初始化
watch(() => props.data, () => {
  initVisibleData()
  if (props.autoScroll) {
    startAutoScroll()
  }
}, { deep: true })

onMounted(() => {
  initVisibleData()
  if (props.autoScroll) {
    startAutoScroll()
  }
})

onBeforeUnmount(() => {
  stopAutoScroll()
})
</script>

<style scoped>
.scroll-table-container {
  width: 100%;
  overflow: hidden;
}

.scroll-table {
  width: 100%;
  border-collapse: collapse;
  table-layout: fixed;
}

.scroll-table thead tr {
  background: rgba(0,163,255,0.15);
  border-radius: 4px 4px 0px 0px;
}

.scroll-table th {
  padding: 8px 4px;
  text-align: center;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  color: #FFFFFF;
  white-space: nowrap;
  line-height: 1.2;
  /* 移除省略号效果，确保文字完整显示 */
}

.scroll-table td {
  padding: 0 15px;
  text-align: left;
  font-family: PingFang-SC, PingFang-SC;
  font-weight: 400;
  font-size: 14px;
  color: #FFFFFF;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.light-row {
  background: rgba(0, 80, 179, 0.3);
}

.dark-row {
  background: rgba(0, 80, 179, 0.2);
}
</style> 