<template>
  <div class="search-container">
    <el-form :model="searchForm" :inline="true" class="search-form">
      <el-form-item label="是否生效:">
        <el-select 
          v-model="searchForm.isEnabled" 
          placeholder="是" 
          clearable 
          class="search-select"
        >
          <el-option 
            v-for="item in enabledStatusOptions" 
            :key="item.value" 
            :label="item.label" 
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      
      <el-form-item>
        <el-input 
          v-model="searchForm.ruleName" 
          placeholder="输入规则名称" 
          clearable 
          class="keyword-input"
        />
      </el-form-item>
      
      <el-form-item>
        <el-button type="primary" @click="handleSearch" class="search-btn">查询</el-button>
        <el-button @click="handleReset" class="reset-btn">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { reactive } from 'vue'
import { ENABLED_STATUS_OPTIONS } from '@/constants/bridge'

const emit = defineEmits(['search', 'reset'])

// 搜索表单数据
const searchForm = reactive({
  ruleName: '',
  isEnabled: null,
})

// 生效状态选项
const enabledStatusOptions = ENABLED_STATUS_OPTIONS

// 处理搜索
const handleSearch = () => {
  const params = {}
  
  if (searchForm.ruleName) {
    params.ruleName = searchForm.ruleName
  }
  
  if (searchForm.isEnabled !== null && searchForm.isEnabled !== '') {
    params.isEnabled = searchForm.isEnabled
  }
  
  if (searchForm.keyword) {
    params.keyword = searchForm.keyword
  }
  
  emit('search', params)
}

// 处理重置
const handleReset = () => {
  searchForm.ruleName = ''
  searchForm.isEnabled = null
  searchForm.keyword = ''
  emit('reset')
}
</script>

<style scoped>
.search-container {
  background: white;
  padding: 16px;
  margin-bottom: 16px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-form {
  margin: 0;
}

:deep(.el-form-item) {
  margin-bottom: 16px;
  margin-right: 16px;
}

:deep(.el-form-item__label) {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #282828;
  line-height: 22px;
  padding-right: 8px;
}

.search-input,
.search-select {
  width: 200px;
}

.keyword-input {
  width: 240px;
}

:deep(.el-input__inner) {
  height: 32px;
  line-height: 32px;
  /* border: 1px solid #DCDFE6; */
  border-radius: 2px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #606266;
}

:deep(.el-select .el-input__inner) {
  height: 32px;
  line-height: 32px;
}

.search-btn {
  width: 80px;
  height: 32px;
  background: #0277FD;
  border-radius: 2px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 14px;
  color: #FFFFFF;
  border: none;
  margin-right: 8px;
}

.reset-btn {
  width: 80px;
  height: 32px;
  background: #FFFFFF;
  border: 1px solid #DCDFE6;
  border-radius: 2px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 14px;
  color: #606266;
}

.search-btn:hover {
  background: #1890ff;
}

.reset-btn:hover {
  color: #0277FD;
  border-color: #0277FD;
}
</style> 