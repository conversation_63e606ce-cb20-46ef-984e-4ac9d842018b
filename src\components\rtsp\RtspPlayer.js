/**
 * RTSP播放器组件
 * 基于JSMpeg实现的RTSP视频播放器
 */
class RtspPlayer {
  /**
   * 创建RTSP播放器实例
   * @param {Object} options 配置选项
   * @param {HTMLCanvasElement} options.canvas 播放器画布元素
   * @param {string} options.videoId 视频流唯一标识
   * @param {RtspWebSocketManager} options.wsManager WebSocket连接管理器
   * @param {boolean} options.autoplay 是否自动播放
   * @param {boolean} options.audio 是否播放音频
   * @param {Function} options.onStatusChange 状态变化回调
   */
  constructor(options) {
    this.canvas = options.canvas;
    this.videoId = options.videoId;
    this.wsManager = options.wsManager;
    this.autoplay = options.autoplay !== false;
    this.audio = options.audio !== false;
    this.onStatusChange = options.onStatusChange || (() => {});
    
    this.player = null;
    this.isPlaying = false;
    this.status = 'stopped';
    this.bufferData = []; // 用于缓存二进制数据
    this.isPlayerCreated = false;
    this.errorCount = 0; // 错误计数器
    this.maxErrorCount = 10; // 最大允许错误数
    this.chunkSize = 16 * 1024; // 将大数据包分片，降低到16KB
    this.lastFrameTime = 0; // 上次处理帧的时间
    this.minFrameInterval = 30; // 最小帧间隔(ms)
    this.receivedBytes = 0; // 已接收的数据字节数
    
    // 绑定方法
    this._handleVideoStream = this._handleVideoStream.bind(this);
    this._resetErrorCount = this._resetErrorCount.bind(this);
    
    // 定期重置错误计数
    this.errorResetInterval = setInterval(this._resetErrorCount, 5000);
  }
  
  /**
   * 重置错误计数
   * @private
   */
  _resetErrorCount() {
    if (this.errorCount > 0) {
      this.errorCount = Math.max(0, this.errorCount - 2); // 逐渐减少错误计数
    }
  }
  
  /**
   * 处理接收到的视频流数据
   * @param {ArrayBuffer} data 视频流数据 - 直接的ArrayBuffer数据
   * @private
   */
  _handleVideoStream(data) {
    // 确保JSMpeg已加载
    if (!window.JSMpeg) {
      console.error('JSMpeg未加载');
      this._updateStatus('JSMpeg未加载', true);
      return;
    }

    try {
      // 将ArrayBuffer转换为Uint8Array
      const uint8Array = new Uint8Array(data);
      
      // 累计接收字节数
      this.receivedBytes += uint8Array.length;
      
      // 每隔一段时间记录一次数据量
      if (this.receivedBytes % (1024 * 1024) < uint8Array.length) { // 每接收约1MB记录一次
        console.log(`已接收 ${Math.floor(this.receivedBytes / 1024 / 1024)}MB 数据`);
      }
      
      // 限制帧率，避免处理过多数据
      const now = Date.now();
      if (now - this.lastFrameTime < this.minFrameInterval) {
        return; // 丢弃帧，控制帧率
      }
      this.lastFrameTime = now;
      
      // 检查数据是否有效
      if (!this._isValidMpegData(uint8Array)) {
        return; // 丢弃无效数据
      }
      
      // 如果数据包太大，分片处理
      if (uint8Array.length > this.chunkSize) {
        this._processLargeDataChunks(uint8Array);
        return;
      }
      
      // 缓存一定量的数据，然后创建播放器
      if (!this.isPlayerCreated) {
        this.bufferData.push(uint8Array);
        
        // 当累积了足够的数据时创建播放器
        if (this.bufferData.length >= 5) {
          this._createPlayer();
        }
        return;
      }

      // 如果播放器已创建但还没初始化完成，继续缓存数据
      if (!this.player) {
        this.bufferData.push(uint8Array);
        return;
      }

      // 如果播放器已创建并且可用，则直接处理数据
      this._writeDataToPlayer(uint8Array);
    } catch (error) {
      console.error('处理视频数据失败', error);
      this.errorCount++;
      
      // 如果错误过多，重新创建播放器
      if (this.errorCount > this.maxErrorCount) {
        console.warn('错误过多，尝试重新创建播放器');
        this.stop();
        
        // 短暂延迟后重新开始播放
        setTimeout(() => {
          this.play().catch(err => {
            console.error('重新播放失败', err);
          });
        }, 1000);
      }
    }
  }
  
  /**
   * 检查数据是否为有效的MPEG数据
   * @param {Uint8Array} data 数据
   * @returns {boolean} 是否有效
   * @private
   */
  _isValidMpegData(data) {
    // 数据太小，不可能是有效的MPEG帧
    if (data.length < 4) {
      return false;
    }
    
    // 检查是否包含MPEG头部标记(0x00 0x00 0x01)
    // 这是简化的检查，实际MPEG格式更复杂
    for (let i = 0; i < data.length - 3; i++) {
      if (data[i] === 0 && data[i+1] === 0 && data[i+2] === 1) {
        return true;
      }
    }
    
    return false;
  }
  
  /**
   * 处理大型数据包，将其分片
   * @param {Uint8Array} data 大型数据包
   * @private
   */
  _processLargeDataChunks(data) {
    const totalLength = data.length;
    const chunks = Math.ceil(totalLength / this.chunkSize);
    
    console.log(`将 ${totalLength} 字节数据分为 ${chunks} 个片段处理`);
    
    for (let i = 0; i < chunks; i++) {
      const start = i * this.chunkSize;
      const end = Math.min(start + this.chunkSize, totalLength);
      const chunk = data.subarray(start, end);
      
      // 处理或缓存每个分片
      if (!this.isPlayerCreated) {
        this.bufferData.push(chunk);
        
        if (this.bufferData.length >= 5 && i === chunks - 1) {
          this._createPlayer();
        }
      } else if (!this.player) {
        this.bufferData.push(chunk);
      } else {
        try {
          // 每个分片之间添加小延迟，避免一次性处理过多数据
          setTimeout(() => {
            this._writeDataToPlayer(chunk);
          }, i * 10); // 增加到10ms间隔
        } catch (error) {
          console.error(`处理数据分片 ${i + 1}/${chunks} 失败`, error);
        }
      }
    }
  }
  
  /**
   * 安全地将数据写入播放器
   * @param {Uint8Array} data 要写入的数据
   * @private
   */
  _writeDataToPlayer(data) {
    try {
      // 检查数据的有效性
      if (!data || data.length === 0) {
        return;
      }
      
      // 确保player和demuxer存在
      if (!this.player || !this.player.demuxer) {
        console.warn('播放器或解码器未初始化');
        return;
      }
      
      // 直接使用player上的解码器，不使用player.write或demuxer.write
      if (this.player.video && this.player.video.decode) {
        // 提取视频数据，跳过可能有问题的demuxer
        const videoData = this._extractVideoData(data);
        if (videoData && videoData.length > 0) {
          try {
            this.player.video.decode(videoData);
          } catch (e) {
            console.warn('视频解码失败', e);
          }
        }
      } else if (this.player.demuxer && typeof this.player.demuxer.write === 'function') {
        // 如果没有直接访问解码器的方法，退回到demuxer
        try {
          this.player.demuxer.write(data);
        } catch (e) {
          console.warn('写入demuxer失败', e);
          this.errorCount++;
        }
      } else {
        console.warn('无法找到有效的解码方法');
        return;
      }
      
      if (!this.isPlaying) {
        this.isPlaying = true;
        this.status = 'playing';
        this._updateStatus('视频播放中');
      }
    } catch (error) {
      console.error('写入视频数据失败', error);
      this.errorCount++;
    }
  }
  
  /**
   * 尝试从数据中提取视频帧
   * @param {Uint8Array} data 原始数据
   * @returns {Uint8Array|null} 提取的视频数据
   * @private
   */
  _extractVideoData(data) {
    // 简单实现：查找视频起始码并提取
    for (let i = 0; i < data.length - 4; i++) {
      // 检查MPEG视频帧起始码(0x00 0x00 0x01 0xB3)
      if (data[i] === 0 && data[i+1] === 0 && data[i+2] === 1 && data[i+3] === 0xB3) {
        return data.subarray(i);
      }
    }
    return null;
  }
  
  /**
   * 创建JSMpeg播放器实例
   * @private
   */
  _createPlayer() {
    try {
      // 标记播放器创建过程已开始
      this.isPlayerCreated = true;
      
      // 如果有已存在的播放器实例，先销毁
      if (this.player) {
        this.player.destroy();
        this.player = null;
      }
      
      console.log('创建JSMpeg播放器');
      
      // 直接使用JSMpeg提供的内置解码器和播放器，不使用自定义source
      const bufferSize = 512 * 1024; // 减小缓冲区，避免内存问题
      
      // 创建一个模拟的WebSocket URL，这是JSMpeg要求的格式
      const dummyUrl = 'ws://localhost:8080/' + this.videoId;
      
      // 创建播放器实例，关键：完全禁用WebAssembly
      this.player = new JSMpeg.Player(dummyUrl, {
        canvas: this.canvas,
        autoplay: true,
        audio: false, // 禁用音频，专注于视频
        pauseWhenHidden: false,
        disableGl: true, // 禁用WebGL
        disableWebAssembly: true, // 强制禁用WebAssembly
        videoBufferSize: bufferSize,
        audioBufferSize: 0, // 不需要音频缓冲
        maxAudioLag: 0,
        streaming: true,
        progressive: false,
        throttled: true, // 启用节流
        chunkSize: this.chunkSize,
        decodeFirstFrame: true,
        // 拦截ws连接
        onConnectionCreate: (url, options) => {
          // 返回一个模拟的WebSocket对象
          return {
            onopen: null,
            onclose: null,
            onmessage: null,
            send: () => {},
            close: () => {},
            readyState: 1, // 模拟已连接状态
            constructor: {
              prototype: {
                OPEN: 1
              }
            }
          };
        }
      });
      
      // 强制将player.source设置为可用状态
      if (this.player && this.player.source) {
        this.player.source.established = true;
        this.player.source.completed = false;
        this.player.source.progress = 0;
      }
      
      // 增加额外的错误处理
      if (this.player && this.player.video) {
        const originalDecode = this.player.video.decode;
        this.player.video.decode = (data) => {
          try {
            return originalDecode.call(this.player.video, data);
          } catch (error) {
            console.warn('视频解码错误，忽略此帧', error);
            return false;
          }
        };
      }
      
      // 处理缓存的数据
      if (this.bufferData.length > 0 && this.player) {
        console.log(`处理 ${this.bufferData.length} 帧缓存数据`);
        
        // 延迟处理缓存数据，确保播放器完全初始化
        setTimeout(() => {
          // 只处理最新的几帧数据
          const recentFrames = this.bufferData.slice(-5);
          console.log(`只处理最新的 ${recentFrames.length} 帧数据`);
          
          // 逐个处理缓存的数据，使用延迟避免一次性处理过多
          recentFrames.forEach((data, index) => {
            setTimeout(() => {
              this._writeDataToPlayer(data);
              
              // 最后一个数据处理完毕后清空缓存
              if (index === recentFrames.length - 1) {
                this.bufferData = [];
              }
            }, index * 50); // 每个数据包之间有50ms的间隔
          });
        }, 300); // 给播放器300ms的初始化时间
      }
      
      this._updateStatus('播放器已创建');
      this.errorCount = 0; // 重置错误计数
    } catch (error) {
      console.error('创建播放器失败', error);
      this._updateStatus('创建播放器失败: ' + error.message, true);
      
      // 重置状态，允许下次重试
      this.isPlayerCreated = false;
    }
  }
  
  /**
   * 更新播放器状态
   * @param {string} message 状态消息
   * @param {boolean} isError 是否为错误状态
   * @private
   */
  _updateStatus(message, isError = false) {
    this.onStatusChange({
      videoId: this.videoId,
      message,
      isError,
      status: this.status
    });
  }
  
  /**
   * 开始播放视频
   * @returns {Promise} 播放结果的Promise
   */
  play() {
    return new Promise(async (resolve, reject) => {
      if (this.isPlaying) {
        resolve();
        return;
      }
      
      try {
        this.status = 'connecting';
        this._updateStatus('正在连接WebSocket...');
        
        // 重置缓存和状态
        this.bufferData = [];
        this.isPlayerCreated = false;
        this.errorCount = 0;
        this.receivedBytes = 0;
        this.lastFrameTime = 0;
        
        // 确保WebSocket连接
        await this.wsManager.connect();
        
        // 注册监听器
        this.wsManager.registerVideoListener(this.videoId, this._handleVideoStream);
        
        // 发送播放请求
        this._updateStatus('正在请求视频流...');
        await this.wsManager.sendPlayRequest(this.videoId);
        
        resolve();
      } catch (error) {
        this.status = 'error';
        this._updateStatus('播放失败: ' + error.message, true);
        reject(error);
      }
    });
  }
  
  /**
   * 停止播放视频
   */
  stop() {
    if (this.player) {
      this.player.destroy();
      this.player = null;
    }
    
    // 移除监听器
    this.wsManager.unregisterVideoListener(this.videoId);
    
    this.isPlaying = false;
    this.isPlayerCreated = false;
    this.bufferData = [];
    this.errorCount = 0;
    this.receivedBytes = 0;
    this.status = 'stopped';
    this._updateStatus('已停止播放');
  }
  
  /**
   * 销毁播放器实例
   */
  destroy() {
    this.stop();
    
    // 清除错误重置定时器
    if (this.errorResetInterval) {
      clearInterval(this.errorResetInterval);
      this.errorResetInterval = null;
    }
  }
}

export default RtspPlayer; 