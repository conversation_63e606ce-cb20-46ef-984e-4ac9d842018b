import request from '@/utils/request';

/**
 * 上传文件
 * @param {File} file - 要上传的文件
 * @param {string} source - 文件来源，默认为'inner'
 * @returns {Promise<object>} 上传结果
 */
export function uploadFile(file, source = 'inner') {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('source', source);

  return request({
    url: '/file/upload',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    transformRequest: [(data) => data],
    timeout: 60000
  });
}