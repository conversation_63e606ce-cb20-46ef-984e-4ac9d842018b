<template>
  <teleport to="body">
    <transition name="fade">
      <div v-if="modelValue" class="modal-overlay" @click.self="closeModal">
        <div class="modal-container">
          <div class="modal-header">
            <div class="modal-title">管线风险详情</div>
            <div class="close-icon" @click="closeModal">×</div>
          </div>
          <div class="modal-content">
            <div class="info-section">
              <div class="info-row">
                <span class="info-label">风险编号:</span>
                <span class="info-value">{{ pipelineData.riskCode || '-' }}</span>
              </div>
              <div class="info-row">
                <span class="info-label">管线编码:</span>
                <span class="info-value">{{ pipelineData.code || '-' }}</span>
              </div>
              <div class="info-row">
                <span class="info-label">管网类型:</span>
                <span class="info-value">{{ pipelineData.type || '-' }}</span>
              </div>
              <div class="info-row">
                <span class="info-label">管材:</span>
                <span class="info-value">{{ pipelineData.material || '-' }}</span>
              </div>
              <div class="info-row">
                <span class="info-label">管径:</span>
                <span class="info-value">{{ pipelineData.diameter || '-' }}</span>
              </div>
              <div class="info-row">
                <span class="info-label">管长:</span>
                <span class="info-value">{{ pipelineData.length || '-' }}</span>
              </div>
              <div class="info-row">
                <span class="info-label">所在道路:</span>
                <span class="info-value">{{ pipelineData.location || '-' }}</span>
              </div>
              <div class="info-row">
                <span class="info-label">建设时间:</span>
                <span class="info-value">{{ pipelineData.buildTime || '-' }}</span>
              </div>
              <div class="info-row">
                <span class="info-label">风险等级:</span>
                <span class="info-value" :style="{ color: getRiskColor(pipelineData.riskLevel) }">
                  {{ pipelineData.riskLevel || '-' }}
                </span>
              </div>
              <div class="info-row">
                <span class="info-label">风险值:</span>
                <span class="info-value">{{ pipelineData.riskValue || '-' }}</span>
              </div>
              <div class="info-row">
                <span class="info-label">管控状态:</span>
                <span class="info-value">{{ pipelineData.controlStatus || '-' }}</span>
              </div>
              <div class="info-row">
                <span class="info-label">评估日期:</span>
                <span class="info-value">{{ pipelineData.buildTime || '-' }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </transition>
  </teleport>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue'

const props = defineProps({
  modelValue: Boolean,
  pipelineData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue'])

// 关闭弹窗
const closeModal = () => {
  emit('update:modelValue', false)
}

// 根据风险等级获取对应的颜色
const getRiskColor = (riskLevel) => {
  switch (riskLevel) {
    case '重大风险':
      return '#FF2330'
    case '较大风险':
      return '#FF9000'
    case '一般风险':
      return '#FFD11B'
    case '低风险':
      return '#00B0FF'
    default:
      return '#FFFFFF'
  }
}
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.modal-container {
  width: 550px;
  background: linear-gradient(180deg, rgba(0, 22, 72, 0.9) 0%, rgba(0, 35, 91, 0.9) 100%);
  border: 1px solid rgba(59, 141, 242, 0.5);
  box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.05);
  border-radius: 4px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid rgba(59, 141, 242, 0.3);
}

.modal-title {
  font-family: PingFangSC, 'PingFang SC';
  font-weight: 500;
  font-size: 16px;
  color: #FFFFFF;
}

.close-icon {
  font-size: 20px;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
}

.close-icon:hover {
  color: #FFFFFF;
}

.modal-content {
  padding: 20px;
  max-height: 500px;
  overflow-y: auto;
}

.info-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.info-row {
  display: flex;
  align-items: center;
}

.info-label {
  font-family: PingFangSC, 'PingFang SC';
  font-weight: 500;
  font-size: 14px;
  color: #FFFFFF;
  width: 90px;
  flex-shrink: 0;
}

.info-value {
  font-family: PingFangSC, 'PingFang SC';
  font-weight: 400;
  font-size: 14px;
  color: #FFFFFF;
  flex: 1;
}

/* 淡入淡出动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>