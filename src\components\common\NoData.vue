<template>
  <div class="no-data-container">
    <div class="no-data-content">
      <el-icon class="no-data-icon"><FolderOpened /></el-icon>
      <span class="no-data-text">暂无数据</span>
    </div>
  </div>
</template>

<script setup>
import { ElIcon } from 'element-plus'
import { FolderOpened } from '@element-plus/icons-vue'
</script>

<style scoped>
.no-data-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%;
}

.no-data-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: rgba(255, 255, 255, 0.6);
}

.no-data-icon {
  font-size: 48px;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.no-data-text {
  font-size: 14px;
}
</style>