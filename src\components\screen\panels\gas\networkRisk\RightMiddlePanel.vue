<template>
  <PanelBox title="风险清单" class="gas-network-risk-right-middle">
    <template #extra>
      <div class="tab-buttons">
        <div 
          class="tab-btn" 
          :class="{ active: activeTab === 'pipeline' }" 
          @click="changeTab('pipeline')"
        >
          管网
        </div>
        <div class="divider"></div>
        <div 
          class="tab-btn" 
          :class="{ active: activeTab === 'station' }" 
          @click="changeTab('station')"
        >
          场站
        </div>
      </div>
    </template>
    <div class="panel-content">
      <!-- 管网风险列表 -->
      <div v-if="activeTab === 'pipeline'" class="risk-table">
        <div class="table-header">
          <div class="header-item code">管线编码</div>
          <div class="header-item age">管龄</div>
          <div class="header-item material">材质</div>
          <div class="header-item diameter">管径</div>
          <div class="header-item risk-level">风险等级</div>
        </div>
        <div class="table-body">
          <div 
            v-for="(item, index) in pipelineList" 
            :key="index" 
            class="table-row"
            @click="openPipelineDetail(item)"
          >
            <div class="row-item code" :style="{ color: getRiskColor(item.riskLevelName) }">{{ item.pipelineCode }}</div>
            <div class="row-item age" :style="{ color: getRiskColor(item.riskLevelName) }">{{ item.pipelineAge }}年</div>
            <div class="row-item material" :style="{ color: getRiskColor(item.riskLevelName) }">{{ item.pipelineMaterialName }}</div>
            <div class="row-item diameter" :style="{ color: getRiskColor(item.riskLevelName) }">{{ item.pipelineDiameter }}</div>
            <div class="row-item risk-level" :style="{ color: getRiskColor(item.riskLevelName) }">{{ item.riskLevelName || '-' }}</div>
          </div>
        </div>
        <div class="table-footer" v-if="pipelineTotal > 12">
          <div class="more-btn" @click="handleMoreClick('pipeline')">更多</div>
        </div>
      </div>
      
      <!-- 场站风险列表 -->
      <div v-else class="risk-table">
        <div class="table-header">
          <div class="header-item station-name">场站名称</div>
          <div class="header-item station-type">场站类型</div>
          <div class="header-item risk-level">风险等级</div>
        </div>
        <div class="table-body">
          <div 
            v-for="(item, index) in stationList" 
            :key="index" 
            class="table-row"
            @click="openStationDetail(item)"
          >
            <div class="row-item station-name" :style="{ color: getRiskColor(item.riskLevelName) }">{{ item.stationName }}</div>
            <div class="row-item station-type" :style="{ color: getRiskColor(item.riskLevelName) }">{{ item.stationTypeName }}</div>
            <div class="row-item risk-level" :style="{ color: getRiskColor(item.riskLevelName) }">{{ item.riskLevelName || '-' }}</div>
          </div>
        </div>
        <div class="table-footer" v-if="stationTotal > 12">
          <div class="more-btn" @click="handleMoreClick('station')">更多</div>
        </div>
      </div>
    </div>
    
    <!-- 管线详情弹窗 -->
    <PipelineDetailModal 
      :model-value="pipelineDetailVisible" 
      :pipeline-data="selectedPipeline"
      @update:model-value="pipelineDetailVisible = $event"
    />
    
    <!-- 场站详情弹窗 --> 
    <StationDetailModal 
      :model-value="stationDetailVisible" 
      :station-data="selectedStation"
      @update:model-value="stationDetailVisible = $event"
    />
    
    <!-- 管线风险查询弹窗 -->
    <PipelineRiskModal
      :model-value="pipelineRiskVisible"
      @update:model-value="pipelineRiskVisible = $event"
      @position="handlePosition"
    />
    
    <!-- 场站风险查询弹窗 -->
    <StationRiskModal
      :model-value="stationRiskVisible"
      @update:model-value="stationRiskVisible = $event"
      @position="handlePosition"
    />
  </PanelBox>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import PanelBox from '@/components/screen/PanelBox.vue'
import PipelineDetailModal from './PipelineDetailModal.vue'
import StationDetailModal from './StationDetailModal.vue'
import PipelineRiskModal from './PipelineRiskModal.vue'
import StationRiskModal from './StationRiskModal.vue'
import { getPipelineRiskPipelineList, getPipelineRiskStationList } from '@/api/gas'

// 定义当前激活的标签页
const activeTab = ref('pipeline') // 默认选择管网

// 弹窗控制
const pipelineDetailVisible = ref(false)
const stationDetailVisible = ref(false)
const selectedPipeline = ref({})
const selectedStation = ref({})

// 风险列表弹窗控制
const pipelineRiskVisible = ref(false)
const stationRiskVisible = ref(false)

// 管网风险数据
const pipelineList = ref([])
const pipelineTotal = ref(0)

// 场站风险数据
const stationList = ref([])
const stationTotal = ref(0)

// 获取管网风险数据
const fetchPipelineData = async () => {
  try {
    const res = await getPipelineRiskPipelineList({
      pageSize: 12,
      pageNum: 1
    })
    if (res.code === 200) {
      pipelineList.value = res.data.records
      pipelineTotal.value = res.data.total
    }
  } catch (error) {
    console.error('获取管网风险数据失败:', error)
  }
}

// 获取场站风险数据
const fetchStationData = async () => {
  try {
    const res = await getPipelineRiskStationList({
      pageSize: 12,
      pageNum: 1
    })
    if (res.code === 200) {
      stationList.value = res.data.records
      stationTotal.value = res.data.total
    }
  } catch (error) {
    console.error('获取场站风险数据失败:', error)
  }
}

// 切换标签页
const changeTab = (tab) => {
  activeTab.value = tab
  if (tab === 'pipeline') {
    fetchPipelineData()
  } else {
    fetchStationData()
  }
}

// 打开管线详情弹窗
const openPipelineDetail = (item) => {
  selectedPipeline.value = item
  pipelineDetailVisible.value = true
}

// 打开场站详情弹窗
const openStationDetail = (item) => {
  selectedStation.value = item
  stationDetailVisible.value = true
}

// 点击更多按钮处理
const handleMoreClick = (type) => {
  if (type === 'pipeline') {
    pipelineRiskVisible.value = true
  } else if (type === 'station') {
    stationRiskVisible.value = true
  }
}

// 处理风险列表中的定位功能
const handlePosition = (item) => {
  console.log('定位到项目:', item)
}

// 根据风险等级获取对应的颜色
const getRiskColor = (riskLevel) => {
  switch (riskLevel) {
    case '重大风险':
      return '#FF2330'
    case '较大风险':
      return '#FF9000'
    case '一般风险':
      return '#FFD11B'
    case '低风险':
      return '#00B0FF'
    default:
      return '#FFFFFF'
  }
}

onMounted(() => {
  fetchPipelineData()
})
</script>

<style scoped>
.gas-network-risk-right-middle {
  height: 660px;
}

.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
}

/* 切换标签相关样式 */
.tab-buttons {
  display: flex;
  align-items: center;
  margin-right: 30px;
}

.divider {
  width: 1px;
  height: 16px;
  background-color: rgba(255, 255, 255, 0.8);
  margin: 0 10px;
}

.tab-btn {
  cursor: pointer;
  font-family: PingFangSC, 'PingFang SC';
  font-weight: 500;
  font-size: 16px;
  color: rgba(255, 255, 255, 0.3);
  line-height: 22px;
}

.tab-btn.active {
  color: rgba(255, 255, 255, 0.8);
}

/* 表格相关样式 */
.risk-table {
  flex: 1;
  display: flex;
  flex-direction: column;
  width: 100%;
  overflow: hidden;
}

.table-header {
  display: flex;
  width: 100%;
  height: 40px;
  background: linear-gradient(90deg, rgba(0,80,167,0.2) 0%, rgba(0,80,167,0.4) 100%);
  font-family: PingFangSC, 'PingFang SC';
  font-weight: 500;
  font-size: 14px;
  color: #FFFFFF;
  line-height: 40px;
}

.table-body {
  flex: 1;
  overflow-y: auto;
}

.table-row {
  display: flex;
  width: 100%;
  height: 40px;
  line-height: 40px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  cursor: pointer;
}

.table-row:hover {
  background-color: rgba(59, 141, 242, 0.1);
}

.header-item,
.row-item {
  text-align: center;
  font-family: PingFangSC, 'PingFang SC';
  font-weight: 500;
  font-size: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 管网表格列宽设置 */
.code {
  flex: 1.2;
}

.age {
  flex: 0.8;
}

.material {
  flex: 0.8;
}

.diameter {
  flex: 0.8;
}

.risk-level {
  flex: 1;
}

/* 场站表格列宽设置 */
.station-name {
  flex: 2;
}

.station-type {
  flex: 1;
}

/* 更多按钮样式 */
.table-footer {
  height: 30px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding-right: 10px;
}

.more-btn {
  cursor: pointer;
  font-family: PingFangSC, 'PingFang SC';
  font-size: 12px;
  color: #3AA1FF;
  text-decoration: underline;
  padding: 5px;
}

.more-btn:hover {
  color: #66B8FF;
}

/* 媒体查询适配不同分辨率 */
@media screen and (min-width: 1920px) and (max-width: 2560px) {
  .gas-network-risk-right-middle {
    height: 660px;
  }
}

@media screen and (max-width: 1919px) {
  .gas-network-risk-right-middle {
    height: 580px;
  }
}

@media screen and (min-width: 2561px) {
  .gas-network-risk-right-middle {
    height: 760px;
  }
}

/* 940px-1055px高度的屏幕特别优化 */
@media screen and (min-height: 910px) and (max-height: 1055px) {
  .panel-content {
    padding: 10px;
  }
  
  .tab-btn {
    font-size: 14px;
    line-height: 20px;
  }
  
  .divider {
    height: 14px;
  }
  
  .table-header {
    height: 36px;
    line-height: 36px;
    font-size: 13px;
  }
  
  .table-row {
    height: 36px;
    line-height: 36px;
  }
  
  .header-item,
  .row-item {
    font-size: 13px;
  }
  
  .table-footer {
    height: 24px;
  }
  
  .more-btn {
    font-size: 11px;
    padding: 4px;
  }
}

/* 910px高度的屏幕特别优化 */
@media screen and (min-height: 900px) and (max-height: 939px) {
  .gas-network-risk-right-middle {
    height: 500px;
  }
  
  .panel-content {
    padding: 8px;
  }
  
  .tab-btn {
    font-size: 13px;
    line-height: 18px;
  }
  
  .divider {
    height: 13px;
  }
  
  .table-header {
    height: 32px;
    line-height: 32px;
    font-size: 12px;
  }
  
  .table-row {
    height: 32px;
    line-height: 32px;
  }
  
  .header-item,
  .row-item {
    font-size: 12px;
  }
  
  .table-footer {
    height: 22px;
  }
  
  .more-btn {
    font-size: 10px;
    padding: 3px;
  }
}
</style> 