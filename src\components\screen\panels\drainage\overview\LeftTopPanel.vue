<template>
  <PanelBox title="基础设施" class="left-top-panel">
    <template #extra>
      <div class="tab-buttons">
        <div class="tab-btn" :class="{ active: activeTab === 'rainwater' }" @click="changeTab('rainwater')">
          雨水管网
        </div>
        <div class="divider"></div>
        <div class="tab-btn" :class="{ active: activeTab === 'sewage' }" @click="changeTab('sewage')">
          污水管网
        </div>
      </div>
    </template>
    <div class="panel-content">
      <!-- 顶部统计数据 -->
      <div class="pipeline-stats" v-if="activeTab === 'rainwater'">
        <div class="stats-item">
          <div class="stats-indicator">
            <div class="indicator-outer rain-outer"></div>
            <div class="indicator-inner rain-inner"></div>
          </div>
          <div class="stats-info">
            <div class="stats-label">雨水管线</div>
            <div class="stats-value-wrapper">
              <div class="stats-value rain-gradient">{{ pipelineLength }}</div>
              <div class="stats-unit">公里</div>
            </div>
          </div>
        </div>
        <div class="stats-item">
          <div class="stats-indicator">
            <div class="indicator-outer pump-outer"></div>
            <div class="indicator-inner pump-inner"></div>
          </div>
          <div class="stats-info">
            <div class="stats-label">泵站</div>
            <div class="stats-value-wrapper">
              <div class="stats-value pump-gradient">{{ pumpStationCount }}</div>
              <div class="stats-unit">个</div>
            </div>
          </div>
        </div>
        <div class="stats-item">
          <div class="stats-indicator">
            <div class="indicator-outer outlet-outer"></div>
            <div class="indicator-inner outlet-inner"></div>
          </div>
          <div class="stats-info">
            <div class="stats-label">排口</div>
            <div class="stats-value-wrapper">
              <div class="stats-value outlet-gradient">{{ outletCount }}</div>
              <div class="stats-unit">个</div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 污水管网顶部数据 -->
      <div class="pipeline-stats" v-if="activeTab === 'sewage'">
        <div class="stats-item">
          <div class="stats-indicator">
            <div class="indicator-outer sewage-outer"></div>
            <div class="indicator-inner sewage-inner"></div>
          </div>
          <div class="stats-info">
            <div class="stats-label">污水管网</div>
            <div class="stats-value-wrapper">
              <div class="stats-value sewage-gradient">{{ sewageData.pipelineLength }}</div>
              <div class="stats-unit">公里</div>
            </div>
          </div>
        </div>
        <div class="stats-item">
          <div class="stats-indicator">
            <div class="indicator-outer rainmix-outer"></div>
            <div class="indicator-inner rainmix-inner"></div>
          </div>
          <div class="stats-info">
            <div class="stats-label">雨污混接</div>
            <div class="stats-value-wrapper">
              <div class="stats-value rainmix-gradient">{{ sewageData.mixedCount || 342 }}</div>
              <div class="stats-unit">公里</div>
            </div>
          </div>
        </div>
        <div class="stats-item">
          <div class="stats-indicator">
            <div class="indicator-outer outlet-outer"></div>
            <div class="indicator-inner outlet-inner"></div>
          </div>
          <div class="stats-info">
            <div class="stats-label">排口</div>
            <div class="stats-value-wrapper">
              <div class="stats-value outlet-gradient">{{ sewageData.outletCount }}</div>
              <div class="stats-unit">个</div>
            </div>
          </div>
        </div>
        <div class="stats-item">
          <div class="stats-indicator">
            <div class="indicator-outer plant-outer"></div>
            <div class="indicator-inner plant-inner"></div>
          </div>
          <div class="stats-info">
            <div class="stats-label">污水厂</div>
            <div class="stats-value-wrapper">
              <div class="stats-value plant-gradient">{{ sewageData.plantCount || 6 }}</div>
              <div class="stats-unit">个</div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 雨水管网内容 -->
      <div class="content-wrapper" v-if="activeTab === 'rainwater'">
        <div class="age-chart">
          <div class="chart-container" ref="chartRef"></div>
          <div class="center-text">
            <div class="age-title">管龄</div>
          </div>
        </div>
        <div class="age-list">
          <div class="age-item" v-for="(item, index) in ageItems" :key="index">
            <div class="age-indicator" :style="{ background: item.color }"></div>
            <div class="age-name">{{ item.name }}</div>
            <div class="age-value">{{ item.value }} <span class="unit-text">KM</span></div>
          </div>
        </div>
      </div>
      
      <!-- 污水管网内容 -->
      <div class="sewage-content-wrapper" v-if="activeTab === 'sewage'">
        <div class="sewage-charts">
          <div class="sewage-chart">
            <div class="chart-container1" ref="sewageChartLeft"></div>
            <div class="center-text1">
              <div class="age-title1">污水</div>
              <div class="age-title1">管网</div>
            </div>
            <!-- <div class="chart-label sewage-label-left">{{ sewageData.ageLabel1 || '45公里' }}</div> -->
          </div>
          <div class="sewage-chart">
            <div class="chart-container1" ref="sewageChartRight"></div>
            <div class="center-text1">
              <div class="age-title1">雨污</div>
              <div class="age-title1">混接</div>
            </div>
            <!-- <div class="chart-label sewage-label-right">{{ sewageData.ageLabel2 || '37公里' }}</div> -->
          </div>
        </div>
        <!-- <div class="age-lists">
          <div class="age-list sewage-age-list">
            <div class="age-item" v-for="(item, index) in sewageData.ageStatistics" :key="'sewage'+index">
              <div class="age-indicator" :style="{ background: item.color }"></div>
              <div class="age-name">{{ item.name }}</div>
              <div class="age-value">{{ item.value }} <span class="unit-text">KM</span></div>
            </div>
          </div>
          <div class="age-list sewage-age-list">
            <div class="age-item" v-for="(item, index) in sewageData.mixedAgeStatistics" :key="'mixed'+index">
              <div class="age-indicator" :style="{ background: item.color }"></div>
              <div class="age-name">{{ item.name }}</div>
              <div class="age-value">{{ item.value }} <span class="unit-text">KM</span></div>
            </div>
          </div>
        </div> -->
      </div>
    </div>
  </PanelBox>
</template>

<script setup>
import { ref, onMounted, nextTick, computed, watch } from 'vue'
import * as echarts from 'echarts'
import PanelBox from '@/components/screen/PanelBox.vue'
// import { getRainwaterStatistics, getSewageStatistics } from '@/api/drainage' // 引入 API 函数，需要后端提供

// 综合态势总览左上面板组件

// 定义数据源
const activeTab = ref('rainwater') // 默认选择雨水管网
const chartRef = ref(null)
const sewageChartLeft = ref(null)
const sewageChartRight = ref(null)
let chartInstance = null
let sewageLeftChartInstance = null
let sewageRightChartInstance = null

// 管道年龄颜色映射
const ageColorMap = {
  '5年以下': '#FFCB47',
  '5-10年': '#22CBFF',
  '10年以上': '#19BE6B'
}

// 模拟数据 - 雨水管网
const rainwaterData = ref({
  pipelineLength: 511,
  pumpStationCount: 304,
  outletCount: 56,
  ageStatistics: [
    { name: '5年以下', value: 88, color: '#FFCB47' },
    { name: '5-10年', value: 121, color: '#22CBFF' },
    { name: '10年以上', value: 259, color: '#19BE6B' }
  ]
})

// 模拟数据 - 污水管网
const sewageData = ref({
  pipelineLength: 1438,
  pumpStationCount: 278,
  outletCount: 56,
  plantCount: 6,
  mixedCount: 342,
  ageLabel1: '45公里',
  ageLabel2: '37公里',
  ageStatistics: [
    { name: '5年以下', value: 76, color: '#FFCB47' },
    { name: '5-10年', value: 98, color: '#22CBFF' },
    { name: '10年以上', value: 224, color: '#19BE6B' }
  ],
  mixedAgeStatistics: [
    { name: '5年以下', value: 23, color: '#FFCB47' },
    { name: '5-10年', value: 37, color: '#22CBFF' },
    { name: '10年以上', value: 27, color: '#19BE6B' }
  ]
})

// 计算当前展示数据
const currentData = computed(() => {
  return activeTab.value === 'rainwater' ? rainwaterData.value : sewageData.value
})

const pipelineLength = computed(() => currentData.value.pipelineLength)
const pumpStationCount = computed(() => currentData.value.pumpStationCount)
const outletCount = computed(() => currentData.value.outletCount)
const ageItems = computed(() => currentData.value.ageStatistics)

// 监听数据变化，更新图表
watch([currentData], () => {
  if (activeTab.value === 'rainwater' && chartInstance) {
    updateChart()
  } else if (activeTab.value === 'sewage') {
    updateSewageCharts()
  }
})

// 切换标签页
const changeTab = (tab) => {
  activeTab.value = tab
  fetchData(tab)
  nextTick(() => {
    if (tab === 'rainwater') {
      // 当切换回雨水管网时，需要等待DOM更新后再初始化图表
      setTimeout(() => {
        if (chartRef.value) {
          if (chartInstance) {
            // 如果图表实例存在但已被销毁，需要重新创建
            if (chartInstance.isDisposed && chartInstance.isDisposed()) {
              chartInstance = null;
              initChart();
            } else {
              // 如果图表实例存在且未被销毁，则更新数据
              updateChart();
            }
          } else {
            // 如果图表实例不存在，创建新的实例
            initChart();
          }
        }
      }, 0);
    } else if (tab === 'sewage') {
      // 在切换到污水管网前，销毁雨水图表实例，避免内存泄漏
      if (chartInstance && !chartInstance.isDisposed()) {
        chartInstance.dispose();
      }
      initSewageCharts();
    }
  })
}

// 接口请求方法
const fetchData = async (tabType) => {
  try {
    // 实际项目中需要替换为真实的API调用
    // let response
    // if (tabType === 'rainwater') {
    //   response = await getRainwaterStatistics()
    // } else {
    //   response = await getSewageStatistics()
    // }
    // 
    // if (response.code === 200 && response.data) {
    //   if (tabType === 'rainwater') {
    //     rainwaterData.value = {
    //       pipelineLength: response.data.pipelineLength,
    //       pumpStationCount: response.data.pumpStationCount,
    //       outletCount: response.data.outletCount,
    //       ageStatistics: response.data.ageStatistics.map(item => ({
    //         name: item.name,
    //         value: item.value,
    //         color: ageColorMap[item.name] || '#ccc'
    //       }))
    //     }
    //   } else {
    //     sewageData.value = {
    //       // 同上处理
    //     }
    //   }
    // }

    // 目前使用模拟数据，无需实际调用API
    console.log(`切换到${tabType === 'rainwater' ? '雨水管网' : '污水管网'}标签页`)
  } catch (error) {
    console.error(`获取${tabType === 'rainwater' ? '雨水管网' : '污水管网'}数据失败:`, error)
  }
}

// 初始化雨水图表
const initChart = () => {
  if (!chartRef.value) return

  chartInstance = echarts.init(chartRef.value)
  updateChart()

  window.addEventListener('resize', () => {
    chartInstance && chartInstance.resize()
  })
}

// 初始化污水图表
const initSewageCharts = () => {
  if (!sewageChartLeft.value || !sewageChartRight.value) return
  
  // 确保销毁之前的实例
  if (sewageLeftChartInstance && !sewageLeftChartInstance.isDisposed()) {
    sewageLeftChartInstance.dispose();
  }
  
  if (sewageRightChartInstance && !sewageRightChartInstance.isDisposed()) {
    sewageRightChartInstance.dispose();
  }
  
  // 创建新的实例
  sewageLeftChartInstance = echarts.init(sewageChartLeft.value)
  sewageRightChartInstance = echarts.init(sewageChartRight.value)
  
  updateSewageCharts()
  
  window.addEventListener('resize', () => {
    sewageLeftChartInstance && sewageLeftChartInstance.resize()
    sewageRightChartInstance && sewageRightChartInstance.resize()
  })
}

// 更新图表数据
const updateChart = () => {
  if (!chartInstance) return
  const data = ageItems.value
  const colorList = data.map(item => item.color)
  const valueList = data.map(item => Number(item.value || 0))

  const option = {
    backgroundColor: 'transparent',
    series: [{
      type: 'pie',
      radius: ['75%', '85%'],
      center: ['50%', '50%'],
      startAngle: 0,
      itemStyle: {
        borderRadius: 0,
        borderColor: 'transparent',
        borderWidth: 0
      },
      label: {
        show: false
      },
      silent: true,
      data: valueList.map((value, index) => ({
        value,
        name: data[index].name,
        itemStyle: {
          color: colorList[index]
        }
      }))
    }]
  }

  chartInstance.setOption(option, true)
}

// 更新污水图表
const updateSewageCharts = () => {
  if (!sewageLeftChartInstance || !sewageRightChartInstance) return
  
  const sewageData = currentData.value.ageStatistics
  const mixedData = currentData.value.mixedAgeStatistics
  
  // 左侧污水管网饼图
  const leftOption = {
    backgroundColor: 'transparent',
    series: [{
      type: 'pie',
      radius: ['25%', '35%'],
      center: ['50%', '50%'],
      startAngle: 0,
      itemStyle: {
        borderRadius: 0,
        borderColor: 'transparent',
        borderWidth: 0
      },
      label: {
        show: true,
        position: 'outside',
        formatter: '{b}\n{c}',
        color: '#fff',
        fontSize: 10,
        fontWeight: 'bold'
      },
      labelLine: {
        show: true,
        length: 5,
        length2: 5,
        // lineStyle: {
        //   color: '#999'
        // }
      },
      data: sewageData.map(item => ({
        value: Number(item.value || 0),
        name: item.name,
        itemStyle: {
          color: item.color
        }
      }))
    }]
  }
  
  // 右侧雨污混接饼图
  const rightOption = {
    backgroundColor: 'transparent',
    series: [{
      type: 'pie',
      radius: ['25%', '35%'],
      center: ['50%', '50%'],
      startAngle: 0,
      itemStyle: {
        borderRadius: 0,
        borderColor: 'transparent',
        borderWidth: 0
      },
      label: {
        show: true,
        position: 'outside',
        formatter: '{b}\n{c}',
        color: '#fff',
        fontSize: 10,
        fontWeight: 'bold'
      },
      labelLine: {
        show: true,
        length: 5,
        length2: 5,
      },
      data: mixedData.map(item => ({
        value: Number(item.value || 0),
        name: item.name,
        itemStyle: {
          color: item.color
        }
      }))
    }]
  }
  
  sewageLeftChartInstance.setOption(leftOption, true)
  sewageRightChartInstance.setOption(rightOption, true)
}

onMounted(async () => {
  await nextTick()
  if (activeTab.value === 'rainwater') {
    initChart()
  }
  // 初始化时调用接口获取默认标签页数据
  await fetchData(activeTab.value)
})
</script>

<style scoped>
.left-top-panel {
  height: 310px;
}

.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
}

.pipeline-stats {
  display: flex;
  justify-content: space-around;
  padding: 0 15px 20px;
}

.stats-item {
  display: flex;
  align-items: flex-start;
  position: relative;
}

.stats-indicator {
  position: relative;
  width: 9px;
  height: 9px;
  margin-right: 6px;
  margin-top: 2px;
}

.indicator-outer {
  width: 9px;
  height: 9px;
  border-radius: 50%;
  position: absolute;
  top: 0;
  left: 0;
}

.indicator-inner {
  width: 5px;
  height: 5px;
  border-radius: 50%;
  position: absolute;
  top: 2px;
  left: 2px;
}

.rain-outer {
  background: rgba(5, 90, 219, 0.4);
}

.rain-inner {
  background: #055ADB;
}

.pump-outer {
  background: rgba(35, 202, 255, 0.4);
}

.pump-inner {
  background: #23CAFF;
}

.outlet-outer {
  background: rgba(63, 216, 124, 0.4);
}

.outlet-inner {
  background: #3FD87C;
}

.sewage-outer {
  background: rgba(87, 77, 234, 0.4);
}

.sewage-inner {
  background: #574DEA;
}

.rainmix-outer {
  background: rgba(118, 97, 234, 0.4);
}

.rainmix-inner {
  background: #7661EA;
}

.plant-outer {
  background: rgba(186, 88, 234, 0.4);
}

.plant-inner {
  background: #BA58EA;
}

.stats-info {
  display: flex;
  flex-direction: column;
}

.stats-label {
  font-family: PingFangSC, 'PingFang SC';
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
  margin-bottom: 2px;
}

.stats-value-wrapper {
  display: flex;
  align-items: baseline;
}

.stats-value {
  font-family: 'D-DIN', 'D-DIN';
  font-weight: bold;
  font-size: 24px;
  line-height: 26px;
  font-style: normal;
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
}

.rain-gradient {
  background: linear-gradient(90deg, #FFFFFF 0%, #055ADB 100%);
  -webkit-background-clip: text;
}

.pump-gradient {
  background: linear-gradient(90deg, #E2FBFF 0%, #23CAFF 100%);
  -webkit-background-clip: text;
}

.outlet-gradient {
  background: linear-gradient(90deg, #FFFFFF 0%, #36F281 100%);
  -webkit-background-clip: text;
}

.sewage-gradient {
  background: linear-gradient(90deg, #FFFFFF 0%, #574DEA 100%);
  -webkit-background-clip: text;
}

.rainmix-gradient {
  background: linear-gradient(90deg, #FFFFFF 0%, #7661EA 100%);
  -webkit-background-clip: text;
}

.plant-gradient {
  background: linear-gradient(90deg, #FFFFFF 0%, #BA58EA 100%);
  -webkit-background-clip: text;
}

.stats-unit {
  font-family: PingFangSC, 'PingFang SC';
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
  margin-left: 4px;
}

.content-wrapper {
  display: flex;
  align-items: center;
  gap: 20px;
  height: 100%;
}

.sewage-content-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 10px;
  height: 100%;
}

.sewage-charts {
  display: flex;
  justify-content: space-around;
  width: 100%;
  gap: 20px;
}

.sewage-chart {
  position: relative;
  width: 200px;
  height: 200px;
}


.tab-buttons {
  display: flex;
  align-items: center;
  margin-right: 30px;
}

.divider {
  width: 1px;
  height: 16px;
  background-color: rgba(255, 255, 255, 0.8);
  margin: 0 10px;
}

.tab-btn {
  cursor: pointer;
  font-family: PingFangSC, 'PingFang SC';
  font-weight: 500;
  font-size: 16px;
  color: rgba(255, 255, 255, 0.3);
  line-height: 22px;
}

.tab-btn.active {
  color: rgba(255, 255, 255, 0.8);
}

.age-chart {
  width: 170px;
  height: 170px;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.chart-container {
  width: 170px;
  height: 170px;
  background-image: url('@/assets/images/screen/gas/guanwangfengxian.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  position: relative;
}
.chart-container1 {
  width: 170px;
  height: 170px;
  background-image: url('@/assets/images/screen/gas/guanwangfengxian.png');
  background-size: 50% 50%;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;
}

.center-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #fff;
}
.center-text1 {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #fff;
}

.age-title {
  font-size: 16px;
  font-weight: 500;
  color: #FFFFFF;
}
.age-title1 {
  font-size: 12px;
  font-weight: 500;
  color: #FFFFFF;
}

.age-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.age-item {
  display: flex;
  align-items: center;
  height: 28px;
  width: 220px;
  padding: 0 15px;
}

.age-indicator {
  width: 8px;
  height: 8px;
  margin-right: 10px;
}

.age-name {
  width: 70px;
  color: #FFFFFF;
  font-size: 14px;
}

.age-value {
  color: #ffffff;
  font-size: 16px;
  font-weight: bold;
  font-family: 'DIN Alternate', sans-serif;
  width: 80px;
  text-align: right;
  margin-left: auto;
}

.unit-text {
  color: #85A5C3;
  font-size: 12px;
  font-weight: normal;
  margin-left: 2px;
}

/* 适配不同屏幕尺寸 */
@media screen and (min-width: 1920px) and (max-width: 2560px) {
  .left-top-panel {
    height: 280px;
  }

  .age-chart {
    width: 150px;
    height: 150px;
  }

  .chart-container {
    width: 150px;
    height: 150px;
  }
  .chart-container1 {
    width: 200px;
    height: 200px;
  }
  
  .sewage-chart {
    width: 200px;
    height: 200px;
  }
}

/* 适配不同屏幕尺寸 */
@media screen and (min-height: 900px) and (max-height: 940px) {
  .pipeline-stats {
    padding: 0 3% 1%;
  }
}

@media screen and (max-width: 1919px) {
  .left-top-panel {
    height: 260px;
  }

  .age-chart {
    width: 140px;
    height: 140px;
  }

  .chart-container {
    width: 140px;
    height: 140px;
  }
  .chart-container1 {
    width: 200px;
    height: 200px;
  }
  
  .sewage-chart {
    width: 200px;
    height: 200px;
  }

  .age-item {
    height: 25px;
    width: 180px;
  }
}
</style>