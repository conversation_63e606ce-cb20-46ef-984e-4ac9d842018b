<template>
  <div class="gas-risk-explosion-assessment">
    <!-- 上部分区域：风险值统计 -->
    <div class="risk-stats-section">
      <div class="risk-card major-risk">
        <div class="risk-title">重大风险</div>
        <div class="risk-value">
          <span class="value">{{ riskLevelData['7001'] || 0 }}</span>
          <span class="unit">个</span>
        </div>
      </div>
      <div class="risk-card high-risk">
        <div class="risk-title">较大风险</div>
        <div class="risk-value">
          <span class="value">{{ riskLevelData['7002'] || 0 }}</span>
          <span class="unit">个</span>
        </div>
      </div>
      <div class="risk-card normal-risk">
        <div class="risk-title">一般风险</div>
        <div class="risk-value">
          <span class="value">{{ riskLevelData['7003'] || 0 }}</span>
          <span class="unit">个</span>
        </div>
      </div>
      <div class="risk-card low-risk">
        <div class="risk-title">低风险</div>
        <div class="risk-value">
          <span class="value">{{ riskLevelData['7004'] || 0 }}</span>
          <span class="unit">个</span>
        </div>
      </div>
    </div>

    <!-- 中部分区域：查询表单 -->
    <div class="search-section">
      <GasStationRiskSearch @search="handleSearch" @reset="handleReset" />
    </div>

    <!-- 下部分区域：按钮和表格 -->
    <div class="table-section">
      <!-- 按钮区域 -->
      <div class="table-header">
        <div class="button-group">
          <el-button type="primary" class="operation-btn" @click="handleExport">导出</el-button>
          <el-button type="primary" class="operation-btn" @click="handleConfigRisk">风险指标配置</el-button>
        </div>
      </div>

      <!-- 表格区域 -->
      <div class="table-container">
        <el-table
          :data="tableData"
          style="width: 100%"
          :header-cell-style="headerCellStyle"
          :row-class-name="tableRowClassName"
          @row-click="handleRowClick"
          height="calc(100vh - 690px)"
          v-loading="loading"
        >
          <el-table-column label="序号" min-width="60">
            <template #default="{ $index }">
              {{ (currentPage - 1) * pageSize + $index + 1 }}
            </template>
          </el-table-column>
          <el-table-column prop="riskCode" label="风险编码" min-width="120" />
          <el-table-column prop="pressureLevel" label="压力级别" min-width="100" />
          <el-table-column prop="lineCode" label="管线编码" min-width="120" />
          <el-table-column prop="diameter" label="管径(mm)" min-width="100" />
          <el-table-column prop="material" label="管材" min-width="100" />
          <el-table-column prop="buildTime" label="建设时间" min-width="120" />
          <el-table-column prop="roadName" label="所在道路" min-width="150" />
          <el-table-column label="风险等级" min-width="120">
            <template #default="scope">
              <div :class="getRiskLevelClass(scope.row.riskLevel)">
                {{ getRiskLevelText(scope.row.riskLevel) }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="assessmentDate" label="评估日期" min-width="120" />
          <el-table-column prop="stationStatusName" label="管控状态" min-width="120" />
          <el-table-column label="操作" min-width="220" fixed="right" align="center">
            <template #default="scope">
              <div class="operation-btns">
                <div class="operation-btn-row">
                  <span class="operation-btn-text" @click.stop="handleAssessRecord(scope.row)">评估记录</span>
                  <span class="operation-divider">|</span>
                  <span class="operation-btn-text" @click.stop="handleEdit(scope.row)">修改</span>
                  <span class="operation-divider">|</span>
                  <span class="operation-btn-text" @click.stop="handleLocation(scope.row)">定位</span>
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页区域 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 30, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :pager-count="5"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive } from 'vue';
import { ElTable, ElTableColumn, ElButton, ElPagination } from 'element-plus';
import GasStationRiskSearch from './components/GasStationRiskSearch.vue';
import { getStationRiskAssessmentPage, getStationRiskStatistics } from '@/api/gas';
import { RISK_LEVEL_MAP, RISK_LEVEL, CONTROL_STATUS_MAP } from '@/constants/gas';

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const tableData = ref([]);
const loading = ref(false);

// 查询参数
const queryParams = ref({});

// 风险统计数据
const riskLevelData = reactive({
  '7001': 0, // 重大风险
  '7002': 0, // 较大风险
  '7003': 0, // 一般风险
  '7004': 0  // 低风险
});

// 获取风险等级文本
const getRiskLevelText = (level) => {
  return RISK_LEVEL_MAP[level] || '';
};

// 获取风险等级样式
const getRiskLevelClass = (level) => {
  const map = {
    [RISK_LEVEL.CRITICAL_RISK]: 'risk-level-major',
    [RISK_LEVEL.MAJOR_RISK]: 'risk-level-high',
    [RISK_LEVEL.NORMAL_RISK]: 'risk-level-normal',
    [RISK_LEVEL.LOW_RISK]: 'risk-level-low'
  };
  return ['risk-level-tag', map[level]];
};

// 处理搜索
const handleSearch = (formData) => {
  queryParams.value = formData;
  currentPage.value = 1;
  fetchRiskData();
};

// 处理重置
const handleReset = () => {
  queryParams.value = {};
  currentPage.value = 1;
  fetchRiskData();
};

// 获取风险统计数据
const fetchRiskStatistics = async () => {
  try {
    const res = await getStationRiskStatistics();
    if (res.code === 200 && res.data) {
      // 重置数据
      Object.keys(riskLevelData).forEach(key => {
        riskLevelData[key] = 0;
      });
      
      // 处理接口返回的数据
      const riskLevelStats = res.data.riskLevelStatistics || [];
      
      // 遍历统计数据，更新对应风险等级的值
      riskLevelStats.forEach(item => {
        if (item.code && item.count !== undefined) {
          riskLevelData[item.code] = item.count;
        }
      });
    }
  } catch (error) {
    console.error('获取风险统计数据失败', error);
    // 重置为默认值0
    riskLevelData['7001'] = 0;
    riskLevelData['7002'] = 0;
    riskLevelData['7003'] = 0;
    riskLevelData['7004'] = 0;
  }
};

// 获取风险数据
const fetchRiskData = async () => {
  loading.value = true;
  try {
    const res = await getStationRiskAssessmentPage(
      currentPage.value,
      pageSize.value,
      queryParams.value
    );
    if (res.code === 200 && res.data) {
      tableData.value = res.data.records || [];
      total.value = res.data.total || 0;
    } else {
      tableData.value = [];
      total.value = 0;
    }
  } catch (error) {
    console.error('获取风险评估数据失败', error);
    tableData.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
  }
};

// 处理分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val;
  currentPage.value = 1;
  fetchRiskData();
};

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val;
  fetchRiskData();
};

// 表头样式
const headerCellStyle = {
  background: '#F4F4F4',
  fontFamily: 'PingFangSC, PingFang SC',
  fontWeight: '500',
  fontSize: '14px',
  color: '#282828',
  height: '64px'
};

// 斑马纹和选中行样式
const tableRowClassName = ({ rowIndex }) => {
  if (rowIndex % 2 === 1) {
    return 'zebra-row';
  }
  return '';
};

// 处理行点击
const handleRowClick = (row) => {
  console.log('行数据:', row);
};

// 操作按钮处理函数
const handleExport = () => {
  console.log('导出');
};

const handleConfigRisk = () => {
  console.log('风险指标配置');
};

const handleAssessRecord = (row) => {
  console.log('评估记录:', row);
};

const handleEdit = (row) => {
  console.log('修改:', row);
};

const handleLocation = (row) => {
  console.log('定位:', row);
};

onMounted(() => {
  fetchRiskStatistics();
  fetchRiskData();
});
</script>

<style scoped>
.gas-risk-explosion-assessment {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  background-color: #F0F2F5; /* 修改背景颜色，让白色区块更加明显 */
  gap: 16px; /* 使用gap替代margin-bottom，更加统一的间距 */
}

/* 风险统计区域样式 */
.risk-stats-section {
  width: 100%;
  max-width: 1648px;
  height: 132px;
  background: #FFFFFF;
  padding: 16px;
  display: flex;
  justify-content: space-between;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  margin-bottom: 0; /* 移除margin-bottom，使用父容器的gap */
}

.risk-card {
  width: 380px;
  height: 100px;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 0 24px;
}

.risk-card:not(:last-child) {
  margin-right: 32px;
}

.major-risk {
  background: linear-gradient(180deg, #F62609 0%, #FF7D52 100%);
}

.high-risk {
  background: linear-gradient(180deg, #F68409 0%, #FFC46A 100%);
}

.normal-risk {
  background: linear-gradient(180deg, #F6CE09 0%, #F7DD5D 100%);
}

.low-risk {
  background: linear-gradient(180deg, #0979F6 0%, #40C3FA 100%);
}

.risk-title {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 18px;
  color: #FFFFFF;
  margin-bottom: 8px;
}

.risk-value {
  display: flex;
  align-items: baseline;
}

.value {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 30px;
  color: #FFFFFF;
  margin-right: 4px;
}

.unit {
  font-size: 14px;
  color: #FFFFFF;
}

/* 搜索表单区域 */
.search-section {
  background: #FFFFFF;
  padding: 16px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  margin-bottom: 0; /* 移除margin-bottom，使用父容器的gap */
}

/* 表格区域样式 */
.table-section {
  background: #FFFFFF;
  padding: 16px;
  border-radius: 4px;
  flex: 1;
  display: flex;
  flex-direction: column;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

/* 表格头部样式 */
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.button-group {
  display: flex;
  gap: 8px;
}

.operation-btn {
  min-width: 80px;
  height: 32px;
  background: #0277FD;
  border-radius: 2px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 14px;
  color: #FFFFFF;
  padding: 0 16px;
}

/* 表格样式 */
.table-container {
  margin-bottom: 16px;
  flex: 1;
  width: 100%;
  overflow: hidden;
}

:deep(.el-table) {
  overflow-x: hidden !important;
}

:deep(.el-table th) {
  background-color: #F4F4F4;
  height: 64px;
}

:deep(.el-table tr) {
  height: 48px;
}

:deep(.zebra-row) {
  background-color: #F8FAFD;
}

:deep(.el-table__row:hover) {
  background: #EEF5FF !important;
}

:deep(.el-table__row.selected-row) {
  background: #EEF5FF !important;
}

/* 风险等级标签样式 */
.risk-level-tag {
  width: 72px;
  height: 32px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
}

.risk-level-major {
  background: rgba(255,0,0,0.1);
  border: 1px solid #FF0000;
  color: #FF0000;
}

.risk-level-high {
  background: rgba(255,133,0,0.1);
  border: 1px solid #FF8500;
  color: #FF8500;
}

.risk-level-normal {
  background: rgba(255,211,0,0.1);
  border: 1px solid #FFD300;
  color: #FFD300;
}

.risk-level-low {
  background: rgba(0,122,255,0.1);
  border: 1px solid #007AFF;
  color: #007AFF;
}

.operation-btns {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.operation-btn-row {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 4px 0;
}

.operation-btn-text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #0086FF;
  cursor: pointer;
}

.operation-divider {
  margin: 0 4px;
  color: #CED3DA;
}

/* 分页样式 */
.pagination-container {
  display: flex;
  justify-content: flex-end;
  padding: 10px 0;
  margin-top: 8px;
  min-height: 32px;
  position: relative;
  z-index: 10;
}

:deep(.el-pagination) {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #222222;
  padding-right: 0;
}

:deep(.el-pagination .el-pager li) {
  width: 24px;
  height: 24px;
  background: rgba(255,255,255,0.99);
  border-radius: 2px;
  border: 1px solid #EEEEEE;
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.el-pagination .el-pager li.is-active) {
  width: 24px;
  height: 24px;
  background: #0086FF;
  border-radius: 2px;
  color: #FFFFFF;
  border: none;
}

:deep(.el-pagination .btn-prev),
:deep(.el-pagination .btn-next) {
  width: 24px;
  height: 24px;
  background: #FFFFFF;
  border-radius: 2px;
  border: 1px solid #EEEEEE;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}

:deep(.el-pagination .el-pagination__jump) {
  margin-left: 16px;
}

:deep(.el-pagination .el-pagination__jump .el-input__inner) {
  height: 24px;
  width: 36px;
}

:deep(.el-pagination .el-pagination__sizes) {
  margin-right: 16px;
}

:deep(.el-pagination .el-pagination__sizes .el-input__inner) {
  height: 24px;
}

:deep(.el-pagination .el-pagination__total) {
  margin-right: 16px;
}

/* 防止表格过长时遮挡分页组件 */
:deep(.el-table__body-wrapper) {
  overflow-y: auto !important;
  min-height: 200px;
}

/* 确保操作列文字不换行 */
:deep(.cell) {
  white-space: nowrap;
}

/* 响应式适配 */
@media screen and (max-width: 1920px) {
  .risk-stats-section {
    width: 100%;
  }
  
  .risk-card {
    width: calc(25% - 24px);
  }
}
</style> 