<template>
  <div class="search-container">
    <el-form ref="searchFormRef" :model="searchForm" inline>
      <el-form-item label="是否生效:">
        <el-select v-model="searchForm.isEnabled" placeholder="请选择" style="width: 120px;" clearable>
          <el-option 
            v-for="item in enabledStatusOptions" 
            :key="item.value" 
            :label="item.label" 
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      
      <el-form-item label="" style="margin-left: 20px;">
        <el-input 
          v-model="searchForm.ruleName" 
          placeholder="输入规则名称"
          style="width: 200px;"
          clearable
          class="search-input-no-border"
        />
      </el-form-item>
      
      <el-form-item>
        <el-button type="primary" @click="handleSearch">查询</el-button>
        <el-button @click="handleReset">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { ENABLED_STATUS_OPTIONS } from '@/constants/drainage';

const emit = defineEmits(['search', 'reset']);

// 搜索表单
const searchForm = reactive({
  ruleName: '',
  isEnabled: ''  // 保持为空字符串，这样可以选择true/false或者清空
});

// 表单引用
const searchFormRef = ref(null);

// 生效状态选项
const enabledStatusOptions = ENABLED_STATUS_OPTIONS;

// 处理搜索
const handleSearch = () => {
  emit('search', { ...searchForm });
};

// 处理重置
const handleReset = () => {
  if (searchFormRef.value) {
    searchFormRef.value.resetFields();
  }
  searchForm.ruleName = '';
  searchForm.isEnabled = '';
  emit('reset');
};
</script>

<style scoped>
.search-container {
  background: #FFFFFF;
  padding: 20px;
  margin-bottom: 16px;
  border-radius: 4px;
}

:deep(.el-form-item) {
  margin-bottom: 0;
  margin-right: 20px;
}

:deep(.el-form-item__label) {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #282828;
}

:deep(.el-input__inner) {
  height: 32px;
  border: 1px solid #D9D9D9;
  border-radius: 2px;
}

/* 无边框搜索输入框样式 */
:deep(.search-input-no-border .el-input__wrapper) {
  border: none;
  box-shadow: none;
  background-color: transparent;
  padding: 0;
}

:deep(.search-input-no-border .el-input__inner) {
  border: none;
  background-color: transparent;
  height: 32px;
}

:deep(.el-select) {
  width: 120px;
}

:deep(.el-button) {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 14px;
}

:deep(.el-button--primary) {
  background: #0277FD;
  border-color: #0277FD;
  color: #FFFFFF;
}
</style> 