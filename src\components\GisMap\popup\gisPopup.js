import { defineAsyncComponent } from "vue";

const demoPopup = defineAsyncComponent(() => import("./demoPopup.vue"));

const NormalDetailPopup = defineAsyncComponent(() => import("./NormalDetail/index.vue"));
const DeviceDetailPopup = defineAsyncComponent(() => import("./DeviceDetail/index.vue"));

/**
 * 弹窗组件(目前已接入的弹窗)
 */
export const gisPopups= {
  normal: demoPopup,
  gas_pipeline_point: NormalDetailPopup, // 燃气管道点
  gas_station: NormalDetailPopup, // 燃气场站
  gas_well: NormalDetailPopup, // 燃气窨井
  gas_dangerous_source: NormalDetailPopup, // 燃气危险源
  gas_protection_target: NormalDetailPopup, // 燃气防护目标
  gas_combustible: DeviceDetailPopup, // 燃气可燃气体监测仪
  gas_manhole_cover: DeviceDetailPopup, // 燃气井盖监测
  drainage_pump_station: NormalDetailPopup, // 排水泵站
  drainage_sewage_works: NormalDetailPopup, // 污水处理厂
  drainage_water_outlet: NormalDetailPopup, // 排水口
  drainage_pipeline_point: NormalDetailPopup, // 排水管点
  drainage_well: NormalDetailPopup, // 排水窨井
  drainage_flooding_point: NormalDetailPopup, // 排水易涝点
  drainage_dangerous_source: NormalDetailPopup, // 排水危险源
  drainage_protection_target: NormalDetailPopup, // 排水防护目标
  drainage_combustible: DeviceDetailPopup, // 排水可燃气体监测仪
  drainage_manhole_cover: DeviceDetailPopup, // 排水井盖监测
  drainage_level: DeviceDetailPopup, // 排水液位计
  drainage_water_quality: DeviceDetailPopup, // 排水水质监测仪
  heating_pipeline_point: NormalDetailPopup, // 供热管点
  heating_well: NormalDetailPopup, // 供热窨井
  heating_enterprise: NormalDetailPopup, // 供热企业
  heating_source_works: NormalDetailPopup, // 供热源厂
  heating_station: NormalDetailPopup, // 供热场站
  heating_user: NormalDetailPopup, // 供热用户
  heating_dangerous_source: NormalDetailPopup, // 供热危险源
  heating_protection_target: NormalDetailPopup, // 供热防护目标
  heating_combustible: DeviceDetailPopup, // 供热可燃气体监测仪
  heating_manhole_cover: DeviceDetailPopup, // 供热井盖监测
  heating_temperature: DeviceDetailPopup, // 供热温度监测仪
  bridge_temperature: DeviceDetailPopup, // 桥梁温度监测仪
  bridge_static_level: DeviceDetailPopup, // 桥梁静力水准仪
  bridge_displacement: DeviceDetailPopup, // 桥梁位移监测仪
  bridge_strain: DeviceDetailPopup, // 桥梁应变监测仪
  bridge_crack_sensor: DeviceDetailPopup, // 桥梁裂缝监测仪
};
