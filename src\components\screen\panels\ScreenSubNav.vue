<template>
  <div v-if="showSubNav" class="screen-sub-nav">
    <div class="sub-nav-container">
      <router-link
        v-for="(item, index) in getSubNavItems()" 
        :key="index"
        :to="`/${activeTab}/${item.value}`"
        custom
        v-slot="{ navigate, isActive }"
      >
        <div 
          :class="['sub-nav-item', isActive ? 'active' : '']"
          @click="navigate"
        >
          {{ item.label }}
          <div v-if="isActive" class="yellow-triangle"></div>
        </div>
      </router-link>
    </div>
  </div>
</template>

<script setup>
import { defineProps, computed } from 'vue';

const props = defineProps({
  activeTab: {
    type: String,
    required: true
  },
  activeSubTab: {
    type: String,
    required: true,
    default: 'overview'
  }
});

// 是否显示二级导航
const showSubNav = computed(() => props.activeTab !== 'bridge');

// 二级导航项数据
const subNavItems = {
  comprehensive: [
    { label: '综合态势总览', value: 'overview' },
    { label: '综合风险隐患', value: 'risk' },
    { label: '综合运行监测', value: 'monitoring' },
    { label: '协同联动处置', value: 'coordination' },
    { label: '应急辅助决策', value: 'emergency' }
  ],
  gas: [
    { label: '综合态势', value: 'overview' },
    { label: '管网风险', value: 'network-risk' },
    { label: '监测报警', value: 'monitoring' },
    { label: '辅助决策', value: 'decision-screen' }
  ],
  drainage: [
    { label: '综合态势', value: 'overview' },
    { label: '风险隐患', value: 'risk' },
    { label: '易涝点风险', value: 'flooding-risk' },
    { label: '监测报警', value: 'monitoring' },
    { label: '辅助决策', value: 'decision' }
  ],
  heating: [
    { label: '综合态势', value: 'overview' },
    { label: '风险隐患', value: 'risk' },
    { label: '监测报警', value: 'monitoring' },
    { label: '辅助决策', value: 'decision' }
  ]
};

// 获取当前一级菜单对应的二级菜单项
const getSubNavItems = () => {
  return subNavItems[props.activeTab] || [];
};
</script>

<style scoped>
.screen-sub-nav {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sub-nav-container {
  display: flex;
  gap: 15px;
}

.sub-nav-item {
  padding: 6px 20px;
  color: #FFFFFF;
  cursor: pointer;
  font-family: 'PingFangSC', 'PingFang SC', sans-serif;
  font-weight: 600;
  font-size: 16px;
  line-height: 22px;
  letter-spacing: 1px;
  font-style: normal;
  transition: all 0.3s;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 32px;
  text-shadow: 0 0 6px rgba(0, 0, 0, 0.8), 0 0 3px rgba(0, 0, 0, 0.5);
  background: rgba(0, 0, 0, 0.25);
  border-radius: 16px;
  backdrop-filter: blur(1px);
}

.sub-nav-item:hover {
  color: #61D7FF;
  background: rgba(0, 0, 0, 0.4);
}

.sub-nav-item.active {
  background: linear-gradient(270deg, rgba(0,177,248,0.59) 0%, #0079EF 100%);
  border-radius: 16px;
  /* border: 1px solid; */
  border-image: linear-gradient(270deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.2)) 1 1;
  color: #FFFFFF;
  font-family: 'HYZhuZiMeiXinTiJ', sans-serif;
  font-size: 18px;
  line-height: 20px;
  font-weight: 400;
  text-shadow:none;
}

.yellow-triangle {
  width: 0;
  height: 0;
  position: absolute;
  bottom: -16px;
  left: 50%;
  transform: translateX(-50%);
  border-left: 4.5px solid transparent;
  border-right: 4.5px solid transparent;
  border-bottom: 6px solid #FFD541;
  transform-origin: center top;
  transform: translateX(-50%) rotate(180deg);
}
</style> 