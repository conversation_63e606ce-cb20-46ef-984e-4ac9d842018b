/**
 * 模板引擎工具类
 */
import Handlebars from 'handlebars'
import { templateVariables } from '../config/defaultConfig'

// 注册自定义助手
Handlebars.registerHelper('if_eq', function(a, b, options) {
  if (a === b) {
    return options.fn(this)
  }
  return options.inverse(this)
})

// 注册计算序号的助手
Handlebars.registerHelper('calculate_index', function(page, size, index) {
  return (page - 1) * size + index + 1
})

// 注册条件表达式助手
Handlebars.registerHelper('or', function() {
  for (let i = 0; i < arguments.length - 1; i++) {
    if (arguments[i]) {
      return true
    }
  }
  return false
})

/**
 * 编译模板
 * @param {String} template - 模板字符串
 * @param {Object} data - 模板数据
 * @returns {String} - 编译后的字符串
 */
export function compileTemplate(template, data) {
  try {
    const compiledTemplate = Handlebars.compile(template)

    // 处理模板变量
    const templateData = {}

    // 遍历模板变量映射
    for (const [key, getter] of Object.entries(templateVariables)) {
      if (typeof getter === 'function') {
        templateData[key] = getter(data)
      }
    }

    // 合并原始数据和处理后的模板数据
    const mergedData = { ...data, ...templateData }

    return compiledTemplate(mergedData)
  } catch (error) {
    console.error('模板编译失败', error)
    throw error
  }
}

/**
 * 生成代码
 * @param {Object} config - 配置数据
 * @param {String} templateType - 模板类型
 * @returns {Promise<String>} - 生成的代码
 */
export async function generateCode(config, templateType) {
  try {
    let templateUrl = ''

    // 根据模板类型确定模板路径
    switch (templateType) {
      case 'main':
        templateUrl = '/src/views/admin/system/code-generator/templates/index.template.hbs'
        break
      case 'search':
        templateUrl = '/src/views/admin/system/code-generator/templates/SearchForm.template.hbs'
        break
      case 'form':
        templateUrl = '/src/views/admin/system/code-generator/templates/EditForm.template.hbs'
        break
      case 'api':
        templateUrl = '/src/views/admin/system/code-generator/templates/api.template.hbs'
        break
      default:
        throw new Error(`未知的模板类型: ${templateType}`)
    }

    // 加载模板
    const templateContent = await fetch(templateUrl).then(response => response.text())

    // 编译模板
    return compileTemplate(templateContent, config)
  } catch (error) {
    console.error('代码生成失败', error)
    throw error
  }
}

/**
 * 生成所有代码
 * @param {Object} config - 配置数据
 * @returns {Promise<Object>} - 生成的所有代码
 */
export async function generateAllCode(config) {
  try {
    // 加载所有模板
    const indexTemplate = await fetch('/src/views/admin/system/code-generator/templates/index.template.hbs').then(response => response.text())
    const searchTemplate = await fetch('/src/views/admin/system/code-generator/templates/SearchForm.template.hbs').then(response => response.text())
    const formTemplate = await fetch('/src/views/admin/system/code-generator/templates/EditForm.template.hbs').then(response => response.text())
    const apiTemplate = await fetch('/src/views/admin/system/code-generator/templates/api.template.hbs').then(response => response.text())

    // 编译所有模板
    const mainCode = compileTemplate(indexTemplate, config)
    const searchCode = config.search.fields.length > 0 ? compileTemplate(searchTemplate, config) : ''
    const formCode = config.form.fields.length > 0 ? compileTemplate(formTemplate, config) : ''
    const apiCode = compileTemplate(apiTemplate, config)

    return {
      main: mainCode,
      search: searchCode,
      form: formCode,
      api: apiCode
    }
  } catch (error) {
    console.error('生成所有代码失败', error)
    throw error
  }
}
