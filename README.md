# Vue 3 + Vite

This template should help get you started developing with Vue 3 in Vite. The template uses Vue 3 `<script setup>` SFCs, check out the [script setup docs](https://v3.vuejs.org/api/sfc-script-setup.html#sfc-script-setup) to learn more.

Learn more about IDE Support for Vue in the [Vue Docs Scaling up Guide](https://vuejs.org/guide/scaling-up/tooling.html#ide-support).

# 城市生命线安全监管平台

这是一个基于Vue 3的城市生命线安全监管平台大屏展示项目，用于展示城市燃气、排水、热力、供水等生命线设施的监控和管理数据。

## 功能特点

- 支持多种专项切换（综合、燃气、排水、热力、桥梁等）
- 响应式布局设计，适配不同屏幕比例（16:9、21:9、32:9）
- 动态数据展示和图表可视化
- 模块化组件设计，便于扩展和维护
- 全屏地图底层铺设，面板叠层设计

## 技术栈

- Vue 3 (Composition API)
- ECharts 图表库
- CSS3/Flexbox/Grid布局
- 响应式设计
- Tailwind CSS
- Font Awesome图标库

## 项目结构

```
src/
  ├── components/
  │   └── screen/
  │       ├── panels/                # 各专项面板组件
  │       │   ├── comprehensive/     # 综合专项面板
  │       │   ├── gas/               # 燃气专项面板
  │       │   ├── drainage/          # 排水专项面板
  │       │   ├── heating/           # 热力专项面板
  │       │   └── bridge/            # 桥梁专项面板
  │       ├── ChartBox.vue           # 图表容器组件
  │       ├── PanelBox.vue           # 面板容器组件
  │       └── StatCard.vue           # 统计卡片组件
  └── views/
      └── screen/
          └── index.vue              # 大屏主视图
```

## 布局设计

项目采用了创新的叠层布局设计：

- **底层地图**：地图组件置于底层，铺满整个屏幕
- **UI层**：所有面板和控制组件位于上层，半透明背景增强可读性
- **交互设计**：通过CSS pointer-events属性确保用户能正确点击各层元素

这种布局设计的优势：
- 充分利用屏幕空间展示地图数据
- 各专项面板数据与地理位置直观关联
- 增强空间感和沉浸式体验

## 屏幕适配说明

项目针对不同屏幕比例进行了响应式适配：

- **16:9 标准屏幕**：基础设计尺寸，两侧面板固定宽度380px
- **21:9 超宽屏幕**：两侧面板宽度调整为420px，内边距适当增加
- **32:9 双宽屏**：两侧面板宽度调整为450px，内边距进一步增加

页面布局使用了Grid和Flex布局，确保在不同屏幕比例下内容能够合理分布：

```css
.main-content {
  display: grid;
  grid-template-columns: 380px minmax(0, 1fr) 380px; /* 两侧固定宽度，中间自适应 */
  gap: 10px;
}
```

## 组件通信

- 通过Vue的`props`和`emit`实现父子组件通信
- 使用计算属性和监听器响应数据变化
- 中央地图组件接收当前专项信息，动态切换显示内容

## 图表类型

- 饼图（Pie Chart）：用于展示燃气类型分布
- 仪表盘（Gauge Chart）：用于展示告警完成率和压力监测
- 折线图（Line Chart）：用于展示趋势分析
- 柱状图（Bar Chart）：用于展示各类告警统计

## 扩展指南

### 添加新专项

1. 在`src/components/screen/panels/`下创建新专项文件夹
2. 实现该专项的左上、左中、左下、右上、右中、右下六个面板组件
3. 在`src/views/screen/index.vue`中导入新组件并添加到组件映射中
4. 在导航组件中添加新专项选项

### 自定义图表

所有图表都使用`ChartBox`组件，只需传入ECharts配置即可：

```vue
<ChartBox :options="chartOptions" height="100%" />
```

## 特殊路由设计

项目实现了特殊的路由设计，支持不同专项的导航：

- 通用路由格式：`/:primaryTab/:secondaryTab`（例如 `/gas/overview`）
- 桥梁专项特殊处理：使用直接路由 `/bridge`，无二级导航

## 运行说明

1. 安装依赖：`npm install`
2. 开发模式：`npm run dev`
3. 构建生产版本：`npm run build`

## 性能优化

- 使用Vue组件的`v-memo`和`v-once`减少不必要的重渲染
- 懒加载和按需加载图表
- 使用CSS变量实现主题一致性
- 轻量级动画效果，避免性能消耗
- CSS pointer-events优化叠层组件的点击处理

## 请求处理

项目配置了统一的请求拦截器，实现了：

- 统一请求头管理
- Token处理
- 错误处理
- 响应格式兼容

## 未来计划

- 添加实时数据源连接
- 增加地图标注和地理信息显示
- 提供更多交互功能和动画效果
- 支持暗/亮主题切换
- 优化移动端适配

## 项目概述

这是一个基于Vue 3和ECharts的大屏数据可视化项目，展示城市安全监管数据。

## 组件结构

- `PanelBox.vue`: 统一的面板容器组件
- `ChartBox.vue`: 图表组件，封装了ECharts的渲染和更新逻辑
- `NavIcon.vue`: 导航图标组件
- `StatCard.vue`: 统计卡片组件

## 大屏适配方案优化记录

### 1. 初始适配方案（基于比例缩放）

初始方案使用了基于最小缩放比例的适配方法：

```javascript
// 计算缩放比例
const widthScale = currentWidth / baseWidth
const heightScale = currentHeight / baseHeight

// 取最小的缩放比例，确保内容完全显示
scale.value = Math.min(widthScale, heightScale)

// 计算中心点偏移
const translateX = (currentWidth / scale.value - baseWidth) / 2
const translateY = (currentHeight / scale.value - baseHeight) / 2
```

**优点**：确保内容完整显示，不会裁剪
**缺点**：在不同比例的屏幕上会出现上下或左右留白

### 2. 改进适配方案（填充模式）

改进后的方案采用填充模式，确保内容始终填满屏幕：

```javascript
// 策略：填充模式，使内容始终填满屏幕
scaleVal = Math.max(currentWidth / baseWidth, currentHeight / baseHeight)

// 计算中心偏移量，使内容居中
translateX = (currentWidth - baseWidth * scaleVal) / (2 * scaleVal)
translateY = (currentHeight - baseHeight * scaleVal) / (2 * scaleVal)
```

**优点**：
- 在所有屏幕比例（16:9、21:9、32:9）下都能填满屏幕
- 没有留白区域
- 内容始终保持居中显示

**缺点**：
- 在某些极端比例下，可能导致部分内容超出视口范围
- 需要在布局设计时考虑核心内容不要靠近边缘

### 3. 更进一步：可配置的适配模式（灵活适配）

为了满足不同场景的需求，实现了可配置的适配模式：

```javascript
export function useScreen(screenRef, options = {}) {
  const {
    mode = 'cover',
    baseWidth = 3840,
    baseHeight = 1080
  } = options
  
  // ... 其他代码 ...
  
  // 根据模式选择缩放策略
  if (mode === 'contain') {
    // 包含模式 - 确保内容完全可见，可能有留白
    scaleVal = Math.min(currentWidth / baseWidth, currentHeight / baseHeight)
  } else { // cover模式
    // 填充模式 - 确保屏幕填满，可能裁剪部分内容
    scaleVal = Math.max(currentWidth / baseWidth, currentHeight / baseHeight)
  }
}
```

添加了运行时切换适配模式的功能：
- 界面右下角添加了适配模式指示器和切换按钮
- 支持通过快捷键 `Alt+M` 快速切换适配模式
- 适配模式信息会在控制台打印，便于调试

**填充模式(Cover)**：
- 屏幕完全填满，无留白
- 在极端比例下可能裁剪部分内容
- 适合展示大屏，视觉效果优先

**包含模式(Contain)**：
- 确保所有内容可见
- 可能在两侧或上下有留白
- 适合内容完整性优先的场景

### 使用方法

```javascript
// 使用默认配置（填充模式）
const { scale } = useScreen(screenRef)

// 使用自定义配置
const { scale, resize } = useScreen(screenRef, {
  mode: 'contain', // 'contain' 或 'cover'
  baseWidth: 1920,  // 设计基准宽度
  baseHeight: 1080  // 设计基准高度
})

// 动态切换适配模式
const toggleMode = () => {
  currentMode = currentMode === 'cover' ? 'contain' : 'cover'
  resize() // 应用新的缩放策略
}
```

### 最佳实践

1. **设计时的注意事项**：
   - 核心内容应放置在中心区域，避免在填充模式下被裁剪
   - 边缘区域可放置次要信息或装饰元素

2. **适配策略选择**：
   - 展示大屏：优先选择填充模式(cover)
   - 操作界面：优先选择包含模式(contain)
   - 考虑为用户提供切换选项

3. **测试验证**：
   - 在多种屏幕比例下测试（16:9、21:9、32:9等）
   - 验证核心内容在各种模式下的可见性

### 适配原理解析

1. **使用max而非min**：
   - `Math.min`会导致"包含模式"，确保所有内容可见，但会产生留白
   - `Math.max`实现"填充模式"，确保屏幕被填满，可能裁剪部分内容

2. **计算偏移量**：
   - 缩放后的内容尺寸为`baseWidth * scaleVal`和`baseHeight * scaleVal`
   - 计算实际屏幕尺寸与缩放后内容的差值，得到总偏移量
   - 除以2使内容居中，再除以缩放比例得到实际偏移距离

3. **变换顺序**：
   - 先缩放(scale)，再平移(translate)
   - 变换原点设置为左上角，便于计算

### 适用场景

此适配方案适用于大屏展示，特别是数据可视化场景，能在不同比例的显示设备上提供一致的填满屏幕的体验。

### 4. 响应式布局适配（优化版）

在进一步分析需求后，我们发现之前的缩放方案存在一些局限性。对于真正的大屏显示，更合理的方案是使用响应式布局，而不是整体缩放。

新的适配方案采用了响应式布局原则：

```css
.screen-container {
  width: 100vw; /* 使用视口宽度 */
  height: 100vh; /* 使用视口高度 */
  position: relative;
  overflow: hidden;
}

.main-content {
  display: grid;
  grid-template-columns: 380px minmax(0, 1fr) 380px; /* 两侧固定宽度，中间自适应 */
  gap: 20px;
}
```

**原理**：
- 使用CSS网格布局(Grid)实现两侧面板固定宽度，中间区域自适应
- 使用`minmax(0, 1fr)`确保中间区域能正确伸缩
- 根据不同屏幕宽度，使用媒体查询调整两侧面板的宽度和间距

**优点**：
- 充分利用屏幕空间，无论什么比例都不会出现滚动条
- 两侧面板保持合理尺寸和可读性，不会失真
- 中间区域自适应伸缩，更好地适应不同比例的屏幕
- 不需要JavaScript计算和处理缩放比例

**媒体查询适配**：

```css
/* 16:9 到 21:9 的屏幕 */
@media (min-width: 1920px) and (max-width: 2560px) {
  .main-content {
    grid-template-columns: 380px minmax(0, 1fr) 380px;
  }
}

/* 21:9 的超宽屏 */
@media (min-width: 2561px) and (max-width: 3440px) {
  .main-content {
    grid-template-columns: 420px minmax(0, 1fr) 420px;
  }
}

/* 32:9 的超宽屏 */
@media (min-width: 3441px) {
  .main-content {
    grid-template-columns: 450px minmax(0, 1fr) 450px;
  }
}
```

此方案在各种比例的显示设备上(16:9, 21:9, 32:9)都能提供最佳的显示效果，保证两侧面板清晰可见，中间内容自适应填充，同时避免了整体缩放可能带来的变形和比例问题。

## 请求处理优化

### 1. 请求拦截器配置

在 `src/utils/request.js` 中配置了统一的请求拦截器，主要功能包括：

```javascript
// 请求拦截器
service.interceptors.request.use(
  config => {
    // 强制设置请求头
    config.headers['Content-Type'] = 'application/json'
    config.headers['Connection'] = 'close'
    
    // 获取token
    const token = localStorage.getItem('Admin-Token')
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`
    }
    
    return config
  },
  error => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)
```

### 2. 响应拦截器配置

响应拦截器处理了不同格式的响应数据：

```javascript
// 响应拦截器
service.interceptors.response.use(
  response => {
    // 直接返回完整响应，不进行数据提取
    return response
  },
  error => {
    // 处理302重定向
    if (error.response && error.response.status === 302) {
      const redirectUrl = error.response.headers.location
      if (redirectUrl) {
        window.location.href = redirectUrl
        return
      }
    }
    return Promise.reject(error)
  }
)
```

### 3. 登录流程优化

在 `src/stores/user.js` 中优化了登录流程：

```javascript
const login = (loginData) => {
  return new Promise((resolve, reject) => {
    const { username, password } = loginData
    const encrypt = new JSEncrypt()
    encrypt.setPublicKey(PUBLIC_KEY)
    const newPassword = encrypt.encrypt(password)
    
    loginAuth({
      username,
      password: newPassword
    })
      .then(response => {
        // 处理嵌套的response.data.data结构
        if (response.data && response.data.data && response.data.data.accessToken) {
          const accessToken = response.data.data.accessToken
          localStorage.setItem(TOKEN, accessToken)
          token.value = accessToken
          setTimeStamp()
          resolve()
        } else if (response.data && response.data.accessToken) {
          // 兼容处理直接在response.data中的accessToken
          const accessToken = response.data.accessToken
          localStorage.setItem(TOKEN, accessToken)
          token.value = accessToken
          setTimeStamp()
          resolve()
        } else {
          console.error('登录响应中未找到token', response)
          reject(new Error('登录响应格式错误'))
        }
      })
      .catch(error => {
        reject(error)
      })
  })
}
```

### 4. 代理配置优化

在 `vite.config.js` 中优化了代理配置：

```javascript
server: {
  proxy: {
    '/api': {
      target: 'http://***********:32021',
      changeOrigin: true,
      rewrite: (path) => path.replace(/^\/api/, ''),
      timeout: 50000,
      followRedirects: true
    }
  }
}
```

### 5. 请求处理最佳实践

1. **统一请求头管理**：
   - 在请求拦截器中统一设置 `Content-Type` 和 `Connection` 头
   - 确保所有请求使用相同的请求头配置

2. **Token 处理**：
   - 登录成功后从响应中提取 token
   - 支持嵌套的响应数据结构
   - 将 token 存储在 localStorage 中
   - 在请求拦截器中自动添加 token 到请求头

3. **错误处理**：
   - 统一处理 302 重定向
   - 提供详细的错误日志
   - 支持自定义错误处理逻辑

4. **响应格式兼容**：
   - 支持多种响应格式（嵌套的 data 结构）
   - 提供灵活的响应处理机制
   - 保持向后兼容性

## 安装与使用

```bash
# 安装依赖
npm install

# 开发模式运行
npm run dev

# 构建生产版本
npm run build
```

### 兼容性说明

本项目使用 Vite + Vue 3 + Tailwind CSS 构建，需要：

- Node.js 16.x 或更高版本
- 使用 ESM 模块系统
- 确保依赖版本与 package.json 一致，特别是：
  - Tailwind CSS: ^3.4.1
  - PostCSS: ^8.5.3
  - Vite: ^6.2.0

如遇到 "Failed to load PostCSS config" 类型错误，请尝试：
1. 删除 node_modules 文件夹
2. 清理 npm 缓存：`npm cache clean --force`
3. 重新安装依赖：`npm install`

# 环境配置说明

项目支持开发环境和生产环境的不同配置。

## 环境变量文件

- `.env.development`: 开发环境配置
- `.env.production`: 生产环境配置

## 环境变量说明

- `VITE_APP_TITLE`: 应用标题
- `VITE_API_BASE_URL`: API基础URL
- `VITE_APP_BASE_API`: API请求基础路径
- `NODE_ENV`: 环境类型
- `VITE_APP_PORT`: 开发服务器端口（仅开发环境）
- `VITE_APP_PROXY_TARGET`: API代理目标地址

## 构建命令

- `npm run dev`: 以开发模式启动项目
- `npm run build`: 构建生产环境版本
- `npm run build:dev`: 构建开发环境版本
- `npm run build:prod`: 构建生产环境版本（同npm run build）
- `npm run preview`: 本地预览构建后的项目

## 添加自定义环境变量

在相应的环境文件中添加以`VITE_`为前缀的变量，例如：
```
VITE_CUSTOM_VARIABLE=custom_value
```

在代码中可以通过`import.meta.env.VITE_CUSTOM_VARIABLE`访问该变量。

## 燃气专项mis端菜单
燃气首页
燃气信息资源管理
  燃气信息资源管理
    燃气管网基础信息管理
    燃气场站基础信息管理
    窨井基础信息管理
    危险源信息查询
    防护目标信息查询
    燃气监测设备信息查询
燃气管网爆炸风险评估
  燃气管网爆炸风险评估
    燃气管网爆炸风险评估管理
    燃气管网爆炸风险热力图
燃气泄漏实时监测与报警
  燃气泄漏实时监测与报警
    报警阈值管理
    视频监控监测
    设备运行监测
    燃气泄漏报警提醒
    燃气泄漏报警分析
    燃气泄漏报警处置
燃气泄漏爆炸预测预警
  燃气泄漏爆炸预测预警
    可燃气体泄漏溯源分析
    可燃气体扩散范围分析
    爆炸损伤范围分析
燃气泄漏爆炸辅助决策
  燃气泄漏爆炸辅助决策
    燃气泄漏处置方案管理
    燃气综合统计分析
    燃气安全运行评估报告

## 排水专项mis端菜单
排水首页
排水基础数据管理
  排水基础数据管理
    管网基础信息管理
    排水口基础信息管理
    雨水篦子基础信息管理
    排水窨井基础信息管理
    排水泵站基础信息管理
    CCTV检测信息管理
    污水厂基础信息管理
    易涝点基础信息管理
    监测设备基础信息管理
    排水基础数据统计分析
排水风险隐患管理
  排水风险隐患管理
    排水管网风险评估
    污水厂风险评估
    排水泵站风险评估
    排水隐患信息管理
排水监测报警管理
 排水监测报警管理
    报警阈值管理
    视频监控监测
    管网流量监测
    污水溢流监测
    可燃气体积累监测
    易涝点积水监测
    排污水质监测
    井盖状态监测
    排水报警信息管理
    排水报警信息处置
    排水报警统计分析
预测预警分析
  预测预警分析
    排水管网模型预测预警
    内涝模型预测预警
辅助决策支持
  辅助决策支持
    防汛调度辅助决策
    管网运维改造辅助决策
    排水安全风险评估报告
    排水应急事件管理
    防汛物资管理
    危险源信息管理
    防护目标信息管理

## 供热专项mis端菜单
供热首页
供热基础信息管理
  供热基础信息管理
    供热企业信息管理
    热源信息管理
    换热站信息管理
    管网信息管理
    机组信息管理
    供热区域建筑信息管理
    供热用户信息管理
    供热窨井信息管理
    供热设备信息管理
    基础数据统计分析
供热风险管理
  供热风险管理
    供热管网风险信息
    供热场站风险信息
    供热隐患信息管理
    供热安全事故信息管理
    危险源信息管理
    防护目标信息管理
    供热管网风险分布
    供热场站风险分布
    供热隐患分布
    防护目标分布
供热安全监测预警
  供热安全监测预警
    监测预警阈值管理
    管网运行监测预警管理
    热源运行安全风险防控
    换热站运行安全风险防控
    供热管网泄漏研判分析
    预案管理
辅助决策支持
  辅助决策支持
    态势数据分级管理
    态势数据报送
    空间分析
    态势标绘与展示

## 桥梁专项mis端菜单
桥梁首页
桥梁基础数据
  桥梁基础数据
    桥梁资产管理
    桥梁构件管理
    桥梁布点方案
桥梁设备管理
  桥梁设备管理
    桥梁设备信息
    设备分组管理
桥梁实时监测    
  桥梁实时监测    
    桥梁视频监控
    环境数据监测与分析
    静态响应监测与分析
    动态响应监测与分析
    意外震动监测与分析
    交通荷载监测与分析
桥梁监测报警管理
  桥梁监测报警管理
    监测报警阈值管理
    桥梁报警信息管理
    桥梁报警信息处置
桥梁检测养护管理
  桥梁检测养护管理
    检测养护计划管理
    检测养护记录管理
    检测报告管理
    维修养护数据管理
    病害数据管理
桥梁数据分析
  桥梁数据分析
    极值分析
    对比分析
    关联分析
    多通道分析
    挠度分析
    多设备频谱分析
    多时段频谱分析
    滤波分析
桥梁数据统计分析
  桥梁数据统计分析
    桥梁信息统计与分析
    报警信息统计与分析
    安全评估统计与分析
    超载数据统计与分析
    监测数据统计与分析
桥梁安全评估
  桥梁安全评估
    安全评估报告
    桥梁安全评分

## 综合专项mis端菜单
行业综合监管
  行业综合监管
    通知公告
    规划建设
    督查督办
    考核评价
    专家咨询
    资料中心
面向公众服务
  面向公众服务
    报警通报
    公众报警
事件分析研判
  事件分析研判
    应急事件管理
    应急事件统计
协同联动处置
  协同联动处置
    预警信息管理
    预警信息处置
综合风险管控
  综合风险管控
    隐患排查治理
    风险评估标识
    风险区域划分
    风险四色图
应急联动管理
  应急联动管理
    应急预案管理
    应急资源管理
    应急专家管理
设备运维管理
  设备运维管理
    值班管理
    巡检管理
    巡检工单
    设备维修
    设备监控

## 系统管理mis端菜单
系统管理
  系统管理
    用户管理
    组织管理
    角色管理
    登陆日志