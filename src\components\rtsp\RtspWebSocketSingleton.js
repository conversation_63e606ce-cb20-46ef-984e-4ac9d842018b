import RtspWebSocketManager from './RtspWebSocketManager';

/**
 * RTSP WebSocket管理器单例
 * 确保整个应用共享同一个WebSocket连接
 */
class RtspWebSocketSingleton {
  static instance = null;
  
  /**
   * 获取RTSP WebSocket管理器实例
   * @param {string} wsUrl WebSocket服务器地址
   * @returns {RtspWebSocketManager} WebSocket管理器实例
   */
  static getInstance(wsUrl) {
    if (!RtspWebSocketSingleton.instance && wsUrl) {
      RtspWebSocketSingleton.instance = new RtspWebSocketManager(wsUrl);
    }
    return RtspWebSocketSingleton.instance;
  }
  
  /**
   * 销毁当前实例
   */
  static destroyInstance() {
    if (RtspWebSocketSingleton.instance) {
      RtspWebSocketSingleton.instance.disconnect();
      RtspWebSocketSingleton.instance = null;
    }
  }
}

export default RtspWebSocketSingleton; 