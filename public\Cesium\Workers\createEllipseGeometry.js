/**
 *    ████████                   ██                   ██       ██                  ██      ██
 *   ██░░░░░░██                 ░░                   ░██      ░██                 ░██     ░██
 *  ██      ░░   █████  ███████  ██ ██   ██  ██████  ░██   █  ░██  ██████  ██████ ░██     ░██
 * ░██          ██░░░██░░██░░░██░██░██  ░██ ██░░░░   ░██  ███ ░██ ██░░░░██░░██░░█ ░██  ██████
 * ░██    █████░███████ ░██  ░██░██░██  ░██░░█████   ░██ ██░██░██░██   ░██ ░██ ░  ░██ ██░░░██
 * ░░██  ░░░░██░██░░░░  ░██  ░██░██░██  ░██ ░░░░░██  ░████ ░░████░██   ░██ ░██    ░██░██  ░██
 *  ░░████████ ░░██████ ███  ░██░██░░██████ ██████   ░██░   ░░░██░░██████ ░███    ███░░██████
 *   ░░░░░░░░   ░░░░░░ ░░░   ░░ ░░  ░░░░░░ ░░░░░░    ░░       ░░  ░░░░░░  ░░░    ░░░  ░░░░░░
 *
 * @license
 * GeniusWorld Web SDK - http://zydlxx.cn:1234/
 * Version 4.0.0.B#develop-d3ff2330@202405231107
 *
 *
 * Copyright © 2020 - 2023 正元地理信息集团股份有限公司. All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
define(["./Matrix3-79d15570","./defaultValue-7b61670d","./EllipseGeometry-158fdbc2","./Math-6acd1674","./Transforms-6a5d79d3","./Matrix2-d550732e","./RuntimeError-7dc4ea5a","./combine-bc3d0d90","./ComponentDatatype-e95dda25","./WebGLConstants-68839929","./EllipseGeometryLibrary-48098a0e","./GeometryAttribute-d24f9032","./GeometryAttributes-410c425f","./GeometryInstance-b3218e0a","./GeometryOffsetAttribute-4f73a4af","./GeometryPipeline-ab8f5560","./AttributeCompression-aa106b76","./EncodedCartesian3-e2f2e578","./IndexDatatype-7c192505","./IntersectionTests-044bd161","./Plane-e4eb0e88","./VertexFormat-6d750b6e"],(function(e,t,r,a,n,i,o,d,s,l,c,b,m,f,p,u,y,G,E,C,x,A){"use strict";return function(a,n){return t.defined(n)&&(a=r.EllipseGeometry.unpack(a,n)),a._center=e.Cartesian3.clone(a._center),a._ellipsoid=e.Ellipsoid.clone(a._ellipsoid),r.EllipseGeometry.createGeometry(a)}}));
