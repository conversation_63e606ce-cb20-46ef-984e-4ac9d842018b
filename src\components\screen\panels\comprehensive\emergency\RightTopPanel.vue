<template>
  <PanelBox title="应急资源统计">
    <div class="panel-content">
      <div class="stats-grid">
        <div class="stats-item">
          <div class="item-title">避难场所</div>
          <div class="item-value">9</div>
          <div class="item-bg"></div>
        </div>
        <div class="stats-item">
          <div class="item-title">应急队伍</div>
          <div class="item-value">21</div>
          <div class="item-bg"></div>
        </div>
        <div class="stats-item">
          <div class="item-title">物资装备</div>
          <div class="item-value">12613</div>
          <div class="item-bg"></div>
        </div>
        <div class="stats-item">
          <div class="item-title">救援人员</div>
          <div class="item-value">49</div>
          <div class="item-bg"></div>
        </div>
        <div class="stats-item">
          <div class="item-title">医疗机构</div>
          <div class="item-value">12</div>
          <div class="item-bg"></div>
        </div>
        <div class="stats-item">
          <div class="item-title">应急仓库</div>
          <div class="item-value">17</div>
          <div class="item-bg"></div>
        </div>
      </div>
    </div>
  </PanelBox>
</template>

<script setup>
import PanelBox from '@/components/screen/PanelBox.vue'
// 综合态势总览右上面板组件
</script>

<style scoped>
.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 15px;
  height: 100%;
}

.stats-item {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.item-title {
  position: relative;
  z-index: 2;
  width: 112px;
  height: 27px;
  background: url('@/assets/images/screen/comprehensive/title_bg.png') no-repeat center center;
  background-size: 100% 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: PingFangSC, "PingFang SC";
  font-weight: 500;
  font-size: 14px;
  color: #D7F2FF;
}

.item-value {
  position: relative;
  z-index: 2;
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 26px;
  color: #FFFFFF;
  margin: 0.5rem 0px 1rem 0px;;
}

.item-bg {
  position: absolute;
  bottom: 0;
  width: 108px;
  height: 69px;
  background: url('@/assets/images/screen/comprehensive/tongji_bg.png') no-repeat center center;
  background-size: 100% 100%;
  z-index: 1;
}
</style> 