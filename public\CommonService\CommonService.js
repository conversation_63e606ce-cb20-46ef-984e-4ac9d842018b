/*!
 * Genius World 通用查询、分析接口
 *  version:4.0.0.B#28d1adcf@202405281209
 * 版权所有：Copyright by 正元地信 http://www.geniuses.com.cn
 */
(function webpackUniversalModuleDefinition(root, factory) {
	if(typeof exports === 'object' && typeof module === 'object')
		module.exports = factory();
	else if(typeof define === 'function' && define.amd)
		define([], factory);
	else if(typeof exports === 'object')
		exports["CommonService"] = factory();
	else
		root["CommonService"] = factory();
})(self, () => {
return /******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ "./src/RegisterServer.js":
/*!*******************************!*\
  !*** ./src/RegisterServer.js ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _SupportedVersion_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./SupportedVersion.js */ "./src/SupportedVersion.js");
function _typeof(obj) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && "function" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }, _typeof(obj); }
function _regeneratorRuntime() { "use strict"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return exports; }; var exports = {}, Op = Object.prototype, hasOwn = Op.hasOwnProperty, defineProperty = Object.defineProperty || function (obj, key, desc) { obj[key] = desc.value; }, $Symbol = "function" == typeof Symbol ? Symbol : {}, iteratorSymbol = $Symbol.iterator || "@@iterator", asyncIteratorSymbol = $Symbol.asyncIterator || "@@asyncIterator", toStringTagSymbol = $Symbol.toStringTag || "@@toStringTag"; function define(obj, key, value) { return Object.defineProperty(obj, key, { value: value, enumerable: !0, configurable: !0, writable: !0 }), obj[key]; } try { define({}, ""); } catch (err) { define = function define(obj, key, value) { return obj[key] = value; }; } function wrap(innerFn, outerFn, self, tryLocsList) { var protoGenerator = outerFn && outerFn.prototype instanceof Generator ? outerFn : Generator, generator = Object.create(protoGenerator.prototype), context = new Context(tryLocsList || []); return defineProperty(generator, "_invoke", { value: makeInvokeMethod(innerFn, self, context) }), generator; } function tryCatch(fn, obj, arg) { try { return { type: "normal", arg: fn.call(obj, arg) }; } catch (err) { return { type: "throw", arg: err }; } } exports.wrap = wrap; var ContinueSentinel = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var IteratorPrototype = {}; define(IteratorPrototype, iteratorSymbol, function () { return this; }); var getProto = Object.getPrototypeOf, NativeIteratorPrototype = getProto && getProto(getProto(values([]))); NativeIteratorPrototype && NativeIteratorPrototype !== Op && hasOwn.call(NativeIteratorPrototype, iteratorSymbol) && (IteratorPrototype = NativeIteratorPrototype); var Gp = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(IteratorPrototype); function defineIteratorMethods(prototype) { ["next", "throw", "return"].forEach(function (method) { define(prototype, method, function (arg) { return this._invoke(method, arg); }); }); } function AsyncIterator(generator, PromiseImpl) { function invoke(method, arg, resolve, reject) { var record = tryCatch(generator[method], generator, arg); if ("throw" !== record.type) { var result = record.arg, value = result.value; return value && "object" == _typeof(value) && hasOwn.call(value, "__await") ? PromiseImpl.resolve(value.__await).then(function (value) { invoke("next", value, resolve, reject); }, function (err) { invoke("throw", err, resolve, reject); }) : PromiseImpl.resolve(value).then(function (unwrapped) { result.value = unwrapped, resolve(result); }, function (error) { return invoke("throw", error, resolve, reject); }); } reject(record.arg); } var previousPromise; defineProperty(this, "_invoke", { value: function value(method, arg) { function callInvokeWithMethodAndArg() { return new PromiseImpl(function (resolve, reject) { invoke(method, arg, resolve, reject); }); } return previousPromise = previousPromise ? previousPromise.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(innerFn, self, context) { var state = "suspendedStart"; return function (method, arg) { if ("executing" === state) throw new Error("Generator is already running"); if ("completed" === state) { if ("throw" === method) throw arg; return doneResult(); } for (context.method = method, context.arg = arg;;) { var delegate = context.delegate; if (delegate) { var delegateResult = maybeInvokeDelegate(delegate, context); if (delegateResult) { if (delegateResult === ContinueSentinel) continue; return delegateResult; } } if ("next" === context.method) context.sent = context._sent = context.arg;else if ("throw" === context.method) { if ("suspendedStart" === state) throw state = "completed", context.arg; context.dispatchException(context.arg); } else "return" === context.method && context.abrupt("return", context.arg); state = "executing"; var record = tryCatch(innerFn, self, context); if ("normal" === record.type) { if (state = context.done ? "completed" : "suspendedYield", record.arg === ContinueSentinel) continue; return { value: record.arg, done: context.done }; } "throw" === record.type && (state = "completed", context.method = "throw", context.arg = record.arg); } }; } function maybeInvokeDelegate(delegate, context) { var methodName = context.method, method = delegate.iterator[methodName]; if (undefined === method) return context.delegate = null, "throw" === methodName && delegate.iterator["return"] && (context.method = "return", context.arg = undefined, maybeInvokeDelegate(delegate, context), "throw" === context.method) || "return" !== methodName && (context.method = "throw", context.arg = new TypeError("The iterator does not provide a '" + methodName + "' method")), ContinueSentinel; var record = tryCatch(method, delegate.iterator, context.arg); if ("throw" === record.type) return context.method = "throw", context.arg = record.arg, context.delegate = null, ContinueSentinel; var info = record.arg; return info ? info.done ? (context[delegate.resultName] = info.value, context.next = delegate.nextLoc, "return" !== context.method && (context.method = "next", context.arg = undefined), context.delegate = null, ContinueSentinel) : info : (context.method = "throw", context.arg = new TypeError("iterator result is not an object"), context.delegate = null, ContinueSentinel); } function pushTryEntry(locs) { var entry = { tryLoc: locs[0] }; 1 in locs && (entry.catchLoc = locs[1]), 2 in locs && (entry.finallyLoc = locs[2], entry.afterLoc = locs[3]), this.tryEntries.push(entry); } function resetTryEntry(entry) { var record = entry.completion || {}; record.type = "normal", delete record.arg, entry.completion = record; } function Context(tryLocsList) { this.tryEntries = [{ tryLoc: "root" }], tryLocsList.forEach(pushTryEntry, this), this.reset(!0); } function values(iterable) { if (iterable) { var iteratorMethod = iterable[iteratorSymbol]; if (iteratorMethod) return iteratorMethod.call(iterable); if ("function" == typeof iterable.next) return iterable; if (!isNaN(iterable.length)) { var i = -1, next = function next() { for (; ++i < iterable.length;) if (hasOwn.call(iterable, i)) return next.value = iterable[i], next.done = !1, next; return next.value = undefined, next.done = !0, next; }; return next.next = next; } } return { next: doneResult }; } function doneResult() { return { value: undefined, done: !0 }; } return GeneratorFunction.prototype = GeneratorFunctionPrototype, defineProperty(Gp, "constructor", { value: GeneratorFunctionPrototype, configurable: !0 }), defineProperty(GeneratorFunctionPrototype, "constructor", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, toStringTagSymbol, "GeneratorFunction"), exports.isGeneratorFunction = function (genFun) { var ctor = "function" == typeof genFun && genFun.constructor; return !!ctor && (ctor === GeneratorFunction || "GeneratorFunction" === (ctor.displayName || ctor.name)); }, exports.mark = function (genFun) { return Object.setPrototypeOf ? Object.setPrototypeOf(genFun, GeneratorFunctionPrototype) : (genFun.__proto__ = GeneratorFunctionPrototype, define(genFun, toStringTagSymbol, "GeneratorFunction")), genFun.prototype = Object.create(Gp), genFun; }, exports.awrap = function (arg) { return { __await: arg }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, asyncIteratorSymbol, function () { return this; }), exports.AsyncIterator = AsyncIterator, exports.async = function (innerFn, outerFn, self, tryLocsList, PromiseImpl) { void 0 === PromiseImpl && (PromiseImpl = Promise); var iter = new AsyncIterator(wrap(innerFn, outerFn, self, tryLocsList), PromiseImpl); return exports.isGeneratorFunction(outerFn) ? iter : iter.next().then(function (result) { return result.done ? result.value : iter.next(); }); }, defineIteratorMethods(Gp), define(Gp, toStringTagSymbol, "Generator"), define(Gp, iteratorSymbol, function () { return this; }), define(Gp, "toString", function () { return "[object Generator]"; }), exports.keys = function (val) { var object = Object(val), keys = []; for (var key in object) keys.push(key); return keys.reverse(), function next() { for (; keys.length;) { var key = keys.pop(); if (key in object) return next.value = key, next.done = !1, next; } return next.done = !0, next; }; }, exports.values = values, Context.prototype = { constructor: Context, reset: function reset(skipTempReset) { if (this.prev = 0, this.next = 0, this.sent = this._sent = undefined, this.done = !1, this.delegate = null, this.method = "next", this.arg = undefined, this.tryEntries.forEach(resetTryEntry), !skipTempReset) for (var name in this) "t" === name.charAt(0) && hasOwn.call(this, name) && !isNaN(+name.slice(1)) && (this[name] = undefined); }, stop: function stop() { this.done = !0; var rootRecord = this.tryEntries[0].completion; if ("throw" === rootRecord.type) throw rootRecord.arg; return this.rval; }, dispatchException: function dispatchException(exception) { if (this.done) throw exception; var context = this; function handle(loc, caught) { return record.type = "throw", record.arg = exception, context.next = loc, caught && (context.method = "next", context.arg = undefined), !!caught; } for (var i = this.tryEntries.length - 1; i >= 0; --i) { var entry = this.tryEntries[i], record = entry.completion; if ("root" === entry.tryLoc) return handle("end"); if (entry.tryLoc <= this.prev) { var hasCatch = hasOwn.call(entry, "catchLoc"), hasFinally = hasOwn.call(entry, "finallyLoc"); if (hasCatch && hasFinally) { if (this.prev < entry.catchLoc) return handle(entry.catchLoc, !0); if (this.prev < entry.finallyLoc) return handle(entry.finallyLoc); } else if (hasCatch) { if (this.prev < entry.catchLoc) return handle(entry.catchLoc, !0); } else { if (!hasFinally) throw new Error("try statement without catch or finally"); if (this.prev < entry.finallyLoc) return handle(entry.finallyLoc); } } } }, abrupt: function abrupt(type, arg) { for (var i = this.tryEntries.length - 1; i >= 0; --i) { var entry = this.tryEntries[i]; if (entry.tryLoc <= this.prev && hasOwn.call(entry, "finallyLoc") && this.prev < entry.finallyLoc) { var finallyEntry = entry; break; } } finallyEntry && ("break" === type || "continue" === type) && finallyEntry.tryLoc <= arg && arg <= finallyEntry.finallyLoc && (finallyEntry = null); var record = finallyEntry ? finallyEntry.completion : {}; return record.type = type, record.arg = arg, finallyEntry ? (this.method = "next", this.next = finallyEntry.finallyLoc, ContinueSentinel) : this.complete(record); }, complete: function complete(record, afterLoc) { if ("throw" === record.type) throw record.arg; return "break" === record.type || "continue" === record.type ? this.next = record.arg : "return" === record.type ? (this.rval = this.arg = record.arg, this.method = "return", this.next = "end") : "normal" === record.type && afterLoc && (this.next = afterLoc), ContinueSentinel; }, finish: function finish(finallyLoc) { for (var i = this.tryEntries.length - 1; i >= 0; --i) { var entry = this.tryEntries[i]; if (entry.finallyLoc === finallyLoc) return this.complete(entry.completion, entry.afterLoc), resetTryEntry(entry), ContinueSentinel; } }, "catch": function _catch(tryLoc) { for (var i = this.tryEntries.length - 1; i >= 0; --i) { var entry = this.tryEntries[i]; if (entry.tryLoc === tryLoc) { var record = entry.completion; if ("throw" === record.type) { var thrown = record.arg; resetTryEntry(entry); } return thrown; } } throw new Error("illegal catch attempt"); }, delegateYield: function delegateYield(iterable, resultName, nextLoc) { return this.delegate = { iterator: values(iterable), resultName: resultName, nextLoc: nextLoc }, "next" === this.method && (this.arg = undefined), ContinueSentinel; } }, exports; }
function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }
function _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err); } _next(undefined); }); }; }
function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }
function _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }
function _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, "prototype", { writable: false }); return Constructor; }
function _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }
function _toPropertyKey(arg) { var key = _toPrimitive(arg, "string"); return _typeof(key) === "symbol" ? key : String(key); }
function _toPrimitive(input, hint) { if (_typeof(input) !== "object" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || "default"); if (_typeof(res) !== "object") return res; throw new TypeError("@@toPrimitive must return a primitive value."); } return (hint === "string" ? String : Number)(input); }


/**
 * 注册服务端管理，用于版本控制
 * @private
 */
var RegisterServer = /*#__PURE__*/function () {
  function RegisterServer() {
    _classCallCheck(this, RegisterServer);
  }
  _createClass(RegisterServer, null, [{
    key: "parseServiceLicenseUri",
    value: function parseServiceLicenseUri(serviceRootUri) {
      var url = new URL("./license", serviceRootUri);
      url.search = "";
      url.hash = "";
      return url.toString();
    }
  }, {
    key: "registerServer",
    value: function registerServer(serviceAnyUrl) {
      var licenseUrl = RegisterServer.parseServiceLicenseUri(serviceAnyUrl);
      // 没有reject，全部resolve
      return new Promise(function (resolve, reject) {
        fetch(licenseUrl).then(function (r) {
          return r.json();
        }).then(function (licenceInfo) {
          var version = _SupportedVersion_js__WEBPACK_IMPORTED_MODULE_0__["default"].getVersionFromResponse(licenceInfo);
          RegisterServer.registeredServer[licenseUrl] = version;
          RegisterServer.registeredServer[serviceAnyUrl] = version;
          resolve(version);
        })["catch"](function (error) {
          reject(error);
        });
      });
    }
  }, {
    key: "getVersionByServiceAnyURI",
    value: function () {
      var _getVersionByServiceAnyURI = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee(uri) {
        var version, key;
        return _regeneratorRuntime().wrap(function _callee$(_context) {
          while (1) switch (_context.prev = _context.next) {
            case 0:
              version = RegisterServer.registeredServer[uri];
              if (!version) {
                _context.next = 3;
                break;
              }
              return _context.abrupt("return", version);
            case 3:
              key = RegisterServer.parseServiceLicenseUri(uri);
              version = RegisterServer.registeredServer[key];
              if (version) {
                _context.next = 9;
                break;
              }
              _context.next = 8;
              return RegisterServer.registerServer(uri);
            case 8:
              return _context.abrupt("return", _context.sent);
            case 9:
              return _context.abrupt("return", version);
            case 10:
            case "end":
              return _context.stop();
          }
        }, _callee);
      }));
      function getVersionByServiceAnyURI(_x) {
        return _getVersionByServiceAnyURI.apply(this, arguments);
      }
      return getVersionByServiceAnyURI;
    }()
  }]);
  return RegisterServer;
}();
_defineProperty(RegisterServer, "registeredServer", {});
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (RegisterServer);

/***/ }),

/***/ "./src/SupportedVersion.js":
/*!*********************************!*\
  !*** ./src/SupportedVersion.js ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }
function _nonIterableRest() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }
function _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : "undefined" != typeof Symbol && arr[Symbol.iterator] || arr["@@iterator"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i["return"] && (_r = _i["return"](), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }
function _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }
function _createForOfIteratorHelper(o, allowArrayLike) { var it = typeof Symbol !== "undefined" && o[Symbol.iterator] || o["@@iterator"]; if (!it) { if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === "number") { if (it) o = it; var i = 0; var F = function F() {}; return { s: F, n: function n() { if (i >= o.length) return { done: true }; return { done: false, value: o[i++] }; }, e: function e(_e2) { throw _e2; }, f: F }; } throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); } var normalCompletion = true, didErr = false, err; return { s: function s() { it = it.call(o); }, n: function n() { var step = it.next(); normalCompletion = step.done; return step; }, e: function e(_e3) { didErr = true; err = _e3; }, f: function f() { try { if (!normalCompletion && it["return"] != null) it["return"](); } finally { if (didErr) throw err; } } }; }
function _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }
function _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }
var licenseValidRegex = new RegExp("^License OK$", "");
function getByValue(map, searchValue) {
  var _iterator = _createForOfIteratorHelper(map.entries()),
    _step;
  try {
    for (_iterator.s(); !(_step = _iterator.n()).done;) {
      var _step$value = _slicedToArray(_step.value, 2),
        key = _step$value[0],
        value = _step$value[1];
      if (searchValue.search(value) !== -1) {
        return key;
      }
    }
  } catch (err) {
    _iterator.e(err);
  } finally {
    _iterator.f();
  }
  var arr = searchValue.split(".");
  var _iterator2 = _createForOfIteratorHelper(map.entries()),
    _step2;
  try {
    for (_iterator2.s(); !(_step2 = _iterator2.n()).done;) {
      var _step2$value = _slicedToArray(_step2.value, 2),
        _key = _step2$value[0],
        _value = _step2$value[1];
      var values = _value.split(".");
      for (var i = 0; i < values.length; i++) {
        if (values[0] === arr[0] && values[1] === arr[1]) {
          return _key;
        }
      }
    }
  } catch (err) {
    _iterator2.e(err);
  } finally {
    _iterator2.f();
  }
  return SupportedVersion.unknown;
}
var latestVersions = new Map();
latestVersions.set(-1, "unknown");
latestVersions.set(320, "3.2.0.R");
latestVersions.set(330, "3.3.0.R");
latestVersions.set(340, "3.4.0.R");
latestVersions.set(341, "3.4.1.R");
latestVersions.set(350, "3.5.0.R");
latestVersions.set(400, "4.0.0.X");
latestVersions.set(400, "4.0.0.X");
latestVersions.set(410, "4.1.0.X");
latestVersions.set(420, "4.2.0.X");
latestVersions.set(430, "4.3.0.X");
latestVersions.set(440, "4.4.0.X");
latestVersions.set(450, "4.5.0.X");
latestVersions.set(460, "4.6.0.X");
latestVersions.set(500, "5.0.0.X");
latestVersions.set(510, "5.1.0.X");
latestVersions.set(520, "5.2.0.X");
var SupportedVersion = Object.freeze({
  r320: 320,
  r330: 330,
  r340: 340,
  r341: 341,
  r350: 350,
  r400: 400,
  r410: 410,
  r420: 420,
  r430: 430,
  r440: 440,
  r450: 450,
  r460: 460,
  r500: 500,
  r510: 510,
  r520: 520,
  unknown: -1,
  getVersionFromResponse: function getVersionFromResponse(licenseInfo) {
    var licenseStatus = licenseInfo["LicenseStatus"];
    if (!licenseValidRegex.test(licenseStatus === null || licenseStatus === void 0 ? void 0 : licenseStatus.StatusDescripe)) {
      return this.unknown;
    }
    var versionNumber = getByValue(latestVersions, licenseStatus.Version);
    return versionNumber;
  },
  getStringByVersionNum: function getStringByVersionNum(versionNum) {
    return latestVersions.get(versionNum) || this.unknown;
  }
});
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SupportedVersion);

/***/ }),

/***/ "./src/core/DeveloperError.js":
/*!************************************!*\
  !*** ./src/core/DeveloperError.js ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _defaultValue_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./defaultValue.js */ "./src/core/defaultValue.js");
function _typeof(obj) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && "function" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }, _typeof(obj); }
function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }
function _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }
function _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, "prototype", { writable: false }); return Constructor; }
function _toPropertyKey(arg) { var key = _toPrimitive(arg, "string"); return _typeof(key) === "symbol" ? key : String(key); }
function _toPrimitive(input, hint) { if (_typeof(input) !== "object" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || "default"); if (_typeof(res) !== "object") return res; throw new TypeError("@@toPrimitive must return a primitive value."); } return (hint === "string" ? String : Number)(input); }
function _inherits(subClass, superClass) { if (typeof superClass !== "function" && superClass !== null) { throw new TypeError("Super expression must either be null or a function"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); Object.defineProperty(subClass, "prototype", { writable: false }); if (superClass) _setPrototypeOf(subClass, superClass); }
function _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = _getPrototypeOf(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _getPrototypeOf(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _possibleConstructorReturn(this, result); }; }
function _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === "object" || typeof call === "function")) { return call; } else if (call !== void 0) { throw new TypeError("Derived constructors may only return object or undefined"); } return _assertThisInitialized(self); }
function _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError("this hasn't been initialised - super() hasn't been called"); } return self; }
function _wrapNativeSuper(Class) { var _cache = typeof Map === "function" ? new Map() : undefined; _wrapNativeSuper = function _wrapNativeSuper(Class) { if (Class === null || !_isNativeFunction(Class)) return Class; if (typeof Class !== "function") { throw new TypeError("Super expression must either be null or a function"); } if (typeof _cache !== "undefined") { if (_cache.has(Class)) return _cache.get(Class); _cache.set(Class, Wrapper); } function Wrapper() { return _construct(Class, arguments, _getPrototypeOf(this).constructor); } Wrapper.prototype = Object.create(Class.prototype, { constructor: { value: Wrapper, enumerable: false, writable: true, configurable: true } }); return _setPrototypeOf(Wrapper, Class); }; return _wrapNativeSuper(Class); }
function _construct(Parent, args, Class) { if (_isNativeReflectConstruct()) { _construct = Reflect.construct.bind(); } else { _construct = function _construct(Parent, args, Class) { var a = [null]; a.push.apply(a, args); var Constructor = Function.bind.apply(Parent, a); var instance = new Constructor(); if (Class) _setPrototypeOf(instance, Class.prototype); return instance; }; } return _construct.apply(null, arguments); }
function _isNativeReflectConstruct() { if (typeof Reflect === "undefined" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === "function") return true; try { Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); return true; } catch (e) { return false; } }
function _isNativeFunction(fn) { return Function.toString.call(fn).indexOf("[native code]") !== -1; }
function _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }
function _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }


/**
 * Constructs an exception object that is thrown due to a developer error, e.g., invalid argument,
 * argument out of range, etc.  This exception should only be thrown during development;
 * it usually indicates a bug in the calling code.  This exception should never be
 * caught; instead the calling code should strive not to generate it.
 * <br /><br />
 * On the other hand, a {@link RuntimeError} indicates an exception that may
 * be thrown at runtime, e.g., out of memory, that the calling code should be prepared
 * to catch.
 * <br /><br />
 * 参考 {@link https://segmentfault.com/a/1190000015894152},把Cesium的ES5改造成ES6语法.
 *
 * @alias DeveloperError
 * @private
 * @extends Error
 * @see {@link RuntimeError}
 */
var DeveloperError = /*#__PURE__*/function (_Error) {
  _inherits(DeveloperError, _Error);
  var _super = _createSuper(DeveloperError);
  /**
   *
   * @param {String} [message] The error message for this exception.
   * @constructor
   */
  function DeveloperError(message) {
    var _this;
    _classCallCheck(this, DeveloperError);
    message = (0,_defaultValue_js__WEBPACK_IMPORTED_MODULE_0__["default"])(message, "developer attention please");
    _this = _super.call(this, message);
    /**
     * 'DeveloperError' indicating that this exception was thrown due to a developer error.
     * @type {String}
     * @readonly
     */
    _this.name = "DeveloperError";

    // Maintains proper stack trace for where our error was thrown (only available on V8)
    if (Error.captureStackTrace) {
      Error.captureStackTrace(_assertThisInitialized(_this), DeveloperError);
    } else {
      console.trace(message);
    }
    return _this;
  }

  /**
   * 测试jsdoc生成类的原型方法的文档生成
   * @param {Object} options 某个对象
   * @param {Number} [param2=121] 某个对象
   * @param {Object} [options.scene] 无默认值的参数.
   * @param {Number} [options.width=2] 有默认值的参数.
   * @return {Boolean} 测试返回
   */
  _createClass(DeveloperError, [{
    key: "testJSDoc",
    value: function testJSDoc(options, param2) {
      console.dir(options);
      console.dir(param2);
      return false;
    }

    /**
     * 不支持初始化的错误，用来指定父类接口
     */
  }], [{
    key: "throwInstantiationError",
    value: function throwInstantiationError() {
      throw new DeveloperError("This function defines an interface and should not be called directly.");
    }
  }]);
  return DeveloperError;
}( /*#__PURE__*/_wrapNativeSuper(Error));
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DeveloperError);

/***/ }),

/***/ "./src/core/KeysToLowerCase.js":
/*!*************************************!*\
  !*** ./src/core/KeysToLowerCase.js ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _defaultValue_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./defaultValue.js */ "./src/core/defaultValue.js");
/* harmony import */ var _RuntimeError_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./RuntimeError.js */ "./src/core/RuntimeError.js");
/* harmony import */ var _defined_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./defined.js */ "./src/core/defined.js");
function _typeof(obj) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && "function" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }, _typeof(obj); }




/**
 * 将javascript 对象的属性名从转换成小写并修改服务端的内部保留字段名称US_成us
 * @private
 * @param {Object} object 被转换对象
 * @param {Object} deep 是否递归
 * @return {*}
 */
function KeysToLowerCase(object, deep) {
  if (object === null || _typeof(object) !== "object") {
    return object;
  }
  deep = (0,_defaultValue_js__WEBPACK_IMPORTED_MODULE_0__["default"])(deep, false);
  var result = new object.constructor();
  for (var propertyName in object) {
    if (object.hasOwnProperty(propertyName)) {
      var value = object[propertyName];
      if (deep) {
        value = KeysToLowerCase(value, deep);
      }
      var lowerCasePropertyName = propertyName.toLowerCase();
      if (lowerCasePropertyName !== propertyName && (0,_defined_js__WEBPACK_IMPORTED_MODULE_2__["default"])(object[lowerCasePropertyName])) {
        console.error(object);
        throw new _RuntimeError_js__WEBPACK_IMPORTED_MODULE_1__["default"]("当前对象的键名大小写同时存在");
      }
      lowerCasePropertyName = lowerCasePropertyName.replace(/^(us_)/g, "us");
      // 肯定小写的key键了,但由于递归此处应当将值进行更新
      result[lowerCasePropertyName] = value;
    }
  }
  return result;
}
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (KeysToLowerCase);

/***/ }),

/***/ "./src/core/Resource.js":
/*!******************************!*\
  !*** ./src/core/Resource.js ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
function _typeof(obj) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && "function" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }, _typeof(obj); }
function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }
function _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }
function _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, "prototype", { writable: false }); return Constructor; }
function _toPropertyKey(arg) { var key = _toPrimitive(arg, "string"); return _typeof(key) === "symbol" ? key : String(key); }
function _toPrimitive(input, hint) { if (_typeof(input) !== "object" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || "default"); if (_typeof(res) !== "object") return res; throw new TypeError("@@toPrimitive must return a primitive value."); } return (hint === "string" ? String : Number)(input); }
function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }
function _nonIterableRest() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }
function _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }
function _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }
function _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : "undefined" != typeof Symbol && arr[Symbol.iterator] || arr["@@iterator"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i["return"] && (_r = _i["return"](), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }
function _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }
function combineCustomResponseData(_ref) {
  var _ref2 = _slicedToArray(_ref, 2),
    backUpResponse = _ref2[0],
    parseData = _ref2[1];
  backUpResponse.data = parseData;
  return backUpResponse;
}
function backUpResponse(response) {
  var backUpResponse = {
    status: response.status,
    statusText: response.statusText,
    url: response.url,
    data: response.json()
  };
  return Promise.all([backUpResponse, backUpResponse.data]);
}
var Resource = /*#__PURE__*/function () {
  function Resource() {
    _classCallCheck(this, Resource);
  }
  _createClass(Resource, null, [{
    key: "getAbortController",
    value: function getAbortController() {
      var controller = new AbortController();
      var timeOut = setTimeout(function () {
        controller.abort();
        clearTimeout(timeOut);
      }, 1800000); // 3min  3 * 60s
      return controller;
    }
  }, {
    key: "getJSON",
    value: function getJSON(_ref3) {
      var url = _ref3.url;
      return fetch(url, {
        method: "GET",
        mode: "cors",
        redirect: "follow",
        signal: this.getAbortController().signal
      }).then(backUpResponse).then(combineCustomResponseData);
    }
  }, {
    key: "postJSON",
    value: function postJSON(_ref4) {
      var url = _ref4.url,
        _ref4$body = _ref4.body,
        body = _ref4$body === void 0 ? "" : _ref4$body,
        _ref4$headers = _ref4.headers,
        headers = _ref4$headers === void 0 ? {} : _ref4$headers;
      return fetch(url, {
        method: "POST",
        mode: "cors",
        redirect: "follow",
        headers: headers,
        body: body,
        signal: this.getAbortController().signal
      }).then(backUpResponse).then(combineCustomResponseData);
    }
  }]);
  return Resource;
}();
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Resource);

/***/ }),

/***/ "./src/core/ResponseType.js":
/*!**********************************!*\
  !*** ./src/core/ResponseType.js ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
// formData 用不上.不区分text和document,document是从text转换解析出来的.
/**
 * 网络请求响应类型
 * @enum
 * @readonly
 * @private
 * @type {{ARRAYBUFFER: symbol, BLOB: symbol, JSON: symbol, TEXT: symbol}}
 */
var ResponseType = {
  /**
   * 网络请求返回arraybuffer
   */
  ARRAYBUFFER: Symbol("ARRAYBUFFER"),
  /**
   * 网络请求返回blob对象
   */
  BLOB: Symbol("BLOB"),
  /**
   * 网络请求返回 json
   */
  JSON: Symbol("JSON"),
  /**
   * 网络请求返回文字字符
   */
  TEXT: Symbol("TEXT")
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Object.freeze(ResponseType));

/***/ }),

/***/ "./src/core/RuntimeError.js":
/*!**********************************!*\
  !*** ./src/core/RuntimeError.js ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _defaultValue_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./defaultValue.js */ "./src/core/defaultValue.js");
function _typeof(obj) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && "function" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }, _typeof(obj); }
function _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }
function _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, "prototype", { writable: false }); return Constructor; }
function _toPropertyKey(arg) { var key = _toPrimitive(arg, "string"); return _typeof(key) === "symbol" ? key : String(key); }
function _toPrimitive(input, hint) { if (_typeof(input) !== "object" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || "default"); if (_typeof(res) !== "object") return res; throw new TypeError("@@toPrimitive must return a primitive value."); } return (hint === "string" ? String : Number)(input); }
function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }
function _inherits(subClass, superClass) { if (typeof superClass !== "function" && superClass !== null) { throw new TypeError("Super expression must either be null or a function"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); Object.defineProperty(subClass, "prototype", { writable: false }); if (superClass) _setPrototypeOf(subClass, superClass); }
function _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = _getPrototypeOf(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _getPrototypeOf(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _possibleConstructorReturn(this, result); }; }
function _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === "object" || typeof call === "function")) { return call; } else if (call !== void 0) { throw new TypeError("Derived constructors may only return object or undefined"); } return _assertThisInitialized(self); }
function _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError("this hasn't been initialised - super() hasn't been called"); } return self; }
function _wrapNativeSuper(Class) { var _cache = typeof Map === "function" ? new Map() : undefined; _wrapNativeSuper = function _wrapNativeSuper(Class) { if (Class === null || !_isNativeFunction(Class)) return Class; if (typeof Class !== "function") { throw new TypeError("Super expression must either be null or a function"); } if (typeof _cache !== "undefined") { if (_cache.has(Class)) return _cache.get(Class); _cache.set(Class, Wrapper); } function Wrapper() { return _construct(Class, arguments, _getPrototypeOf(this).constructor); } Wrapper.prototype = Object.create(Class.prototype, { constructor: { value: Wrapper, enumerable: false, writable: true, configurable: true } }); return _setPrototypeOf(Wrapper, Class); }; return _wrapNativeSuper(Class); }
function _construct(Parent, args, Class) { if (_isNativeReflectConstruct()) { _construct = Reflect.construct.bind(); } else { _construct = function _construct(Parent, args, Class) { var a = [null]; a.push.apply(a, args); var Constructor = Function.bind.apply(Parent, a); var instance = new Constructor(); if (Class) _setPrototypeOf(instance, Class.prototype); return instance; }; } return _construct.apply(null, arguments); }
function _isNativeReflectConstruct() { if (typeof Reflect === "undefined" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === "function") return true; try { Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); return true; } catch (e) { return false; } }
function _isNativeFunction(fn) { return Function.toString.call(fn).indexOf("[native code]") !== -1; }
function _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }
function _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }


/**
 * n.运行时异常
 *
 * @alias RuntimeError
 * @extends Error
 */
var RuntimeError = /*#__PURE__*/function (_Error) {
  _inherits(RuntimeError, _Error);
  var _super = _createSuper(RuntimeError);
  /**
   * n.运行时异常
   * @param {String} [message] The error message for this exception.
   * @constructor
   */
  function RuntimeError(message) {
    var _this;
    _classCallCheck(this, RuntimeError);
    message = (0,_defaultValue_js__WEBPACK_IMPORTED_MODULE_0__["default"])(message, "RuntimeError:");
    _this = _super.call(this, message);
    /**
     * 'RuntimeError' indicating that this exception was thrown due to a runtime error.
     * @type {String}
     * @readonly
     */
    _this.name = "RuntimeError";
    if (Error.captureStackTrace) {
      // Maintains proper stack trace for where our error was thrown (only available on V8)
      Error.captureStackTrace(_assertThisInitialized(_this), RuntimeError);
    } else {
      // 参考Cesium手动捕捉异常堆栈信息使用toString()抛出,测试不行.当前这种方法也可以
      console.trace(message);
    }
    return _this;
  }
  return _createClass(RuntimeError);
}( /*#__PURE__*/_wrapNativeSuper(Error));
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (RuntimeError);

/***/ }),

/***/ "./src/core/StatisticsType.js":
/*!************************************!*\
  !*** ./src/core/StatisticsType.js ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/**
 * 统计类型，Agg 聚合，Count 个数统计
 * @enum {string}
 * @readonly
 */
var StatisticsType = {
  /**
   * 计数
   * @readonly
   */
  Count: "count",
  /**
   * 聚合，包括求和、最大最小值，没有单独的sum
   * @readonly
   */
  Agg: "agg"
};
/**
 * 验证是否有效类型
 * @param v
 * @return {boolean}
 * @private
 */
StatisticsType._valid = function (v) {
  return v === this.Agg || v === this.Count;
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (StatisticsType);

/***/ }),

/***/ "./src/core/convertSearchParamToJSONString.js":
/*!****************************************************!*\
  !*** ./src/core/convertSearchParamToJSONString.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ convertSearchParamToJSONString)
/* harmony export */ });
function convertSearchParamToJSONString(searchParams) {
  var o = {};
  searchParams.forEach(function (value, key, parent) {
    o[key] = value;
  });
  return JSON.stringify(o);
}

/***/ }),

/***/ "./src/core/defaultValue.js":
/*!**********************************!*\
  !*** ./src/core/defaultValue.js ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/**
 * Returns the first parameter if not undefined, otherwise the second parameter.
 * Useful for setting a default value for a parameter.
 *
 * @private
 * @function
 * @param {*} a
 * @param {*} b
 * @returns {*} Returns the first parameter if not undefined, otherwise the second parameter.
 */
function defaultValue(a, b) {
  // 不能替换成 a || b; 比如 false ||true / 0||1 和 defaultValue(a, b)结果不同
  if (a !== undefined && a !== null) {
    return a;
  }
  return b;
}

/**
 * A frozen empty object that can be used as the default value for options passed as
 * an object literal.
 * @private
 * @type {Object}
 * @memberof defaultValue
 */
defaultValue.EMPTY_OBJECT = Object.freeze({});
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (defaultValue);

/***/ }),

/***/ "./src/core/defined.js":
/*!*****************************!*\
  !*** ./src/core/defined.js ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/**
 * 判断传入对象非 undefined 且不为null
 * @function
 * @param {Object} value
 * @returns {boolean}
 * @private
 */
function defined(value) {
  return value !== undefined && value !== null;
}
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (defined);

/***/ }),

/***/ "./src/core/getFlatUniqueArray.js":
/*!****************************************!*\
  !*** ./src/core/getFlatUniqueArray.js ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ getFlatUniqueArray)
/* harmony export */ });
function _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }
function _nonIterableSpread() { throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }
function _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }
function _iterableToArray(iter) { if (typeof Symbol !== "undefined" && iter[Symbol.iterator] != null || iter["@@iterator"] != null) return Array.from(iter); }
function _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }
function _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }
function getFlatUniqueArray(v) {
  // return [...new Set([].concat(v).flat(Infinity))]; 不必 Infinity
  return _toConsumableArray(new Set([].concat(v).flat(2)));
}

/***/ }),

/***/ "./src/core/multiEqual.js":
/*!********************************!*\
  !*** ./src/core/multiEqual.js ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ multiEqual)
/* harmony export */ });
function multiEqual() {
  var set = new Set(arguments);
  return set.size === 1;
}

/***/ }),

/***/ "./src/results/PipingAnalyseResult.js":
/*!********************************************!*\
  !*** ./src/results/PipingAnalyseResult.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _ResponseResult_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ResponseResult.js */ "./src/results/ResponseResult.js");
/* harmony import */ var _core_KeysToLowerCase_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../core/KeysToLowerCase.js */ "./src/core/KeysToLowerCase.js");
/* harmony import */ var _core_defined_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../core/defined.js */ "./src/core/defined.js");
function _typeof(obj) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && "function" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }, _typeof(obj); }
function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }
function _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }
function _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, "prototype", { writable: false }); return Constructor; }
function _toPropertyKey(arg) { var key = _toPrimitive(arg, "string"); return _typeof(key) === "symbol" ? key : String(key); }
function _toPrimitive(input, hint) { if (_typeof(input) !== "object" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || "default"); if (_typeof(res) !== "object") return res; throw new TypeError("@@toPrimitive must return a primitive value."); } return (hint === "string" ? String : Number)(input); }
function _get() { if (typeof Reflect !== "undefined" && Reflect.get) { _get = Reflect.get.bind(); } else { _get = function _get(target, property, receiver) { var base = _superPropBase(target, property); if (!base) return; var desc = Object.getOwnPropertyDescriptor(base, property); if (desc.get) { return desc.get.call(arguments.length < 3 ? target : receiver); } return desc.value; }; } return _get.apply(this, arguments); }
function _superPropBase(object, property) { while (!Object.prototype.hasOwnProperty.call(object, property)) { object = _getPrototypeOf(object); if (object === null) break; } return object; }
function _inherits(subClass, superClass) { if (typeof superClass !== "function" && superClass !== null) { throw new TypeError("Super expression must either be null or a function"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); Object.defineProperty(subClass, "prototype", { writable: false }); if (superClass) _setPrototypeOf(subClass, superClass); }
function _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }
function _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = _getPrototypeOf(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _getPrototypeOf(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _possibleConstructorReturn(this, result); }; }
function _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === "object" || typeof call === "function")) { return call; } else if (call !== void 0) { throw new TypeError("Derived constructors may only return object or undefined"); } return _assertThisInitialized(self); }
function _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError("this hasn't been initialised - super() hasn't been called"); } return self; }
function _isNativeReflectConstruct() { if (typeof Reflect === "undefined" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === "function") return true; try { Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); return true; } catch (e) { return false; } }
function _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }



/**
 * 管道系统分析 {@link PipingAnalyse} 返回的结果的基类。根据具体的分析类型，有子类型{@link PipingBurstResult},{@link PipingCollisionResult},
 * {@link PipingConnectionResult},{@link PipingFlowDirResult},{@link PipingTracingResult},
 * {@link PipingTransectResult},{@link PipingVerticalSectionResult}。
 * @extends ResponseResult
 */
var PipingAnalyseResult = /*#__PURE__*/function (_ResponseResult) {
  _inherits(PipingAnalyseResult, _ResponseResult);
  var _super = _createSuper(PipingAnalyseResult);
  function PipingAnalyseResult(options) {
    var _this;
    _classCallCheck(this, PipingAnalyseResult);
    _this = _super.call(this, options);
    _this._pipeLines = [];
    _this._pipePoints = [];
    var services = [].concat(_this._data);
    services = (0,_core_KeysToLowerCase_js__WEBPACK_IMPORTED_MODULE_1__["default"])(services, true);
    _this._json = services;
    for (var i = 0; i < services.length; i++) {
      var service = services[i];
      if ((0,_core_defined_js__WEBPACK_IMPORTED_MODULE_2__["default"])(service["lineresult"]) && Number(service["lineresult"]["recordnum"]) > 0) {
        var lineRecords = service["lineresult"]["records"]; // 是个数组
        for (var j = 0; j < lineRecords.length; j++) {
          lineRecords[j]["servicename"] = service["servicename"]; // 给每条记录都加上这个
        }

        _this._pipeLines = _this._pipeLines.concat(lineRecords);
      }
      if ((0,_core_defined_js__WEBPACK_IMPORTED_MODULE_2__["default"])(service["pointresult"]) && Number(service["pointresult"]["recordnum"]) > 0) {
        var pointRecords = service["pointresult"]["records"]; // 是个数组
        for (var _j = 0; _j < pointRecords.length; _j++) {
          pointRecords[_j]["servicename"] = service["servicename"]; // 给每条记录都加上这个
        }

        _this._pipePoints = _this._pipePoints.concat(pointRecords);
      }
    }
    return _this;
  }

  /**
   * 返回分析得到的管段数据个数
   * @return {number}
   */
  _createClass(PipingAnalyseResult, [{
    key: "lineCount",
    get: function get() {
      return this._pipeLines.length;
    }

    /**
     * 返回分析得到的管点个数
     * @return {number}
     */
  }, {
    key: "pointCount",
    get: function get() {
      return this._pipePoints.length;
    }

    /**
     * 得到分析结果中指定索引处的管段信息
     * @param {number} index 索引
     * @return {undefined|*}
     */
  }, {
    key: "getLineRecord",
    value: function getLineRecord(index) {
      return this._pipeLines[index];
    }

    /**
     * 得到分析结果中指定索引处的管点信息
     * @param {number} index 索引
     * @return {undefined|*}
     */
  }, {
    key: "getPointRecord",
    value: function getPointRecord(index) {
      return this._pipePoints[index];
    }

    /**
     * json对象所有键名改成小写,服务端保留字段名以 us_ 开头，该字段名替换成 us开头
     * @return {Array<Object>|Object}
     */
  }, {
    key: "toJSON",
    value: function toJSON() {
      return _get(_getPrototypeOf(PipingAnalyseResult.prototype), "toJSON", this).call(this);
    }
  }]);
  return PipingAnalyseResult;
}(_ResponseResult_js__WEBPACK_IMPORTED_MODULE_0__["default"]);
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PipingAnalyseResult);

/***/ }),

/***/ "./src/results/PipingBurstResult.js":
/*!******************************************!*\
  !*** ./src/results/PipingBurstResult.js ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _PipingAnalyseResult_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./PipingAnalyseResult.js */ "./src/results/PipingAnalyseResult.js");
function _typeof(obj) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && "function" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }, _typeof(obj); }
function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }
function _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }
function _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, "prototype", { writable: false }); return Constructor; }
function _toPropertyKey(arg) { var key = _toPrimitive(arg, "string"); return _typeof(key) === "symbol" ? key : String(key); }
function _toPrimitive(input, hint) { if (_typeof(input) !== "object" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || "default"); if (_typeof(res) !== "object") return res; throw new TypeError("@@toPrimitive must return a primitive value."); } return (hint === "string" ? String : Number)(input); }
function _get() { if (typeof Reflect !== "undefined" && Reflect.get) { _get = Reflect.get.bind(); } else { _get = function _get(target, property, receiver) { var base = _superPropBase(target, property); if (!base) return; var desc = Object.getOwnPropertyDescriptor(base, property); if (desc.get) { return desc.get.call(arguments.length < 3 ? target : receiver); } return desc.value; }; } return _get.apply(this, arguments); }
function _superPropBase(object, property) { while (!Object.prototype.hasOwnProperty.call(object, property)) { object = _getPrototypeOf(object); if (object === null) break; } return object; }
function _inherits(subClass, superClass) { if (typeof superClass !== "function" && superClass !== null) { throw new TypeError("Super expression must either be null or a function"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); Object.defineProperty(subClass, "prototype", { writable: false }); if (superClass) _setPrototypeOf(subClass, superClass); }
function _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }
function _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = _getPrototypeOf(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _getPrototypeOf(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _possibleConstructorReturn(this, result); }; }
function _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === "object" || typeof call === "function")) { return call; } else if (call !== void 0) { throw new TypeError("Derived constructors may only return object or undefined"); } return _assertThisInitialized(self); }
function _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError("this hasn't been initialised - super() hasn't been called"); } return self; }
function _isNativeReflectConstruct() { if (typeof Reflect === "undefined" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === "function") return true; try { Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); return true; } catch (e) { return false; } }
function _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }


/**
 * @typedef {Object} DescribePipingBurstResult
 *
 * 当前对象并不存在，此处仅用来描述数据结构.{@link PipingBurstResult}
 *
 * @property  {String} usid 系统唯一标识符
 * @property  {String} uskey 用户的唯一标识符
 */

/**
 * 管道爆炸分析结果类
 * @extends PipingAnalyseResult
 */
var PipingBurstResult = /*#__PURE__*/function (_PipingAnalyseResult) {
  _inherits(PipingBurstResult, _PipingAnalyseResult);
  var _super = _createSuper(PipingBurstResult);
  function PipingBurstResult(options) {
    _classCallCheck(this, PipingBurstResult);
    return _super.call(this, options);
  }

  /**
   * 得到分析结果中指定索引处的管段信息
   * @param {Number} index 索引值
   * @return {DescribePipingBurstResult}
   */
  _createClass(PipingBurstResult, [{
    key: "getLineRecord",
    value: function getLineRecord(index) {
      return _get(_getPrototypeOf(PipingBurstResult.prototype), "getLineRecord", this).call(this, index);
    }

    /**
     * 得到分析结果中指定索引处的管点信息
     * @param index 索引值
     * @return {DescribePipingBurstResult}
     */
  }, {
    key: "getPointRecord",
    value: function getPointRecord(index) {
      return _get(_getPrototypeOf(PipingBurstResult.prototype), "getPointRecord", this).call(this, index);
    }
  }]);
  return PipingBurstResult;
}(_PipingAnalyseResult_js__WEBPACK_IMPORTED_MODULE_0__["default"]);
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PipingBurstResult);

/***/ }),

/***/ "./src/results/PipingClearDistanceResult.js":
/*!**************************************************!*\
  !*** ./src/results/PipingClearDistanceResult.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _PipingAnalyseResult_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./PipingAnalyseResult.js */ "./src/results/PipingAnalyseResult.js");
function _typeof(obj) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && "function" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }, _typeof(obj); }
function _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }
function _nonIterableSpread() { throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }
function _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }
function _iterableToArray(iter) { if (typeof Symbol !== "undefined" && iter[Symbol.iterator] != null || iter["@@iterator"] != null) return Array.from(iter); }
function _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }
function _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }
function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }
function _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }
function _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, "prototype", { writable: false }); return Constructor; }
function _toPropertyKey(arg) { var key = _toPrimitive(arg, "string"); return _typeof(key) === "symbol" ? key : String(key); }
function _toPrimitive(input, hint) { if (_typeof(input) !== "object" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || "default"); if (_typeof(res) !== "object") return res; throw new TypeError("@@toPrimitive must return a primitive value."); } return (hint === "string" ? String : Number)(input); }
function _get() { if (typeof Reflect !== "undefined" && Reflect.get) { _get = Reflect.get.bind(); } else { _get = function _get(target, property, receiver) { var base = _superPropBase(target, property); if (!base) return; var desc = Object.getOwnPropertyDescriptor(base, property); if (desc.get) { return desc.get.call(arguments.length < 3 ? target : receiver); } return desc.value; }; } return _get.apply(this, arguments); }
function _superPropBase(object, property) { while (!Object.prototype.hasOwnProperty.call(object, property)) { object = _getPrototypeOf(object); if (object === null) break; } return object; }
function _inherits(subClass, superClass) { if (typeof superClass !== "function" && superClass !== null) { throw new TypeError("Super expression must either be null or a function"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); Object.defineProperty(subClass, "prototype", { writable: false }); if (superClass) _setPrototypeOf(subClass, superClass); }
function _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }
function _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = _getPrototypeOf(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _getPrototypeOf(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _possibleConstructorReturn(this, result); }; }
function _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === "object" || typeof call === "function")) { return call; } else if (call !== void 0) { throw new TypeError("Derived constructors may only return object or undefined"); } return _assertThisInitialized(self); }
function _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError("this hasn't been initialised - super() hasn't been called"); } return self; }
function _isNativeReflectConstruct() { if (typeof Reflect === "undefined" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === "function") return true; try { Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); return true; } catch (e) { return false; } }
function _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }


/**
 * @typedef {Object} DescribePipingClearDistanceResult
 *
 * 当前对象并不存在，此处仅用来描述数据结构.{@link PipingClearDistanceResult}
 *
 * @property  {String} coordinates 管段的起始和中止点坐标，如 ”116.0,40.0,22.0 116.0,40.0,22.0“
 * @property  {String} service 管道系统的名称,形如 "block_a000002_js"
 * @property  {String} usealt
 * @property  {String} usedeep
 * @property  {String} usept_key
 * @property  {String} usflowdir
 * @property  {String} usid
 * @property  {String} uskey
 * @property  {String} uslttype
 * @property  {String} uspdiam
 * @property  {String} uspheight
 * @property  {String} uspmater
 * @property  {String} uspsize
 * @property  {String} uspwidth
 * @property  {String} ussalt
 * @property  {String} ussdeep
 * @property  {String} usspt_key
 * @property  {String} clearDistance "不符合净距标准"或者"符合净距标准"
 */

/**
 * 管道净距分析结果类
 * @extends PipingAnalyseResult
 */
var PipingClearDistanceResult = /*#__PURE__*/function (_PipingAnalyseResult) {
  _inherits(PipingClearDistanceResult, _PipingAnalyseResult);
  var _super = _createSuper(PipingClearDistanceResult);
  function PipingClearDistanceResult(options) {
    var _this;
    _classCallCheck(this, PipingClearDistanceResult);
    _this = _super.call(this, options);
    var allPipelines = [];
    _this._json.forEach(function (services, servicesInexm, json) {
      var layerguid = services["layerguid"];
      var layername = services["layername"];
      var conform = services["lineresult"].results.find(function (v) {
        return v.type === "符合净距标准";
      });
      var inconformity = services["lineresult"].results.find(function (v) {
        return v.type === "不符合净距标准";
      });
      if (conform && conform["recordnum"] > 0) {
        conform["records"].forEach(function (tube, tubeIndex, tubeArray) {
          tubeArray[tubeIndex]["servicename"] = layerguid;
          tubeArray[tubeIndex]["clearDistance"] = "符合净距标准"; // todo 应该不会覆盖掉，目前测试环境下没有被覆盖掉
        });

        allPipelines.push.apply(allPipelines, _toConsumableArray(conform["records"]));
      }
      if (inconformity && inconformity["recordnum"] > 0) {
        inconformity["records"].forEach(function (tube, tubeIndex, tubeArray) {
          tubeArray[tubeIndex]["servicename"] = layerguid;
          tubeArray[tubeIndex]["clearDistance"] = "不符合净距标准"; // todo 应该不会覆盖掉，目前测试环境下没有被覆盖掉
        });

        allPipelines.push.apply(allPipelines, _toConsumableArray(inconformity["records"]));
      }
    });
    _this._pipeLines = allPipelines;
    return _this;
  }

  /**
   * 净距分析不计算管点，只计算碰撞的管段
   * @return {number} 返回 0
   */
  _createClass(PipingClearDistanceResult, [{
    key: "pointCount",
    get: function get() {
      return 0;
    }

    /**
     * 得到分析结果中指定索引处的管段信息
     * @param {Number} index 索引值
     * @return {DescribePipingClearDistanceResult}
     */
  }, {
    key: "getLineRecord",
    value: function getLineRecord(index) {
      return _get(_getPrototypeOf(PipingClearDistanceResult.prototype), "getLineRecord", this).call(this, index);
    }

    /**
     * 碰撞分析不计算管点，只计算碰撞的管段
     * @param index 索引值
     * @return {undefined}
     */
  }, {
    key: "getPointRecord",
    value: function getPointRecord(index) {
      return _get(_getPrototypeOf(PipingClearDistanceResult.prototype), "getPointRecord", this).call(this, index);
    }
  }]);
  return PipingClearDistanceResult;
}(_PipingAnalyseResult_js__WEBPACK_IMPORTED_MODULE_0__["default"]);
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PipingClearDistanceResult);

/***/ }),

/***/ "./src/results/PipingCollisionResult.js":
/*!**********************************************!*\
  !*** ./src/results/PipingCollisionResult.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _PipingAnalyseResult_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./PipingAnalyseResult.js */ "./src/results/PipingAnalyseResult.js");
function _typeof(obj) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && "function" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }, _typeof(obj); }
function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }
function _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }
function _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, "prototype", { writable: false }); return Constructor; }
function _toPropertyKey(arg) { var key = _toPrimitive(arg, "string"); return _typeof(key) === "symbol" ? key : String(key); }
function _toPrimitive(input, hint) { if (_typeof(input) !== "object" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || "default"); if (_typeof(res) !== "object") return res; throw new TypeError("@@toPrimitive must return a primitive value."); } return (hint === "string" ? String : Number)(input); }
function _get() { if (typeof Reflect !== "undefined" && Reflect.get) { _get = Reflect.get.bind(); } else { _get = function _get(target, property, receiver) { var base = _superPropBase(target, property); if (!base) return; var desc = Object.getOwnPropertyDescriptor(base, property); if (desc.get) { return desc.get.call(arguments.length < 3 ? target : receiver); } return desc.value; }; } return _get.apply(this, arguments); }
function _superPropBase(object, property) { while (!Object.prototype.hasOwnProperty.call(object, property)) { object = _getPrototypeOf(object); if (object === null) break; } return object; }
function _inherits(subClass, superClass) { if (typeof superClass !== "function" && superClass !== null) { throw new TypeError("Super expression must either be null or a function"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); Object.defineProperty(subClass, "prototype", { writable: false }); if (superClass) _setPrototypeOf(subClass, superClass); }
function _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }
function _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = _getPrototypeOf(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _getPrototypeOf(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _possibleConstructorReturn(this, result); }; }
function _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === "object" || typeof call === "function")) { return call; } else if (call !== void 0) { throw new TypeError("Derived constructors may only return object or undefined"); } return _assertThisInitialized(self); }
function _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError("this hasn't been initialised - super() hasn't been called"); } return self; }
function _isNativeReflectConstruct() { if (typeof Reflect === "undefined" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === "function") return true; try { Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); return true; } catch (e) { return false; } }
function _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }


/**
 * @typedef {Object} DescribePipingCollisionResult
 *
 * 当前对象并不存在，此处仅用来描述数据结构.{@link PipingCollisionResult}
 *
 * @property  {String} coordinates 管段的起始和中止点坐标，如 ”116.0,40.0,22.0 116.0,40.0,22.0“
 * @property  {String} service 管道系统的名称,形如 "block_a000002_js"
 * @property  {String} usealt
 * @property  {String} usedeep
 * @property  {String} usept_key
 * @property  {String} usflowdir
 * @property  {String} usid
 * @property  {String} uskey
 * @property  {String} uslttype
 * @property  {String} uspdiam
 * @property  {String} uspheight
 * @property  {String} uspmater
 * @property  {String} uspsize
 * @property  {String} uspwidth
 * @property  {String} ussalt
 * @property  {String} ussdeep
 * @property  {String} usspt_key
 * @property  {String} horizon_distance 碰撞的水平净距
 * @property  {String} vertical_distance 碰撞的垂直净距
 */

/**
 * 管道碰撞分析结果类
 * @extends PipingAnalyseResult
 */
var PipingCollisionResult = /*#__PURE__*/function (_PipingAnalyseResult) {
  _inherits(PipingCollisionResult, _PipingAnalyseResult);
  var _super = _createSuper(PipingCollisionResult);
  function PipingCollisionResult(options) {
    var _this;
    _classCallCheck(this, PipingCollisionResult);
    _this = _super.call(this, options);
    var allPipelines = [];
    // 服务端变更结构设计,使得能够支持多根管段碰撞分析时保留对应管段的顺序编号,将该结构拉平成数组进行保存 wangkang 2021年11月16日
    var _loop = function _loop() {
      var pipeService = _this._pipeLines[i];
      if (pipeService["collisionnum"] > 0) {
        var theLabel = pipeService["label"]; // 数字,服务端给管段顺序编的号
        // 数组对象,便于拉平结构,把label给加上
        pipeService["collisionlines"].forEach(function (value, index, array) {
          array[index]["label"] = theLabel;
          array[index]["servicename"] = pipeService["servicename"];
          // 把这个距离转成数值格式
          array[index]["vertical_distance"] = Number(array[index]["vertical_distance"]);
          array[index]["horizon_distance"] = Number(array[index]["horizon_distance"]);
        });
        allPipelines = allPipelines.concat(pipeService["collisionlines"]);
      }
    };
    for (var i = 0; i < _this._pipeLines.length; i++) {
      _loop();
    }
    _this._pipeLines = allPipelines;
    return _this;
  }

  /**
   * 碰撞分析不计算管点，只计算碰撞的管段
   * @return {number} 返回 0
   */
  _createClass(PipingCollisionResult, [{
    key: "pointCount",
    get: function get() {
      return 0;
    }

    /**
     * 得到分析结果中指定索引处的管段信息
     * @param {Number} index 索引值
     * @return {DescribePipingCollisionResult}
     */
  }, {
    key: "getLineRecord",
    value: function getLineRecord(index) {
      return _get(_getPrototypeOf(PipingCollisionResult.prototype), "getLineRecord", this).call(this, index);
    }

    /**
     * 碰撞分析不计算管点，只计算碰撞的管段
     * @param index 索引值
     * @return {undefined}
     */
  }, {
    key: "getPointRecord",
    value: function getPointRecord(index) {
      return _get(_getPrototypeOf(PipingCollisionResult.prototype), "getPointRecord", this).call(this, index);
    }
  }]);
  return PipingCollisionResult;
}(_PipingAnalyseResult_js__WEBPACK_IMPORTED_MODULE_0__["default"]);
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PipingCollisionResult);

/***/ }),

/***/ "./src/results/PipingConnectionResult.js":
/*!***********************************************!*\
  !*** ./src/results/PipingConnectionResult.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _PipingAnalyseResult_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./PipingAnalyseResult.js */ "./src/results/PipingAnalyseResult.js");
/* harmony import */ var _core_defined_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../core/defined.js */ "./src/core/defined.js");
function _typeof(obj) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && "function" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }, _typeof(obj); }
function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }
function _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }
function _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, "prototype", { writable: false }); return Constructor; }
function _toPropertyKey(arg) { var key = _toPrimitive(arg, "string"); return _typeof(key) === "symbol" ? key : String(key); }
function _toPrimitive(input, hint) { if (_typeof(input) !== "object" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || "default"); if (_typeof(res) !== "object") return res; throw new TypeError("@@toPrimitive must return a primitive value."); } return (hint === "string" ? String : Number)(input); }
function _get() { if (typeof Reflect !== "undefined" && Reflect.get) { _get = Reflect.get.bind(); } else { _get = function _get(target, property, receiver) { var base = _superPropBase(target, property); if (!base) return; var desc = Object.getOwnPropertyDescriptor(base, property); if (desc.get) { return desc.get.call(arguments.length < 3 ? target : receiver); } return desc.value; }; } return _get.apply(this, arguments); }
function _superPropBase(object, property) { while (!Object.prototype.hasOwnProperty.call(object, property)) { object = _getPrototypeOf(object); if (object === null) break; } return object; }
function _inherits(subClass, superClass) { if (typeof superClass !== "function" && superClass !== null) { throw new TypeError("Super expression must either be null or a function"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); Object.defineProperty(subClass, "prototype", { writable: false }); if (superClass) _setPrototypeOf(subClass, superClass); }
function _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }
function _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = _getPrototypeOf(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _getPrototypeOf(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _possibleConstructorReturn(this, result); }; }
function _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === "object" || typeof call === "function")) { return call; } else if (call !== void 0) { throw new TypeError("Derived constructors may only return object or undefined"); } return _assertThisInitialized(self); }
function _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError("this hasn't been initialised - super() hasn't been called"); } return self; }
function _isNativeReflectConstruct() { if (typeof Reflect === "undefined" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === "function") return true; try { Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); return true; } catch (e) { return false; } }
function _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }



/**
 * @typedef {Object} DescribePipingAnalyseConnectionResult
 *
 * 当前对象并不存在，此处仅用来描述数据结构.{@link PipingConnectionResult}
 *
 * @property  {String} usid 系统唯一标识符
 * @property  {String} uskey 用户的唯一标识符
 * @property  {String} geometry geojson几何描述字符串,MultiLineString或者 MultiPoint类型
 * @property  {String} usgeometry geojson几何描述字符串，MultiLineString或者 MultiPoint类型。为了兼容老版本,预备弃用
 * @property  {String} [uswell_id] 管点数据才有,形如  "e84e4153-eeae-4ec3-a380-5f2fe78a5267"
 */

/**
 * 管道联通分析结果类
 * @extends PipingAnalyseResult
 */
var PipingConnectionResult = /*#__PURE__*/function (_PipingAnalyseResult) {
  _inherits(PipingConnectionResult, _PipingAnalyseResult);
  var _super = _createSuper(PipingConnectionResult);
  function PipingConnectionResult(options) {
    var _this;
    _classCallCheck(this, PipingConnectionResult);
    _this = _super.call(this, options);
    _this._connected = false;
    _this._connectLength = 0;
    var currentPipingService = [].concat(_this._json)[0];
    if ((0,_core_defined_js__WEBPACK_IMPORTED_MODULE_1__["default"])(currentPipingService)) {
      _this._connected = currentPipingService["conn"];
      _this._connectLength = Number(currentPipingService["length"]);
    }
    return _this;
  }

  /**
   * 管段之间是否联通
   * @return {boolean}
   */
  _createClass(PipingConnectionResult, [{
    key: "connected",
    get: function get() {
      return this._connected;
    }

    /**
     * 管段联通的长度，
     * @return {number}
     */
  }, {
    key: "connectedLength",
    get: function get() {
      return this._connectLength;
    }

    /**
     * 得到分析结果中指定索引处的管段信息
     * @param {Number} index 索引值
     * @return {DescribePipingAnalyseConnectionResult}
     */
  }, {
    key: "getLineRecord",
    value: function getLineRecord(index) {
      return _get(_getPrototypeOf(PipingConnectionResult.prototype), "getLineRecord", this).call(this, index);
    }

    /**
     * 得到分析结果中指定索引处的管点信息
     * @param index 索引值
     * @return {DescribePipingAnalyseConnectionResult}
     */
  }, {
    key: "getPointRecord",
    value: function getPointRecord(index) {
      return _get(_getPrototypeOf(PipingConnectionResult.prototype), "getPointRecord", this).call(this, index);
    }
  }]);
  return PipingConnectionResult;
}(_PipingAnalyseResult_js__WEBPACK_IMPORTED_MODULE_0__["default"]);
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PipingConnectionResult);

/***/ }),

/***/ "./src/results/PipingFlowDirResult.js":
/*!********************************************!*\
  !*** ./src/results/PipingFlowDirResult.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _PipingAnalyseResult_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./PipingAnalyseResult.js */ "./src/results/PipingAnalyseResult.js");
function _typeof(obj) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && "function" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }, _typeof(obj); }
function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }
function _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }
function _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, "prototype", { writable: false }); return Constructor; }
function _toPropertyKey(arg) { var key = _toPrimitive(arg, "string"); return _typeof(key) === "symbol" ? key : String(key); }
function _toPrimitive(input, hint) { if (_typeof(input) !== "object" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || "default"); if (_typeof(res) !== "object") return res; throw new TypeError("@@toPrimitive must return a primitive value."); } return (hint === "string" ? String : Number)(input); }
function _get() { if (typeof Reflect !== "undefined" && Reflect.get) { _get = Reflect.get.bind(); } else { _get = function _get(target, property, receiver) { var base = _superPropBase(target, property); if (!base) return; var desc = Object.getOwnPropertyDescriptor(base, property); if (desc.get) { return desc.get.call(arguments.length < 3 ? target : receiver); } return desc.value; }; } return _get.apply(this, arguments); }
function _superPropBase(object, property) { while (!Object.prototype.hasOwnProperty.call(object, property)) { object = _getPrototypeOf(object); if (object === null) break; } return object; }
function _inherits(subClass, superClass) { if (typeof superClass !== "function" && superClass !== null) { throw new TypeError("Super expression must either be null or a function"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); Object.defineProperty(subClass, "prototype", { writable: false }); if (superClass) _setPrototypeOf(subClass, superClass); }
function _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }
function _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = _getPrototypeOf(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _getPrototypeOf(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _possibleConstructorReturn(this, result); }; }
function _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === "object" || typeof call === "function")) { return call; } else if (call !== void 0) { throw new TypeError("Derived constructors may only return object or undefined"); } return _assertThisInitialized(self); }
function _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError("this hasn't been initialised - super() hasn't been called"); } return self; }
function _isNativeReflectConstruct() { if (typeof Reflect === "undefined" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === "function") return true; try { Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); return true; } catch (e) { return false; } }
function _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }


/**
 * @typedef {Object} DescribePipingAnalyseFlowDirResult
 *
 * 当前对象并不存在，此处仅用来描述数据结构.{@link PipingFlowDirResult}
 *
 * @property  {String} usid 系统唯一标识符
 * @property  {String} uskey 用户的唯一标识符
 * @property  {String} usgeometry geojson几何描述字符串，MultiLineString或者 MultiPoint类型
 * @property  {String} [uswell_id] 管点数据才有,形如  "e84e4153-eeae-4ec3-a380-5f2fe78a5267"
 */

/**
 * 管段流向分析结果类
 * @extends PipingAnalyseResult
 */
var PipingFlowDirResult = /*#__PURE__*/function (_PipingAnalyseResult) {
  _inherits(PipingFlowDirResult, _PipingAnalyseResult);
  var _super = _createSuper(PipingFlowDirResult);
  function PipingFlowDirResult(options) {
    _classCallCheck(this, PipingFlowDirResult);
    return _super.call(this, options); // 服务端已经将结构展开,此处不需要其他处理
  }

  /**
   * 得到分析结果中指定索引处的管段信息
   * @param {Number} index 索引值
   * @return {DescribePipingAnalyseFlowDirResult}
   */
  _createClass(PipingFlowDirResult, [{
    key: "getLineRecord",
    value: function getLineRecord(index) {
      return _get(_getPrototypeOf(PipingFlowDirResult.prototype), "getLineRecord", this).call(this, index);
    }

    /**
     * 得到分析结果中指定索引处的管点信息
     * @param index 索引值
     * @return {DescribePipingAnalyseFlowDirResult}
     */
  }, {
    key: "getPointRecord",
    value: function getPointRecord(index) {
      return _get(_getPrototypeOf(PipingFlowDirResult.prototype), "getPointRecord", this).call(this, index);
    }
  }]);
  return PipingFlowDirResult;
}(_PipingAnalyseResult_js__WEBPACK_IMPORTED_MODULE_0__["default"]);
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PipingFlowDirResult);

/***/ }),

/***/ "./src/results/PipingTracingResult.js":
/*!********************************************!*\
  !*** ./src/results/PipingTracingResult.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _PipingAnalyseResult_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./PipingAnalyseResult.js */ "./src/results/PipingAnalyseResult.js");
function _typeof(obj) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && "function" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }, _typeof(obj); }
function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }
function _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }
function _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, "prototype", { writable: false }); return Constructor; }
function _toPropertyKey(arg) { var key = _toPrimitive(arg, "string"); return _typeof(key) === "symbol" ? key : String(key); }
function _toPrimitive(input, hint) { if (_typeof(input) !== "object" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || "default"); if (_typeof(res) !== "object") return res; throw new TypeError("@@toPrimitive must return a primitive value."); } return (hint === "string" ? String : Number)(input); }
function _get() { if (typeof Reflect !== "undefined" && Reflect.get) { _get = Reflect.get.bind(); } else { _get = function _get(target, property, receiver) { var base = _superPropBase(target, property); if (!base) return; var desc = Object.getOwnPropertyDescriptor(base, property); if (desc.get) { return desc.get.call(arguments.length < 3 ? target : receiver); } return desc.value; }; } return _get.apply(this, arguments); }
function _superPropBase(object, property) { while (!Object.prototype.hasOwnProperty.call(object, property)) { object = _getPrototypeOf(object); if (object === null) break; } return object; }
function _inherits(subClass, superClass) { if (typeof superClass !== "function" && superClass !== null) { throw new TypeError("Super expression must either be null or a function"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); Object.defineProperty(subClass, "prototype", { writable: false }); if (superClass) _setPrototypeOf(subClass, superClass); }
function _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }
function _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = _getPrototypeOf(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _getPrototypeOf(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _possibleConstructorReturn(this, result); }; }
function _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === "object" || typeof call === "function")) { return call; } else if (call !== void 0) { throw new TypeError("Derived constructors may only return object or undefined"); } return _assertThisInitialized(self); }
function _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError("this hasn't been initialised - super() hasn't been called"); } return self; }
function _isNativeReflectConstruct() { if (typeof Reflect === "undefined" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === "function") return true; try { Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); return true; } catch (e) { return false; } }
function _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }


/**
 * @typedef {Object} DescribePipingAnalyseTracingResult
 *
 * 当前对象并不存在，此处仅用来描述数据结构.{@link PipingTracingResult}
 *
 * @property  {String} usid 系统唯一标识符
 * @property  {String} uskey 用户的唯一标识符
 * @property  {String} usgeometry geojson几何描述字符串，MultiLineString或者 MultiPoint类型
 * @property  {String} [uswell_id] 管点数据才有,形如  "e84e4153-eeae-4ec3-a380-5f2fe78a5267"
 */

/**
 * 管道爆炸分析结果类
 * @extends PipingAnalyseResult
 */
var PipingTracingResult = /*#__PURE__*/function (_PipingAnalyseResult) {
  _inherits(PipingTracingResult, _PipingAnalyseResult);
  var _super = _createSuper(PipingTracingResult);
  function PipingTracingResult(options) {
    _classCallCheck(this, PipingTracingResult);
    return _super.call(this, options); // 服务端和流向分析调的相同方法,实测相同的参数,返回值相同.服务端保留这个接口名字,业务上听起来感觉不一样 wangkang 2021年11月10日
    // 服务端已经将结构展开,此处不需要其他处理
  }

  /**
   * 得到分析结果中指定索引处的管段信息
   * @param {Number} index 索引值
   * @return {DescribePipingAnalyseTracingResult}
   */
  _createClass(PipingTracingResult, [{
    key: "getLineRecord",
    value: function getLineRecord(index) {
      return _get(_getPrototypeOf(PipingTracingResult.prototype), "getLineRecord", this).call(this, index);
    }

    /**
     * 得到分析结果中指定索引处的管点信息
     * @param index 索引值
     * @return {DescribePipingAnalyseTracingResult}
     */
  }, {
    key: "getPointRecord",
    value: function getPointRecord(index) {
      return _get(_getPrototypeOf(PipingTracingResult.prototype), "getPointRecord", this).call(this, index);
    }
  }]);
  return PipingTracingResult;
}(_PipingAnalyseResult_js__WEBPACK_IMPORTED_MODULE_0__["default"]);
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PipingTracingResult);

/***/ }),

/***/ "./src/results/PipingTransectResult.js":
/*!*********************************************!*\
  !*** ./src/results/PipingTransectResult.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _PipingAnalyseResult_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./PipingAnalyseResult.js */ "./src/results/PipingAnalyseResult.js");
function _typeof(obj) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && "function" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }, _typeof(obj); }
function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }
function _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }
function _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, "prototype", { writable: false }); return Constructor; }
function _toPropertyKey(arg) { var key = _toPrimitive(arg, "string"); return _typeof(key) === "symbol" ? key : String(key); }
function _toPrimitive(input, hint) { if (_typeof(input) !== "object" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || "default"); if (_typeof(res) !== "object") return res; throw new TypeError("@@toPrimitive must return a primitive value."); } return (hint === "string" ? String : Number)(input); }
function _get() { if (typeof Reflect !== "undefined" && Reflect.get) { _get = Reflect.get.bind(); } else { _get = function _get(target, property, receiver) { var base = _superPropBase(target, property); if (!base) return; var desc = Object.getOwnPropertyDescriptor(base, property); if (desc.get) { return desc.get.call(arguments.length < 3 ? target : receiver); } return desc.value; }; } return _get.apply(this, arguments); }
function _superPropBase(object, property) { while (!Object.prototype.hasOwnProperty.call(object, property)) { object = _getPrototypeOf(object); if (object === null) break; } return object; }
function _inherits(subClass, superClass) { if (typeof superClass !== "function" && superClass !== null) { throw new TypeError("Super expression must either be null or a function"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); Object.defineProperty(subClass, "prototype", { writable: false }); if (superClass) _setPrototypeOf(subClass, superClass); }
function _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }
function _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = _getPrototypeOf(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _getPrototypeOf(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _possibleConstructorReturn(this, result); }; }
function _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === "object" || typeof call === "function")) { return call; } else if (call !== void 0) { throw new TypeError("Derived constructors may only return object or undefined"); } return _assertThisInitialized(self); }
function _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError("this hasn't been initialised - super() hasn't been called"); } return self; }
function _isNativeReflectConstruct() { if (typeof Reflect === "undefined" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === "function") return true; try { Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); return true; } catch (e) { return false; } }
function _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }


/**
 * @typedef {Object} DescribePipingAnalyseTransectResult
 *
 * 当前对象并不存在，此处仅用来描述数据结构.{@link PipingTransectResult}
 *
 * @property  {String} usid 系统唯一标识符
 * @property  {String} uskey 用户的唯一标识符
 * @property  {String} usedeep
 * @property  {String} usedholeco
 * @property  {String} usept_idx
 * @property  {String} usept_key
 * @property  {String} usflowdir
 * @property  {String} uslttype
 * @property  {String} uspdiam
 * @property  {String} uspheight
 * @property  {String} uspmater
 * @property  {String} uspsize
 * @property  {String} uspwidth
 * @property  {String} ussalt
 * @property  {String} ussdeep
 * @property  {String} usshape_leng
 * @property  {String} usspt_idx
 * @property  {String} usspt_key
 * @property  {Array.<Object>} intersect_points 形如[{intersect_point: "119.9571754464566311,36.7579354308978878,40.0498651686670755"},{intersect_point: "119.9571754464566311,36.7579354308978878,40.0498651686670755"}]
 * @property  {String} usgeometry geojson几何描述字符串，MultiLineString或者 MultiPoint类型
 * @property  {String} usealt 管点数据才有,形如  "e84e4153-eeae-4ec3-a380-5f2fe78a5267"
 */

/**
 * 管道横断面分析结果类
 * @extends PipingAnalyseResult
 */
var PipingTransectResult = /*#__PURE__*/function (_PipingAnalyseResult) {
  _inherits(PipingTransectResult, _PipingAnalyseResult);
  var _super = _createSuper(PipingTransectResult);
  function PipingTransectResult(options) {
    _classCallCheck(this, PipingTransectResult);
    return _super.call(this, options);
  }

  /**
   * 横断面分析不计算管点，只计算碰撞的管段
   * @return {number} 返回 0
   */
  _createClass(PipingTransectResult, [{
    key: "pointCount",
    get: function get() {
      return 0;
    }

    /**
     * 得到分析结果中指定索引处的管段信息
     * @param {Number} index 索引值
     * @return {DescribePipingAnalyseTransectResult}
     */
  }, {
    key: "getLineRecord",
    value: function getLineRecord(index) {
      return _get(_getPrototypeOf(PipingTransectResult.prototype), "getLineRecord", this).call(this, index);
    }

    /**
     * 管道数据横断面分析不分析管点
     * @param index 索引值
     * @return {undefined}
     */
  }, {
    key: "getPointRecord",
    value: function getPointRecord(index) {
      return _get(_getPrototypeOf(PipingTransectResult.prototype), "getPointRecord", this).call(this, index);
    }
  }]);
  return PipingTransectResult;
}(_PipingAnalyseResult_js__WEBPACK_IMPORTED_MODULE_0__["default"]);
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PipingTransectResult);

/***/ }),

/***/ "./src/results/PipingVerticalSectionResult.js":
/*!****************************************************!*\
  !*** ./src/results/PipingVerticalSectionResult.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _PipingAnalyseResult_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./PipingAnalyseResult.js */ "./src/results/PipingAnalyseResult.js");
function _typeof(obj) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && "function" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }, _typeof(obj); }
function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }
function _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }
function _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, "prototype", { writable: false }); return Constructor; }
function _toPropertyKey(arg) { var key = _toPrimitive(arg, "string"); return _typeof(key) === "symbol" ? key : String(key); }
function _toPrimitive(input, hint) { if (_typeof(input) !== "object" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || "default"); if (_typeof(res) !== "object") return res; throw new TypeError("@@toPrimitive must return a primitive value."); } return (hint === "string" ? String : Number)(input); }
function _get() { if (typeof Reflect !== "undefined" && Reflect.get) { _get = Reflect.get.bind(); } else { _get = function _get(target, property, receiver) { var base = _superPropBase(target, property); if (!base) return; var desc = Object.getOwnPropertyDescriptor(base, property); if (desc.get) { return desc.get.call(arguments.length < 3 ? target : receiver); } return desc.value; }; } return _get.apply(this, arguments); }
function _superPropBase(object, property) { while (!Object.prototype.hasOwnProperty.call(object, property)) { object = _getPrototypeOf(object); if (object === null) break; } return object; }
function _inherits(subClass, superClass) { if (typeof superClass !== "function" && superClass !== null) { throw new TypeError("Super expression must either be null or a function"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); Object.defineProperty(subClass, "prototype", { writable: false }); if (superClass) _setPrototypeOf(subClass, superClass); }
function _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }
function _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = _getPrototypeOf(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _getPrototypeOf(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _possibleConstructorReturn(this, result); }; }
function _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === "object" || typeof call === "function")) { return call; } else if (call !== void 0) { throw new TypeError("Derived constructors may only return object or undefined"); } return _assertThisInitialized(self); }
function _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError("this hasn't been initialised - super() hasn't been called"); } return self; }
function _isNativeReflectConstruct() { if (typeof Reflect === "undefined" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === "function") return true; try { Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); return true; } catch (e) { return false; } }
function _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }


/**
 * @typedef {Object} DescribePipingAnalyseVerticalSectionResult
 *
 * 当前对象并不存在，此处仅用来描述数据结构.{@link PipingVerticalSectionResult}
 * 当前测试返回值为空，不清楚实际返回结果
 *
 * @property  {String} usid 系统唯一标识符
 * @property  {String} uskey 用户的唯一标识符
 */

/**
 * 管道纵断面分析结果类
 * @extends PipingAnalyseResult
 */
var PipingVerticalSectionResult = /*#__PURE__*/function (_PipingAnalyseResult) {
  _inherits(PipingVerticalSectionResult, _PipingAnalyseResult);
  var _super = _createSuper(PipingVerticalSectionResult);
  function PipingVerticalSectionResult(options) {
    _classCallCheck(this, PipingVerticalSectionResult);
    return _super.call(this, options);
  }

  /**
   * 纵断面分析不计算管点，只计算碰撞的管段
   * @return {number} 返回 0
   */
  _createClass(PipingVerticalSectionResult, [{
    key: "pointCount",
    get: function get() {
      return 0;
    }

    /**
     * 得到分析结果中指定索引处的管段信息
     * @param {Number} index 索引值
     * @return {DescribePipingAnalyseVerticalSectionResult}
     */
  }, {
    key: "getLineRecord",
    value: function getLineRecord(index) {
      return _get(_getPrototypeOf(PipingVerticalSectionResult.prototype), "getLineRecord", this).call(this, index);
    }

    /**
     * 管道数据横断面分析不分析管点
     * @param index 索引值
     * @return {undefined}
     */
  }, {
    key: "getPointRecord",
    value: function getPointRecord(index) {
      return _get(_getPrototypeOf(PipingVerticalSectionResult.prototype), "getPointRecord", this).call(this, index);
    }
  }]);
  return PipingVerticalSectionResult;
}(_PipingAnalyseResult_js__WEBPACK_IMPORTED_MODULE_0__["default"]);
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PipingVerticalSectionResult);

/***/ }),

/***/ "./src/results/QueryFieldValueResult.js":
/*!**********************************************!*\
  !*** ./src/results/QueryFieldValueResult.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _ResponseResult_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ResponseResult.js */ "./src/results/ResponseResult.js");
/* harmony import */ var _core_KeysToLowerCase_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../core/KeysToLowerCase.js */ "./src/core/KeysToLowerCase.js");
function _typeof(obj) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && "function" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }, _typeof(obj); }
function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }
function _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }
function _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, "prototype", { writable: false }); return Constructor; }
function _toPropertyKey(arg) { var key = _toPrimitive(arg, "string"); return _typeof(key) === "symbol" ? key : String(key); }
function _toPrimitive(input, hint) { if (_typeof(input) !== "object" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || "default"); if (_typeof(res) !== "object") return res; throw new TypeError("@@toPrimitive must return a primitive value."); } return (hint === "string" ? String : Number)(input); }
function _inherits(subClass, superClass) { if (typeof superClass !== "function" && superClass !== null) { throw new TypeError("Super expression must either be null or a function"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); Object.defineProperty(subClass, "prototype", { writable: false }); if (superClass) _setPrototypeOf(subClass, superClass); }
function _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }
function _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = _getPrototypeOf(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _getPrototypeOf(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _possibleConstructorReturn(this, result); }; }
function _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === "object" || typeof call === "function")) { return call; } else if (call !== void 0) { throw new TypeError("Derived constructors may only return object or undefined"); } return _assertThisInitialized(self); }
function _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError("this hasn't been initialised - super() hasn't been called"); } return self; }
function _isNativeReflectConstruct() { if (typeof Reflect === "undefined" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === "function") return true; try { Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); return true; } catch (e) { return false; } }
function _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }



/**
 * 存储指定字段的唯一值列表的结果
 * @extends ResponseResult
 */
var QueryFieldValueResult = /*#__PURE__*/function (_ResponseResult) {
  _inherits(QueryFieldValueResult, _ResponseResult);
  var _super = _createSuper(QueryFieldValueResult);
  /**
   *
   * @param options {Object} 输入项
   * @param {ArrayBuffer|String|JSON|Blob|Response} options.response 参考{@link ResponseResult}
   */
  function QueryFieldValueResult(options) {
    var _this;
    _classCallCheck(this, QueryFieldValueResult);
    _this = _super.call(this, options);
    _this._fieldName = "";
    _this._valueType = "";
    _this._fieldDistinctValues = [];
    var service = [].concat(_this._data)[0]; // 字段唯一值列表查询，只要第一个
    service = (0,_core_KeysToLowerCase_js__WEBPACK_IMPORTED_MODULE_1__["default"])(service, true);
    _this._json = service;
    if (service["recordnum"] > 0) {
      var fieldMeta = service["records"];
      // let recordForCurrentField = [].concat(service['records'])[0];
      _this._fieldName = fieldMeta["fieldname"];
      _this._valueType = fieldMeta["datatype"];
      _this._fieldDistinctValues = [].concat(fieldMeta["values"]);
    }
    return _this;
  }

  /**
   * 字段值类型
   * @readonly
   * @return {String} {@link FieldType}
   */
  _createClass(QueryFieldValueResult, [{
    key: "valueType",
    get: function get() {
      return this._valueType;
    }

    /**
     * 结果值个数
     * @readonly
     * @return {number}
     */
  }, {
    key: "valueCount",
    get: function get() {
      return this._fieldDistinctValues.length;
    }

    /**
     * 字段名称
     * @readonly
     * @return {String}
     */
  }, {
    key: "fieldName",
    get: function get() {
      return this._fieldName;
    }

    /**
     * 字段列表的第 index个值,字符形式，空值为{}
     * @param {Number} index 索引
     * @return {String|Object)
     */
  }, {
    key: "getRecord",
    value: function getRecord(index) {
      return this._fieldDistinctValues[index];
    }
  }]);
  return QueryFieldValueResult;
}(_ResponseResult_js__WEBPACK_IMPORTED_MODULE_0__["default"]);
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (QueryFieldValueResult);

/***/ }),

/***/ "./src/results/QueryMetaResult.js":
/*!****************************************!*\
  !*** ./src/results/QueryMetaResult.js ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _ResponseResult_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ResponseResult.js */ "./src/results/ResponseResult.js");
/* harmony import */ var _core_defined_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../core/defined.js */ "./src/core/defined.js");
/* harmony import */ var _core_KeysToLowerCase_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../core/KeysToLowerCase.js */ "./src/core/KeysToLowerCase.js");
function _typeof(obj) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && "function" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }, _typeof(obj); }
function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }
function _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }
function _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, "prototype", { writable: false }); return Constructor; }
function _toPropertyKey(arg) { var key = _toPrimitive(arg, "string"); return _typeof(key) === "symbol" ? key : String(key); }
function _toPrimitive(input, hint) { if (_typeof(input) !== "object" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || "default"); if (_typeof(res) !== "object") return res; throw new TypeError("@@toPrimitive must return a primitive value."); } return (hint === "string" ? String : Number)(input); }
function _inherits(subClass, superClass) { if (typeof superClass !== "function" && superClass !== null) { throw new TypeError("Super expression must either be null or a function"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); Object.defineProperty(subClass, "prototype", { writable: false }); if (superClass) _setPrototypeOf(subClass, superClass); }
function _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }
function _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = _getPrototypeOf(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _getPrototypeOf(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _possibleConstructorReturn(this, result); }; }
function _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === "object" || typeof call === "function")) { return call; } else if (call !== void 0) { throw new TypeError("Derived constructors may only return object or undefined"); } return _assertThisInitialized(self); }
function _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError("this hasn't been initialised - super() hasn't been called"); } return self; }
function _isNativeReflectConstruct() { if (typeof Reflect === "undefined" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === "function") return true; try { Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); return true; } catch (e) { return false; } }
function _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }




/**
 * @typedef {Object} FieldType
 *
 * 当前数据结构并不存在，此处仅用来列举字段数据类型的描述值,在{@link FieldInfo}的中使用
 * @readonly
 * @property  {String} int2="int2"  int2
 * @property  {String} int4="int4"  int4
 * @property  {String} int8="int8"  int8
 * @property  {String} float4="float4"  float4
 * @property  {String} float8="float8"  float8
 * @property  {String} text="text"  text
 * @property  {String} bool="bool"  bool
 * @property  {String} geometry="geometry"  geometry
 */

/**
 * @typedef {Object} FieldInfo
 *
 * 当前类并不存在，此处仅用来描述数据结构,在{@link QueryMetaResult}的中使用
 *
 * @enum
 * @readonly
 * @property  {String} Name
 * @property  {Number} Size
 * @property  {FieldType|Number} Type
 */

/**
 * @extends ResponseResult
 */
var QueryMetaResult = /*#__PURE__*/function (_ResponseResult) {
  _inherits(QueryMetaResult, _ResponseResult);
  var _super = _createSuper(QueryMetaResult);
  /**
   * 图层元数据查询结果类。在{@link Query}类中执行图层元数据查询得到，不可以直接初始化.
   *
   * @param options {Object} 输入项
   * @param {ArrayBuffer|String|JSON|Blob|Response} options.response 参考{@link ResponseResult}
   */
  function QueryMetaResult(options) {
    var _this;
    _classCallCheck(this, QueryMetaResult);
    _this = _super.call(this, options);
    _this._fieldRecords = [];
    _this._extent = undefined;
    var service = [].concat(_this._data)[0];
    service = (0,_core_KeysToLowerCase_js__WEBPACK_IMPORTED_MODULE_2__["default"])(service, true);
    _this._json = service;
    if ((0,_core_defined_js__WEBPACK_IMPORTED_MODULE_1__["default"])(service)) {
      var bbox = service["bound"].split(",");
      _this._extent = {
        north: Number(bbox[0]),
        south: Number(bbox[1]),
        east: Number(bbox[2]),
        west: Number(bbox[3])
      };
      if (Number(service["recordnum"]) > 0) {
        var fieldRecords = [].concat(service["records"]);
        for (var i = 0; i < fieldRecords.length; i++) {
          fieldRecords[i]["size"] = Number(fieldRecords[i]["size"]);
        }
        _this._fieldRecords = fieldRecords;
      }
    }
    return _this;
  }

  /**
   * 字段总数
   * @readonly
   * @return {number}
   */
  _createClass(QueryMetaResult, [{
    key: "fieldCount",
    get: function get() {
      return this._fieldRecords.length;
    }

    /**
     * 获取指定索引处的字段信息
     * @param {Number}index 字段列表中的索引位置
     * @return {FieldInfo}
     */
  }, {
    key: "getRecord",
    value: function getRecord(index) {
      return this._fieldRecords[index];
    }

    /**
     * 获取四至范围，经纬度，十进制形式
     * @return {{east: number, south: number, north: number, west: number}}
     */
  }, {
    key: "getExtent",
    value: function getExtent() {
      return this._extent;
    }
  }]);
  return QueryMetaResult;
}(_ResponseResult_js__WEBPACK_IMPORTED_MODULE_0__["default"]);
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (QueryMetaResult);

/***/ }),

/***/ "./src/results/QueryPropertyResult.js":
/*!********************************************!*\
  !*** ./src/results/QueryPropertyResult.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _ResponseResult_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ResponseResult.js */ "./src/results/ResponseResult.js");
/* harmony import */ var _core_KeysToLowerCase_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../core/KeysToLowerCase.js */ "./src/core/KeysToLowerCase.js");
function _typeof(obj) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && "function" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }, _typeof(obj); }
function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }
function _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }
function _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, "prototype", { writable: false }); return Constructor; }
function _toPropertyKey(arg) { var key = _toPrimitive(arg, "string"); return _typeof(key) === "symbol" ? key : String(key); }
function _toPrimitive(input, hint) { if (_typeof(input) !== "object" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || "default"); if (_typeof(res) !== "object") return res; throw new TypeError("@@toPrimitive must return a primitive value."); } return (hint === "string" ? String : Number)(input); }
function _inherits(subClass, superClass) { if (typeof superClass !== "function" && superClass !== null) { throw new TypeError("Super expression must either be null or a function"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); Object.defineProperty(subClass, "prototype", { writable: false }); if (superClass) _setPrototypeOf(subClass, superClass); }
function _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }
function _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = _getPrototypeOf(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _getPrototypeOf(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _possibleConstructorReturn(this, result); }; }
function _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === "object" || typeof call === "function")) { return call; } else if (call !== void 0) { throw new TypeError("Derived constructors may only return object or undefined"); } return _assertThisInitialized(self); }
function _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError("this hasn't been initialised - super() hasn't been called"); } return self; }
function _isNativeReflectConstruct() { if (typeof Reflect === "undefined" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === "function") return true; try { Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); return true; } catch (e) { return false; } }
function _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }



/**
 * @typedef {Object} DescribeQueryPropertyResult
 *
 * 当前对象并不存在，此处仅用来描述数据结构.{@link QueryPropertyResult},由于用户数据结果不固定，此处仅说明管道系统数据的固定字段
 *
 * @property  {String} usid 系统唯一标识符
 * @property  {String} uskey 用户的唯一标识符
 * @property  {String} service 数据服务的名称，如"block_a000003_gd_line"
 * @property  {String} geometry geojson几何描述字符串，MultiLineString或者 MultiPoint类型
 * @property  {String} [geometryType] 管线数据 => "point" || "line"
 * @property  {String} [usedeep] 管段终点埋深. deep of end position的意思.
 * @property  {String} [usept_key] 管段终点（管点）的【用户的唯一标识符】 （uskey）
 * @property  {String} [usealt] 管段终点高度
 * @property  {String} [ussdeep] 管段起点埋深. deep of start position的意思.
 * @property  {String} [usspt_key] 管段起点（管点）的【用户的唯一标识符】 （uskey）
 * @property  {String} [ussalt] 管段起点高度
 * @property  {String} [uspdiam] 圆管管段的管径
 * @property  {String} [uspheight] 方管管段的高度
 * @property  {String} [uspwidth] 方管管段的宽度
 * @property  {String} [uswell_id] 管点数据才有,形如  "e84e4153-eeae-4ec3-a380-5f2fe78a5267"
 * @property  {String} [usflowdir] 管段内的流向，参考{@link PipeLineDir}
 */

/**
 * @extends ResponseResult
 */
var QueryPropertyResult = /*#__PURE__*/function (_ResponseResult) {
  _inherits(QueryPropertyResult, _ResponseResult);
  var _super = _createSuper(QueryPropertyResult);
  /**
   * 图层属性查询结果类。在{@link Query}类中执行属性查询、空间查询、空间属性联合查询得到，不可以直接初始化.
   *
   * @param options {Object} 输入项
   * @param {ArrayBuffer|String|JSON|Blob|Response} options.response 参考{@link ResponseResult}
   */
  function QueryPropertyResult(options) {
    var _this;
    _classCallCheck(this, QueryPropertyResult);
    _this = _super.call(this, options);
    _this._records = [];
    if (!_this.data) {
      return _possibleConstructorReturn(_this);
    }
    var services = [].concat(_this._data);
    services = (0,_core_KeysToLowerCase_js__WEBPACK_IMPORTED_MODULE_1__["default"])(services, true);
    _this._json = services;

    // dataquery中只有属性查询/空间查询支持多图层,所以遍历一下
    for (var i = 0; i < services.length; i++) {
      var service = services[i];
      if (Number(service["recordnum"]) > 0) {
        var records = [].concat(service["records"]);
        for (var j = 0; j < records.length; j++) {
          records[j]["servicename"] = service["servicename"];
        }
        _this._records = _this._records.concat(records);
      }
    }
    return _this;
  }

  /**
   * 查询
   * @readonly
   * @return {number}
   */
  _createClass(QueryPropertyResult, [{
    key: "recordCount",
    get: function get() {
      return this._records.length;
    }

    /**
     * 获取指定索引处的字段信息
     * @param {Number}index 字段列表中的索引位置
     * @return {undefined|FieldInfo}
     */
  }, {
    key: "getRecord",
    value: function getRecord(index) {
      return this._records[index];
    }
  }]);
  return QueryPropertyResult;
}(_ResponseResult_js__WEBPACK_IMPORTED_MODULE_0__["default"]);
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (QueryPropertyResult);

/***/ }),

/***/ "./src/results/ResponseResult.js":
/*!***************************************!*\
  !*** ./src/results/ResponseResult.js ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _core_defined_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../core/defined.js */ "./src/core/defined.js");
/* harmony import */ var _core_RuntimeError_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../core/RuntimeError.js */ "./src/core/RuntimeError.js");
function _typeof(obj) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && "function" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }, _typeof(obj); }
function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }
function _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }
function _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, "prototype", { writable: false }); return Constructor; }
function _toPropertyKey(arg) { var key = _toPrimitive(arg, "string"); return _typeof(key) === "symbol" ? key : String(key); }
function _toPrimitive(input, hint) { if (_typeof(input) !== "object" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || "default"); if (_typeof(res) !== "object") return res; throw new TypeError("@@toPrimitive must return a primitive value."); } return (hint === "string" ? String : Number)(input); }


var ResponseResult = /*#__PURE__*/function () {
  /**
   * 网络请求返回结果
   * @param options {Object} 输入项
   * @param {ArrayBuffer|String|Object|Array|Blob|Response} options.response 网络请求解析结果
   */
  function ResponseResult(options) {
    _classCallCheck(this, ResponseResult);
    if (!(0,_core_defined_js__WEBPACK_IMPORTED_MODULE_0__["default"])(options.response)) {
      throw new _core_RuntimeError_js__WEBPACK_IMPORTED_MODULE_1__["default"]("response参数无效");
    }
    this._url = options.response.url;
    this._status = options.response.status;
    this._statusText = options.response.statusText;
    this._json = null;
    this._data = options.response.data;
    if (this._status !== 200 && this.data) {
      console.warn(this.data);
    }
  }

  /**
   * 服务端响应的原始数据结果
   */
  _createClass(ResponseResult, [{
    key: "data",
    get: function get() {
      return this._data;
    }

    /**
     * 服务发送的url
     */
  }, {
    key: "url",
    get: function get() {
      return this._url;
    }

    /**
     * http响应码 (200,)
     */
  }, {
    key: "status",
    get: function get() {
      return this._status;
    }

    /**
     * 服务端响应结果转成的json对象
     * <br>
     * json对象所有键名为小写.服务端的系统保留字段名以 <strong>US_</strong>开头，该类型字段名替换成<strong>us</strong>开头
     * @return {Array<Object>|Object}
     */
  }, {
    key: "toJSON",
    value: function toJSON() {
      return this._json;
    }
  }]);
  return ResponseResult;
}();
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ResponseResult);

/***/ }),

/***/ "./src/service/BIMQuery.js":
/*!*********************************!*\
  !*** ./src/service/BIMQuery.js ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _results_ResponseResult_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../results/ResponseResult.js */ "./src/results/ResponseResult.js");
/* harmony import */ var _RegisterServer_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../RegisterServer.js */ "./src/RegisterServer.js");
/* harmony import */ var _SupportedVersion_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../SupportedVersion.js */ "./src/SupportedVersion.js");
/* harmony import */ var _warning_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../warning.js */ "./src/warning.js");
/* harmony import */ var _core_Resource_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../core/Resource.js */ "./src/core/Resource.js");
function _typeof(obj) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && "function" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }, _typeof(obj); }
function _regeneratorRuntime() { "use strict"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return exports; }; var exports = {}, Op = Object.prototype, hasOwn = Op.hasOwnProperty, defineProperty = Object.defineProperty || function (obj, key, desc) { obj[key] = desc.value; }, $Symbol = "function" == typeof Symbol ? Symbol : {}, iteratorSymbol = $Symbol.iterator || "@@iterator", asyncIteratorSymbol = $Symbol.asyncIterator || "@@asyncIterator", toStringTagSymbol = $Symbol.toStringTag || "@@toStringTag"; function define(obj, key, value) { return Object.defineProperty(obj, key, { value: value, enumerable: !0, configurable: !0, writable: !0 }), obj[key]; } try { define({}, ""); } catch (err) { define = function define(obj, key, value) { return obj[key] = value; }; } function wrap(innerFn, outerFn, self, tryLocsList) { var protoGenerator = outerFn && outerFn.prototype instanceof Generator ? outerFn : Generator, generator = Object.create(protoGenerator.prototype), context = new Context(tryLocsList || []); return defineProperty(generator, "_invoke", { value: makeInvokeMethod(innerFn, self, context) }), generator; } function tryCatch(fn, obj, arg) { try { return { type: "normal", arg: fn.call(obj, arg) }; } catch (err) { return { type: "throw", arg: err }; } } exports.wrap = wrap; var ContinueSentinel = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var IteratorPrototype = {}; define(IteratorPrototype, iteratorSymbol, function () { return this; }); var getProto = Object.getPrototypeOf, NativeIteratorPrototype = getProto && getProto(getProto(values([]))); NativeIteratorPrototype && NativeIteratorPrototype !== Op && hasOwn.call(NativeIteratorPrototype, iteratorSymbol) && (IteratorPrototype = NativeIteratorPrototype); var Gp = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(IteratorPrototype); function defineIteratorMethods(prototype) { ["next", "throw", "return"].forEach(function (method) { define(prototype, method, function (arg) { return this._invoke(method, arg); }); }); } function AsyncIterator(generator, PromiseImpl) { function invoke(method, arg, resolve, reject) { var record = tryCatch(generator[method], generator, arg); if ("throw" !== record.type) { var result = record.arg, value = result.value; return value && "object" == _typeof(value) && hasOwn.call(value, "__await") ? PromiseImpl.resolve(value.__await).then(function (value) { invoke("next", value, resolve, reject); }, function (err) { invoke("throw", err, resolve, reject); }) : PromiseImpl.resolve(value).then(function (unwrapped) { result.value = unwrapped, resolve(result); }, function (error) { return invoke("throw", error, resolve, reject); }); } reject(record.arg); } var previousPromise; defineProperty(this, "_invoke", { value: function value(method, arg) { function callInvokeWithMethodAndArg() { return new PromiseImpl(function (resolve, reject) { invoke(method, arg, resolve, reject); }); } return previousPromise = previousPromise ? previousPromise.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(innerFn, self, context) { var state = "suspendedStart"; return function (method, arg) { if ("executing" === state) throw new Error("Generator is already running"); if ("completed" === state) { if ("throw" === method) throw arg; return doneResult(); } for (context.method = method, context.arg = arg;;) { var delegate = context.delegate; if (delegate) { var delegateResult = maybeInvokeDelegate(delegate, context); if (delegateResult) { if (delegateResult === ContinueSentinel) continue; return delegateResult; } } if ("next" === context.method) context.sent = context._sent = context.arg;else if ("throw" === context.method) { if ("suspendedStart" === state) throw state = "completed", context.arg; context.dispatchException(context.arg); } else "return" === context.method && context.abrupt("return", context.arg); state = "executing"; var record = tryCatch(innerFn, self, context); if ("normal" === record.type) { if (state = context.done ? "completed" : "suspendedYield", record.arg === ContinueSentinel) continue; return { value: record.arg, done: context.done }; } "throw" === record.type && (state = "completed", context.method = "throw", context.arg = record.arg); } }; } function maybeInvokeDelegate(delegate, context) { var methodName = context.method, method = delegate.iterator[methodName]; if (undefined === method) return context.delegate = null, "throw" === methodName && delegate.iterator["return"] && (context.method = "return", context.arg = undefined, maybeInvokeDelegate(delegate, context), "throw" === context.method) || "return" !== methodName && (context.method = "throw", context.arg = new TypeError("The iterator does not provide a '" + methodName + "' method")), ContinueSentinel; var record = tryCatch(method, delegate.iterator, context.arg); if ("throw" === record.type) return context.method = "throw", context.arg = record.arg, context.delegate = null, ContinueSentinel; var info = record.arg; return info ? info.done ? (context[delegate.resultName] = info.value, context.next = delegate.nextLoc, "return" !== context.method && (context.method = "next", context.arg = undefined), context.delegate = null, ContinueSentinel) : info : (context.method = "throw", context.arg = new TypeError("iterator result is not an object"), context.delegate = null, ContinueSentinel); } function pushTryEntry(locs) { var entry = { tryLoc: locs[0] }; 1 in locs && (entry.catchLoc = locs[1]), 2 in locs && (entry.finallyLoc = locs[2], entry.afterLoc = locs[3]), this.tryEntries.push(entry); } function resetTryEntry(entry) { var record = entry.completion || {}; record.type = "normal", delete record.arg, entry.completion = record; } function Context(tryLocsList) { this.tryEntries = [{ tryLoc: "root" }], tryLocsList.forEach(pushTryEntry, this), this.reset(!0); } function values(iterable) { if (iterable) { var iteratorMethod = iterable[iteratorSymbol]; if (iteratorMethod) return iteratorMethod.call(iterable); if ("function" == typeof iterable.next) return iterable; if (!isNaN(iterable.length)) { var i = -1, next = function next() { for (; ++i < iterable.length;) if (hasOwn.call(iterable, i)) return next.value = iterable[i], next.done = !1, next; return next.value = undefined, next.done = !0, next; }; return next.next = next; } } return { next: doneResult }; } function doneResult() { return { value: undefined, done: !0 }; } return GeneratorFunction.prototype = GeneratorFunctionPrototype, defineProperty(Gp, "constructor", { value: GeneratorFunctionPrototype, configurable: !0 }), defineProperty(GeneratorFunctionPrototype, "constructor", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, toStringTagSymbol, "GeneratorFunction"), exports.isGeneratorFunction = function (genFun) { var ctor = "function" == typeof genFun && genFun.constructor; return !!ctor && (ctor === GeneratorFunction || "GeneratorFunction" === (ctor.displayName || ctor.name)); }, exports.mark = function (genFun) { return Object.setPrototypeOf ? Object.setPrototypeOf(genFun, GeneratorFunctionPrototype) : (genFun.__proto__ = GeneratorFunctionPrototype, define(genFun, toStringTagSymbol, "GeneratorFunction")), genFun.prototype = Object.create(Gp), genFun; }, exports.awrap = function (arg) { return { __await: arg }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, asyncIteratorSymbol, function () { return this; }), exports.AsyncIterator = AsyncIterator, exports.async = function (innerFn, outerFn, self, tryLocsList, PromiseImpl) { void 0 === PromiseImpl && (PromiseImpl = Promise); var iter = new AsyncIterator(wrap(innerFn, outerFn, self, tryLocsList), PromiseImpl); return exports.isGeneratorFunction(outerFn) ? iter : iter.next().then(function (result) { return result.done ? result.value : iter.next(); }); }, defineIteratorMethods(Gp), define(Gp, toStringTagSymbol, "Generator"), define(Gp, iteratorSymbol, function () { return this; }), define(Gp, "toString", function () { return "[object Generator]"; }), exports.keys = function (val) { var object = Object(val), keys = []; for (var key in object) keys.push(key); return keys.reverse(), function next() { for (; keys.length;) { var key = keys.pop(); if (key in object) return next.value = key, next.done = !1, next; } return next.done = !0, next; }; }, exports.values = values, Context.prototype = { constructor: Context, reset: function reset(skipTempReset) { if (this.prev = 0, this.next = 0, this.sent = this._sent = undefined, this.done = !1, this.delegate = null, this.method = "next", this.arg = undefined, this.tryEntries.forEach(resetTryEntry), !skipTempReset) for (var name in this) "t" === name.charAt(0) && hasOwn.call(this, name) && !isNaN(+name.slice(1)) && (this[name] = undefined); }, stop: function stop() { this.done = !0; var rootRecord = this.tryEntries[0].completion; if ("throw" === rootRecord.type) throw rootRecord.arg; return this.rval; }, dispatchException: function dispatchException(exception) { if (this.done) throw exception; var context = this; function handle(loc, caught) { return record.type = "throw", record.arg = exception, context.next = loc, caught && (context.method = "next", context.arg = undefined), !!caught; } for (var i = this.tryEntries.length - 1; i >= 0; --i) { var entry = this.tryEntries[i], record = entry.completion; if ("root" === entry.tryLoc) return handle("end"); if (entry.tryLoc <= this.prev) { var hasCatch = hasOwn.call(entry, "catchLoc"), hasFinally = hasOwn.call(entry, "finallyLoc"); if (hasCatch && hasFinally) { if (this.prev < entry.catchLoc) return handle(entry.catchLoc, !0); if (this.prev < entry.finallyLoc) return handle(entry.finallyLoc); } else if (hasCatch) { if (this.prev < entry.catchLoc) return handle(entry.catchLoc, !0); } else { if (!hasFinally) throw new Error("try statement without catch or finally"); if (this.prev < entry.finallyLoc) return handle(entry.finallyLoc); } } } }, abrupt: function abrupt(type, arg) { for (var i = this.tryEntries.length - 1; i >= 0; --i) { var entry = this.tryEntries[i]; if (entry.tryLoc <= this.prev && hasOwn.call(entry, "finallyLoc") && this.prev < entry.finallyLoc) { var finallyEntry = entry; break; } } finallyEntry && ("break" === type || "continue" === type) && finallyEntry.tryLoc <= arg && arg <= finallyEntry.finallyLoc && (finallyEntry = null); var record = finallyEntry ? finallyEntry.completion : {}; return record.type = type, record.arg = arg, finallyEntry ? (this.method = "next", this.next = finallyEntry.finallyLoc, ContinueSentinel) : this.complete(record); }, complete: function complete(record, afterLoc) { if ("throw" === record.type) throw record.arg; return "break" === record.type || "continue" === record.type ? this.next = record.arg : "return" === record.type ? (this.rval = this.arg = record.arg, this.method = "return", this.next = "end") : "normal" === record.type && afterLoc && (this.next = afterLoc), ContinueSentinel; }, finish: function finish(finallyLoc) { for (var i = this.tryEntries.length - 1; i >= 0; --i) { var entry = this.tryEntries[i]; if (entry.finallyLoc === finallyLoc) return this.complete(entry.completion, entry.afterLoc), resetTryEntry(entry), ContinueSentinel; } }, "catch": function _catch(tryLoc) { for (var i = this.tryEntries.length - 1; i >= 0; --i) { var entry = this.tryEntries[i]; if (entry.tryLoc === tryLoc) { var record = entry.completion; if ("throw" === record.type) { var thrown = record.arg; resetTryEntry(entry); } return thrown; } } throw new Error("illegal catch attempt"); }, delegateYield: function delegateYield(iterable, resultName, nextLoc) { return this.delegate = { iterator: values(iterable), resultName: resultName, nextLoc: nextLoc }, "next" === this.method && (this.arg = undefined), ContinueSentinel; } }, exports; }
function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }
function _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }
function _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, "prototype", { writable: false }); return Constructor; }
function _toPropertyKey(arg) { var key = _toPrimitive(arg, "string"); return _typeof(key) === "symbol" ? key : String(key); }
function _toPrimitive(input, hint) { if (_typeof(input) !== "object" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || "default"); if (_typeof(res) !== "object") return res; throw new TypeError("@@toPrimitive must return a primitive value."); } return (hint === "string" ? String : Number)(input); }
function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }
function _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err); } _next(undefined); }); }; }





function verifyServerVersionAndInitUrl(_x) {
  return _verifyServerVersionAndInitUrl.apply(this, arguments);
}
/**
 * BIM查询相关类.
 */
function _verifyServerVersionAndInitUrl() {
  _verifyServerVersionAndInitUrl = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee(serverUrl) {
    var version, url;
    return _regeneratorRuntime().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          _context.next = 2;
          return _RegisterServer_js__WEBPACK_IMPORTED_MODULE_1__["default"].getVersionByServiceAnyURI(serverUrl);
        case 2:
          version = _context.sent;
          if (!(version < _SupportedVersion_js__WEBPACK_IMPORTED_MODULE_2__["default"].r320)) {
            _context.next = 6;
            break;
          }
          _warning_js__WEBPACK_IMPORTED_MODULE_3__["default"].ByVersion("BIMQuery", _SupportedVersion_js__WEBPACK_IMPORTED_MODULE_2__["default"].r320, version);
          return _context.abrupt("return", null);
        case 6:
          url = new URL(serverUrl);
          url.search = "";
          url.hash = "";
          return _context.abrupt("return", {
            version: version,
            url: url
          });
        case 10:
        case "end":
          return _context.stop();
      }
    }, _callee);
  }));
  return _verifyServerVersionAndInitUrl.apply(this, arguments);
}
var BIMQuery = /*#__PURE__*/function () {
  function BIMQuery() {
    _classCallCheck(this, BIMQuery);
  }
  _createClass(BIMQuery, null, [{
    key: "queryDataStructure",
    value:
    /**
     * 结构化查询，返回对象中可以看到部件按族分类。在数据发布时，勾选试用楼层发布的BIM数据会额外拥有按照楼层分布的数据的信息
     * @param serverUrl {String} GServer服务地址
     * @param layerId {String} BIM数据唯一表示uuid
     * @return {Promise<ResponseResult>}
     */
    function queryDataStructure(serverUrl, layerId) {
      return verifyServerVersionAndInitUrl(serverUrl).then(function (result) {
        if (!result) {
          return Promise.reject("服务版本不支持");
        }
        var version = result.version;
        switch (version) {
          case _SupportedVersion_js__WEBPACK_IMPORTED_MODULE_2__["default"].r320:
          case _SupportedVersion_js__WEBPACK_IMPORTED_MODULE_2__["default"].r330:
            {
              var url = new URL("./search", serverUrl);
              var searchParams = url.searchParams;
              searchParams.set("t", "4");
              searchParams.set("c", layerId);
              searchParams.set("version", "2");
              return _core_Resource_js__WEBPACK_IMPORTED_MODULE_4__["default"].getJSON({
                url: url.toString()
              });
            }
          // case SupportedVersion.r340:
          // case SupportedVersion.r341:
          // case SupportedVersion.r350: 
          default:
            {
              var _url = new URL("./v2/bim/struct/".concat(layerId), serverUrl);
              return _core_Resource_js__WEBPACK_IMPORTED_MODULE_4__["default"].postJSON({
                url: _url.toString(),
                body: "",
                headers: {
                  "Content-Type": "application/json;charset=UTF-8"
                }
              });
            }
          // default: {
          //     return Promise.reject("无法识别的服务版本");
          // }
        }
      }).then(function (response) {
        // 不能按照楼层数目排序，因为该level也可能叫室外地坪等其他名称，不一定是数字
        return new _results_ResponseResult_js__WEBPACK_IMPORTED_MODULE_0__["default"]({
          response: response
        });
      });
    }

    /**
     * 部件查询(单体化查询)
     * @param serverUrl {String} GServer服务地址
     * @param layerId {String} BIM数据唯一表示uuid,3.2-3.3版本服务使用的是图层名称
     * @param elementId {String} 部件的唯一表示uuid
     * @return {Promise<ResponseResult>}
     */
  }, {
    key: "queryPartInfo",
    value: function queryPartInfo(serverUrl, layerId, elementId) {
      return verifyServerVersionAndInitUrl(serverUrl).then(function (result) {
        if (!result) {
          return Promise.reject("服务版本不支持");
        }
        var version = result.version;
        switch (version) {
          case _SupportedVersion_js__WEBPACK_IMPORTED_MODULE_2__["default"].r320:
          case _SupportedVersion_js__WEBPACK_IMPORTED_MODULE_2__["default"].r330:
            {
              var url = new URL("./search", serverUrl);
              var searchParams = url.searchParams;
              searchParams.set("t", "5");
              searchParams.set("c", layerId);
              searchParams.set("k", elementId);
              searchParams.set("version", "2");
              return _core_Resource_js__WEBPACK_IMPORTED_MODULE_4__["default"].getJSON({
                url: url.toString()
              });
            }
          // case SupportedVersion.r340:
          // case SupportedVersion.r341:
          // case SupportedVersion.r350:
          default:
            {
              var _url2 = new URL("./v2/bim/single/".concat(layerId, "/").concat(elementId), serverUrl);
              return _core_Resource_js__WEBPACK_IMPORTED_MODULE_4__["default"].postJSON({
                url: _url2.toString(),
                body: "",
                headers: {
                  "Content-Type": "application/json;charset=UTF-8"
                }
              });
            }
          // default: {
          //     return Promise.reject("无法识别的服务版本");
          // }
        }
      }).then(function (response) {
        // 不能按照楼层数目排序，因为该level也可能叫室外地坪等其他名称，不一定是数字
        return new _results_ResponseResult_js__WEBPACK_IMPORTED_MODULE_0__["default"]({
          response: response
        });
      });
    }
  }]);
  return BIMQuery;
}();
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (BIMQuery);

/***/ }),

/***/ "./src/service/GeologicalAnalysis.js":
/*!*******************************************!*\
  !*** ./src/service/GeologicalAnalysis.js ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _RegisterServer_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../RegisterServer.js */ "./src/RegisterServer.js");
/* harmony import */ var _warning_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../warning.js */ "./src/warning.js");
/* harmony import */ var _SupportedVersion_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../SupportedVersion.js */ "./src/SupportedVersion.js");
function _typeof(obj) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && "function" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }, _typeof(obj); }
function _regeneratorRuntime() { "use strict"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return exports; }; var exports = {}, Op = Object.prototype, hasOwn = Op.hasOwnProperty, defineProperty = Object.defineProperty || function (obj, key, desc) { obj[key] = desc.value; }, $Symbol = "function" == typeof Symbol ? Symbol : {}, iteratorSymbol = $Symbol.iterator || "@@iterator", asyncIteratorSymbol = $Symbol.asyncIterator || "@@asyncIterator", toStringTagSymbol = $Symbol.toStringTag || "@@toStringTag"; function define(obj, key, value) { return Object.defineProperty(obj, key, { value: value, enumerable: !0, configurable: !0, writable: !0 }), obj[key]; } try { define({}, ""); } catch (err) { define = function define(obj, key, value) { return obj[key] = value; }; } function wrap(innerFn, outerFn, self, tryLocsList) { var protoGenerator = outerFn && outerFn.prototype instanceof Generator ? outerFn : Generator, generator = Object.create(protoGenerator.prototype), context = new Context(tryLocsList || []); return defineProperty(generator, "_invoke", { value: makeInvokeMethod(innerFn, self, context) }), generator; } function tryCatch(fn, obj, arg) { try { return { type: "normal", arg: fn.call(obj, arg) }; } catch (err) { return { type: "throw", arg: err }; } } exports.wrap = wrap; var ContinueSentinel = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var IteratorPrototype = {}; define(IteratorPrototype, iteratorSymbol, function () { return this; }); var getProto = Object.getPrototypeOf, NativeIteratorPrototype = getProto && getProto(getProto(values([]))); NativeIteratorPrototype && NativeIteratorPrototype !== Op && hasOwn.call(NativeIteratorPrototype, iteratorSymbol) && (IteratorPrototype = NativeIteratorPrototype); var Gp = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(IteratorPrototype); function defineIteratorMethods(prototype) { ["next", "throw", "return"].forEach(function (method) { define(prototype, method, function (arg) { return this._invoke(method, arg); }); }); } function AsyncIterator(generator, PromiseImpl) { function invoke(method, arg, resolve, reject) { var record = tryCatch(generator[method], generator, arg); if ("throw" !== record.type) { var result = record.arg, value = result.value; return value && "object" == _typeof(value) && hasOwn.call(value, "__await") ? PromiseImpl.resolve(value.__await).then(function (value) { invoke("next", value, resolve, reject); }, function (err) { invoke("throw", err, resolve, reject); }) : PromiseImpl.resolve(value).then(function (unwrapped) { result.value = unwrapped, resolve(result); }, function (error) { return invoke("throw", error, resolve, reject); }); } reject(record.arg); } var previousPromise; defineProperty(this, "_invoke", { value: function value(method, arg) { function callInvokeWithMethodAndArg() { return new PromiseImpl(function (resolve, reject) { invoke(method, arg, resolve, reject); }); } return previousPromise = previousPromise ? previousPromise.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(innerFn, self, context) { var state = "suspendedStart"; return function (method, arg) { if ("executing" === state) throw new Error("Generator is already running"); if ("completed" === state) { if ("throw" === method) throw arg; return doneResult(); } for (context.method = method, context.arg = arg;;) { var delegate = context.delegate; if (delegate) { var delegateResult = maybeInvokeDelegate(delegate, context); if (delegateResult) { if (delegateResult === ContinueSentinel) continue; return delegateResult; } } if ("next" === context.method) context.sent = context._sent = context.arg;else if ("throw" === context.method) { if ("suspendedStart" === state) throw state = "completed", context.arg; context.dispatchException(context.arg); } else "return" === context.method && context.abrupt("return", context.arg); state = "executing"; var record = tryCatch(innerFn, self, context); if ("normal" === record.type) { if (state = context.done ? "completed" : "suspendedYield", record.arg === ContinueSentinel) continue; return { value: record.arg, done: context.done }; } "throw" === record.type && (state = "completed", context.method = "throw", context.arg = record.arg); } }; } function maybeInvokeDelegate(delegate, context) { var methodName = context.method, method = delegate.iterator[methodName]; if (undefined === method) return context.delegate = null, "throw" === methodName && delegate.iterator["return"] && (context.method = "return", context.arg = undefined, maybeInvokeDelegate(delegate, context), "throw" === context.method) || "return" !== methodName && (context.method = "throw", context.arg = new TypeError("The iterator does not provide a '" + methodName + "' method")), ContinueSentinel; var record = tryCatch(method, delegate.iterator, context.arg); if ("throw" === record.type) return context.method = "throw", context.arg = record.arg, context.delegate = null, ContinueSentinel; var info = record.arg; return info ? info.done ? (context[delegate.resultName] = info.value, context.next = delegate.nextLoc, "return" !== context.method && (context.method = "next", context.arg = undefined), context.delegate = null, ContinueSentinel) : info : (context.method = "throw", context.arg = new TypeError("iterator result is not an object"), context.delegate = null, ContinueSentinel); } function pushTryEntry(locs) { var entry = { tryLoc: locs[0] }; 1 in locs && (entry.catchLoc = locs[1]), 2 in locs && (entry.finallyLoc = locs[2], entry.afterLoc = locs[3]), this.tryEntries.push(entry); } function resetTryEntry(entry) { var record = entry.completion || {}; record.type = "normal", delete record.arg, entry.completion = record; } function Context(tryLocsList) { this.tryEntries = [{ tryLoc: "root" }], tryLocsList.forEach(pushTryEntry, this), this.reset(!0); } function values(iterable) { if (iterable) { var iteratorMethod = iterable[iteratorSymbol]; if (iteratorMethod) return iteratorMethod.call(iterable); if ("function" == typeof iterable.next) return iterable; if (!isNaN(iterable.length)) { var i = -1, next = function next() { for (; ++i < iterable.length;) if (hasOwn.call(iterable, i)) return next.value = iterable[i], next.done = !1, next; return next.value = undefined, next.done = !0, next; }; return next.next = next; } } return { next: doneResult }; } function doneResult() { return { value: undefined, done: !0 }; } return GeneratorFunction.prototype = GeneratorFunctionPrototype, defineProperty(Gp, "constructor", { value: GeneratorFunctionPrototype, configurable: !0 }), defineProperty(GeneratorFunctionPrototype, "constructor", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, toStringTagSymbol, "GeneratorFunction"), exports.isGeneratorFunction = function (genFun) { var ctor = "function" == typeof genFun && genFun.constructor; return !!ctor && (ctor === GeneratorFunction || "GeneratorFunction" === (ctor.displayName || ctor.name)); }, exports.mark = function (genFun) { return Object.setPrototypeOf ? Object.setPrototypeOf(genFun, GeneratorFunctionPrototype) : (genFun.__proto__ = GeneratorFunctionPrototype, define(genFun, toStringTagSymbol, "GeneratorFunction")), genFun.prototype = Object.create(Gp), genFun; }, exports.awrap = function (arg) { return { __await: arg }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, asyncIteratorSymbol, function () { return this; }), exports.AsyncIterator = AsyncIterator, exports.async = function (innerFn, outerFn, self, tryLocsList, PromiseImpl) { void 0 === PromiseImpl && (PromiseImpl = Promise); var iter = new AsyncIterator(wrap(innerFn, outerFn, self, tryLocsList), PromiseImpl); return exports.isGeneratorFunction(outerFn) ? iter : iter.next().then(function (result) { return result.done ? result.value : iter.next(); }); }, defineIteratorMethods(Gp), define(Gp, toStringTagSymbol, "Generator"), define(Gp, iteratorSymbol, function () { return this; }), define(Gp, "toString", function () { return "[object Generator]"; }), exports.keys = function (val) { var object = Object(val), keys = []; for (var key in object) keys.push(key); return keys.reverse(), function next() { for (; keys.length;) { var key = keys.pop(); if (key in object) return next.value = key, next.done = !1, next; } return next.done = !0, next; }; }, exports.values = values, Context.prototype = { constructor: Context, reset: function reset(skipTempReset) { if (this.prev = 0, this.next = 0, this.sent = this._sent = undefined, this.done = !1, this.delegate = null, this.method = "next", this.arg = undefined, this.tryEntries.forEach(resetTryEntry), !skipTempReset) for (var name in this) "t" === name.charAt(0) && hasOwn.call(this, name) && !isNaN(+name.slice(1)) && (this[name] = undefined); }, stop: function stop() { this.done = !0; var rootRecord = this.tryEntries[0].completion; if ("throw" === rootRecord.type) throw rootRecord.arg; return this.rval; }, dispatchException: function dispatchException(exception) { if (this.done) throw exception; var context = this; function handle(loc, caught) { return record.type = "throw", record.arg = exception, context.next = loc, caught && (context.method = "next", context.arg = undefined), !!caught; } for (var i = this.tryEntries.length - 1; i >= 0; --i) { var entry = this.tryEntries[i], record = entry.completion; if ("root" === entry.tryLoc) return handle("end"); if (entry.tryLoc <= this.prev) { var hasCatch = hasOwn.call(entry, "catchLoc"), hasFinally = hasOwn.call(entry, "finallyLoc"); if (hasCatch && hasFinally) { if (this.prev < entry.catchLoc) return handle(entry.catchLoc, !0); if (this.prev < entry.finallyLoc) return handle(entry.finallyLoc); } else if (hasCatch) { if (this.prev < entry.catchLoc) return handle(entry.catchLoc, !0); } else { if (!hasFinally) throw new Error("try statement without catch or finally"); if (this.prev < entry.finallyLoc) return handle(entry.finallyLoc); } } } }, abrupt: function abrupt(type, arg) { for (var i = this.tryEntries.length - 1; i >= 0; --i) { var entry = this.tryEntries[i]; if (entry.tryLoc <= this.prev && hasOwn.call(entry, "finallyLoc") && this.prev < entry.finallyLoc) { var finallyEntry = entry; break; } } finallyEntry && ("break" === type || "continue" === type) && finallyEntry.tryLoc <= arg && arg <= finallyEntry.finallyLoc && (finallyEntry = null); var record = finallyEntry ? finallyEntry.completion : {}; return record.type = type, record.arg = arg, finallyEntry ? (this.method = "next", this.next = finallyEntry.finallyLoc, ContinueSentinel) : this.complete(record); }, complete: function complete(record, afterLoc) { if ("throw" === record.type) throw record.arg; return "break" === record.type || "continue" === record.type ? this.next = record.arg : "return" === record.type ? (this.rval = this.arg = record.arg, this.method = "return", this.next = "end") : "normal" === record.type && afterLoc && (this.next = afterLoc), ContinueSentinel; }, finish: function finish(finallyLoc) { for (var i = this.tryEntries.length - 1; i >= 0; --i) { var entry = this.tryEntries[i]; if (entry.finallyLoc === finallyLoc) return this.complete(entry.completion, entry.afterLoc), resetTryEntry(entry), ContinueSentinel; } }, "catch": function _catch(tryLoc) { for (var i = this.tryEntries.length - 1; i >= 0; --i) { var entry = this.tryEntries[i]; if (entry.tryLoc === tryLoc) { var record = entry.completion; if ("throw" === record.type) { var thrown = record.arg; resetTryEntry(entry); } return thrown; } } throw new Error("illegal catch attempt"); }, delegateYield: function delegateYield(iterable, resultName, nextLoc) { return this.delegate = { iterator: values(iterable), resultName: resultName, nextLoc: nextLoc }, "next" === this.method && (this.arg = undefined), ContinueSentinel; } }, exports; }
function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }
function _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }
function _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, "prototype", { writable: false }); return Constructor; }
function _toPropertyKey(arg) { var key = _toPrimitive(arg, "string"); return _typeof(key) === "symbol" ? key : String(key); }
function _toPrimitive(input, hint) { if (_typeof(input) !== "object" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || "default"); if (_typeof(res) !== "object") return res; throw new TypeError("@@toPrimitive must return a primitive value."); } return (hint === "string" ? String : Number)(input); }
function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }
function _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err); } _next(undefined); }); }; }



function preBuildUrl(_x, _x2) {
  return _preBuildUrl.apply(this, arguments);
}
/**
 * @typedef {Object} GeologicalAnalysisResult
 * @property {String} url 拼接的URL
 */
/**
 * 地质体分析服务
 */
function _preBuildUrl() {
  _preBuildUrl = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee(serverUrl, endpoint) {
    var version, url;
    return _regeneratorRuntime().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          _context.next = 2;
          return _RegisterServer_js__WEBPACK_IMPORTED_MODULE_0__["default"].getVersionByServiceAnyURI(serverUrl);
        case 2:
          version = _context.sent;
          if (!(version < _SupportedVersion_js__WEBPACK_IMPORTED_MODULE_2__["default"].r341)) {
            _context.next = 6;
            break;
          }
          _warning_js__WEBPACK_IMPORTED_MODULE_1__["default"].ByVersion("GeologicalAnalysis ".concat(endpoint), _SupportedVersion_js__WEBPACK_IMPORTED_MODULE_2__["default"].r341, version);
          return _context.abrupt("return", null);
        case 6:
          url = new URL("./v2/geologicalAnalysis/".concat(endpoint), serverUrl);
          url.search = "";
          url.hash = "";
          return _context.abrupt("return", url);
        case 10:
        case "end":
          return _context.stop();
      }
    }, _callee);
  }));
  return _preBuildUrl.apply(this, arguments);
}
var GeologicalAnalysis = /*#__PURE__*/function () {
  function GeologicalAnalysis() {
    _classCallCheck(this, GeologicalAnalysis);
  }
  _createClass(GeologicalAnalysis, null, [{
    key: "slice",
    value:
    /**
     * 面剖切，线段或者或者多个线段
     * @param serverUrl {String} 部署服务的地址。推荐写成<code>http://*************/license</code> 形式,便于自检.
     * @param layerGuid {String} 图层guid
     * @param lineSegments {Array.<Number|Array.<Number>>}线段,或者多个线段（仅有两个端点），三维经纬度坐标
     * @param rotateAngle {Number} 剖面夹角，指以终点到起点线段为旋转轴，剖面与水平面逆时针方向夹角，单位为度
     * @param offset {Number} 线的横向偏移距离，单位为米
     * @return {Promise<GeologicalAnalysisResult>}
     */
    function slice(serverUrl, layerGuid, lineSegments, rotateAngle, offset) {
      return preBuildUrl(serverUrl, "slice").then(function (url) {
        if (!url) {
          return Promise.reject("服务版本不支持");
        }
        var searchParams = new URLSearchParams();
        searchParams.set("LayerGuid", layerGuid);
        searchParams.set("Lines", lineSegments.flat(Infinity).join(","));
        searchParams.set("RotateAngle", rotateAngle.toString());
        searchParams.set("Offset", offset.toString());
        url.search = searchParams.toString();
        return {
          url: url.toString()
        };
      });
    }

    /**
     * 连续线剖切。
     * @param serverUrl {String} 部署服务的地址。推荐写成<code>http://*************/license</code> 形式,便于自检.
     * @param layerGuid {String} 图层guid
     * @param lineStrip {Array.<Number>} 连续线，三维经纬度坐标
     * @return {Promise<GeologicalAnalysisResult>}
     */
  }, {
    key: "sliceStrip",
    value: function sliceStrip(serverUrl, layerGuid, lineStrip) {
      return preBuildUrl(serverUrl, "sliceStrip").then(function (url) {
        if (!url) {
          return Promise.reject("服务版本不支持");
        }
        var searchParams = new URLSearchParams();
        searchParams.set("LayerGuid", layerGuid);
        searchParams.set("LineStrip", lineStrip.flat(Infinity).join(","));
        url.search = searchParams.toString();
        return {
          url: url.toString()
        };
      });
    }

    /**
     * 方形隧道开挖。
     * @param serverUrl {String} 部署服务的地址。推荐写成<code>http://*************/license</code> 形式,便于自检.
     * @param layerGuid {String} 图层guid
     * @param lineStrip {Array.<Number>} 连续线，三维经纬度坐标
     * @param width {Number} 方形隧道断面的宽度，单位为米
     * @param height {Number} 方形隧道断面的高度，单位为米
     * @return {Promise<GeologicalAnalysisResult>}
     */
  }, {
    key: "squareTunnel",
    value: function squareTunnel(serverUrl, layerGuid, lineStrip, width, height) {
      return preBuildUrl(serverUrl, "squareTunnel").then(function (url) {
        if (!url) {
          return Promise.reject("服务版本不支持");
        }
        var searchParams = new URLSearchParams();
        searchParams.set("LayerGuid", layerGuid);
        searchParams.set("Width", width.toString());
        searchParams.set("Height", height.toString());
        searchParams.set("LineStrip", lineStrip.flat(Infinity).join(","));
        url.search = searchParams.toString();
        return {
          url: url.toString()
        };
      });
    }

    /**
     * 圆形隧道开挖。
     * @param serverUrl {String} 部署服务的地址。推荐写成<code>http://*************/license</code> 形式,便于自检.
     * @param layerGuid {String} 图层guid
     * @param lineStrip {Array.<Number>} 连续线，三维经纬度坐标
     * @param radius {Number} 圆形隧道断面的半径，单位为米
     * @return {Promise<GeologicalAnalysisResult>}
     */
  }, {
    key: "circleTunnel",
    value: function circleTunnel(serverUrl, layerGuid, lineStrip, radius) {
      return preBuildUrl(serverUrl, "circleTunnel").then(function (url) {
        if (!url) {
          return Promise.reject("服务版本不支持");
        }
        var searchParams = new URLSearchParams();
        searchParams.set("LayerGuid", layerGuid);
        searchParams.set("Radius", radius.toString());
        searchParams.set("LineStrip", lineStrip.flat(Infinity).join(","));
        url.search = searchParams.toString();
        return {
          url: url.toString()
        };
      });
    }

    /**
     * 基坑开挖。
     * @param serverUrl {String} 部署服务的地址。推荐写成<code>http://*************/license</code> 形式,便于自检.
     * @param layerGuid {String} 图层guid
     * @param polygon {Array.<Number>} 多边形，三维经纬度坐标
     * @param alt {Number} 基坑底面高程值
     * @return {Promise<GeologicalAnalysisResult>}
     */
  }, {
    key: "foundationPit",
    value: function foundationPit(serverUrl, layerGuid, polygon, alt) {
      return preBuildUrl(serverUrl, "foundationPit").then(function (url) {
        if (!url) {
          return Promise.reject("服务版本不支持");
        }
        var searchParams = new URLSearchParams();
        searchParams.set("LayerGuid", layerGuid);
        searchParams.set("Polygon", polygon.flat(Infinity).join(","));
        searchParams.set("Alt", alt.toString());
        url.search = searchParams.toString();
        return {
          url: url.toString()
        };
      });
    }

    /**
     * 壕沟开挖。
     * @param serverUrl {String} 部署服务的地址。推荐写成<code>http://*************/license</code> 形式,便于自检.
     * @param layerGuid {String} 图层guid
     * @param lineStrip {Array.<Number>} 多边形，三维经纬度坐标
     * @param slope {Number} 坡度，单位为度
     * @param width {Number} 壕沟底面宽度，单位为米
     * @return {Promise<GeologicalAnalysisResult>}
     */
  }, {
    key: "trench",
    value: function trench(serverUrl, layerGuid, lineStrip, slope, width) {
      return preBuildUrl(serverUrl, "trench").then(function (url) {
        if (!url) {
          return Promise.reject("服务版本不支持");
        }
        var searchParams = new URLSearchParams();
        searchParams.set("LayerGuid", layerGuid);
        searchParams.set("Slope", slope.toString());
        searchParams.set("Width", width.toString());
        searchParams.set("LineStrip", lineStrip.flat(Infinity).join(","));
        url.search = searchParams.toString();
        return {
          url: url.toString()
        };
      });
    }

    /**
     * 虚拟钻孔。
     * @param serverUrl {String} 部署服务的地址。推荐写成<code>http://*************/license</code> 形式,便于自检.
     * @param layerGuid {String} 图层guid
     * @param points {Array.<Number>} 一个或者多个点，三维经纬度坐标
     * @param radius {Number} 钻孔半径，单位为米
     * @return {Promise<GeologicalAnalysisResult>}
     */
  }, {
    key: "well",
    value: function well(serverUrl, layerGuid, points, radius) {
      return preBuildUrl(serverUrl, "well").then(function (url) {
        if (!url) {
          return Promise.reject("服务版本不支持");
        }
        var searchParams = new URLSearchParams();
        searchParams.set("LayerGuid", layerGuid);
        searchParams.set("Radius", radius.toString());
        searchParams.set("Points", points.flat(Infinity).join(","));
        url.search = searchParams.toString();
        return {
          url: url.toString()
        };
      });
    }
  }]);
  return GeologicalAnalysis;
}();
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (GeologicalAnalysis);

/***/ }),

/***/ "./src/service/PipeLineDir.js":
/*!************************************!*\
  !*** ./src/service/PipeLineDir.js ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/**
 * 管线方向描述,上游、下游、双向，或者说是正向，逆向，双向，在{@link PipingAnalyse}中作为入参使用。
 * @readonly
 * @enum
 */
var PipeLineDir = {
  /**
   * 正向，上游
   * @readonly
   * @type {Number}
   */
  FORWARD: 1,
  /**
   * 逆向。下游
   * @readonly
   * @type {Number}
   */
  REVERSE: 2,
  /**
   * 双向
   * @readonly
   * @type {Number}
   */
  DOUBLE: 3
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Object.freeze(PipeLineDir));

/***/ }),

/***/ "./src/service/PipingAnalyse.js":
/*!**************************************!*\
  !*** ./src/service/PipingAnalyse.js ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _core_defined_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../core/defined.js */ "./src/core/defined.js");
/* harmony import */ var _core_RuntimeError_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../core/RuntimeError.js */ "./src/core/RuntimeError.js");
/* harmony import */ var _results_PipingBurstResult_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../results/PipingBurstResult.js */ "./src/results/PipingBurstResult.js");
/* harmony import */ var _results_PipingAnalyseResult_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../results/PipingAnalyseResult.js */ "./src/results/PipingAnalyseResult.js");
/* harmony import */ var _results_PipingCollisionResult_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../results/PipingCollisionResult.js */ "./src/results/PipingCollisionResult.js");
/* harmony import */ var _results_PipingFlowDirResult_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../results/PipingFlowDirResult.js */ "./src/results/PipingFlowDirResult.js");
/* harmony import */ var _results_PipingConnectionResult_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../results/PipingConnectionResult.js */ "./src/results/PipingConnectionResult.js");
/* harmony import */ var _results_PipingTracingResult_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../results/PipingTracingResult.js */ "./src/results/PipingTracingResult.js");
/* harmony import */ var _results_PipingTransectResult_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../results/PipingTransectResult.js */ "./src/results/PipingTransectResult.js");
/* harmony import */ var _results_PipingVerticalSectionResult_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../results/PipingVerticalSectionResult.js */ "./src/results/PipingVerticalSectionResult.js");
/* harmony import */ var _results_PipingClearDistanceResult_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../results/PipingClearDistanceResult.js */ "./src/results/PipingClearDistanceResult.js");
/* harmony import */ var _RegisterServer_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../RegisterServer.js */ "./src/RegisterServer.js");
/* harmony import */ var _SupportedVersion_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../SupportedVersion.js */ "./src/SupportedVersion.js");
/* harmony import */ var _warning_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../warning.js */ "./src/warning.js");
/* harmony import */ var _core_convertSearchParamToJSONString_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../core/convertSearchParamToJSONString.js */ "./src/core/convertSearchParamToJSONString.js");
/* harmony import */ var _core_Resource_js__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../core/Resource.js */ "./src/core/Resource.js");
/* harmony import */ var _core_getFlatUniqueArray_js__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../core/getFlatUniqueArray.js */ "./src/core/getFlatUniqueArray.js");
/* harmony import */ var _core_multiEqual_js__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ../core/multiEqual.js */ "./src/core/multiEqual.js");
function _typeof(obj) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && "function" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }, _typeof(obj); }
function _regeneratorRuntime() { "use strict"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return exports; }; var exports = {}, Op = Object.prototype, hasOwn = Op.hasOwnProperty, defineProperty = Object.defineProperty || function (obj, key, desc) { obj[key] = desc.value; }, $Symbol = "function" == typeof Symbol ? Symbol : {}, iteratorSymbol = $Symbol.iterator || "@@iterator", asyncIteratorSymbol = $Symbol.asyncIterator || "@@asyncIterator", toStringTagSymbol = $Symbol.toStringTag || "@@toStringTag"; function define(obj, key, value) { return Object.defineProperty(obj, key, { value: value, enumerable: !0, configurable: !0, writable: !0 }), obj[key]; } try { define({}, ""); } catch (err) { define = function define(obj, key, value) { return obj[key] = value; }; } function wrap(innerFn, outerFn, self, tryLocsList) { var protoGenerator = outerFn && outerFn.prototype instanceof Generator ? outerFn : Generator, generator = Object.create(protoGenerator.prototype), context = new Context(tryLocsList || []); return defineProperty(generator, "_invoke", { value: makeInvokeMethod(innerFn, self, context) }), generator; } function tryCatch(fn, obj, arg) { try { return { type: "normal", arg: fn.call(obj, arg) }; } catch (err) { return { type: "throw", arg: err }; } } exports.wrap = wrap; var ContinueSentinel = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var IteratorPrototype = {}; define(IteratorPrototype, iteratorSymbol, function () { return this; }); var getProto = Object.getPrototypeOf, NativeIteratorPrototype = getProto && getProto(getProto(values([]))); NativeIteratorPrototype && NativeIteratorPrototype !== Op && hasOwn.call(NativeIteratorPrototype, iteratorSymbol) && (IteratorPrototype = NativeIteratorPrototype); var Gp = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(IteratorPrototype); function defineIteratorMethods(prototype) { ["next", "throw", "return"].forEach(function (method) { define(prototype, method, function (arg) { return this._invoke(method, arg); }); }); } function AsyncIterator(generator, PromiseImpl) { function invoke(method, arg, resolve, reject) { var record = tryCatch(generator[method], generator, arg); if ("throw" !== record.type) { var result = record.arg, value = result.value; return value && "object" == _typeof(value) && hasOwn.call(value, "__await") ? PromiseImpl.resolve(value.__await).then(function (value) { invoke("next", value, resolve, reject); }, function (err) { invoke("throw", err, resolve, reject); }) : PromiseImpl.resolve(value).then(function (unwrapped) { result.value = unwrapped, resolve(result); }, function (error) { return invoke("throw", error, resolve, reject); }); } reject(record.arg); } var previousPromise; defineProperty(this, "_invoke", { value: function value(method, arg) { function callInvokeWithMethodAndArg() { return new PromiseImpl(function (resolve, reject) { invoke(method, arg, resolve, reject); }); } return previousPromise = previousPromise ? previousPromise.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(innerFn, self, context) { var state = "suspendedStart"; return function (method, arg) { if ("executing" === state) throw new Error("Generator is already running"); if ("completed" === state) { if ("throw" === method) throw arg; return doneResult(); } for (context.method = method, context.arg = arg;;) { var delegate = context.delegate; if (delegate) { var delegateResult = maybeInvokeDelegate(delegate, context); if (delegateResult) { if (delegateResult === ContinueSentinel) continue; return delegateResult; } } if ("next" === context.method) context.sent = context._sent = context.arg;else if ("throw" === context.method) { if ("suspendedStart" === state) throw state = "completed", context.arg; context.dispatchException(context.arg); } else "return" === context.method && context.abrupt("return", context.arg); state = "executing"; var record = tryCatch(innerFn, self, context); if ("normal" === record.type) { if (state = context.done ? "completed" : "suspendedYield", record.arg === ContinueSentinel) continue; return { value: record.arg, done: context.done }; } "throw" === record.type && (state = "completed", context.method = "throw", context.arg = record.arg); } }; } function maybeInvokeDelegate(delegate, context) { var methodName = context.method, method = delegate.iterator[methodName]; if (undefined === method) return context.delegate = null, "throw" === methodName && delegate.iterator["return"] && (context.method = "return", context.arg = undefined, maybeInvokeDelegate(delegate, context), "throw" === context.method) || "return" !== methodName && (context.method = "throw", context.arg = new TypeError("The iterator does not provide a '" + methodName + "' method")), ContinueSentinel; var record = tryCatch(method, delegate.iterator, context.arg); if ("throw" === record.type) return context.method = "throw", context.arg = record.arg, context.delegate = null, ContinueSentinel; var info = record.arg; return info ? info.done ? (context[delegate.resultName] = info.value, context.next = delegate.nextLoc, "return" !== context.method && (context.method = "next", context.arg = undefined), context.delegate = null, ContinueSentinel) : info : (context.method = "throw", context.arg = new TypeError("iterator result is not an object"), context.delegate = null, ContinueSentinel); } function pushTryEntry(locs) { var entry = { tryLoc: locs[0] }; 1 in locs && (entry.catchLoc = locs[1]), 2 in locs && (entry.finallyLoc = locs[2], entry.afterLoc = locs[3]), this.tryEntries.push(entry); } function resetTryEntry(entry) { var record = entry.completion || {}; record.type = "normal", delete record.arg, entry.completion = record; } function Context(tryLocsList) { this.tryEntries = [{ tryLoc: "root" }], tryLocsList.forEach(pushTryEntry, this), this.reset(!0); } function values(iterable) { if (iterable) { var iteratorMethod = iterable[iteratorSymbol]; if (iteratorMethod) return iteratorMethod.call(iterable); if ("function" == typeof iterable.next) return iterable; if (!isNaN(iterable.length)) { var i = -1, next = function next() { for (; ++i < iterable.length;) if (hasOwn.call(iterable, i)) return next.value = iterable[i], next.done = !1, next; return next.value = undefined, next.done = !0, next; }; return next.next = next; } } return { next: doneResult }; } function doneResult() { return { value: undefined, done: !0 }; } return GeneratorFunction.prototype = GeneratorFunctionPrototype, defineProperty(Gp, "constructor", { value: GeneratorFunctionPrototype, configurable: !0 }), defineProperty(GeneratorFunctionPrototype, "constructor", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, toStringTagSymbol, "GeneratorFunction"), exports.isGeneratorFunction = function (genFun) { var ctor = "function" == typeof genFun && genFun.constructor; return !!ctor && (ctor === GeneratorFunction || "GeneratorFunction" === (ctor.displayName || ctor.name)); }, exports.mark = function (genFun) { return Object.setPrototypeOf ? Object.setPrototypeOf(genFun, GeneratorFunctionPrototype) : (genFun.__proto__ = GeneratorFunctionPrototype, define(genFun, toStringTagSymbol, "GeneratorFunction")), genFun.prototype = Object.create(Gp), genFun; }, exports.awrap = function (arg) { return { __await: arg }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, asyncIteratorSymbol, function () { return this; }), exports.AsyncIterator = AsyncIterator, exports.async = function (innerFn, outerFn, self, tryLocsList, PromiseImpl) { void 0 === PromiseImpl && (PromiseImpl = Promise); var iter = new AsyncIterator(wrap(innerFn, outerFn, self, tryLocsList), PromiseImpl); return exports.isGeneratorFunction(outerFn) ? iter : iter.next().then(function (result) { return result.done ? result.value : iter.next(); }); }, defineIteratorMethods(Gp), define(Gp, toStringTagSymbol, "Generator"), define(Gp, iteratorSymbol, function () { return this; }), define(Gp, "toString", function () { return "[object Generator]"; }), exports.keys = function (val) { var object = Object(val), keys = []; for (var key in object) keys.push(key); return keys.reverse(), function next() { for (; keys.length;) { var key = keys.pop(); if (key in object) return next.value = key, next.done = !1, next; } return next.done = !0, next; }; }, exports.values = values, Context.prototype = { constructor: Context, reset: function reset(skipTempReset) { if (this.prev = 0, this.next = 0, this.sent = this._sent = undefined, this.done = !1, this.delegate = null, this.method = "next", this.arg = undefined, this.tryEntries.forEach(resetTryEntry), !skipTempReset) for (var name in this) "t" === name.charAt(0) && hasOwn.call(this, name) && !isNaN(+name.slice(1)) && (this[name] = undefined); }, stop: function stop() { this.done = !0; var rootRecord = this.tryEntries[0].completion; if ("throw" === rootRecord.type) throw rootRecord.arg; return this.rval; }, dispatchException: function dispatchException(exception) { if (this.done) throw exception; var context = this; function handle(loc, caught) { return record.type = "throw", record.arg = exception, context.next = loc, caught && (context.method = "next", context.arg = undefined), !!caught; } for (var i = this.tryEntries.length - 1; i >= 0; --i) { var entry = this.tryEntries[i], record = entry.completion; if ("root" === entry.tryLoc) return handle("end"); if (entry.tryLoc <= this.prev) { var hasCatch = hasOwn.call(entry, "catchLoc"), hasFinally = hasOwn.call(entry, "finallyLoc"); if (hasCatch && hasFinally) { if (this.prev < entry.catchLoc) return handle(entry.catchLoc, !0); if (this.prev < entry.finallyLoc) return handle(entry.finallyLoc); } else if (hasCatch) { if (this.prev < entry.catchLoc) return handle(entry.catchLoc, !0); } else { if (!hasFinally) throw new Error("try statement without catch or finally"); if (this.prev < entry.finallyLoc) return handle(entry.finallyLoc); } } } }, abrupt: function abrupt(type, arg) { for (var i = this.tryEntries.length - 1; i >= 0; --i) { var entry = this.tryEntries[i]; if (entry.tryLoc <= this.prev && hasOwn.call(entry, "finallyLoc") && this.prev < entry.finallyLoc) { var finallyEntry = entry; break; } } finallyEntry && ("break" === type || "continue" === type) && finallyEntry.tryLoc <= arg && arg <= finallyEntry.finallyLoc && (finallyEntry = null); var record = finallyEntry ? finallyEntry.completion : {}; return record.type = type, record.arg = arg, finallyEntry ? (this.method = "next", this.next = finallyEntry.finallyLoc, ContinueSentinel) : this.complete(record); }, complete: function complete(record, afterLoc) { if ("throw" === record.type) throw record.arg; return "break" === record.type || "continue" === record.type ? this.next = record.arg : "return" === record.type ? (this.rval = this.arg = record.arg, this.method = "return", this.next = "end") : "normal" === record.type && afterLoc && (this.next = afterLoc), ContinueSentinel; }, finish: function finish(finallyLoc) { for (var i = this.tryEntries.length - 1; i >= 0; --i) { var entry = this.tryEntries[i]; if (entry.finallyLoc === finallyLoc) return this.complete(entry.completion, entry.afterLoc), resetTryEntry(entry), ContinueSentinel; } }, "catch": function _catch(tryLoc) { for (var i = this.tryEntries.length - 1; i >= 0; --i) { var entry = this.tryEntries[i]; if (entry.tryLoc === tryLoc) { var record = entry.completion; if ("throw" === record.type) { var thrown = record.arg; resetTryEntry(entry); } return thrown; } } throw new Error("illegal catch attempt"); }, delegateYield: function delegateYield(iterable, resultName, nextLoc) { return this.delegate = { iterator: values(iterable), resultName: resultName, nextLoc: nextLoc }, "next" === this.method && (this.arg = undefined), ContinueSentinel; } }, exports; }
function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }
function _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }
function _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, "prototype", { writable: false }); return Constructor; }
function _toPropertyKey(arg) { var key = _toPrimitive(arg, "string"); return _typeof(key) === "symbol" ? key : String(key); }
function _toPrimitive(input, hint) { if (_typeof(input) !== "object" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || "default"); if (_typeof(res) !== "object") return res; throw new TypeError("@@toPrimitive must return a primitive value."); } return (hint === "string" ? String : Number)(input); }
function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }
function _nonIterableRest() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }
function _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : "undefined" != typeof Symbol && arr[Symbol.iterator] || arr["@@iterator"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i["return"] && (_r = _i["return"](), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }
function _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }
function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }
function _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err); } _next(undefined); }); }; }
function _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }
function _nonIterableSpread() { throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }
function _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }
function _iterableToArray(iter) { if (typeof Symbol !== "undefined" && iter[Symbol.iterator] != null || iter["@@iterator"] != null) return Array.from(iter); }
function _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }
function _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }


















var AnalyseType = Object.freeze({
  burst: "burst",
  // 爆管分析
  collision: "collision",
  // 碰撞分析
  clearDistance: "clearDistance",
  // 净距分析
  connection: "connection",
  //连通分析
  flowdir: "flowdir",
  // 流向分析
  tracing: "tracing",
  // 追踪分析
  transect: "transect",
  // 横断面分析
  verticalsec: "verticalsect" //纵断面分析
});

function _checkPositionIsArrayOf3Number(position) {
  return (0,_core_defined_js__WEBPACK_IMPORTED_MODULE_0__["default"])(position) && position instanceof Array && position.length > 0 && position.length % 3 === 0;
}

/**
 * 去除重复的图层ID，忽略管网数据类型,官网分析不区分具体类型,服务端没有唯一性检查.
 * @private
 * @param pipeNetId
 * @return {String[]|String}
 */
function getUniquePipeNetIds(pipeNetId) {
  var validPipeIds = [].concat(pipeNetId);
  validPipeIds.forEach(function (value, index, array) {
    array[index] = value.split(",");
  });
  validPipeIds = validPipeIds.flat(2);
  validPipeIds.forEach(function (id, index, array) {
    id = id.toLowerCase();
    id = id.replace("-line", "");
    id = id.replace("-point", "");
    id = id.replace("-container", "");
    id = id.replace("-joint", "");
    id = id.replace("-well", "");
    id = id.replace("-equipment", "");
    array[index] = id;
  });
  return _toConsumableArray(new Set(validPipeIds));
}
function verifyServerVersionAndInitUrl(_x) {
  return _verifyServerVersionAndInitUrl.apply(this, arguments);
}
function _verifyServerVersionAndInitUrl() {
  _verifyServerVersionAndInitUrl = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee(serverUrl) {
    var version, url;
    return _regeneratorRuntime().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          _context.next = 2;
          return _RegisterServer_js__WEBPACK_IMPORTED_MODULE_11__["default"].getVersionByServiceAnyURI(serverUrl);
        case 2:
          version = _context.sent;
          if (!(version < _SupportedVersion_js__WEBPACK_IMPORTED_MODULE_12__["default"].r320)) {
            _context.next = 6;
            break;
          }
          _warning_js__WEBPACK_IMPORTED_MODULE_13__["default"].ByVersion("PipingAnalyse", _SupportedVersion_js__WEBPACK_IMPORTED_MODULE_12__["default"].r320, version);
          return _context.abrupt("return", null);
        case 6:
          url = new URL(serverUrl);
          url.search = "";
          url.hash = "";
          return _context.abrupt("return", {
            version: version,
            url: url
          });
        case 10:
        case "end":
          return _context.stop();
      }
    }, _callee);
  }));
  return _verifyServerVersionAndInitUrl.apply(this, arguments);
}
function PackParam(_ref) {
  var url = _ref.url,
    version = _ref.version,
    analyseType = _ref.analyseType;
  this.url = url;
  this.version = version;
  this.analyseType = analyseType;
}
function getPipeLineWithFixParamURLLessEqual330(_ref2) {
  var serverUrl = _ref2.serverUrl,
    analyseType = _ref2.analyseType,
    pipeNetId = _ref2.pipeNetId;
  var url = new URL("./pipeline", serverUrl);
  var searchParams = url.searchParams;
  searchParams.set("version", "2");
  searchParams.set("ct", "2");
  searchParams.set("rt", analyseType);
  searchParams.set("service", getUniquePipeNetIds(pipeNetId).join(","));
  return url;
}
function assignResultByAnalyseType(_ref3) {
  var _ref4 = _slicedToArray(_ref3, 2),
    response = _ref4[0],
    packParam = _ref4[1];
  switch (packParam.analyseType) {
    case AnalyseType.burst:
      {
        return new _results_PipingBurstResult_js__WEBPACK_IMPORTED_MODULE_2__["default"]({
          response: response
        });
      }
    case AnalyseType.collision:
      {
        return new _results_PipingCollisionResult_js__WEBPACK_IMPORTED_MODULE_4__["default"]({
          response: response
        });
      }
    case AnalyseType.connection:
      {
        return new _results_PipingConnectionResult_js__WEBPACK_IMPORTED_MODULE_6__["default"]({
          response: response
        });
      }
    case AnalyseType.flowdir:
      {
        return new _results_PipingFlowDirResult_js__WEBPACK_IMPORTED_MODULE_5__["default"]({
          response: response
        });
      }
    case AnalyseType.tracing:
      {
        return new _results_PipingTracingResult_js__WEBPACK_IMPORTED_MODULE_7__["default"]({
          response: response
        });
      }
    case AnalyseType.transect:
      {
        return new _results_PipingTransectResult_js__WEBPACK_IMPORTED_MODULE_8__["default"]({
          response: response
        });
      }
    case AnalyseType.verticalsec:
      {
        return new _results_PipingVerticalSectionResult_js__WEBPACK_IMPORTED_MODULE_9__["default"]({
          response: response
        });
      }
    case AnalyseType.clearDistance:
      {
        return new _results_PipingClearDistanceResult_js__WEBPACK_IMPORTED_MODULE_10__["default"]({
          response: response
        });
      }
    default:
      {
        return new _results_PipingAnalyseResult_js__WEBPACK_IMPORTED_MODULE_3__["default"]({
          response: response
        });
      }
  }
}

/**
 * 管道系统分析服务。指定一个或者多个管道图层，针对分析类型自动进行管段、管点或者管段和管点的分析计算。
 * 当前服务管线名称应当为类似 block_suzhoupipeline_zl,图层名称有区别于{@link Query}中管线的图层名称block_suzhoupipeline_zl_line形式。
 */
var PipingAnalyse = /*#__PURE__*/function () {
  /**
   * 弃用
   * @constructor
   * @deprecated
   */
  function PipingAnalyse() {
    _classCallCheck(this, PipingAnalyse);
    console.error("deprecated");
  }
  /**
   * 爆管分析，分析指定管段爆管以后对指定管道的哪些部件有影响,要求该管道数据需要有阀门才能正确分析，否则结果为0;
   * @static
   * @async
   * @param {String} serverUrl 部署的服务地址url,如http://*************:1153/或者http://*************:1153或者http://*************:1153/pipeline
   * @param {String} pipeNetId 管道数据名称,单个数据
   * @param {String} pipeLineId 管段的ID
   * @param {String|Array.<String>} discardValves 不参与该爆管分析的阀门部件id(损坏的、无效的阀门等）
   * @return {Promise<PipingBurstResult>}
   */
  _createClass(PipingAnalyse, null, [{
    key: "burst",
    value: function burst(serverUrl, pipeNetId, pipeLineId) {
      var discardValves = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : "";
      var analyseType = AnalyseType.burst;
      return verifyServerVersionAndInitUrl(serverUrl).then(function (result) {
        if (!result) {
          return Promise.reject("服务版本不支持");
        }
        var version = result.version;
        switch (version) {
          case _SupportedVersion_js__WEBPACK_IMPORTED_MODULE_12__["default"].r320:
          case _SupportedVersion_js__WEBPACK_IMPORTED_MODULE_12__["default"].r330:
            {
              var url = getPipeLineWithFixParamURLLessEqual330({
                serverUrl: serverUrl,
                analyseType: analyseType,
                pipeNetId: pipeNetId
              });
              url.searchParams.set("aparam", "0,".concat(pipeLineId));
              if ((0,_core_defined_js__WEBPACK_IMPORTED_MODULE_0__["default"])(discardValves) && discardValves !== "") {
                console.warn("\u5F53\u524D\u670D\u52A1\u7248\u672C:".concat(_SupportedVersion_js__WEBPACK_IMPORTED_MODULE_12__["default"].getStringByVersionNum(version), "\nPipingAnalyse.burst\u51FD\u6570exceptedValveIds \u65E0\u6548."));
              }
              return Promise.all([_core_Resource_js__WEBPACK_IMPORTED_MODULE_15__["default"].getJSON({
                url: url.toString()
              }), new PackParam({
                url: url,
                version: version,
                analyseType: analyseType
              })]);
            }
          // case SupportedVersion.r340:
          // case SupportedVersion.r341:
          // case SupportedVersion.r350: 
          default:
            {
              var _url = new URL("./v2/pipelineAnalysis/".concat(analyseType, "/").concat(getUniquePipeNetIds(pipeNetId).join(","), "/").concat(pipeLineId), serverUrl);
              var searchParams = new URLSearchParams();
              searchParams.set("discardValves", "".concat((0,_core_getFlatUniqueArray_js__WEBPACK_IMPORTED_MODULE_16__["default"])(discardValves).join(",")));
              return Promise.all([_core_Resource_js__WEBPACK_IMPORTED_MODULE_15__["default"].postJSON({
                url: _url.toString(),
                body: (0,_core_convertSearchParamToJSONString_js__WEBPACK_IMPORTED_MODULE_14__["default"])(searchParams),
                headers: {
                  "Content-Type": "application/json;charset=UTF-8"
                }
              }), new PackParam({
                url: _url,
                version: version,
                analyseType: analyseType
              })]);
            }
          // default: {
          //     return Promise.reject("无法识别的服务版本");
          // }
        }
      }).then(assignResultByAnalyseType);
    }

    /**
     * 管段碰撞分析,只分析管线，不分析管点.分析结果含有水平净距和垂直净距
     * @static
     * @async
     * @param {String} serverUrl 部署的服务地址url,如http://*************:1153/或者http://*************:1153或者http://*************:1153/pipeline
     * @param {String|Array.<String>} pipeNetId 管道数据名称,可多份管道数据
     * @param {Array.<Number>} startPosition 起点坐标数组，应该为[lon0,lat0,height0,lon1,lat1,height1]形式
     * @param {Array.<Number>} endPosition 终点坐标数组，应该为[lon0,lat0,height0,lon1,lat1,height1]形式
     * @param {Number|Array.<Number>} diameter 管径
     * @param {Number|Array.<Number>} radius 碰撞分析半径
     * @return {Promise<PipingCollisionResult>}
     */
  }, {
    key: "collision",
    value: function collision(serverUrl, pipeNetId, startPosition, endPosition, diameter, radius) {
      startPosition = [].concat(startPosition);
      endPosition = [].concat(endPosition);
      if (!_checkPositionIsArrayOf3Number(startPosition) || !_checkPositionIsArrayOf3Number(endPosition)) {
        throw new _core_RuntimeError_js__WEBPACK_IMPORTED_MODULE_1__["default"]("传入的起始点、中止点坐标应该为[longitude,latitude,height]数组形式");
      }
      diameter = [].concat(diameter);
      radius = [].concat(radius);
      if (!(0,_core_multiEqual_js__WEBPACK_IMPORTED_MODULE_17__["default"])(diameter.length, radius.length, startPosition.length / 3, endPosition.length / 3)) {
        throw new _core_RuntimeError_js__WEBPACK_IMPORTED_MODULE_1__["default"]("传入管段参数的个数不一致");
      }
      var analyseType = AnalyseType.collision;
      return verifyServerVersionAndInitUrl(serverUrl).then(function (result) {
        if (!result) {
          return Promise.reject("服务版本不支持");
        }
        var version = result.version;
        switch (version) {
          case _SupportedVersion_js__WEBPACK_IMPORTED_MODULE_12__["default"].r320:
          case _SupportedVersion_js__WEBPACK_IMPORTED_MODULE_12__["default"].r330:
            {
              var url = getPipeLineWithFixParamURLLessEqual330({
                serverUrl: serverUrl,
                analyseType: analyseType,
                pipeNetId: pipeNetId
              });
              var pipeSegmentCount = startPosition.length / 3;
              var param = "";
              for (var i = 0; i < pipeSegmentCount; i++) {
                param += "".concat(startPosition[3 * i], ",").concat(startPosition[3 * i + 1], ",").concat(startPosition[3 * i + 2], ",").concat(endPosition[3 * i], ",").concat(endPosition[3 * i + 1], ",").concat(endPosition[3 * i + 2], ",").concat(diameter[i], ",").concat(radius[i], ",");
              }
              url.searchParams.set("aparam", "0,".concat(param));
              return Promise.all([_core_Resource_js__WEBPACK_IMPORTED_MODULE_15__["default"].getJSON({
                url: url.toString()
              }), new PackParam({
                url: url,
                version: version,
                analyseType: analyseType
              })]);
            }
          // case SupportedVersion.r340:
          // case SupportedVersion.r341:
          // case SupportedVersion.r350:
          default:
            {
              var _url2 = new URL("./v2/pipelineAnalysis/".concat(analyseType, "/").concat(getUniquePipeNetIds(pipeNetId).join(",")), serverUrl);
              var searchParams = new URLSearchParams();
              searchParams.set("analysisRadius", "".concat(radius.join(",")));
              searchParams.set("pipeRadius", "".concat(diameter.join(",")));
              searchParams.set("beginPosition", "".concat([].concat(startPosition).join(",")));
              searchParams.set("endPosition", "".concat([].concat(endPosition).join(",")));
              return Promise.all([_core_Resource_js__WEBPACK_IMPORTED_MODULE_15__["default"].postJSON({
                url: _url2.toString(),
                body: (0,_core_convertSearchParamToJSONString_js__WEBPACK_IMPORTED_MODULE_14__["default"])(searchParams),
                headers: {
                  "Content-Type": "application/json;charset=UTF-8"
                }
              }), new PackParam({
                url: _url2,
                version: version,
                analyseType: analyseType
              })]);
            }
          // default: {
          //     return Promise.reject("无法识别的服务版本");
          // }
        }
      }).then(assignResultByAnalyseType);
    }

    /**
     * 净距分析,只分析管线，不分析管点.分析结果为判定是否符合标准。分析结果只有水平净距或者垂直净距.3.3.0及之前版本返回结果为{@link PipingCollisionResult}类型,
     * 3.3.4及更新版本返回 {@link PipingClearDistanceResult}类型。这是因为服务端实现有所变更导致。
     * @static
     * @async
     * @param {String} serverUrl 部署的服务地址url,如http://*************:1153/或者http://*************:1153或者http://*************:1153/pipeline
     * @param {String|Array.<String>} pipeNetId 管道数据名称,可多份管道数据
     * @param {Array.<Number>} startPosition 起点坐标数组，应该为[lon0,lat0,height0,lon1,lat1,height1]形式
     * @param {Array.<Number>} endPosition 终点坐标数组，应该为[lon0,lat0,height0,lon1,lat1,height1]形式
     * @param {Number|Array.<Number>} diameter 管径，单位：毫米
     * @param {Number|Array.<Number>} radius 分析半径，单位：米
     * @param {Number} [standardDistance] 净距标准，单位：米
     * @param {String} [direction] "horizontal"或者"vertical",净距分析方向(水平、垂直).服务端版本号大于3.3.0时，该参数为空情况将自行设置为<strong>horizontal</strong>
     * @return {Promise<PipingCollisionResult|PipingClearDistanceResult>}
     */
  }, {
    key: "clearDistance",
    value: function clearDistance(serverUrl, pipeNetId, startPosition, endPosition, diameter, radius, standardDistance, direction) {
      startPosition = [].concat(startPosition);
      endPosition = [].concat(endPosition);
      if (!_checkPositionIsArrayOf3Number(startPosition) || !_checkPositionIsArrayOf3Number(endPosition)) {
        throw new _core_RuntimeError_js__WEBPACK_IMPORTED_MODULE_1__["default"]("传入的起始点、中止点坐标应该为[longitude,latitude,height]数组形式");
      }
      diameter = [].concat(diameter);
      radius = [].concat(radius);
      if (!(0,_core_multiEqual_js__WEBPACK_IMPORTED_MODULE_17__["default"])(diameter.length, radius.length, startPosition.length / 3, endPosition.length / 3)) {
        throw new _core_RuntimeError_js__WEBPACK_IMPORTED_MODULE_1__["default"]("传入管段参数的个数不一致");
      }
      var analyseType = AnalyseType.clearDistance;
      return verifyServerVersionAndInitUrl(serverUrl).then(function (result) {
        if (!result) {
          return Promise.reject("服务版本不支持");
        }
        var version = result.version;
        if (version !== _SupportedVersion_js__WEBPACK_IMPORTED_MODULE_12__["default"].r320 && version !== _SupportedVersion_js__WEBPACK_IMPORTED_MODULE_12__["default"].r330) {
          // 延续3.4以来的默认值
          direction = direction || "horizontal";
          standardDistance = standardDistance || 10;
        } else {
          // 当前 SupportedVersion.r320 SupportedVersion.r330 版本，所谓净距分析就是碰撞分析
          analyseType = AnalyseType.collision;
          if ((0,_core_defined_js__WEBPACK_IMPORTED_MODULE_0__["default"])(direction) && direction !== "" || (0,_core_defined_js__WEBPACK_IMPORTED_MODULE_0__["default"])(standardDistance)) {
            console.warn("PipingAnalyse.clearDistance：当前服务版本不支持 direction 或 standardDistance参数!");
          }
        }
        switch (version) {
          case _SupportedVersion_js__WEBPACK_IMPORTED_MODULE_12__["default"].r320:
          case _SupportedVersion_js__WEBPACK_IMPORTED_MODULE_12__["default"].r330:
            {
              // 这段代码就是要和碰撞分析一样
              var url = getPipeLineWithFixParamURLLessEqual330({
                serverUrl: serverUrl,
                analyseType: analyseType,
                pipeNetId: pipeNetId
              });
              var pipeSegmentCount = startPosition.length / 3;
              var param = "";
              for (var i = 0; i < pipeSegmentCount; i++) {
                param += "".concat(startPosition[3 * i], ",").concat(startPosition[3 * i + 1], ",").concat(startPosition[3 * i + 2], ",").concat(endPosition[3 * i], ",").concat(endPosition[3 * i + 1], ",").concat(endPosition[3 * i + 2], ",").concat(diameter[i], ",").concat(radius[i], ",");
              }
              url.searchParams.set("aparam", "0,".concat(param));
              return Promise.all([_core_Resource_js__WEBPACK_IMPORTED_MODULE_15__["default"].getJSON({
                url: url.toString()
              }), new PackParam({
                url: url,
                version: version,
                analyseType: analyseType
              })]);
            }
          // case SupportedVersion.r340:
          // case SupportedVersion.r341:
          // case SupportedVersion.r350:
          default:
            {
              var _url3 = new URL("./v2/pipelineAnalysis/".concat(analyseType, "/").concat(getUniquePipeNetIds(pipeNetId).join(",")), serverUrl);
              var searchParams = new URLSearchParams();
              searchParams.set("analysisRadius", "".concat(radius.join(",")));
              searchParams.set("pipeRadius", "".concat(diameter.join(",")));
              searchParams.set("beginPosition", "".concat([].concat(startPosition).join(",")));
              searchParams.set("endPosition", "".concat([].concat(endPosition).join(",")));
              searchParams.set("direction", "".concat(direction));
              searchParams.set("standardDistance", "".concat(standardDistance));
              return Promise.all([_core_Resource_js__WEBPACK_IMPORTED_MODULE_15__["default"].postJSON({
                url: _url3.toString(),
                body: (0,_core_convertSearchParamToJSONString_js__WEBPACK_IMPORTED_MODULE_14__["default"])(searchParams),
                headers: {
                  "Content-Type": "application/json;charset=UTF-8"
                }
              }), new PackParam({
                url: _url3,
                version: version,
                analyseType: analyseType
              })]);
            }
          // default: {
          //     return Promise.reject("无法识别的服务版本");
          // }
        }
      }).then(assignResultByAnalyseType);
    }

    /**
     * 管段联通分析.指定两个管段的ID，查询这两个管段是否联通和联通中经过的节点
     * @static
     * @async
     * @param {String} serverUrl 部署的服务地址url,如http://*************:1153/或者http://*************:1153或者http://*************:1153/pipeline
     * @param {String} pipeNetId 管道数据名称,单个数据
     * @param {String} pipeLineAId 第一个查询管段的us_id
     * @param {String} pipeLineBId 第二个查询管段的us_id
     * @return {Promise<PipingConnectionResult>}
     */
  }, {
    key: "connection",
    value: function connection(serverUrl, pipeNetId, pipeLineAId, pipeLineBId) {
      var analyseType = AnalyseType.connection;
      return verifyServerVersionAndInitUrl(serverUrl).then(function (result) {
        if (!result) {
          return Promise.reject("服务版本不支持");
        }
        var url = result.url;
        var version = result.version;
        switch (version) {
          case _SupportedVersion_js__WEBPACK_IMPORTED_MODULE_12__["default"].r320:
          case _SupportedVersion_js__WEBPACK_IMPORTED_MODULE_12__["default"].r330:
            {
              var _url4 = getPipeLineWithFixParamURLLessEqual330({
                serverUrl: serverUrl,
                analyseType: analyseType,
                pipeNetId: pipeNetId
              });
              _url4.searchParams.set("aparam", "0,".concat(pipeLineAId, ",").concat(pipeLineBId));
              return Promise.all([_core_Resource_js__WEBPACK_IMPORTED_MODULE_15__["default"].getJSON({
                url: _url4.toString()
              }), new PackParam({
                url: _url4,
                version: version,
                analyseType: analyseType
              })]);
            }
          // case SupportedVersion.r340:
          // case SupportedVersion.r341:
          // case SupportedVersion.r350:
          default:
            {
              var _url5 = new URL("./v2/pipelineAnalysis/".concat(analyseType, "/").concat(getUniquePipeNetIds(pipeNetId).join(",")), serverUrl);
              var searchParams = new URLSearchParams();
              searchParams.set("source", pipeLineAId);
              searchParams.set("target", pipeLineBId);
              return Promise.all([_core_Resource_js__WEBPACK_IMPORTED_MODULE_15__["default"].postJSON({
                url: _url5.toString(),
                body: (0,_core_convertSearchParamToJSONString_js__WEBPACK_IMPORTED_MODULE_14__["default"])(searchParams),
                headers: {
                  "Content-Type": "application/json;charset=UTF-8"
                }
              }), new PackParam({
                url: _url5,
                version: version,
                analyseType: analyseType
              })]);
            }
          // default: {
          //     return Promise.reject("无法识别的服务版本");
          // }
        }
      }).then(assignResultByAnalyseType);
    }

    /**
     * 管道流向分析。部分管道的流向是需要明确。指定管段的us_id,和流向的长度进行进行分析
     * @static
     * @async
     * @param {String} serverUrl 部署的服务地址url,如http://*************:1153/或者http://*************:1153或者http://*************:1153/pipeline
     * @param {String} pipeNetId 管道数据名称,单个数据
     * @param {String} pipeLineId 管段的ID
     * @param {PipeLineDir.FORWARD|PipeLineDir.REVERSE|PipeLineDir.DOUBLE} pipeLineDir 管线的流向，正向，逆向或者双向分析
     * @param {Number} flowDist 流向分析的长度
     * @return {Promise<PipingFlowDirResult>}
     */
  }, {
    key: "flowDir",
    value: function flowDir(serverUrl, pipeNetId, pipeLineId, pipeLineDir, flowDist) {
      var analyseType = AnalyseType.flowdir;
      return verifyServerVersionAndInitUrl(serverUrl).then(function (result) {
        if (!result) {
          return Promise.reject("服务版本不支持");
        }
        var version = result.version;
        switch (version) {
          case _SupportedVersion_js__WEBPACK_IMPORTED_MODULE_12__["default"].r320:
          case _SupportedVersion_js__WEBPACK_IMPORTED_MODULE_12__["default"].r330:
            {
              var url = getPipeLineWithFixParamURLLessEqual330({
                serverUrl: serverUrl,
                analyseType: analyseType,
                pipeNetId: pipeNetId
              });
              url.searchParams.set("aparam", "0,".concat(pipeLineId, ",").concat(pipeLineDir, ",").concat(flowDist));
              return Promise.all([_core_Resource_js__WEBPACK_IMPORTED_MODULE_15__["default"].getJSON({
                url: url.toString()
              }), new PackParam({
                url: url,
                version: version,
                analyseType: analyseType
              })]);
            }
          // case SupportedVersion.r340:
          // case SupportedVersion.r341:
          // case SupportedVersion.r350:
          default:
            {
              var _url6 = new URL("./v2/pipelineAnalysis/".concat(analyseType, "/").concat(getUniquePipeNetIds(pipeNetId).join(","), "/").concat(pipeLineId), serverUrl);
              var searchParams = new URLSearchParams();
              searchParams.set("direction", "".concat(pipeLineDir));
              searchParams.set("distance", "".concat(flowDist));
              return Promise.all([_core_Resource_js__WEBPACK_IMPORTED_MODULE_15__["default"].postJSON({
                url: _url6.toString(),
                body: (0,_core_convertSearchParamToJSONString_js__WEBPACK_IMPORTED_MODULE_14__["default"])(searchParams),
                headers: {
                  "Content-Type": "application/json;charset=UTF-8"
                }
              }), new PackParam({
                url: _url6,
                version: version,
                analyseType: analyseType
              })]);
            }
          // default: {
          //     return Promise.reject("无法识别的服务版本");
          // }
        }
      }).then(assignResultByAnalyseType);
    }

    /**
     * 管道追踪分析。指定管段的us_id,在管线指定长度指定方向上进行追踪分析
     * @static
     * @async
     * @param {String} serverUrl 部署的服务地址url,如http://*************:1153/或者http://*************:1153或者http://*************:1153/pipeline
     * @param {String} pipeNetId 管道数据名称,单个数据
     * @param {String} pipeLineId 管段的ID
     * @param {PipeLineDir.FORWARD|PipeLineDir.REVERSE|PipeLineDir.DOUBLE} tracingDir 追踪方法，上游、下游、上下游双向追踪
     * @param {Number} tracingDist 管线追溯长度。
     * @return {Promise<PipingTracingResult>}
     */
  }, {
    key: "tracing",
    value: function tracing(serverUrl, pipeNetId, pipeLineId, tracingDir, tracingDist) {
      var analyseType = AnalyseType.tracing;
      return verifyServerVersionAndInitUrl(serverUrl).then(function (result) {
        if (!result) {
          return Promise.reject("服务版本不支持");
        }
        var version = result.version;
        switch (version) {
          case _SupportedVersion_js__WEBPACK_IMPORTED_MODULE_12__["default"].r320:
          case _SupportedVersion_js__WEBPACK_IMPORTED_MODULE_12__["default"].r330:
            {
              var url = getPipeLineWithFixParamURLLessEqual330({
                serverUrl: serverUrl,
                analyseType: analyseType,
                pipeNetId: pipeNetId
              });
              url.searchParams.set("aparam", "0,".concat(pipeLineId, ",").concat(tracingDir, ",").concat(tracingDist));
              return Promise.all([_core_Resource_js__WEBPACK_IMPORTED_MODULE_15__["default"].getJSON({
                url: url.toString()
              }), new PackParam({
                url: url,
                version: version,
                analyseType: analyseType
              })]);
            }
          // case SupportedVersion.r340:
          // case SupportedVersion.r341:
          // case SupportedVersion.r350:
          default:
            {
              var _url7 = new URL("./v2/pipelineAnalysis/".concat(analyseType, "/").concat(getUniquePipeNetIds(pipeNetId).join(","), "/").concat(pipeLineId), serverUrl);
              var searchParams = new URLSearchParams();
              searchParams.set("direction", "".concat(tracingDir));
              searchParams.set("distance", "".concat(tracingDist));
              return Promise.all([_core_Resource_js__WEBPACK_IMPORTED_MODULE_15__["default"].postJSON({
                url: _url7.toString(),
                body: (0,_core_convertSearchParamToJSONString_js__WEBPACK_IMPORTED_MODULE_14__["default"])(searchParams),
                headers: {
                  "Content-Type": "application/json;charset=UTF-8"
                }
              }), new PackParam({
                url: _url7,
                version: version,
                analyseType: analyseType
              })]);
            }
          // default: {
          //     return Promise.reject("无法识别的服务版本");
          // }
        }
      }).then(assignResultByAnalyseType);
    }

    /**
     * 横断面分析，指定一条线段的起点和终点点，进行横断面分析。只分析管段，不分析管点
     * @static
     * @async
     * @param {String} serverUrl 部署的服务地址url,如http://*************:1153/或者http://*************:1153或者http://*************:1153/pipeline
     * @param {String|Array.<String>} pipeNetId 管道数据名称,可多份管道数据
     * @param {Array.<Number>} startPosition 起点坐标数组，应该为[lon,lat,height]形式
     * @param {Array.<Number>} endPosition 中点坐标数组，应该为[lon,lat,height]形式
     * @return {Promise<PipingTransectResult>}
     */
  }, {
    key: "tranSect",
    value: function tranSect(serverUrl, pipeNetId, startPosition, endPosition) {
      var analyseType = AnalyseType.transect;
      return verifyServerVersionAndInitUrl(serverUrl).then(function (result) {
        if (!result) {
          return Promise.reject("服务版本不支持");
        }
        var version = result.version;
        switch (version) {
          case _SupportedVersion_js__WEBPACK_IMPORTED_MODULE_12__["default"].r320:
          case _SupportedVersion_js__WEBPACK_IMPORTED_MODULE_12__["default"].r330:
            {
              var url = getPipeLineWithFixParamURLLessEqual330({
                serverUrl: serverUrl,
                analyseType: analyseType,
                pipeNetId: pipeNetId
              });
              url.searchParams.set("aparam", "0,".concat(startPosition.join(","), ",").concat(endPosition.join(",")));
              return Promise.all([_core_Resource_js__WEBPACK_IMPORTED_MODULE_15__["default"].getJSON({
                url: url.toString()
              }), new PackParam({
                url: url,
                version: version,
                analyseType: analyseType
              })]);
            }
          // case SupportedVersion.r340:
          // case SupportedVersion.r341:
          // case SupportedVersion.r350:
          default:
            {
              var _url8 = new URL("./v2/pipelineAnalysis/".concat(analyseType, "/").concat(getUniquePipeNetIds(pipeNetId).join(",")), serverUrl);
              var searchParams = new URLSearchParams();
              searchParams.set("beginPosition", startPosition.join(","));
              searchParams.set("endPosition", endPosition.join(","));
              return Promise.all([_core_Resource_js__WEBPACK_IMPORTED_MODULE_15__["default"].postJSON({
                url: _url8.toString(),
                body: (0,_core_convertSearchParamToJSONString_js__WEBPACK_IMPORTED_MODULE_14__["default"])(searchParams),
                headers: {
                  "Content-Type": "application/json;charset=UTF-8"
                }
              }), new PackParam({
                url: _url8,
                version: version,
                analyseType: analyseType
              })]);
            }
          // default: {
          //     return Promise.reject("无法识别的服务版本");
          // }
        }
      }).then(assignResultByAnalyseType);
    }

    /**
     * 纵断面分析，只分析管段，不分析管点
     * @static
     * @async
     * @param {String} serverUrl 部署的服务地址url,如http://*************:1153/或者http://*************:1153或者http://*************:1153/pipeline
     * @param {String} pipeNetId 管道数据名称,单个数据
     * @param {String|Array.<String>} pipeLineIds 查询管段的us_id
     * @return {Promise<PipingVerticalSectionResult>}
     */
  }, {
    key: "verticalSect",
    value: function verticalSect(serverUrl, pipeNetId, pipeLineIds) {
      var analyseType = AnalyseType.verticalsec;
      return verifyServerVersionAndInitUrl(serverUrl).then(function (result) {
        if (!result) {
          return Promise.reject("服务版本不支持");
        }
        var version = result.version;
        switch (version) {
          case _SupportedVersion_js__WEBPACK_IMPORTED_MODULE_12__["default"].r320:
          case _SupportedVersion_js__WEBPACK_IMPORTED_MODULE_12__["default"].r330:
            {
              var url = getPipeLineWithFixParamURLLessEqual330({
                serverUrl: serverUrl,
                analyseType: analyseType,
                pipeNetId: pipeNetId
              });
              url.searchParams.set("aparam", "0,".concat((0,_core_getFlatUniqueArray_js__WEBPACK_IMPORTED_MODULE_16__["default"])(pipeLineIds).join(",")));
              return Promise.all([_core_Resource_js__WEBPACK_IMPORTED_MODULE_15__["default"].getJSON({
                url: url.toString()
              }), new PackParam({
                url: url,
                version: version,
                analyseType: analyseType
              })]);
            }
          // case SupportedVersion.r340:
          // case SupportedVersion.r341:
          // case SupportedVersion.r350:
          default:
            {
              var _url9 = new URL("./v2/pipelineAnalysis/verticalSection/".concat(getUniquePipeNetIds(pipeNetId).join(","), "/").concat((0,_core_getFlatUniqueArray_js__WEBPACK_IMPORTED_MODULE_16__["default"])(pipeLineIds).join(",")), serverUrl);
              var searchParams = _url9.searchParams;
              return Promise.all([_core_Resource_js__WEBPACK_IMPORTED_MODULE_15__["default"].postJSON({
                url: _url9.toString(),
                body: (0,_core_convertSearchParamToJSONString_js__WEBPACK_IMPORTED_MODULE_14__["default"])(searchParams),
                headers: {
                  "Content-Type": "application/json;charset=UTF-8"
                }
              }), new PackParam({
                url: _url9,
                version: version,
                analyseType: analyseType
              })]);
            }
          // default: {
          //     return Promise.reject("无法识别的服务版本");
          // }
        }
      }).then(assignResultByAnalyseType);
    }
  }]);
  return PipingAnalyse;
}();
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PipingAnalyse);

/***/ }),

/***/ "./src/service/Query.js":
/*!******************************!*\
  !*** ./src/service/Query.js ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _results_QueryMetaResult_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../results/QueryMetaResult.js */ "./src/results/QueryMetaResult.js");
/* harmony import */ var _results_ResponseResult_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../results/ResponseResult.js */ "./src/results/ResponseResult.js");
/* harmony import */ var _results_QueryFieldValueResult_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../results/QueryFieldValueResult.js */ "./src/results/QueryFieldValueResult.js");
/* harmony import */ var _results_QueryPropertyResult_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../results/QueryPropertyResult.js */ "./src/results/QueryPropertyResult.js");
/* harmony import */ var _core_Resource_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../core/Resource.js */ "./src/core/Resource.js");
/* harmony import */ var _RegisterServer_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../RegisterServer.js */ "./src/RegisterServer.js");
/* harmony import */ var _SupportedVersion_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../SupportedVersion.js */ "./src/SupportedVersion.js");
/* harmony import */ var _warning_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../warning.js */ "./src/warning.js");
/* harmony import */ var _core_convertSearchParamToJSONString_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../core/convertSearchParamToJSONString.js */ "./src/core/convertSearchParamToJSONString.js");
/* harmony import */ var _core_getFlatUniqueArray_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../core/getFlatUniqueArray.js */ "./src/core/getFlatUniqueArray.js");
/* harmony import */ var _core_defined_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../core/defined.js */ "./src/core/defined.js");
function _typeof(obj) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && "function" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }, _typeof(obj); }
function _regeneratorRuntime() { "use strict"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return exports; }; var exports = {}, Op = Object.prototype, hasOwn = Op.hasOwnProperty, defineProperty = Object.defineProperty || function (obj, key, desc) { obj[key] = desc.value; }, $Symbol = "function" == typeof Symbol ? Symbol : {}, iteratorSymbol = $Symbol.iterator || "@@iterator", asyncIteratorSymbol = $Symbol.asyncIterator || "@@asyncIterator", toStringTagSymbol = $Symbol.toStringTag || "@@toStringTag"; function define(obj, key, value) { return Object.defineProperty(obj, key, { value: value, enumerable: !0, configurable: !0, writable: !0 }), obj[key]; } try { define({}, ""); } catch (err) { define = function define(obj, key, value) { return obj[key] = value; }; } function wrap(innerFn, outerFn, self, tryLocsList) { var protoGenerator = outerFn && outerFn.prototype instanceof Generator ? outerFn : Generator, generator = Object.create(protoGenerator.prototype), context = new Context(tryLocsList || []); return defineProperty(generator, "_invoke", { value: makeInvokeMethod(innerFn, self, context) }), generator; } function tryCatch(fn, obj, arg) { try { return { type: "normal", arg: fn.call(obj, arg) }; } catch (err) { return { type: "throw", arg: err }; } } exports.wrap = wrap; var ContinueSentinel = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var IteratorPrototype = {}; define(IteratorPrototype, iteratorSymbol, function () { return this; }); var getProto = Object.getPrototypeOf, NativeIteratorPrototype = getProto && getProto(getProto(values([]))); NativeIteratorPrototype && NativeIteratorPrototype !== Op && hasOwn.call(NativeIteratorPrototype, iteratorSymbol) && (IteratorPrototype = NativeIteratorPrototype); var Gp = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(IteratorPrototype); function defineIteratorMethods(prototype) { ["next", "throw", "return"].forEach(function (method) { define(prototype, method, function (arg) { return this._invoke(method, arg); }); }); } function AsyncIterator(generator, PromiseImpl) { function invoke(method, arg, resolve, reject) { var record = tryCatch(generator[method], generator, arg); if ("throw" !== record.type) { var result = record.arg, value = result.value; return value && "object" == _typeof(value) && hasOwn.call(value, "__await") ? PromiseImpl.resolve(value.__await).then(function (value) { invoke("next", value, resolve, reject); }, function (err) { invoke("throw", err, resolve, reject); }) : PromiseImpl.resolve(value).then(function (unwrapped) { result.value = unwrapped, resolve(result); }, function (error) { return invoke("throw", error, resolve, reject); }); } reject(record.arg); } var previousPromise; defineProperty(this, "_invoke", { value: function value(method, arg) { function callInvokeWithMethodAndArg() { return new PromiseImpl(function (resolve, reject) { invoke(method, arg, resolve, reject); }); } return previousPromise = previousPromise ? previousPromise.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(innerFn, self, context) { var state = "suspendedStart"; return function (method, arg) { if ("executing" === state) throw new Error("Generator is already running"); if ("completed" === state) { if ("throw" === method) throw arg; return doneResult(); } for (context.method = method, context.arg = arg;;) { var delegate = context.delegate; if (delegate) { var delegateResult = maybeInvokeDelegate(delegate, context); if (delegateResult) { if (delegateResult === ContinueSentinel) continue; return delegateResult; } } if ("next" === context.method) context.sent = context._sent = context.arg;else if ("throw" === context.method) { if ("suspendedStart" === state) throw state = "completed", context.arg; context.dispatchException(context.arg); } else "return" === context.method && context.abrupt("return", context.arg); state = "executing"; var record = tryCatch(innerFn, self, context); if ("normal" === record.type) { if (state = context.done ? "completed" : "suspendedYield", record.arg === ContinueSentinel) continue; return { value: record.arg, done: context.done }; } "throw" === record.type && (state = "completed", context.method = "throw", context.arg = record.arg); } }; } function maybeInvokeDelegate(delegate, context) { var methodName = context.method, method = delegate.iterator[methodName]; if (undefined === method) return context.delegate = null, "throw" === methodName && delegate.iterator["return"] && (context.method = "return", context.arg = undefined, maybeInvokeDelegate(delegate, context), "throw" === context.method) || "return" !== methodName && (context.method = "throw", context.arg = new TypeError("The iterator does not provide a '" + methodName + "' method")), ContinueSentinel; var record = tryCatch(method, delegate.iterator, context.arg); if ("throw" === record.type) return context.method = "throw", context.arg = record.arg, context.delegate = null, ContinueSentinel; var info = record.arg; return info ? info.done ? (context[delegate.resultName] = info.value, context.next = delegate.nextLoc, "return" !== context.method && (context.method = "next", context.arg = undefined), context.delegate = null, ContinueSentinel) : info : (context.method = "throw", context.arg = new TypeError("iterator result is not an object"), context.delegate = null, ContinueSentinel); } function pushTryEntry(locs) { var entry = { tryLoc: locs[0] }; 1 in locs && (entry.catchLoc = locs[1]), 2 in locs && (entry.finallyLoc = locs[2], entry.afterLoc = locs[3]), this.tryEntries.push(entry); } function resetTryEntry(entry) { var record = entry.completion || {}; record.type = "normal", delete record.arg, entry.completion = record; } function Context(tryLocsList) { this.tryEntries = [{ tryLoc: "root" }], tryLocsList.forEach(pushTryEntry, this), this.reset(!0); } function values(iterable) { if (iterable) { var iteratorMethod = iterable[iteratorSymbol]; if (iteratorMethod) return iteratorMethod.call(iterable); if ("function" == typeof iterable.next) return iterable; if (!isNaN(iterable.length)) { var i = -1, next = function next() { for (; ++i < iterable.length;) if (hasOwn.call(iterable, i)) return next.value = iterable[i], next.done = !1, next; return next.value = undefined, next.done = !0, next; }; return next.next = next; } } return { next: doneResult }; } function doneResult() { return { value: undefined, done: !0 }; } return GeneratorFunction.prototype = GeneratorFunctionPrototype, defineProperty(Gp, "constructor", { value: GeneratorFunctionPrototype, configurable: !0 }), defineProperty(GeneratorFunctionPrototype, "constructor", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, toStringTagSymbol, "GeneratorFunction"), exports.isGeneratorFunction = function (genFun) { var ctor = "function" == typeof genFun && genFun.constructor; return !!ctor && (ctor === GeneratorFunction || "GeneratorFunction" === (ctor.displayName || ctor.name)); }, exports.mark = function (genFun) { return Object.setPrototypeOf ? Object.setPrototypeOf(genFun, GeneratorFunctionPrototype) : (genFun.__proto__ = GeneratorFunctionPrototype, define(genFun, toStringTagSymbol, "GeneratorFunction")), genFun.prototype = Object.create(Gp), genFun; }, exports.awrap = function (arg) { return { __await: arg }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, asyncIteratorSymbol, function () { return this; }), exports.AsyncIterator = AsyncIterator, exports.async = function (innerFn, outerFn, self, tryLocsList, PromiseImpl) { void 0 === PromiseImpl && (PromiseImpl = Promise); var iter = new AsyncIterator(wrap(innerFn, outerFn, self, tryLocsList), PromiseImpl); return exports.isGeneratorFunction(outerFn) ? iter : iter.next().then(function (result) { return result.done ? result.value : iter.next(); }); }, defineIteratorMethods(Gp), define(Gp, toStringTagSymbol, "Generator"), define(Gp, iteratorSymbol, function () { return this; }), define(Gp, "toString", function () { return "[object Generator]"; }), exports.keys = function (val) { var object = Object(val), keys = []; for (var key in object) keys.push(key); return keys.reverse(), function next() { for (; keys.length;) { var key = keys.pop(); if (key in object) return next.value = key, next.done = !1, next; } return next.done = !0, next; }; }, exports.values = values, Context.prototype = { constructor: Context, reset: function reset(skipTempReset) { if (this.prev = 0, this.next = 0, this.sent = this._sent = undefined, this.done = !1, this.delegate = null, this.method = "next", this.arg = undefined, this.tryEntries.forEach(resetTryEntry), !skipTempReset) for (var name in this) "t" === name.charAt(0) && hasOwn.call(this, name) && !isNaN(+name.slice(1)) && (this[name] = undefined); }, stop: function stop() { this.done = !0; var rootRecord = this.tryEntries[0].completion; if ("throw" === rootRecord.type) throw rootRecord.arg; return this.rval; }, dispatchException: function dispatchException(exception) { if (this.done) throw exception; var context = this; function handle(loc, caught) { return record.type = "throw", record.arg = exception, context.next = loc, caught && (context.method = "next", context.arg = undefined), !!caught; } for (var i = this.tryEntries.length - 1; i >= 0; --i) { var entry = this.tryEntries[i], record = entry.completion; if ("root" === entry.tryLoc) return handle("end"); if (entry.tryLoc <= this.prev) { var hasCatch = hasOwn.call(entry, "catchLoc"), hasFinally = hasOwn.call(entry, "finallyLoc"); if (hasCatch && hasFinally) { if (this.prev < entry.catchLoc) return handle(entry.catchLoc, !0); if (this.prev < entry.finallyLoc) return handle(entry.finallyLoc); } else if (hasCatch) { if (this.prev < entry.catchLoc) return handle(entry.catchLoc, !0); } else { if (!hasFinally) throw new Error("try statement without catch or finally"); if (this.prev < entry.finallyLoc) return handle(entry.finallyLoc); } } } }, abrupt: function abrupt(type, arg) { for (var i = this.tryEntries.length - 1; i >= 0; --i) { var entry = this.tryEntries[i]; if (entry.tryLoc <= this.prev && hasOwn.call(entry, "finallyLoc") && this.prev < entry.finallyLoc) { var finallyEntry = entry; break; } } finallyEntry && ("break" === type || "continue" === type) && finallyEntry.tryLoc <= arg && arg <= finallyEntry.finallyLoc && (finallyEntry = null); var record = finallyEntry ? finallyEntry.completion : {}; return record.type = type, record.arg = arg, finallyEntry ? (this.method = "next", this.next = finallyEntry.finallyLoc, ContinueSentinel) : this.complete(record); }, complete: function complete(record, afterLoc) { if ("throw" === record.type) throw record.arg; return "break" === record.type || "continue" === record.type ? this.next = record.arg : "return" === record.type ? (this.rval = this.arg = record.arg, this.method = "return", this.next = "end") : "normal" === record.type && afterLoc && (this.next = afterLoc), ContinueSentinel; }, finish: function finish(finallyLoc) { for (var i = this.tryEntries.length - 1; i >= 0; --i) { var entry = this.tryEntries[i]; if (entry.finallyLoc === finallyLoc) return this.complete(entry.completion, entry.afterLoc), resetTryEntry(entry), ContinueSentinel; } }, "catch": function _catch(tryLoc) { for (var i = this.tryEntries.length - 1; i >= 0; --i) { var entry = this.tryEntries[i]; if (entry.tryLoc === tryLoc) { var record = entry.completion; if ("throw" === record.type) { var thrown = record.arg; resetTryEntry(entry); } return thrown; } } throw new Error("illegal catch attempt"); }, delegateYield: function delegateYield(iterable, resultName, nextLoc) { return this.delegate = { iterator: values(iterable), resultName: resultName, nextLoc: nextLoc }, "next" === this.method && (this.arg = undefined), ContinueSentinel; } }, exports; }
function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }
function _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }
function _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, "prototype", { writable: false }); return Constructor; }
function _toPropertyKey(arg) { var key = _toPrimitive(arg, "string"); return _typeof(key) === "symbol" ? key : String(key); }
function _toPrimitive(input, hint) { if (_typeof(input) !== "object" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || "default"); if (_typeof(res) !== "object") return res; throw new TypeError("@@toPrimitive must return a primitive value."); } return (hint === "string" ? String : Number)(input); }
function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }
function _nonIterableRest() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }
function _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }
function _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }
function _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : "undefined" != typeof Symbol && arr[Symbol.iterator] || arr["@@iterator"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i["return"] && (_r = _i["return"](), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }
function _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }
function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }
function _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err); } _next(undefined); }); }; }












/**
 * @typedef {Object} QueryType
 * 数值定义和服务端保持一致。对应{@link Query}的三个查询方法，四种查询结果返回类型.
 * 当前数据结构不支持外部调用，此处仅为描述说明
 * @private
 * @readonly
 * @property  {String} AFieldAllValues="fields_valueList" - 512 查询某字段的值列表，对应返回结果 {@link QueryFieldValueResult}
 * @property  {String} ALayerMeta="fields_dictionary" - 0,查询图层的元数据, 对应返回结果{@link QueryMetaResult}
 * @property  {String} SomeRecordSomeFieldsValues="fields_specific" - 256,（可附加空间空间查询和属性条件查询）查询返回指定字段，对应返回结果{@link QueryPropertyResult}
 * @property  {String} SomeRecordAllFieldsValues="fields_all" - 17,（可附加空间空间查询和属性条件查询）查询返回所有字段，对应返回结果{@link QueryPropertyResult}
 */
var QueryType = Object.freeze({
  AFieldAllValues: {
    num: 512,
    name: "fields_valueList"
  },
  ALayerMeta: {
    name: "fields_dictionary",
    num: 0
  },
  SomeRecordSomeFieldsValues: {
    num: 256,
    name: "fields_specific"
  },
  SomeRecordAllFieldsValues: {
    num: 17,
    name: "fields_all"
  }
});
function verifyServerVersionAndInitUrl(_x) {
  return _verifyServerVersionAndInitUrl.apply(this, arguments);
}
function _verifyServerVersionAndInitUrl() {
  _verifyServerVersionAndInitUrl = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee(serverUrl) {
    var version, url;
    return _regeneratorRuntime().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          _context.next = 2;
          return _RegisterServer_js__WEBPACK_IMPORTED_MODULE_5__["default"].getVersionByServiceAnyURI(serverUrl);
        case 2:
          version = _context.sent;
          if (!(version < _SupportedVersion_js__WEBPACK_IMPORTED_MODULE_6__["default"].r320)) {
            _context.next = 6;
            break;
          }
          _warning_js__WEBPACK_IMPORTED_MODULE_7__["default"].ByVersion("Query", _SupportedVersion_js__WEBPACK_IMPORTED_MODULE_6__["default"].r320, version);
          return _context.abrupt("return", null);
        case 6:
          url = new URL(serverUrl);
          url.search = "";
          url.hash = "";
          return _context.abrupt("return", {
            version: version,
            url: url
          });
        case 10:
        case "end":
          return _context.stop();
      }
    }, _callee);
  }));
  return _verifyServerVersionAndInitUrl.apply(this, arguments);
}
function assignResultByQueryType(_ref) {
  var _ref2 = _slicedToArray(_ref, 2),
    response = _ref2[0],
    packParam = _ref2[1];
  switch (packParam.queryType) {
    case QueryType.ALayerMeta:
      {
        return new _results_QueryMetaResult_js__WEBPACK_IMPORTED_MODULE_0__["default"]({
          response: response
        });
      }
    case QueryType.AFieldAllValues:
      {
        return new _results_QueryFieldValueResult_js__WEBPACK_IMPORTED_MODULE_2__["default"]({
          response: response
        });
      }
    case QueryType.SomeRecordSomeFieldsValues:
    case QueryType.SomeRecordAllFieldsValues:
      {
        return new _results_QueryPropertyResult_js__WEBPACK_IMPORTED_MODULE_3__["default"]({
          response: response
        });
      }
    default:
      {
        // 不会出现这个情况
        return new _results_ResponseResult_js__WEBPACK_IMPORTED_MODULE_1__["default"]({
          response: response
        });
      }
  }
}
function PackParam(_ref3) {
  var url = _ref3.url,
    version = _ref3.version,
    queryType = _ref3.queryType;
  this.url = url;
  this.version = version;
  this.queryType = queryType;
}
function getQueryURLWithFixParam(_ref4) {
  var serverUrl = _ref4.serverUrl,
    queryType = _ref4.queryType,
    layerId = _ref4.layerId;
  var url = new URL("./dataquery", serverUrl);
  var searchParams = url.searchParams;
  searchParams.set("rt", "query");
  searchParams.set("qt", queryType.num.toString());
  searchParams.set("service", (0,_core_getFlatUniqueArray_js__WEBPACK_IMPORTED_MODULE_9__["default"])(layerId).join(","));
  searchParams.set("ct", "2");
  searchParams.set("version", "2");
  searchParams.set("eleva", "1");
  return url;
}

/**
 * 服务端数据查询服务.不建议直接使用，建议使用静态方法.
 * <br>
 * <br>
 * {@link Query#queryMeta} 元数据查询，针对管线模型，矢量面拉伸体模型，矢量点线面数据.查询结果包含四至范围和属性字段信息.
 * <br>
 * {@link Query#queryFieldValue} 查询指定字段有有哪些值，针对管线模型，矢量面拉伸体模型，矢量点线面数据.
 * <br>
 * {@link Query#queryProperty}条件查询,通过组织空间拓扑条件、属性字段判定条件、限定返回字段，灵活获取的各类查询.
 * <br>
 * <br>
 * 以上查询操作方法中，`serverUrl`参数支持`http://172.30.17.100:8176/service`,`http://172.30.17.100`,`http://172.30.17.100:80`
 * <br>`layerName`参数是guid的形式，其中管线数据例外。这是因为管线数据区分管段和管点，
 * <br>
 * <br>非管线模型的其他数据的guid（倾斜摄影单体化查询对应改倾斜摄影模型数据的id):3fef188b-8415-4aa0-a3e9-aa7a8f579461
 * <br>管点: 8f74c7e1-df95-406a-bf98-6624eb61d876-point <br>管段:8f74c7e1-df95-406a-bf98-6624eb61d876-line
 * 三维引擎中加载管线模型数据,使用<strong>Layer</strong>对象加载时，可以从<code>layer.properties['gw_layer-guid']</code>获取
 * <br>可以通过<code>pickedObject.primitive.properties["gw_layer_guid"]</code>或者可以通过<code>tileset.properties["gw_layer_guid"]</code>h获取
 */
var Query = /*#__PURE__*/function () {
  /**
   * @deprecated
   */
  function Query() {
    _classCallCheck(this, Query);
  }

  /**
   * 查询指定图层的某个字段的唯一值列表
   * @static
   * @param {String} serverUrl 部署服务的地址。推荐写成<code>http://*************/license</code> 形式,便于自检.
   * @param {String} layerId 单个图层ID.
   * <br>非管线模型的其他数据的guid（倾斜摄影单体化查询对应改倾斜摄影模型数据的id):3fef188b-8415-4aa0-a3e9-aa7a8f579461
   * <br>管点: 8f74c7e1-df95-406a-bf98-6624eb61d876-point <br>管段:8f74c7e1-df95-406a-bf98-6624eb61d876-line
   * <br>可以通过<code>pickedObject.primitive.properties["gw_layer_guid"]</code>或者可以通过<code>tileset.properties["gw_layer_guid"]</code>h获取
   * @param {String} fieldName 字段名称
   * @param {Object} [pageOption] 分页选项
   * @param {number} [pageOption.num] 页码编号,不写为空
   * @param {number} [pageOption.size] 单页记录个数
   * @return {Promise<QueryFieldValueResult>}
   */
  _createClass(Query, null, [{
    key: "queryFieldValue",
    value: function queryFieldValue(serverUrl, layerId, fieldName) {
      var pageOption = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {
        num: undefined,
        size: undefined
      };
      var queryType = QueryType.AFieldAllValues;
      return verifyServerVersionAndInitUrl(serverUrl).then(function (result) {
        if (!result) {
          return Promise.reject("服务版本不支持");
        }
        var version = result.version;
        switch (version) {
          case _SupportedVersion_js__WEBPACK_IMPORTED_MODULE_6__["default"].r320:
          case _SupportedVersion_js__WEBPACK_IMPORTED_MODULE_6__["default"].r330:
            {
              var url = getQueryURLWithFixParam({
                serverUrl: serverUrl,
                queryType: queryType,
                layerId: layerId
              });
              // 分页偏移
              if ((0,_core_defined_js__WEBPACK_IMPORTED_MODULE_10__["default"])(pageOption.num) || (0,_core_defined_js__WEBPACK_IMPORTED_MODULE_10__["default"])(pageOption.size)) {
                var lastPage = pageOption.num - 1 >= 0 ? pageOption.num - 1 : 0;
                url.searchParams.set("pg", "".concat(pageOption.size * lastPage, ",").concat(pageOption.size, ",0"));
              }
              url.searchParams.set("fd", fieldName);
              return Promise.all([_core_Resource_js__WEBPACK_IMPORTED_MODULE_4__["default"].getJSON({
                url: url.toString()
              }), new PackParam({
                url: url,
                version: version,
                queryType: queryType
              })]);
            }
          // case SupportedVersion.r340:
          // case SupportedVersion.r341:
          // case SupportedVersion.r350:
          default:
            {
              var _url = new URL("./v2/dataQuery/allTypes/".concat(layerId, "/").concat(queryType.name), serverUrl);
              var searchParams = new URLSearchParams();
              if ((0,_core_defined_js__WEBPACK_IMPORTED_MODULE_10__["default"])(pageOption.num) || (0,_core_defined_js__WEBPACK_IMPORTED_MODULE_10__["default"])(pageOption.size)) {
                searchParams.set("pg", "".concat(pageOption.num, ",").concat(pageOption.size));
              }
              searchParams.set("fd", fieldName);
              return Promise.all([_core_Resource_js__WEBPACK_IMPORTED_MODULE_4__["default"].postJSON({
                url: _url.toString(),
                body: (0,_core_convertSearchParamToJSONString_js__WEBPACK_IMPORTED_MODULE_8__["default"])(searchParams),
                headers: {
                  "Content-Type": "application/json;charset=UTF-8"
                }
              }), new PackParam({
                url: _url,
                version: version,
                queryType: queryType
              })]);
            }
          // default: {
          //     return Promise.reject("无法识别的服务版本");
          // }
        }
      }).then(assignResultByQueryType);
    }

    /**
     * 查询图层元数据，包括地理数据的范围和字段信息
     * @static
     * @param {String} serverUrl 部署服务的地址。推荐写成<code>http://*************/license</code> 形式,便于自检.
     * @param {String} layerId 单个图层ID.
     * <br>非管线模型的其他数据的guid（倾斜摄影单体化查询对应改倾斜摄影模型数据的id):3fef188b-8415-4aa0-a3e9-aa7a8f579461
     * <br>管点: 8f74c7e1-df95-406a-bf98-6624eb61d876-point <br>管段:8f74c7e1-df95-406a-bf98-6624eb61d876-line
     * <br>可以通过<code>pickedObject.primitive.properties["gw_layer_guid"]</code>或者可以通过<code>tileset.properties["gw_layer_guid"]</code>h获取
     * @return {Promise<QueryMetaResult>}
     */
  }, {
    key: "queryMeta",
    value: function queryMeta(serverUrl, layerId) {
      var queryType = QueryType.ALayerMeta;
      return verifyServerVersionAndInitUrl(serverUrl).then(function (result) {
        if (!result) {
          return Promise.reject("服务版本不支持");
        }
        var version = result.version;
        switch (version) {
          case _SupportedVersion_js__WEBPACK_IMPORTED_MODULE_6__["default"].r320:
          case _SupportedVersion_js__WEBPACK_IMPORTED_MODULE_6__["default"].r330:
            {
              var url = getQueryURLWithFixParam({
                serverUrl: serverUrl,
                queryType: queryType,
                layerId: layerId
              });
              url.search = url.searchParams.toString();
              return Promise.all([_core_Resource_js__WEBPACK_IMPORTED_MODULE_4__["default"].getJSON({
                url: url.toString()
              }), new PackParam({
                url: url,
                version: version,
                queryType: queryType
              })]);
            }
          // case SupportedVersion.r340:
          // case SupportedVersion.r341:
          // case SupportedVersion.r350: 
          default:
            {
              var _url2 = new URL("./v2/dataQuery/allTypes/".concat(layerId, "/").concat(queryType.name), serverUrl);
              return Promise.all([_core_Resource_js__WEBPACK_IMPORTED_MODULE_4__["default"].postJSON({
                url: _url2.toString(),
                body: "",
                headers: {
                  "Content-Type": "application/json;charset=UTF-8"
                }
              }), new PackParam({
                url: _url2,
                version: version,
                queryType: queryType
              })]);
            }
          // default: {
          //     return Promise.reject("无法识别的服务版本");
          // }
        }
      }).then(assignResultByQueryType);
    }

    /**
     * （可附加空间空间查询和属性条件查询）查询得到指定字段或者所有字段
     * @static
     * @param {String} serverUrl 部署服务的地址。推荐写成<code>http://*************/license</code> 形式,便于自检.
     * @param {String|Array.<String>} layersId 单个图层id者图层id数组
     * <br>非管线模型的其他数据的guid（倾斜摄影单体化查询对应改倾斜摄影模型数据的id):3fef188b-8415-4aa0-a3e9-aa7a8f579461
     * <br>管点: 8f74c7e1-df95-406a-bf98-6624eb61d876-point <br>管段:8f74c7e1-df95-406a-bf98-6624eb61d876-line
     * <br>可以通过<code>pickedObject.primitive.properties["gw_layer_guid"]</code>或者可以通过<code>tileset.properties["gw_layer_guid"]</code>h获取
     * @param {QueryCondition} queryCondition 设置属性判断条件，空间拓扑条件，指定返回字段
     * @param {Object} [pageOption] 分页选项
     * @param {number} [pageOption.num] 页码编号
     * @param {number} [pageOption.size] 单页记录个数
     * @return {Promise<QueryPropertyResult>}
     */
  }, {
    key: "queryProperty",
    value: function queryProperty(serverUrl, layersId, queryCondition) {
      var pageOption = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {
        num: undefined,
        size: undefined
      };
      // todo 使用示例测了空间条件，属性条件没有测
      var fieldNames = queryCondition.fieldNames;
      var queryType = fieldNames.length > 0 ? QueryType.SomeRecordSomeFieldsValues : QueryType.SomeRecordAllFieldsValues;
      return verifyServerVersionAndInitUrl(serverUrl).then(function (result) {
        if (!result) {
          return Promise.reject("服务版本不支持");
        }
        var version = result.version;
        switch (version) {
          case _SupportedVersion_js__WEBPACK_IMPORTED_MODULE_6__["default"].r320:
          case _SupportedVersion_js__WEBPACK_IMPORTED_MODULE_6__["default"].r330:
            {
              var url = getQueryURLWithFixParam({
                serverUrl: serverUrl,
                queryType: queryType,
                layerId: layersId
              });
              // 分页偏移
              if ((0,_core_defined_js__WEBPACK_IMPORTED_MODULE_10__["default"])(pageOption.num) || (0,_core_defined_js__WEBPACK_IMPORTED_MODULE_10__["default"])(pageOption.size)) {
                var lastPage = pageOption.num - 1 >= 0 ? pageOption.num - 1 : 0;
                url.searchParams.set("pg", "".concat(pageOption.size * lastPage, ",").concat(pageOption.size, ",0"));
              }
              url.searchParams.set("fd", fieldNames);
              url.searchParams.set("sc", queryCondition.spatialCondition);
              url.searchParams.set("pc", queryCondition.propertyCondition);
              url.search = url.searchParams.toString();
              return Promise.all([_core_Resource_js__WEBPACK_IMPORTED_MODULE_4__["default"].getJSON({
                url: url.toString()
              }), new PackParam({
                url: url,
                version: version,
                queryType: queryType
              })]);
            }
          // case SupportedVersion.r340:
          // case SupportedVersion.r341:
          // case SupportedVersion.r350: 
          default:
            {
              var _url3 = new URL("./v2/dataQuery/allTypes/".concat((0,_core_getFlatUniqueArray_js__WEBPACK_IMPORTED_MODULE_9__["default"])(layersId).join(","), "/").concat(queryType.name), serverUrl);
              var searchParams = new URLSearchParams();
              if ((0,_core_defined_js__WEBPACK_IMPORTED_MODULE_10__["default"])(pageOption.num) || (0,_core_defined_js__WEBPACK_IMPORTED_MODULE_10__["default"])(pageOption.size)) {
                searchParams.set("pg", "".concat(pageOption.num, ",").concat(pageOption.size));
              }
              searchParams.set("fd", fieldNames);
              searchParams.set("sc", queryCondition.spatialCondition);
              searchParams.set("pc", queryCondition.propertyCondition);
              return Promise.all([_core_Resource_js__WEBPACK_IMPORTED_MODULE_4__["default"].postJSON({
                url: _url3.toString(),
                body: (0,_core_convertSearchParamToJSONString_js__WEBPACK_IMPORTED_MODULE_8__["default"])(searchParams),
                headers: {
                  "Content-Type": "application/json;charset=UTF-8"
                }
              }), new PackParam({
                url: _url3,
                version: version,
                queryType: queryType
              })]);
            }
          // default: {
          //     return Promise.reject("无法识别的服务版本");
          // }
        }
      }).then(assignResultByQueryType);
    }
  }]);
  return Query;
}();
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Query);

/***/ }),

/***/ "./src/service/QueryCondition.js":
/*!***************************************!*\
  !*** ./src/service/QueryCondition.js ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _core_RuntimeError_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../core/RuntimeError.js */ "./src/core/RuntimeError.js");
/* harmony import */ var _SqlOperator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./SqlOperator.js */ "./src/service/SqlOperator.js");
function _typeof(obj) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && "function" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }, _typeof(obj); }
function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }
function _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }
function _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, "prototype", { writable: false }); return Constructor; }
function _toPropertyKey(arg) { var key = _toPrimitive(arg, "string"); return _typeof(key) === "symbol" ? key : String(key); }
function _toPrimitive(input, hint) { if (_typeof(input) !== "object" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || "default"); if (_typeof(res) !== "object") return res; throw new TypeError("@@toPrimitive must return a primitive value."); } return (hint === "string" ? String : Number)(input); }



/**
 * 将经纬度坐标串进行判断，用来相交线和相交面查询
 * @private
 */
function _processPositionTo3DArray(arrayOfLonlatHeight) {
  if (!(arrayOfLonlatHeight.length % 2 === 0 || arrayOfLonlatHeight.length % 3 === 0)) {
    throw new _core_RuntimeError_js__WEBPACK_IMPORTED_MODULE_0__["default"]("多边形顶点坐标数组个数错误");
  }
  // TODO 这个地方不严谨，2*3为公约数的个数时会发生判断异常的情况
  // 增加方法区分2D,3D。保留该旧接口，输出警告。
  var pointIs3D = arrayOfLonlatHeight.length % 3 === 0;
  var pointLength = pointIs3D ? arrayOfLonlatHeight.length / 3 : arrayOfLonlatHeight.length / 2;
  var points = [];
  for (var i = 0; i < pointLength; i++) {
    var pointIndex = (pointIs3D ? 3 : 2) * i; // 第pointIndex个点
    points.push(arrayOfLonlatHeight[pointIndex + 0]);
    points.push(arrayOfLonlatHeight[pointIndex + 1]);
    points.push(pointIs3D ? arrayOfLonlatHeight[pointIndex + 2] : 0.0);
  }
  return points;
}

/**
 * 查询条件，可以设置空间拓扑查询条件、属性（字段）查询条件、指定返回字段。
 * <br>
 * 其中空间查询仅最后一次设置的条件有效。多次设置取仅最后一次有效值。传入坐标参数经纬度均为十进制度形式的地理坐标。
 * <br>
 * 属性查询支持多个条件，多个属性条件之间的联结关系参考{@link QueryCondition.addPropertyCondition}用法。
 * <br>
 * 支持指定返回字段，不指定返回字段则返回该次查询的所有字段。
 * @constructor
 */
var QueryCondition = /*#__PURE__*/function () {
  function QueryCondition() {
    _classCallCheck(this, QueryCondition);
    this._withSpatialCondition = false;
    this._withPropertyConditon = false;
    this._fieldNames = [];
    this._spatialCondition = "";
    this._propertyCondition = [];
  }

  /**
   * @readonly
   * @return {string}
   */
  _createClass(QueryCondition, [{
    key: "spatialCondition",
    get: function get() {
      return this._spatialCondition;
    }

    /**
     * @readonly
     * @return {string}
     */
  }, {
    key: "propertyCondition",
    get: function get() {
      return this._propertyCondition.join("");
    }

    /**
     * @readonly
     * @return {boolean}
     */
  }, {
    key: "withSpatialCondition",
    get: function get() {
      return this._withSpatialCondition;
    }

    /**
     * @readonly
     * @return {boolean}
     */
  }, {
    key: "withPropertyConditon",
    get: function get() {
      return this._withPropertyConditon;
    }

    /**
     * @readonly
     * @return {string}
     */
  }, {
    key: "fieldNames",
    get: function get() {
      return this._fieldNames.join(",");
    }

    /**
     * 检查经度是否规范
     * @param longitude
     * @return {boolean}
     * @private
     */
  }, {
    key: "_checkLongitude",
    value: function _checkLongitude(longitude) {
      if (longitude > 180 || longitude < -180) {
        throw new _core_RuntimeError_js__WEBPACK_IMPORTED_MODULE_0__["default"]("经度范围应为十进制度,-180 ~ 180 之间");
      }
      return true;
    }

    /**
     * 检查纬度是否规范
     * @param latitude 纬度
     * @return {boolean}
     * @private
     */
  }, {
    key: "_checkLatitude",
    value: function _checkLatitude(latitude) {
      if (latitude > 90 || latitude < -90) {
        throw new _core_RuntimeError_js__WEBPACK_IMPORTED_MODULE_0__["default"]("纬度范围应为十进制度,-90 ~ 90 之间");
      }
      return true;
    }

    /**
     * 空间拓扑查询，查询被该圆形区域内包含的记录.支持矢量数据、管线模型、矢量面拉伸体模型、倾斜摄影单体化数据。
     * <br>
     * 用于倾斜摄影单体化查询的时参数含义：指定查询点的地理坐标，半径参数作为单体查询容差（阈值）
     * @param {Number} longitude 经度，十进制度
     * @param {Number} latitude 纬度，十进制度
     * @param {Number} height 高度，十进制度
     * @param {Number} radius 圆域半径，米，必须大于0
     */
  }, {
    key: "setAsCircle",
    value: function setAsCircle(longitude, latitude, height, radius) {
      radius > 0 && this._checkLatitude(latitude) && this._checkLongitude(longitude);
      if (this._withSpatialCondition) {
        console.warn("空间查询条件重置");
      }
      this._withSpatialCondition = true;
      this._spatialCondition = "(3,0,".concat(radius, ",").concat(longitude, ",").concat(latitude, ",").concat(height, ")");
    }

    /**
     * 空间拓扑查询,查询被该多边形包含的记录.支持矢量数据、管线模型、矢量面拉伸体模型。不支持倾斜摄影单体化数据。
     * 弃用当前方法，请立即直接将该函数名称替换成 {@link QueryCondition#setAsPolygon2D}或者 {@link QueryCondition#setAsPolygon3D}
     * @param {Array.<Number>} arrayOfLonlatHeight 指定多边形的各个顶点，可不闭合。数组中坐标形式应为 [经度,纬度,高度,经度,纬度,高度] ,其中高度可以不写
     * @deprecated
     * @see {QueryCondition#setAsPolygon2D}
     * @see {QueryCondition#setAsPolygon3D}
     */
  }, {
    key: "setAsPolygon",
    value: function setAsPolygon(arrayOfLonlatHeight) {
      if (this._withSpatialCondition) {
        console.warn("空间查询条件重置");
      }
      console.warn("[CommonService]:setAsPolygon\u51FD\u6570\u5F03\u7528\uFF0C\u8BF7\u7ACB\u5373\u76F4\u63A5\u5C06\u8BE5\u51FD\u6570\u540D\u79F0\u66FF\u6362\u6210setAsPolygon2D\u6216\u8005setAsPolygon3D\u5373\u53EF!");
      this._withSpatialCondition = true;
      var points = _processPositionTo3DArray(arrayOfLonlatHeight);
      this._spatialCondition = "(2,".concat(points.length / 3, ",").concat(points.join(","), ")");
    }

    /**
     * 空间拓扑查询,查询被该多边形包含的记录.支持矢量数据、管线模型、矢量面拉伸体模型。不支持倾斜摄影单体化数据。
     * @param {Array.<Number>} arrayOfLonlat 指定多边形的各个顶点，可不闭合。数组中坐标形式应为 [经度,纬度,经度,纬度,] 经纬度十进制度表示法。也支持setAsPolygon2D(119.0,38.0...)方式调用,没有高度参数
     */
    // eslint-disable-next-line no-unused-vars
  }, {
    key: "setAsPolygon2D",
    value: function setAsPolygon2D(arrayOfLonlat) {
      if (this._withSpatialCondition) {
        console.info("空间查询条件重置");
      }
      this._withSpatialCondition = true;
      var inputed = Array.prototype.slice.call(arguments).flat(3);
      if (!(inputed.length % 2 === 0)) {
        throw new _core_RuntimeError_js__WEBPACK_IMPORTED_MODULE_0__["default"]("坐标格式化错误");
      }
      var points = [];
      var pointLength = inputed.length / 2;
      for (var i = 0; i < pointLength; i++) {
        var pointIndex = 2 * i; // 第pointIndex个点
        points.push(inputed[pointIndex + 0]);
        points.push(inputed[pointIndex + 1]);
        points.push(0.0);
      }
      this._spatialCondition = "(2,".concat(pointLength, ",").concat(points.join(","), ")");
    }
    /**
     * 空间拓扑查询,查询被该多边形包含的记录.支持矢量数据、管线模型、矢量面拉伸体模型。不支持倾斜摄影单体化数据。
     * @param {Array.<Number>} arrayOfLonlatHeight 指定多边形的各个顶点，可不闭合。数组中坐标形式应为 [经度,纬度,高度，经度,纬度,高度] 经纬度十进制度表示法。也支持setAsPolygon3D(119.0,38.0,181.123,...)方式调用，要有高度参数。
     */
    // eslint-disable-next-line no-unused-vars
  }, {
    key: "setAsPolygon3D",
    value: function setAsPolygon3D(arrayOfLonlatHeight) {
      if (this._withSpatialCondition) {
        console.info("空间查询条件重置");
      }
      this._withSpatialCondition = true;
      var points = Array.prototype.slice.call(arguments).flat(3);
      if (!(points.length % 3 === 0)) {
        throw new _core_RuntimeError_js__WEBPACK_IMPORTED_MODULE_0__["default"]("坐标格式化错误");
      }
      this._spatialCondition = "(2,".concat(points.length / 3, ",").concat(points.join(","), ")");
    }

    /**
     * 空间拓扑查询,查询与该折线相交的记录。支持矢量数据、管线模型、矢量面拉伸体模型。不支持倾斜摄影单体化数据。
     * 弃用当前方法，请立即直接将该函数名称替换成 {@link QueryCondition#setAsPolyline2D}或者 {@link QueryCondition#setAsPolyline3D}
     * @param {Array.<Number>} arrayOfLonlatHeight 指定折线的各个顶点。数组中坐标形式应为 [经度,纬度,高度,经度,纬度,高度] ,其中高度可以统一不写（默认为0)
     * @deprecated
     * @see {QueryCondition#setAsPolyline2D}
     * @see {QueryCondition#setAsPolyline3D}
     */
  }, {
    key: "setAsPolyline",
    value: function setAsPolyline(arrayOfLonlatHeight) {
      console.warn("[CommonService]:setAsPolyline\u51FD\u6570\u5F03\u7528!\u8BF7\u7ACB\u5373\u76F4\u63A5\u5C06\u8BE5\u51FD\u6570\u540D\u79F0\u66FF\u6362\u6210setAsPolyline2D\u6216\u8005setAsPolyline3D\u5373\u53EF!");
      if (this._withSpatialCondition) {
        console.warn("空间查询条件重置");
      }
      this._withSpatialCondition = true;
      var points = _processPositionTo3DArray(arrayOfLonlatHeight);
      this._spatialCondition = "(1,".concat(points.length / 3, ",").concat(points.join(","), ")");
    }

    /**
     * 空间拓扑查询,查询与该折线相交的记录。支持矢量数据、管线模型、矢量面拉伸体模型。不支持倾斜摄影单体化数据。
     * @param {Array.<Number>} arrayOfLonlat 指定折线的各个顶点。数组中坐标形式应为 [经度,纬度,经度,纬度] ,不需要高度
     */
    // eslint-disable-next-line no-unused-vars
  }, {
    key: "setAsPolyline2D",
    value: function setAsPolyline2D(arrayOfLonlat) {
      if (this._withSpatialCondition) {
        console.info("空间查询条件重置");
      }
      this._withSpatialCondition = true;
      var inputed = Array.prototype.slice.call(arguments).flat(3);
      if (!(points.length % 2 === 0)) {
        throw new _core_RuntimeError_js__WEBPACK_IMPORTED_MODULE_0__["default"]("坐标格式化错误");
      }
      var points = [];
      var pointLength = inputed.length / 2;
      for (var i = 0; i < pointLength; i++) {
        var pointIndex = 2 * i; // 第pointIndex个点
        points.push(inputed[pointIndex + 0]);
        points.push(inputed[pointIndex + 1]);
        points.push(0.0);
      }
      this._spatialCondition = "(1,".concat(pointLength, ",").concat(points.join(","), ")");
    }

    /**
     * 空间拓扑查询,查询与该折线相交的记录。支持矢量数据、管线模型、矢量面拉伸体模型。不支持倾斜摄影单体化数据。
     * @param {Array.<Number>} arrayOfLonlatHeight 指定折线的各个顶点。数组中坐标形式应为 [经度,纬度,高度,经度,纬度,高度] ,其中高度一定要写,可以为0.
     */
    // eslint-disable-next-line no-unused-vars
  }, {
    key: "setAsPolyline3D",
    value: function setAsPolyline3D(arrayOfLonlatHeight) {
      if (this._withSpatialCondition) {
        console.info("空间查询条件重置");
      }
      this._withSpatialCondition = true;
      var points = Array.prototype.slice.call(arguments).flat(3);
      if (!(points.length % 3 === 0)) {
        throw new _core_RuntimeError_js__WEBPACK_IMPORTED_MODULE_0__["default"]("坐标格式化错误");
      }
      this._spatialCondition = "(1,".concat(points.length / 3, ",").concat(points.join(","), ")");
    }

    /**
     * 添加指定字段。空间查询,多边形包含.支持矢量，管线模型，矢量面拉伸体模型,倾斜摄影单体化数据
     * @param {String} fieldName 指定查询结果包含该字段
     */
  }, {
    key: "addNeedField",
    value: function addNeedField(fieldName) {
      if (fieldName.trim().length > 0) {
        this._fieldNames.push(fieldName.trim());
      }
    }

    /**
     * 添加属性查询条件，可多次添加.支持矢量，管线模型，矢量面拉伸体模型,倾斜摄影单体化数据
     * @param {symbol} conditionsRelation 与其他属性查询条件的关系,参考{@link SqlOperator}
     * @param {String} fieldName 字段名称
     * @param {symbol} fieldEstimateOperator 字段和字段值的判断关系,参考{@link SqlOperator}
     * @param {String|Number|boolean} fieldValue 字段值
     */
  }, {
    key: "addPropertyCondition",
    value: function addPropertyCondition(conditionsRelation, fieldName, fieldEstimateOperator, fieldValue) {
      this._withPropertyConditon = true;
      this._propertyCondition.push("(".concat(this._getSqlOperator(conditionsRelation), ",").concat(this._getSqlOperator(fieldEstimateOperator), ",").concat(fieldName, ",").concat(fieldValue.toString(), ")"));
    }

    /**
     * 清除属性查询条件
     */
  }, {
    key: "clearPropertyCondition",
    value: function clearPropertyCondition() {
      this._withPropertyConditon = false;
      this._propertyCondition = [];
    }

    /**
     * 清除空间查询条件,取消添加的圆形、多边形或者折线条件
     */
  }, {
    key: "clearSpatialCondition",
    value: function clearSpatialCondition() {
      this._withSpatialCondition = false;
      this._spatialCondition = "";
    }

    /**
     * 清除指定字段
     */
  }, {
    key: "clearNeedField",
    value: function clearNeedField() {
      this._fieldNames = [];
    }

    /**
     * 重置/清空所有的查询条件
     */
  }, {
    key: "reset",
    value: function reset() {
      this.clearPropertyCondition();
      this.clearNeedField();
      this.clearSpatialCondition();
    }

    /**
     * 根据sql操作类型枚举得到对应的网址拼接操作字符
     * @private
     * @param sqlOperator
     * @return {string}
     */
  }, {
    key: "_getSqlOperator",
    value: function _getSqlOperator(sqlOperator) {
      // TODO 未全部验证
      switch (sqlOperator) {
        case _SqlOperator_js__WEBPACK_IMPORTED_MODULE_1__["default"].EQUAL:
          {
            return "equal";
          }
        case _SqlOperator_js__WEBPACK_IMPORTED_MODULE_1__["default"].UNEQUAL:
          {
            return "unequal";
          }
        case _SqlOperator_js__WEBPACK_IMPORTED_MODULE_1__["default"].GREATER:
          {
            return "greater";
          }
        case _SqlOperator_js__WEBPACK_IMPORTED_MODULE_1__["default"].GEQUAL:
          {
            return "greaterequal";
          }
        case _SqlOperator_js__WEBPACK_IMPORTED_MODULE_1__["default"].LESS:
          {
            return "less";
          }
        case _SqlOperator_js__WEBPACK_IMPORTED_MODULE_1__["default"].LEQUAL:
          {
            return "lessequal";
          }
        case _SqlOperator_js__WEBPACK_IMPORTED_MODULE_1__["default"].LIKE:
          {
            return "like";
          }
        case _SqlOperator_js__WEBPACK_IMPORTED_MODULE_1__["default"].LLIKE:
          {
            return "llike";
          }
        case _SqlOperator_js__WEBPACK_IMPORTED_MODULE_1__["default"].RLIKE:
          {
            return "rlike";
          }
        case _SqlOperator_js__WEBPACK_IMPORTED_MODULE_1__["default"].AND:
          {
            return "and";
          }
        case _SqlOperator_js__WEBPACK_IMPORTED_MODULE_1__["default"].OR:
          {
            return "or";
          }
        case _SqlOperator_js__WEBPACK_IMPORTED_MODULE_1__["default"].NOT:
          {
            return "not";
          }
        default:
          {
            throw new _core_RuntimeError_js__WEBPACK_IMPORTED_MODULE_0__["default"]("参数SQL操作符枚举值错误");
          }
      }
    }
  }]);
  return QueryCondition;
}();
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (QueryCondition);

/***/ }),

/***/ "./src/service/SqlOperator.js":
/*!************************************!*\
  !*** ./src/service/SqlOperator.js ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/**
 * SQL条件查询操作类型，在{@link QueryCondition}中作为入参使用。
 * @readonly
 * @enum
 */
var SqlOperator = {
  /**
   * 相等。字段和字段值的判断关系，参考参考{@link QueryCondition#addPropertyCondition}
   * @type {symbol}
   */
  EQUAL: Symbol("equal"),
  /**
   * 不相等。字段和字段值的判断关系，参考参考{@link QueryCondition#addPropertyCondition}
   * @type {symbol}
   */
  UNEQUAL: Symbol("unequal"),
  /**
   * 大于。字段和字段值的判断关系，参考参考{@link QueryCondition#addPropertyCondition}
   * @type {symbol}
   */
  GREATER: Symbol("greater"),
  /**
   * 大于等于。字段和字段值的判断关系，参考参考{@link QueryCondition#addPropertyCondition}
   * @type {symbol}
   */
  GEQUAL: Symbol("greaterequal"),
  /**
   * 小于。字段和字段值的判断关系，参考参考{@link QueryCondition#addPropertyCondition}
   * @type {symbol}
   */
  LESS: Symbol("less"),
  /**
   * 小于等于。字段和字段值的判断关系，参考参考{@link QueryCondition#addPropertyCondition}
   * @type {symbol}
   */
  LEQUAL: Symbol("lessequal"),
  /**
   * 模糊搜索关键词。字段和字段值的判断关系，参考参考{@link QueryCondition#addPropertyCondition}
   * @type {symbol}
   */
  LIKE: Symbol("like"),
  /**
   * 模糊左侧进行搜索。字段和字段值的判断关系，参考参考{@link QueryCondition#addPropertyCondition}
   * @type {symbol}
   */
  LLIKE: Symbol("llike"),
  /**
   * 模糊右侧进行搜索
   * @type {symbol}
   */
  RLIKE: Symbol("rlike"),
  /**
   * 逻辑或,仅用于与其他属性查询条件的关系，参考{@link QueryCondition#addPropertyCondition}
   * @type {symbol}
   */
  OR: Symbol("or"),
  /**
   * 逻辑并,仅用于与其他属性查询条件的关系，参考{@link QueryCondition#addPropertyCondition}
   * @type {symbol}
   */
  AND: Symbol("and"),
  /**
   * 逻辑非,仅用于与其他属性查询条件的关系，参考{@link QueryCondition#addPropertyCondition}
   * @type {symbol}
   */
  NOT: Symbol("not")
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Object.freeze(SqlOperator));

/***/ }),

/***/ "./src/service/Statistics.js":
/*!***********************************!*\
  !*** ./src/service/Statistics.js ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _StatisticsCondition_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./StatisticsCondition.js */ "./src/service/StatisticsCondition.js");
/* harmony import */ var _results_ResponseResult_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../results/ResponseResult.js */ "./src/results/ResponseResult.js");
/* harmony import */ var _RegisterServer_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../RegisterServer.js */ "./src/RegisterServer.js");
/* harmony import */ var _SupportedVersion_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../SupportedVersion.js */ "./src/SupportedVersion.js");
/* harmony import */ var _warning_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../warning.js */ "./src/warning.js");
/* harmony import */ var _core_Resource_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../core/Resource.js */ "./src/core/Resource.js");
/* harmony import */ var _core_getFlatUniqueArray_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../core/getFlatUniqueArray.js */ "./src/core/getFlatUniqueArray.js");
function _typeof(obj) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && "function" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }, _typeof(obj); }
function _regeneratorRuntime() { "use strict"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return exports; }; var exports = {}, Op = Object.prototype, hasOwn = Op.hasOwnProperty, defineProperty = Object.defineProperty || function (obj, key, desc) { obj[key] = desc.value; }, $Symbol = "function" == typeof Symbol ? Symbol : {}, iteratorSymbol = $Symbol.iterator || "@@iterator", asyncIteratorSymbol = $Symbol.asyncIterator || "@@asyncIterator", toStringTagSymbol = $Symbol.toStringTag || "@@toStringTag"; function define(obj, key, value) { return Object.defineProperty(obj, key, { value: value, enumerable: !0, configurable: !0, writable: !0 }), obj[key]; } try { define({}, ""); } catch (err) { define = function define(obj, key, value) { return obj[key] = value; }; } function wrap(innerFn, outerFn, self, tryLocsList) { var protoGenerator = outerFn && outerFn.prototype instanceof Generator ? outerFn : Generator, generator = Object.create(protoGenerator.prototype), context = new Context(tryLocsList || []); return defineProperty(generator, "_invoke", { value: makeInvokeMethod(innerFn, self, context) }), generator; } function tryCatch(fn, obj, arg) { try { return { type: "normal", arg: fn.call(obj, arg) }; } catch (err) { return { type: "throw", arg: err }; } } exports.wrap = wrap; var ContinueSentinel = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var IteratorPrototype = {}; define(IteratorPrototype, iteratorSymbol, function () { return this; }); var getProto = Object.getPrototypeOf, NativeIteratorPrototype = getProto && getProto(getProto(values([]))); NativeIteratorPrototype && NativeIteratorPrototype !== Op && hasOwn.call(NativeIteratorPrototype, iteratorSymbol) && (IteratorPrototype = NativeIteratorPrototype); var Gp = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(IteratorPrototype); function defineIteratorMethods(prototype) { ["next", "throw", "return"].forEach(function (method) { define(prototype, method, function (arg) { return this._invoke(method, arg); }); }); } function AsyncIterator(generator, PromiseImpl) { function invoke(method, arg, resolve, reject) { var record = tryCatch(generator[method], generator, arg); if ("throw" !== record.type) { var result = record.arg, value = result.value; return value && "object" == _typeof(value) && hasOwn.call(value, "__await") ? PromiseImpl.resolve(value.__await).then(function (value) { invoke("next", value, resolve, reject); }, function (err) { invoke("throw", err, resolve, reject); }) : PromiseImpl.resolve(value).then(function (unwrapped) { result.value = unwrapped, resolve(result); }, function (error) { return invoke("throw", error, resolve, reject); }); } reject(record.arg); } var previousPromise; defineProperty(this, "_invoke", { value: function value(method, arg) { function callInvokeWithMethodAndArg() { return new PromiseImpl(function (resolve, reject) { invoke(method, arg, resolve, reject); }); } return previousPromise = previousPromise ? previousPromise.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(innerFn, self, context) { var state = "suspendedStart"; return function (method, arg) { if ("executing" === state) throw new Error("Generator is already running"); if ("completed" === state) { if ("throw" === method) throw arg; return doneResult(); } for (context.method = method, context.arg = arg;;) { var delegate = context.delegate; if (delegate) { var delegateResult = maybeInvokeDelegate(delegate, context); if (delegateResult) { if (delegateResult === ContinueSentinel) continue; return delegateResult; } } if ("next" === context.method) context.sent = context._sent = context.arg;else if ("throw" === context.method) { if ("suspendedStart" === state) throw state = "completed", context.arg; context.dispatchException(context.arg); } else "return" === context.method && context.abrupt("return", context.arg); state = "executing"; var record = tryCatch(innerFn, self, context); if ("normal" === record.type) { if (state = context.done ? "completed" : "suspendedYield", record.arg === ContinueSentinel) continue; return { value: record.arg, done: context.done }; } "throw" === record.type && (state = "completed", context.method = "throw", context.arg = record.arg); } }; } function maybeInvokeDelegate(delegate, context) { var methodName = context.method, method = delegate.iterator[methodName]; if (undefined === method) return context.delegate = null, "throw" === methodName && delegate.iterator["return"] && (context.method = "return", context.arg = undefined, maybeInvokeDelegate(delegate, context), "throw" === context.method) || "return" !== methodName && (context.method = "throw", context.arg = new TypeError("The iterator does not provide a '" + methodName + "' method")), ContinueSentinel; var record = tryCatch(method, delegate.iterator, context.arg); if ("throw" === record.type) return context.method = "throw", context.arg = record.arg, context.delegate = null, ContinueSentinel; var info = record.arg; return info ? info.done ? (context[delegate.resultName] = info.value, context.next = delegate.nextLoc, "return" !== context.method && (context.method = "next", context.arg = undefined), context.delegate = null, ContinueSentinel) : info : (context.method = "throw", context.arg = new TypeError("iterator result is not an object"), context.delegate = null, ContinueSentinel); } function pushTryEntry(locs) { var entry = { tryLoc: locs[0] }; 1 in locs && (entry.catchLoc = locs[1]), 2 in locs && (entry.finallyLoc = locs[2], entry.afterLoc = locs[3]), this.tryEntries.push(entry); } function resetTryEntry(entry) { var record = entry.completion || {}; record.type = "normal", delete record.arg, entry.completion = record; } function Context(tryLocsList) { this.tryEntries = [{ tryLoc: "root" }], tryLocsList.forEach(pushTryEntry, this), this.reset(!0); } function values(iterable) { if (iterable) { var iteratorMethod = iterable[iteratorSymbol]; if (iteratorMethod) return iteratorMethod.call(iterable); if ("function" == typeof iterable.next) return iterable; if (!isNaN(iterable.length)) { var i = -1, next = function next() { for (; ++i < iterable.length;) if (hasOwn.call(iterable, i)) return next.value = iterable[i], next.done = !1, next; return next.value = undefined, next.done = !0, next; }; return next.next = next; } } return { next: doneResult }; } function doneResult() { return { value: undefined, done: !0 }; } return GeneratorFunction.prototype = GeneratorFunctionPrototype, defineProperty(Gp, "constructor", { value: GeneratorFunctionPrototype, configurable: !0 }), defineProperty(GeneratorFunctionPrototype, "constructor", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, toStringTagSymbol, "GeneratorFunction"), exports.isGeneratorFunction = function (genFun) { var ctor = "function" == typeof genFun && genFun.constructor; return !!ctor && (ctor === GeneratorFunction || "GeneratorFunction" === (ctor.displayName || ctor.name)); }, exports.mark = function (genFun) { return Object.setPrototypeOf ? Object.setPrototypeOf(genFun, GeneratorFunctionPrototype) : (genFun.__proto__ = GeneratorFunctionPrototype, define(genFun, toStringTagSymbol, "GeneratorFunction")), genFun.prototype = Object.create(Gp), genFun; }, exports.awrap = function (arg) { return { __await: arg }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, asyncIteratorSymbol, function () { return this; }), exports.AsyncIterator = AsyncIterator, exports.async = function (innerFn, outerFn, self, tryLocsList, PromiseImpl) { void 0 === PromiseImpl && (PromiseImpl = Promise); var iter = new AsyncIterator(wrap(innerFn, outerFn, self, tryLocsList), PromiseImpl); return exports.isGeneratorFunction(outerFn) ? iter : iter.next().then(function (result) { return result.done ? result.value : iter.next(); }); }, defineIteratorMethods(Gp), define(Gp, toStringTagSymbol, "Generator"), define(Gp, iteratorSymbol, function () { return this; }), define(Gp, "toString", function () { return "[object Generator]"; }), exports.keys = function (val) { var object = Object(val), keys = []; for (var key in object) keys.push(key); return keys.reverse(), function next() { for (; keys.length;) { var key = keys.pop(); if (key in object) return next.value = key, next.done = !1, next; } return next.done = !0, next; }; }, exports.values = values, Context.prototype = { constructor: Context, reset: function reset(skipTempReset) { if (this.prev = 0, this.next = 0, this.sent = this._sent = undefined, this.done = !1, this.delegate = null, this.method = "next", this.arg = undefined, this.tryEntries.forEach(resetTryEntry), !skipTempReset) for (var name in this) "t" === name.charAt(0) && hasOwn.call(this, name) && !isNaN(+name.slice(1)) && (this[name] = undefined); }, stop: function stop() { this.done = !0; var rootRecord = this.tryEntries[0].completion; if ("throw" === rootRecord.type) throw rootRecord.arg; return this.rval; }, dispatchException: function dispatchException(exception) { if (this.done) throw exception; var context = this; function handle(loc, caught) { return record.type = "throw", record.arg = exception, context.next = loc, caught && (context.method = "next", context.arg = undefined), !!caught; } for (var i = this.tryEntries.length - 1; i >= 0; --i) { var entry = this.tryEntries[i], record = entry.completion; if ("root" === entry.tryLoc) return handle("end"); if (entry.tryLoc <= this.prev) { var hasCatch = hasOwn.call(entry, "catchLoc"), hasFinally = hasOwn.call(entry, "finallyLoc"); if (hasCatch && hasFinally) { if (this.prev < entry.catchLoc) return handle(entry.catchLoc, !0); if (this.prev < entry.finallyLoc) return handle(entry.finallyLoc); } else if (hasCatch) { if (this.prev < entry.catchLoc) return handle(entry.catchLoc, !0); } else { if (!hasFinally) throw new Error("try statement without catch or finally"); if (this.prev < entry.finallyLoc) return handle(entry.finallyLoc); } } } }, abrupt: function abrupt(type, arg) { for (var i = this.tryEntries.length - 1; i >= 0; --i) { var entry = this.tryEntries[i]; if (entry.tryLoc <= this.prev && hasOwn.call(entry, "finallyLoc") && this.prev < entry.finallyLoc) { var finallyEntry = entry; break; } } finallyEntry && ("break" === type || "continue" === type) && finallyEntry.tryLoc <= arg && arg <= finallyEntry.finallyLoc && (finallyEntry = null); var record = finallyEntry ? finallyEntry.completion : {}; return record.type = type, record.arg = arg, finallyEntry ? (this.method = "next", this.next = finallyEntry.finallyLoc, ContinueSentinel) : this.complete(record); }, complete: function complete(record, afterLoc) { if ("throw" === record.type) throw record.arg; return "break" === record.type || "continue" === record.type ? this.next = record.arg : "return" === record.type ? (this.rval = this.arg = record.arg, this.method = "return", this.next = "end") : "normal" === record.type && afterLoc && (this.next = afterLoc), ContinueSentinel; }, finish: function finish(finallyLoc) { for (var i = this.tryEntries.length - 1; i >= 0; --i) { var entry = this.tryEntries[i]; if (entry.finallyLoc === finallyLoc) return this.complete(entry.completion, entry.afterLoc), resetTryEntry(entry), ContinueSentinel; } }, "catch": function _catch(tryLoc) { for (var i = this.tryEntries.length - 1; i >= 0; --i) { var entry = this.tryEntries[i]; if (entry.tryLoc === tryLoc) { var record = entry.completion; if ("throw" === record.type) { var thrown = record.arg; resetTryEntry(entry); } return thrown; } } throw new Error("illegal catch attempt"); }, delegateYield: function delegateYield(iterable, resultName, nextLoc) { return this.delegate = { iterator: values(iterable), resultName: resultName, nextLoc: nextLoc }, "next" === this.method && (this.arg = undefined), ContinueSentinel; } }, exports; }
function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }
function _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }
function _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, "prototype", { writable: false }); return Constructor; }
function _toPropertyKey(arg) { var key = _toPrimitive(arg, "string"); return _typeof(key) === "symbol" ? key : String(key); }
function _toPrimitive(input, hint) { if (_typeof(input) !== "object" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || "default"); if (_typeof(res) !== "object") return res; throw new TypeError("@@toPrimitive must return a primitive value."); } return (hint === "string" ? String : Number)(input); }
function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }
function _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err); } _next(undefined); }); }; }







function verifyServerVersionAndInitUrl(_x) {
  return _verifyServerVersionAndInitUrl.apply(this, arguments);
}
/**
 * @namespace Statistics
 */
function _verifyServerVersionAndInitUrl() {
  _verifyServerVersionAndInitUrl = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee(serverUrl) {
    var version, url;
    return _regeneratorRuntime().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          _context.next = 2;
          return _RegisterServer_js__WEBPACK_IMPORTED_MODULE_2__["default"].getVersionByServiceAnyURI(serverUrl);
        case 2:
          version = _context.sent;
          if (!(version < _SupportedVersion_js__WEBPACK_IMPORTED_MODULE_3__["default"].r340)) {
            _context.next = 6;
            break;
          }
          _warning_js__WEBPACK_IMPORTED_MODULE_4__["default"].ByVersion("Statistics", _SupportedVersion_js__WEBPACK_IMPORTED_MODULE_3__["default"].r340, version);
          return _context.abrupt("return", null);
        case 6:
          url = new URL(serverUrl);
          url.search = "";
          url.hash = "";
          return _context.abrupt("return", {
            version: version,
            url: url
          });
        case 10:
        case "end":
          return _context.stop();
      }
    }, _callee);
  }));
  return _verifyServerVersionAndInitUrl.apply(this, arguments);
}
var Statistics = /*#__PURE__*/function () {
  function Statistics() {
    _classCallCheck(this, Statistics);
  }
  _createClass(Statistics, null, [{
    key: "getStatistics",
    value:
    /**
     * 静态方法，执行统计分析
     * @example
     *  const statisticsCondition = new CommonService.StatisticsCondition({
     *                     fieldName: fieldName,
     *                     distinct: distinct,
     *                     type: statisticsType,
     *                     sections: sections,
     *                 },
     *  queryCondition
     *  );
     *  CommonService.Statistics.getStatistics(serverUrl, layerId, statisticsCondition).then(function (result) {
     *                 console.log(result);
     *                 window.alert(JSON.stringify(result.data));
     *             });
     * @static
     * @async
     * @param {String} serverUrl 服务地址
     * @param {String|Array.<String>} layerId 数据的ID,或者Id数组
     * @param {StatisticsCondition} statisticsCondition 统计条件，详情参考{@link StatisticsCondition}
     * @return {Promise<ResponseResult>}
     */
    function getStatistics(serverUrl, layerId, statisticsCondition) {
      var body = {
        distinct: statisticsCondition.distinct,
        eleva: "1",
        field: statisticsCondition.fieldName,
        pc: statisticsCondition.queryCondition.withPropertyConditon ? statisticsCondition.queryCondition.propertyCondition : "",
        sc: statisticsCondition.queryCondition.withSpatialCondition ? statisticsCondition.queryCondition.spatialCondition : "",
        sections: statisticsCondition.sections,
        type: statisticsCondition.type
      };
      return verifyServerVersionAndInitUrl(serverUrl).then(function (result) {
        if (!result) {
          return Promise.reject("服务版本不支持");
        }
        var url = new URL("./v2/statisticsAnalysis/statistics/".concat((0,_core_getFlatUniqueArray_js__WEBPACK_IMPORTED_MODULE_6__["default"])(layerId).join(",")), serverUrl);
        return _core_Resource_js__WEBPACK_IMPORTED_MODULE_5__["default"].postJSON({
          url: url.toString(),
          body: JSON.stringify(body),
          headers: {
            "Content-Type": "application/json;charset=UTF-8"
          }
        });
        // const version = result.version;
        // switch (version) {
        //     case SupportedVersion.r340:
        //     case SupportedVersion.r341:
        //     case SupportedVersion.r350: {
        //         const url = new URL(
        //             `./v2/statisticsAnalysis/statistics/${getFlatUniqueArray(layerId).join(",")}`,
        //             serverUrl
        //         );

        //         return Resource.postJSON({
        //             url: url.toString(),
        //             body: JSON.stringify(body),
        //             headers: { "Content-Type": "application/json;charset=UTF-8" },
        //         });
        //     }
        //     default: {
        //         return Promise.reject("无法识别的服务版本");
        //     }
        // }
      }).then(function (response) {
        return new _results_ResponseResult_js__WEBPACK_IMPORTED_MODULE_1__["default"]({
          response: response
        });
      });
    }
  }]);
  return Statistics;
}();
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Statistics);

/***/ }),

/***/ "./src/service/StatisticsCondition.js":
/*!********************************************!*\
  !*** ./src/service/StatisticsCondition.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _QueryCondition_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./QueryCondition.js */ "./src/service/QueryCondition.js");
/* harmony import */ var _core_defaultValue_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../core/defaultValue.js */ "./src/core/defaultValue.js");
/* harmony import */ var _core_StatisticsType_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../core/StatisticsType.js */ "./src/core/StatisticsType.js");
function _typeof(obj) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && "function" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }, _typeof(obj); }
function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }
function _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }
function _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, "prototype", { writable: false }); return Constructor; }
function _toPropertyKey(arg) { var key = _toPrimitive(arg, "string"); return _typeof(key) === "symbol" ? key : String(key); }
function _toPrimitive(input, hint) { if (_typeof(input) !== "object" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || "default"); if (_typeof(res) !== "object") return res; throw new TypeError("@@toPrimitive must return a primitive value."); } return (hint === "string" ? String : Number)(input); }



var StatisticsCondition = /*#__PURE__*/function () {
  /**
   * 条件统计（单字段）若干图层的统计，{@link StatisticsType},针对不同数据类型（数值型、字符型等）字段的个数统计、区间分段、求和。
   * @constructor
   * @param options {Object}
   * @param {String} options.fieldName 字段名称
   * @param {String} [options.sections=""]  分段参数，传入分段的数值锚点，如"0,10";"0,5,10,20"
   * @param {Boolean} [options.distinct=false] 搭配{@link StatisticsType.Count} 才有意义，true/false表示count是否去重，
   * @param {StatisticsType} [options.type=StatisticsType.Agg] StatisticsType.Agg只针对数值型字段,会返回累加值,平均值,最大值,最小值等信息，StatisticsType.Count计数
   * @param {QueryCondition} [queryCondition]  统计（查询条件）{@link QueryCondition}
   */
  function StatisticsCondition(options, queryCondition) {
    _classCallCheck(this, StatisticsCondition);
    this._fieldName = (0,_core_defaultValue_js__WEBPACK_IMPORTED_MODULE_1__["default"])(options.fieldName, "");
    this._distinct = (0,_core_defaultValue_js__WEBPACK_IMPORTED_MODULE_1__["default"])(options.distinct, false);
    this._type = (0,_core_defaultValue_js__WEBPACK_IMPORTED_MODULE_1__["default"])(options.type, _core_StatisticsType_js__WEBPACK_IMPORTED_MODULE_2__["default"].Agg);
    this._sections = (0,_core_defaultValue_js__WEBPACK_IMPORTED_MODULE_1__["default"])(options.sections, "");
    this._queryCondition = (0,_core_defaultValue_js__WEBPACK_IMPORTED_MODULE_1__["default"])(queryCondition, new _QueryCondition_js__WEBPACK_IMPORTED_MODULE_0__["default"]());
  }

  /**
   * 搭配{@link StatisticsType.Count} 使用有效，
   * @memberOf StatisticsCondition
   * @return {*}
   */
  _createClass(StatisticsCondition, [{
    key: "distinct",
    get: function get() {
      return this._distinct;
    },
    set: function set(v) {
      this._distinct = !!v;
    }

    /**
     * 字段名称
     * @memberOf StatisticsCondition
     * @return {*}
     */
  }, {
    key: "fieldName",
    get: function get() {
      return this._fieldName;
    },
    set: function set(v) {
      this._fieldName = (0,_core_defaultValue_js__WEBPACK_IMPORTED_MODULE_1__["default"])(v, "");
    }
  }, {
    key: "sections",
    get: function get() {
      return this._sections;
    },
    set: function set(v) {
      this._sections = (0,_core_defaultValue_js__WEBPACK_IMPORTED_MODULE_1__["default"])(v, "");
    }
  }, {
    key: "type",
    get: function get() {
      return this._type;
    },
    set: function set(v) {
      if (_core_StatisticsType_js__WEBPACK_IMPORTED_MODULE_2__["default"]._valid(v)) {
        this._type = v;
      }
    }
  }, {
    key: "queryCondition",
    get: function get() {
      return this._queryCondition;
    },
    set: function set(v) {
      if (!v || !(v instanceof _QueryCondition_js__WEBPACK_IMPORTED_MODULE_0__["default"])) {
        console.warn("统计条件设置错误");
        return;
      }
      this._queryCondition = v;
    }
  }]);
  return StatisticsCondition;
}();
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (StatisticsCondition);

/***/ }),

/***/ "./src/service/TerrainAnalysis.js":
/*!****************************************!*\
  !*** ./src/service/TerrainAnalysis.js ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _results_ResponseResult_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../results/ResponseResult.js */ "./src/results/ResponseResult.js");
/* harmony import */ var _RegisterServer_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../RegisterServer.js */ "./src/RegisterServer.js");
/* harmony import */ var _SupportedVersion_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../SupportedVersion.js */ "./src/SupportedVersion.js");
/* harmony import */ var _warning_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../warning.js */ "./src/warning.js");
/* harmony import */ var _core_Resource_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../core/Resource.js */ "./src/core/Resource.js");
/* harmony import */ var _core_getFlatUniqueArray_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../core/getFlatUniqueArray.js */ "./src/core/getFlatUniqueArray.js");
function _typeof(obj) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && "function" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }, _typeof(obj); }
function _regeneratorRuntime() { "use strict"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return exports; }; var exports = {}, Op = Object.prototype, hasOwn = Op.hasOwnProperty, defineProperty = Object.defineProperty || function (obj, key, desc) { obj[key] = desc.value; }, $Symbol = "function" == typeof Symbol ? Symbol : {}, iteratorSymbol = $Symbol.iterator || "@@iterator", asyncIteratorSymbol = $Symbol.asyncIterator || "@@asyncIterator", toStringTagSymbol = $Symbol.toStringTag || "@@toStringTag"; function define(obj, key, value) { return Object.defineProperty(obj, key, { value: value, enumerable: !0, configurable: !0, writable: !0 }), obj[key]; } try { define({}, ""); } catch (err) { define = function define(obj, key, value) { return obj[key] = value; }; } function wrap(innerFn, outerFn, self, tryLocsList) { var protoGenerator = outerFn && outerFn.prototype instanceof Generator ? outerFn : Generator, generator = Object.create(protoGenerator.prototype), context = new Context(tryLocsList || []); return defineProperty(generator, "_invoke", { value: makeInvokeMethod(innerFn, self, context) }), generator; } function tryCatch(fn, obj, arg) { try { return { type: "normal", arg: fn.call(obj, arg) }; } catch (err) { return { type: "throw", arg: err }; } } exports.wrap = wrap; var ContinueSentinel = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var IteratorPrototype = {}; define(IteratorPrototype, iteratorSymbol, function () { return this; }); var getProto = Object.getPrototypeOf, NativeIteratorPrototype = getProto && getProto(getProto(values([]))); NativeIteratorPrototype && NativeIteratorPrototype !== Op && hasOwn.call(NativeIteratorPrototype, iteratorSymbol) && (IteratorPrototype = NativeIteratorPrototype); var Gp = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(IteratorPrototype); function defineIteratorMethods(prototype) { ["next", "throw", "return"].forEach(function (method) { define(prototype, method, function (arg) { return this._invoke(method, arg); }); }); } function AsyncIterator(generator, PromiseImpl) { function invoke(method, arg, resolve, reject) { var record = tryCatch(generator[method], generator, arg); if ("throw" !== record.type) { var result = record.arg, value = result.value; return value && "object" == _typeof(value) && hasOwn.call(value, "__await") ? PromiseImpl.resolve(value.__await).then(function (value) { invoke("next", value, resolve, reject); }, function (err) { invoke("throw", err, resolve, reject); }) : PromiseImpl.resolve(value).then(function (unwrapped) { result.value = unwrapped, resolve(result); }, function (error) { return invoke("throw", error, resolve, reject); }); } reject(record.arg); } var previousPromise; defineProperty(this, "_invoke", { value: function value(method, arg) { function callInvokeWithMethodAndArg() { return new PromiseImpl(function (resolve, reject) { invoke(method, arg, resolve, reject); }); } return previousPromise = previousPromise ? previousPromise.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(innerFn, self, context) { var state = "suspendedStart"; return function (method, arg) { if ("executing" === state) throw new Error("Generator is already running"); if ("completed" === state) { if ("throw" === method) throw arg; return doneResult(); } for (context.method = method, context.arg = arg;;) { var delegate = context.delegate; if (delegate) { var delegateResult = maybeInvokeDelegate(delegate, context); if (delegateResult) { if (delegateResult === ContinueSentinel) continue; return delegateResult; } } if ("next" === context.method) context.sent = context._sent = context.arg;else if ("throw" === context.method) { if ("suspendedStart" === state) throw state = "completed", context.arg; context.dispatchException(context.arg); } else "return" === context.method && context.abrupt("return", context.arg); state = "executing"; var record = tryCatch(innerFn, self, context); if ("normal" === record.type) { if (state = context.done ? "completed" : "suspendedYield", record.arg === ContinueSentinel) continue; return { value: record.arg, done: context.done }; } "throw" === record.type && (state = "completed", context.method = "throw", context.arg = record.arg); } }; } function maybeInvokeDelegate(delegate, context) { var methodName = context.method, method = delegate.iterator[methodName]; if (undefined === method) return context.delegate = null, "throw" === methodName && delegate.iterator["return"] && (context.method = "return", context.arg = undefined, maybeInvokeDelegate(delegate, context), "throw" === context.method) || "return" !== methodName && (context.method = "throw", context.arg = new TypeError("The iterator does not provide a '" + methodName + "' method")), ContinueSentinel; var record = tryCatch(method, delegate.iterator, context.arg); if ("throw" === record.type) return context.method = "throw", context.arg = record.arg, context.delegate = null, ContinueSentinel; var info = record.arg; return info ? info.done ? (context[delegate.resultName] = info.value, context.next = delegate.nextLoc, "return" !== context.method && (context.method = "next", context.arg = undefined), context.delegate = null, ContinueSentinel) : info : (context.method = "throw", context.arg = new TypeError("iterator result is not an object"), context.delegate = null, ContinueSentinel); } function pushTryEntry(locs) { var entry = { tryLoc: locs[0] }; 1 in locs && (entry.catchLoc = locs[1]), 2 in locs && (entry.finallyLoc = locs[2], entry.afterLoc = locs[3]), this.tryEntries.push(entry); } function resetTryEntry(entry) { var record = entry.completion || {}; record.type = "normal", delete record.arg, entry.completion = record; } function Context(tryLocsList) { this.tryEntries = [{ tryLoc: "root" }], tryLocsList.forEach(pushTryEntry, this), this.reset(!0); } function values(iterable) { if (iterable) { var iteratorMethod = iterable[iteratorSymbol]; if (iteratorMethod) return iteratorMethod.call(iterable); if ("function" == typeof iterable.next) return iterable; if (!isNaN(iterable.length)) { var i = -1, next = function next() { for (; ++i < iterable.length;) if (hasOwn.call(iterable, i)) return next.value = iterable[i], next.done = !1, next; return next.value = undefined, next.done = !0, next; }; return next.next = next; } } return { next: doneResult }; } function doneResult() { return { value: undefined, done: !0 }; } return GeneratorFunction.prototype = GeneratorFunctionPrototype, defineProperty(Gp, "constructor", { value: GeneratorFunctionPrototype, configurable: !0 }), defineProperty(GeneratorFunctionPrototype, "constructor", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, toStringTagSymbol, "GeneratorFunction"), exports.isGeneratorFunction = function (genFun) { var ctor = "function" == typeof genFun && genFun.constructor; return !!ctor && (ctor === GeneratorFunction || "GeneratorFunction" === (ctor.displayName || ctor.name)); }, exports.mark = function (genFun) { return Object.setPrototypeOf ? Object.setPrototypeOf(genFun, GeneratorFunctionPrototype) : (genFun.__proto__ = GeneratorFunctionPrototype, define(genFun, toStringTagSymbol, "GeneratorFunction")), genFun.prototype = Object.create(Gp), genFun; }, exports.awrap = function (arg) { return { __await: arg }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, asyncIteratorSymbol, function () { return this; }), exports.AsyncIterator = AsyncIterator, exports.async = function (innerFn, outerFn, self, tryLocsList, PromiseImpl) { void 0 === PromiseImpl && (PromiseImpl = Promise); var iter = new AsyncIterator(wrap(innerFn, outerFn, self, tryLocsList), PromiseImpl); return exports.isGeneratorFunction(outerFn) ? iter : iter.next().then(function (result) { return result.done ? result.value : iter.next(); }); }, defineIteratorMethods(Gp), define(Gp, toStringTagSymbol, "Generator"), define(Gp, iteratorSymbol, function () { return this; }), define(Gp, "toString", function () { return "[object Generator]"; }), exports.keys = function (val) { var object = Object(val), keys = []; for (var key in object) keys.push(key); return keys.reverse(), function next() { for (; keys.length;) { var key = keys.pop(); if (key in object) return next.value = key, next.done = !1, next; } return next.done = !0, next; }; }, exports.values = values, Context.prototype = { constructor: Context, reset: function reset(skipTempReset) { if (this.prev = 0, this.next = 0, this.sent = this._sent = undefined, this.done = !1, this.delegate = null, this.method = "next", this.arg = undefined, this.tryEntries.forEach(resetTryEntry), !skipTempReset) for (var name in this) "t" === name.charAt(0) && hasOwn.call(this, name) && !isNaN(+name.slice(1)) && (this[name] = undefined); }, stop: function stop() { this.done = !0; var rootRecord = this.tryEntries[0].completion; if ("throw" === rootRecord.type) throw rootRecord.arg; return this.rval; }, dispatchException: function dispatchException(exception) { if (this.done) throw exception; var context = this; function handle(loc, caught) { return record.type = "throw", record.arg = exception, context.next = loc, caught && (context.method = "next", context.arg = undefined), !!caught; } for (var i = this.tryEntries.length - 1; i >= 0; --i) { var entry = this.tryEntries[i], record = entry.completion; if ("root" === entry.tryLoc) return handle("end"); if (entry.tryLoc <= this.prev) { var hasCatch = hasOwn.call(entry, "catchLoc"), hasFinally = hasOwn.call(entry, "finallyLoc"); if (hasCatch && hasFinally) { if (this.prev < entry.catchLoc) return handle(entry.catchLoc, !0); if (this.prev < entry.finallyLoc) return handle(entry.finallyLoc); } else if (hasCatch) { if (this.prev < entry.catchLoc) return handle(entry.catchLoc, !0); } else { if (!hasFinally) throw new Error("try statement without catch or finally"); if (this.prev < entry.finallyLoc) return handle(entry.finallyLoc); } } } }, abrupt: function abrupt(type, arg) { for (var i = this.tryEntries.length - 1; i >= 0; --i) { var entry = this.tryEntries[i]; if (entry.tryLoc <= this.prev && hasOwn.call(entry, "finallyLoc") && this.prev < entry.finallyLoc) { var finallyEntry = entry; break; } } finallyEntry && ("break" === type || "continue" === type) && finallyEntry.tryLoc <= arg && arg <= finallyEntry.finallyLoc && (finallyEntry = null); var record = finallyEntry ? finallyEntry.completion : {}; return record.type = type, record.arg = arg, finallyEntry ? (this.method = "next", this.next = finallyEntry.finallyLoc, ContinueSentinel) : this.complete(record); }, complete: function complete(record, afterLoc) { if ("throw" === record.type) throw record.arg; return "break" === record.type || "continue" === record.type ? this.next = record.arg : "return" === record.type ? (this.rval = this.arg = record.arg, this.method = "return", this.next = "end") : "normal" === record.type && afterLoc && (this.next = afterLoc), ContinueSentinel; }, finish: function finish(finallyLoc) { for (var i = this.tryEntries.length - 1; i >= 0; --i) { var entry = this.tryEntries[i]; if (entry.finallyLoc === finallyLoc) return this.complete(entry.completion, entry.afterLoc), resetTryEntry(entry), ContinueSentinel; } }, "catch": function _catch(tryLoc) { for (var i = this.tryEntries.length - 1; i >= 0; --i) { var entry = this.tryEntries[i]; if (entry.tryLoc === tryLoc) { var record = entry.completion; if ("throw" === record.type) { var thrown = record.arg; resetTryEntry(entry); } return thrown; } } throw new Error("illegal catch attempt"); }, delegateYield: function delegateYield(iterable, resultName, nextLoc) { return this.delegate = { iterator: values(iterable), resultName: resultName, nextLoc: nextLoc }, "next" === this.method && (this.arg = undefined), ContinueSentinel; } }, exports; }
function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }
function _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }
function _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, "prototype", { writable: false }); return Constructor; }
function _toPropertyKey(arg) { var key = _toPrimitive(arg, "string"); return _typeof(key) === "symbol" ? key : String(key); }
function _toPrimitive(input, hint) { if (_typeof(input) !== "object" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || "default"); if (_typeof(res) !== "object") return res; throw new TypeError("@@toPrimitive must return a primitive value."); } return (hint === "string" ? String : Number)(input); }
function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }
function _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err); } _next(undefined); }); }; }






function verifyServerVersionAndInitUrl(_x) {
  return _verifyServerVersionAndInitUrl.apply(this, arguments);
}
/**
 * @namespace TerrainAnalysis
 */
function _verifyServerVersionAndInitUrl() {
  _verifyServerVersionAndInitUrl = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee(serverUrl) {
    var version, url;
    return _regeneratorRuntime().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          _context.next = 2;
          return _RegisterServer_js__WEBPACK_IMPORTED_MODULE_1__["default"].getVersionByServiceAnyURI(serverUrl);
        case 2:
          version = _context.sent;
          if (!(version < _SupportedVersion_js__WEBPACK_IMPORTED_MODULE_2__["default"].r341)) {
            _context.next = 6;
            break;
          }
          _warning_js__WEBPACK_IMPORTED_MODULE_3__["default"].ByVersion("TerrainAnalysis", _SupportedVersion_js__WEBPACK_IMPORTED_MODULE_2__["default"].r341, version);
          return _context.abrupt("return", null);
        case 6:
          url = new URL(serverUrl);
          url.pathname = "";
          url.search = "";
          url.hash = "";
          return _context.abrupt("return", {
            version: version,
            url: url
          });
        case 11:
        case "end":
          return _context.stop();
      }
    }, _callee);
  }));
  return _verifyServerVersionAndInitUrl.apply(this, arguments);
}
var TerrainAnalysis = /*#__PURE__*/function () {
  function TerrainAnalysis() {
    _classCallCheck(this, TerrainAnalysis);
  }
  _createClass(TerrainAnalysis, null, [{
    key: "getElevation",
    value:
    /**
     * 从服务3.4.1版本（包含）起支持.
     * 静态方法,获取指定经纬度位置的高程.
     * @param serverUrl {String} 服务地址
     * @param layerId {String} layerId数据的ID
     * @param points {Array.<Number>} 一个或者多个二维坐标点，经纬度坐标。
     * @return {Promise<ResponseResult>}
     */
    function getElevation(serverUrl, layerId, points) {
      return verifyServerVersionAndInitUrl(serverUrl).then(function (result) {
        if (!result) {
          return Promise.reject("服务版本不支持");
        }
        var url = result.url;
        // const version = result.version;
        url.pathname = "v2/terrainAnalysis/".concat(layerId);
        var body = {
          method: "getelevation",
          points: [].concat(points).flat(2).join(",")
        };
        return _core_Resource_js__WEBPACK_IMPORTED_MODULE_4__["default"].postJSON({
          url: url.toString(),
          body: JSON.stringify(body),
          headers: {
            "Content-Type": "application/json;charset=UTF-8"
          }
        });
        // switch (version) {
        //     case SupportedVersion.r341:
        //     case SupportedVersion.r350: {
        //         url.pathname = `v2/terrainAnalysis/${layerId}`;
        //         const body = {
        //             method: "getelevation",
        //             points: [].concat(points).flat(2).join(","),
        //         };
        //         return Resource.postJSON({
        //             url: url.toString(),
        //             body: JSON.stringify(body),
        //             headers: { "Content-Type": "application/json;charset=UTF-8" },
        //         });
        //     }
        //     default: {
        //         return Promise.reject("无法识别的服务版本");
        //     }
        // }
      }).then(function (response) {
        return new _results_ResponseResult_js__WEBPACK_IMPORTED_MODULE_0__["default"]({
          response: response
        });
      });
    }

    /**
     * 从服务3.4.1版本（包含）起支持.
     * 静态方法,获取指定经纬度位置的高程,该方法的耗时不确定
     * @param serverUrl {String} 服务地址
     * @param layerId {String} layerId数据的ID
     * @param polygon {Array.<Number>} 二维多边形，经纬度坐标。
     * @return {Promise<ResponseResult>}
     */
  }, {
    key: "getMinMaxeLevation",
    value: function getMinMaxeLevation(serverUrl, layerId, polygon) {
      return verifyServerVersionAndInitUrl(serverUrl).then(function (result) {
        if (!result) {
          return Promise.reject("服务版本不支持");
        }
        var url = new URL("./v2/terrainAnalysis/".concat(layerId), serverUrl);
        var body = {
          method: "getminmaxelevation",
          polygon: [].concat(polygon).flat(2).join(",")
        };
        return _core_Resource_js__WEBPACK_IMPORTED_MODULE_4__["default"].postJSON({
          url: url.toString(),
          body: JSON.stringify(body),
          headers: {
            "Content-Type": "application/json;charset=UTF-8"
          }
        });
        // const version = result.version;
        // switch (version) {
        //     case SupportedVersion.r341:
        //     case SupportedVersion.r350: {
        //         const url = new URL(`./v2/terrainAnalysis/${layerId}`, serverUrl);
        //         const body = {
        //             method: "getminmaxelevation",
        //             polygon: [].concat(polygon).flat(2).join(","),
        //         };
        //         return Resource.postJSON({
        //             url: url.toString(),
        //             body: JSON.stringify(body),
        //             headers: { "Content-Type": "application/json;charset=UTF-8" },
        //         });
        //     }
        //     default: {
        //         return Promise.reject("无法识别的服务版本");
        //     }
        // }
      }).then(function (response) {
        return new _results_ResponseResult_js__WEBPACK_IMPORTED_MODULE_0__["default"]({
          response: response
        });
      });
    }
  }]);
  return TerrainAnalysis;
}();
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TerrainAnalysis);

/***/ }),

/***/ "./src/warning.js":
/*!************************!*\
  !*** ./src/warning.js ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _SupportedVersion_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./SupportedVersion.js */ "./src/SupportedVersion.js");

function warning(_ref) {
  var message = _ref.message;
  console.warn("".concat(message));
}
warning.ByVersion = function (operation, compatibilityVersion, CurrentVersion) {
  console.error("\u670D\u52A1\u63A5\u53E3\u3010".concat(_SupportedVersion_js__WEBPACK_IMPORTED_MODULE_0__["default"].getStringByVersionNum(CurrentVersion), "\u3011\u7248\u4E0D\u652F\u6301\u3010").concat(operation, "\u3011,\u8BF7\u5347\u7EA7\u670D\u52A1\u81F3\u3010").concat(_SupportedVersion_js__WEBPACK_IMPORTED_MODULE_0__["default"].getStringByVersionNum(compatibilityVersion), "\u3011\u53CA\u4EE5\u4E0A\u7248\u672C!"));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (warning);

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	(() => {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = (exports) => {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	})();
/******/ 	
/************************************************************************/
var __webpack_exports__ = {};
// This entry need to be wrapped in an IIFE because it need to be isolated against other modules in the chunk.
(() => {
/*!**********************!*\
  !*** ./src/index.js ***!
  \**********************/
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "BIMQuery": () => (/* reexport safe */ _service_BIMQuery_js__WEBPACK_IMPORTED_MODULE_27__["default"]),
/* harmony export */   "DeveloperError": () => (/* reexport safe */ _core_DeveloperError_js__WEBPACK_IMPORTED_MODULE_6__["default"]),
/* harmony export */   "GeologicalAnalysis": () => (/* reexport safe */ _service_GeologicalAnalysis_js__WEBPACK_IMPORTED_MODULE_28__["default"]),
/* harmony export */   "KeysToLowerCase": () => (/* reexport safe */ _core_KeysToLowerCase_js__WEBPACK_IMPORTED_MODULE_8__["default"]),
/* harmony export */   "PipeLineDir": () => (/* reexport safe */ _service_PipeLineDir_js__WEBPACK_IMPORTED_MODULE_29__["default"]),
/* harmony export */   "PipingAnalyse": () => (/* reexport safe */ _service_PipingAnalyse_js__WEBPACK_IMPORTED_MODULE_30__["default"]),
/* harmony export */   "PipingAnalyseResult": () => (/* reexport safe */ _results_PipingAnalyseResult_js__WEBPACK_IMPORTED_MODULE_14__["default"]),
/* harmony export */   "PipingBurstResult": () => (/* reexport safe */ _results_PipingBurstResult_js__WEBPACK_IMPORTED_MODULE_15__["default"]),
/* harmony export */   "PipingClearDistanceResult": () => (/* reexport safe */ _results_PipingClearDistanceResult_js__WEBPACK_IMPORTED_MODULE_16__["default"]),
/* harmony export */   "PipingCollisionResult": () => (/* reexport safe */ _results_PipingCollisionResult_js__WEBPACK_IMPORTED_MODULE_17__["default"]),
/* harmony export */   "PipingConnectionResult": () => (/* reexport safe */ _results_PipingConnectionResult_js__WEBPACK_IMPORTED_MODULE_18__["default"]),
/* harmony export */   "PipingFlowDirResult": () => (/* reexport safe */ _results_PipingFlowDirResult_js__WEBPACK_IMPORTED_MODULE_19__["default"]),
/* harmony export */   "PipingTracingResult": () => (/* reexport safe */ _results_PipingTracingResult_js__WEBPACK_IMPORTED_MODULE_20__["default"]),
/* harmony export */   "PipingTransectResult": () => (/* reexport safe */ _results_PipingTransectResult_js__WEBPACK_IMPORTED_MODULE_21__["default"]),
/* harmony export */   "PipingVerticalSectionResult": () => (/* reexport safe */ _results_PipingVerticalSectionResult_js__WEBPACK_IMPORTED_MODULE_22__["default"]),
/* harmony export */   "Query": () => (/* reexport safe */ _service_Query_js__WEBPACK_IMPORTED_MODULE_31__["default"]),
/* harmony export */   "QueryCondition": () => (/* reexport safe */ _service_QueryCondition_js__WEBPACK_IMPORTED_MODULE_32__["default"]),
/* harmony export */   "QueryFieldValueResult": () => (/* reexport safe */ _results_QueryFieldValueResult_js__WEBPACK_IMPORTED_MODULE_23__["default"]),
/* harmony export */   "QueryMetaResult": () => (/* reexport safe */ _results_QueryMetaResult_js__WEBPACK_IMPORTED_MODULE_24__["default"]),
/* harmony export */   "QueryPropertyResult": () => (/* reexport safe */ _results_QueryPropertyResult_js__WEBPACK_IMPORTED_MODULE_25__["default"]),
/* harmony export */   "RegisterServer": () => (/* reexport safe */ _RegisterServer_js__WEBPACK_IMPORTED_MODULE_0__["default"]),
/* harmony export */   "Resource": () => (/* reexport safe */ _core_Resource_js__WEBPACK_IMPORTED_MODULE_10__["default"]),
/* harmony export */   "ResponseResult": () => (/* reexport safe */ _results_ResponseResult_js__WEBPACK_IMPORTED_MODULE_26__["default"]),
/* harmony export */   "ResponseType": () => (/* reexport safe */ _core_ResponseType_js__WEBPACK_IMPORTED_MODULE_11__["default"]),
/* harmony export */   "RuntimeError": () => (/* reexport safe */ _core_RuntimeError_js__WEBPACK_IMPORTED_MODULE_12__["default"]),
/* harmony export */   "SqlOperator": () => (/* reexport safe */ _service_SqlOperator_js__WEBPACK_IMPORTED_MODULE_33__["default"]),
/* harmony export */   "Statistics": () => (/* reexport safe */ _service_Statistics_js__WEBPACK_IMPORTED_MODULE_34__["default"]),
/* harmony export */   "StatisticsCondition": () => (/* reexport safe */ _service_StatisticsCondition_js__WEBPACK_IMPORTED_MODULE_35__["default"]),
/* harmony export */   "StatisticsType": () => (/* reexport safe */ _core_StatisticsType_js__WEBPACK_IMPORTED_MODULE_13__["default"]),
/* harmony export */   "SupportedVersion": () => (/* reexport safe */ _SupportedVersion_js__WEBPACK_IMPORTED_MODULE_1__["default"]),
/* harmony export */   "TerrainAnalysis": () => (/* reexport safe */ _service_TerrainAnalysis_js__WEBPACK_IMPORTED_MODULE_36__["default"]),
/* harmony export */   "VERSION": () => (/* binding */ VERSION),
/* harmony export */   "convertSearchParamToJSONString": () => (/* reexport safe */ _core_convertSearchParamToJSONString_js__WEBPACK_IMPORTED_MODULE_3__["default"]),
/* harmony export */   "defaultValue": () => (/* reexport safe */ _core_defaultValue_js__WEBPACK_IMPORTED_MODULE_4__["default"]),
/* harmony export */   "defined": () => (/* reexport safe */ _core_defined_js__WEBPACK_IMPORTED_MODULE_5__["default"]),
/* harmony export */   "getFlatUniqueArray": () => (/* reexport safe */ _core_getFlatUniqueArray_js__WEBPACK_IMPORTED_MODULE_7__["default"]),
/* harmony export */   "multiEqual": () => (/* reexport safe */ _core_multiEqual_js__WEBPACK_IMPORTED_MODULE_9__["default"]),
/* harmony export */   "warning": () => (/* reexport safe */ _warning_js__WEBPACK_IMPORTED_MODULE_2__["default"])
/* harmony export */ });
/* harmony import */ var _RegisterServer_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./RegisterServer.js */ "./src/RegisterServer.js");
/* harmony import */ var _SupportedVersion_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./SupportedVersion.js */ "./src/SupportedVersion.js");
/* harmony import */ var _warning_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./warning.js */ "./src/warning.js");
/* harmony import */ var _core_convertSearchParamToJSONString_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./core/convertSearchParamToJSONString.js */ "./src/core/convertSearchParamToJSONString.js");
/* harmony import */ var _core_defaultValue_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./core/defaultValue.js */ "./src/core/defaultValue.js");
/* harmony import */ var _core_defined_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./core/defined.js */ "./src/core/defined.js");
/* harmony import */ var _core_DeveloperError_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./core/DeveloperError.js */ "./src/core/DeveloperError.js");
/* harmony import */ var _core_getFlatUniqueArray_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./core/getFlatUniqueArray.js */ "./src/core/getFlatUniqueArray.js");
/* harmony import */ var _core_KeysToLowerCase_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./core/KeysToLowerCase.js */ "./src/core/KeysToLowerCase.js");
/* harmony import */ var _core_multiEqual_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./core/multiEqual.js */ "./src/core/multiEqual.js");
/* harmony import */ var _core_Resource_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./core/Resource.js */ "./src/core/Resource.js");
/* harmony import */ var _core_ResponseType_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./core/ResponseType.js */ "./src/core/ResponseType.js");
/* harmony import */ var _core_RuntimeError_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./core/RuntimeError.js */ "./src/core/RuntimeError.js");
/* harmony import */ var _core_StatisticsType_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./core/StatisticsType.js */ "./src/core/StatisticsType.js");
/* harmony import */ var _results_PipingAnalyseResult_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./results/PipingAnalyseResult.js */ "./src/results/PipingAnalyseResult.js");
/* harmony import */ var _results_PipingBurstResult_js__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./results/PipingBurstResult.js */ "./src/results/PipingBurstResult.js");
/* harmony import */ var _results_PipingClearDistanceResult_js__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./results/PipingClearDistanceResult.js */ "./src/results/PipingClearDistanceResult.js");
/* harmony import */ var _results_PipingCollisionResult_js__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./results/PipingCollisionResult.js */ "./src/results/PipingCollisionResult.js");
/* harmony import */ var _results_PipingConnectionResult_js__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./results/PipingConnectionResult.js */ "./src/results/PipingConnectionResult.js");
/* harmony import */ var _results_PipingFlowDirResult_js__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./results/PipingFlowDirResult.js */ "./src/results/PipingFlowDirResult.js");
/* harmony import */ var _results_PipingTracingResult_js__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./results/PipingTracingResult.js */ "./src/results/PipingTracingResult.js");
/* harmony import */ var _results_PipingTransectResult_js__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./results/PipingTransectResult.js */ "./src/results/PipingTransectResult.js");
/* harmony import */ var _results_PipingVerticalSectionResult_js__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ./results/PipingVerticalSectionResult.js */ "./src/results/PipingVerticalSectionResult.js");
/* harmony import */ var _results_QueryFieldValueResult_js__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! ./results/QueryFieldValueResult.js */ "./src/results/QueryFieldValueResult.js");
/* harmony import */ var _results_QueryMetaResult_js__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! ./results/QueryMetaResult.js */ "./src/results/QueryMetaResult.js");
/* harmony import */ var _results_QueryPropertyResult_js__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! ./results/QueryPropertyResult.js */ "./src/results/QueryPropertyResult.js");
/* harmony import */ var _results_ResponseResult_js__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! ./results/ResponseResult.js */ "./src/results/ResponseResult.js");
/* harmony import */ var _service_BIMQuery_js__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! ./service/BIMQuery.js */ "./src/service/BIMQuery.js");
/* harmony import */ var _service_GeologicalAnalysis_js__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! ./service/GeologicalAnalysis.js */ "./src/service/GeologicalAnalysis.js");
/* harmony import */ var _service_PipeLineDir_js__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! ./service/PipeLineDir.js */ "./src/service/PipeLineDir.js");
/* harmony import */ var _service_PipingAnalyse_js__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! ./service/PipingAnalyse.js */ "./src/service/PipingAnalyse.js");
/* harmony import */ var _service_Query_js__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! ./service/Query.js */ "./src/service/Query.js");
/* harmony import */ var _service_QueryCondition_js__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! ./service/QueryCondition.js */ "./src/service/QueryCondition.js");
/* harmony import */ var _service_SqlOperator_js__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! ./service/SqlOperator.js */ "./src/service/SqlOperator.js");
/* harmony import */ var _service_Statistics_js__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! ./service/Statistics.js */ "./src/service/Statistics.js");
/* harmony import */ var _service_StatisticsCondition_js__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! ./service/StatisticsCondition.js */ "./src/service/StatisticsCondition.js");
/* harmony import */ var _service_TerrainAnalysis_js__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! ./service/TerrainAnalysis.js */ "./src/service/TerrainAnalysis.js");
var VERSION = '4.0.0.B#28d1adcf@202405281209';





































})();

/******/ 	return __webpack_exports__;
/******/ })()
;
});
//# sourceMappingURL=CommonService.js.map