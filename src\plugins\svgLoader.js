import SvgIcon from '@/components/SvgIcon.vue';
import icons from '@/assets/icons';

// 创建SVG sprite
function createSvgSprite() {
  // 如果已经存在sprite元素，则直接返回
  if (document.getElementById('svg-sprite-container')) {
    return;
  }

  // 创建SVG sprite容器
  const svgSprite = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
  svgSprite.setAttribute('xmlns', 'http://www.w3.org/2000/svg');
  svgSprite.setAttribute('id', 'svg-sprite-container');
  svgSprite.setAttribute('style', 'position: absolute; width: 0; height: 0; overflow: hidden;');
  svgSprite.setAttribute('aria-hidden', 'true');
  
  document.body.appendChild(svgSprite);

  // 添加SVG图标到sprite
  Object.keys(icons).forEach(iconName => {
    try {
      const svgContent = icons[iconName];
      
      // 从SVG内容中提取viewBox属性，确保获取正确的数字格式
      const viewBoxMatch = svgContent.match(/viewBox=["']([0-9\s]+)["']/);
      const viewBox = viewBoxMatch ? viewBoxMatch[1] : '0 0 1024 1024';
      
      // 验证viewBox格式 - 应该是包含数字和空格的字符串
      if (!/^[\d\s]+$/.test(viewBox)) {
        console.warn(`图标 ${iconName} 的viewBox格式不正确: "${viewBox}", 使用默认值替代`);
        viewBox = '0 0 1024 1024';
      }
      
      // 从SVG内容中提取path元素
      const pathMatch = svgContent.match(/<path[^>]*>/g) || [];
      
      // 创建symbol元素
      const symbol = document.createElementNS('http://www.w3.org/2000/svg', 'symbol');
      symbol.setAttribute('id', `icon-${iconName}`);
      symbol.setAttribute('viewBox', viewBox);
      
      // 将path添加到symbol中
      pathMatch.forEach(path => {
        const pathElement = document.createRange().createContextualFragment(path).firstChild;
        if (pathElement) {
          symbol.appendChild(pathElement.cloneNode(true));
        }
      });
      
      svgSprite.appendChild(symbol);
    } catch (err) {
      console.error(`处理图标 ${iconName} 时出错:`, err);
    }
  });
}

export default {
  install(app) {
    // 注册SvgIcon组件为全局组件
    app.component('SvgIcon', SvgIcon);
    
    // 创建SVG sprite
    if (typeof window !== 'undefined') {
      // 在窗口加载完成后创建SVG sprite
      window.addEventListener('load', createSvgSprite);
      // 也可以立即创建
      createSvgSprite();
    }
  }
}; 