<template>
  <teleport to="body">
    <transition name="fade">
      <div v-if="modelValue" class="modal-overlay" @click.self="closeModal">
        <div class="modal-container">
          <div class="modal-header">
            <div class="modal-title">隐患详情</div>
            <div class="close-icon" @click="closeModal">×</div>
          </div>
          <div class="tab-header">
            <div
              class="tab-item"
              :class="{ active: activeTab === 'info' }"
              @click="activeTab = 'info'"
            >
              隐患信息
            </div>
            <div
              class="tab-item"
              :class="{ active: activeTab === 'flow' }"
              @click="activeTab = 'flow'"
            >
              隐患流程
            </div>
          </div>

          <!-- 隐患信息 Tab -->
          <div v-if="activeTab === 'info'" class="modal-content">
            <div class="info-section">
              <div class="info-row full-width">
                <span class="info-label">隐患来源:</span>
                <span class="info-value">{{ hazardData.source || '系统上报' }}</span>
              </div>
              <div class="info-row full-width">
                <span class="info-label">隐患描述:</span>
                <span class="info-value">{{ hazardData.description || '描述描述描述描述描述描述描述描述' }}</span>
              </div>
              <div class="info-row full-width">
                <span class="info-label">隐患类型:</span>
                <span class="info-value">{{ hazardData.type || '管道渗漏风险' }}</span>
              </div>
              <div class="info-row full-width">
                <span class="info-label">隐患对象:</span>
                <span class="info-value">{{ hazardData.target || '管网（GX1129）' }}</span>
              </div>
              <div class="info-row full-width">
                <span class="info-label">隐患等级:</span>
                <span class="info-value" :style="{ color: getLevelColor(hazardData.level) }">
                  {{ hazardData.level || '重大隐患' }}
                </span>
              </div>
              <div class="info-row full-width">
                <span class="info-label">隐患位置:</span>
                <span class="info-value">{{ hazardData.location || '位置描述位置描述' }}</span>
              </div>
              <div class="info-row full-width">
                <span class="info-label">整改期限:</span>
                <span class="info-value">{{ hazardData.deadline || '2023年1月5日' }}</span>
              </div>
              <div class="info-row full-width">
                <span class="info-label">责任人:</span>
                <span class="info-value">{{ hazardData.responsible || '张三' }}</span>
              </div>
              <div class="info-row full-width">
                <span class="info-label">隐患图片:</span>
                <div class="image-gallery">
                  <div class="image-item" v-for="(img, index) in hazardImages" :key="index">
                    <img :src="img" alt="隐患图片" />
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 隐患流程 Tab -->
          <div v-if="activeTab === 'flow'" class="modal-content">
            <div class="flow-timeline">
              <div 
                v-for="(flow, index) in hazardFlows" 
                :key="index"
                class="flow-item"
              >
                <div class="flow-time">{{ flow.time }}</div>
                <div class="flow-dot"></div>
                <div class="flow-content">
                  <span class="flow-tag">{{ flow.tag }}</span>
                  <span class="flow-text">{{ flow.text }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </transition>
  </teleport>
</template>

<script setup>
import { defineProps, defineEmits, ref } from 'vue'

const props = defineProps({
  modelValue: Boolean,
  hazardData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue'])

// 选中的标签页
const activeTab = ref('info')

// 关闭弹窗
const closeModal = () => {
  emit('update:modelValue', false)
}

// 获取隐患等级对应的颜色
const getLevelColor = (level) => {
  const colorMap = {
    '重大隐患': '#FF2330',
    '较大隐患': '#FF9000',
    '一般隐患': '#FFD11B'
  }
  return colorMap[level] || '#FFFFFF'
}

// 模拟隐患图片数据
const hazardImages = ref([
  '/src/assets/images/screen/drainage/risk/image_sample.png',
  '/src/assets/images/screen/drainage/risk/image_sample.png',
  '/src/assets/images/screen/drainage/risk/image_sample.png',
  '/src/assets/images/screen/drainage/risk/image_sample.png'
])

// 模拟隐患流程数据
const hazardFlows = ref([
  {
    time: '2022-04-10 15:35',
    tag: '管理员',
    text: '上报了隐患'
  },
  {
    time: '2022-04-11 09:20',
    tag: '系统',
    text: '隐患已分派至张三'
  },
  {
    time: '2022-04-12 14:30',
    tag: '张三',
    text: '开始处理隐患'
  },
  {
    time: '2022-04-15 16:45',
    tag: '李四',
    text: '协助处理隐患'
  },
  {
    time: '2022-04-20 10:15',
    tag: '张三',
    text: '隐患已修复，等待验收'
  }
])
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.modal-container {
  width: 550px;
  background: linear-gradient(180deg, rgba(0, 22, 72, 0.9) 0%, rgba(0, 35, 91, 0.9) 100%);
  border: 1px solid rgba(59, 141, 242, 0.5);
  box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.05);
  border-radius: 4px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid rgba(59, 141, 242, 0.3);
}

.modal-title {
  font-family: PingFangSC, 'PingFang SC';
  font-weight: 500;
  font-size: 16px;
  color: #FFFFFF;
}

.close-icon {
  font-size: 20px;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
}

.close-icon:hover {
  color: #FFFFFF;
}

.tab-header {
  display: flex;
  border-bottom: 1px solid rgba(59, 141, 242, 0.3);
  background-color: rgba(0, 35, 91, 0.5);
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 12px 0;
  font-family: PingFangSC, 'PingFang SC';
  font-weight: 500;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  position: relative;
}

.tab-item.active {
  color: #FFFFFF;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 2px;
  background: #3B8DF2;
}

.modal-content {
  padding: 20px;
  max-height: 500px;
  overflow-y: auto;
}

.info-section {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.info-row {
  display: flex;
  align-items: flex-start;
  width: calc(50% - 8px);
}

.info-row.full-width {
  width: 100%;
}

.info-label {
  font-family: PingFangSC, 'PingFang SC';
  font-weight: 500;
  font-size: 14px;
  color: #FFFFFF;
  width: 90px;
  flex-shrink: 0;
}

.info-value {
  font-family: PingFangSC, 'PingFang SC';
  font-weight: 400;
  font-size: 14px;
  color: #FFFFFF;
  flex: 1;
}

.image-gallery {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 5px;
}

.image-item {
  width: 80px;
  height: 80px;
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid rgba(59, 141, 242, 0.5);
}

.image-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 流程时间线样式 */
.flow-timeline {
  display: flex;
  flex-direction: column;
  padding: 0 10px;
}

.flow-item {
  display: flex;
  position: relative;
  padding-bottom: 25px;
}

.flow-item:last-child {
  padding-bottom: 0;
}

.flow-item:not(:last-child)::after {
  content: '';
  position: absolute;
  left: 100px;
  top: 25px;
  bottom: 0;
  width: 1px;
  background: rgba(59, 141, 242, 0.5);
}

.flow-time {
  width: 100px;
  font-family: PingFangSC, 'PingFang SC';
  font-weight: 400;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  text-align: right;
  padding-right: 15px;
  padding-top: 2px;
}

.flow-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: #3B8DF2;
  margin-top: 5px;
  position: relative;
  z-index: 1;
}

.flow-content {
  margin-left: 15px;
  display: flex;
  align-items: center;
  padding: 5px 0;
}

.flow-tag {
  display: inline-block;
  padding: 2px 8px;
  background: rgba(59, 141, 242, 0.3);
  border-radius: 4px;
  font-family: PingFangSC, 'PingFang SC';
  font-weight: 500;
  font-size: 12px;
  color: #3B8DF2;
  margin-right: 10px;
}

.flow-text {
  font-family: PingFangSC, 'PingFang SC';
  font-weight: 400;
  font-size: 14px;
  color: #FFFFFF;
}

/* 淡入淡出动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style> 