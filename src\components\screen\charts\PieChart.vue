<template>
  <div class="pie-chart-container">
    <div ref="chartRef" class="chart-content"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  // 饼图数据，格式 [{name: '类别1', value: 234}, {name: '类别2', value: 345}]
  data: {
    type: Array,
    required: true
  },
  // 图表类型：pie-普通饼图，ring-环形图
  type: {
    type: String,
    default: 'pie',
    validator: (value) => ['pie', 'ring'].includes(value)
  },
  // 环形图内部百分比文字
  centerText: {
    type: String,
    default: ''
  },
  // 颜色列表
  colorList: {
    type: Array,
    default: () => [
      '#00F2F1', '#0082FF', '#FFD24D', 
      '#FF8F35', '#35FF6B', '#00B36B',
      '#FF59C8', '#FF0066', '#B36CFF'
    ]
  },
  // 是否显示图例
  showLegend: {
    type: Boolean,
    default: true
  },
  // 图例位置：top, bottom, left, right
  legendPosition: {
    type: String,
    default: 'right',
    validator: (value) => ['top', 'bottom', 'left', 'right'].includes(value)
  },
  // 是否显示标签
  showLabel: {
    type: Boolean,
    default: true
  },
  // 单位
  unit: {
    type: String,
    default: ''
  },
  // 是否显示提示框
  showTooltip: {
    type: Boolean,
    default: true
  }
})

const chartRef = ref(null)
let chartInstance = null

onMounted(async () => {
  await nextTick()
  if (chartRef.value) {
    initChart()
  }
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
  window.removeEventListener('resize', handleResize)
})

watch(() => props.data, () => {
  if (chartInstance) {
    updateChart()
  }
}, { deep: true })

watch(() => [props.type, props.showLegend, props.legendPosition, props.showLabel], () => {
  if (chartInstance) {
    updateChart()
  }
})

const initChart = () => {
  try {
    if (chartInstance) {
      chartInstance.dispose()
    }
    
    chartInstance = echarts.init(chartRef.value)
    
    window.addEventListener('resize', handleResize)
    
    updateChart()
    chartInstance.resize()
  } catch (error) {
    console.error('初始化饼图失败:', error)
  }
}

const handleResize = () => {
  if (chartInstance) {
    try {
      chartInstance.resize()
    } catch (error) {
      console.error('调整饼图大小失败:', error)
    }
  }
}

const updateChart = () => {
  if (!chartInstance) return
  
  try {
    // 创建本地副本
    const localData = JSON.parse(JSON.stringify(props.data))
    
    // 计算总数用于百分比显示
    const total = localData.reduce((sum, item) => sum + item.value, 0)
    
    // 配置选项
    const option = {
      backgroundColor: 'transparent',
      title: props.title ? {
        text: props.title,
        left: 'center',
        top: 10,
        textStyle: {
          color: '#FFFFFF',
          fontSize: 14,
          fontWeight: 'normal',
          fontFamily: 'PingFangSC, PingFang SC'
        }
      } : null,
      color: props.colorList,
      tooltip: props.showTooltip ? {
        trigger: 'item',
        formatter: (params) => {
          const percent = ((params.value / total) * 100).toFixed(2)
          return `${params.name}: ${params.value}${props.unit} (${percent}%)`
        }
      } : { show: false },
      legend: props.showLegend ? {
        type: 'scroll',
        orient: ['left', 'right'].includes(props.legendPosition) ? 'vertical' : 'horizontal',
        top: props.legendPosition === 'top' ? '5%' : (props.legendPosition === 'bottom' ? 'bottom' : (props.title ? '15%' : '5%')),
        left: props.legendPosition === 'left' ? '5%' : (props.legendPosition === 'right' ? 'right' : 'center'),
        icon: 'circle',
        itemWidth: 10,
        itemHeight: 10,
        pageIconSize: 10,
        pageTextStyle: {
          color: 'rgba(255,255,255,0.7)'
        },
        textStyle: {
          color: 'rgba(255,255,255,0.7)',
          fontSize: 10
        },
        formatter: (name) => {
          const item = localData.find(d => d.name === name)
          if (!item) return name
          
          const percent = ((item.value / total) * 100).toFixed(2)
          return `${name} (${percent}%)`
        }
      } : { show: false },
      series: [{
        name: props.title || '饼图数据',
        type: 'pie',
        radius: props.type === 'ring' ? ['40%', '70%'] : '70%',
        center: ['50%', '50%'],
        data: localData,
        roseType: false,
        itemStyle: {
          borderRadius: 5,
          borderColor: 'rgba(0, 0, 0, 0.1)',
          borderWidth: 2
        },
        label: props.showLabel ? {
          show: true,
          position: props.type === 'ring' ? 'outside' : 'inside',
          formatter: (params) => {
            const percent = ((params.value / total) * 100).toFixed(1)
            return props.type === 'ring' ? 
              `${params.name}: ${percent}%` : 
              `${percent}%`
          },
          color: 'rgba(255,255,255,0.8)',
          fontSize: 10,
          lineHeight: 10,
          distanceToLabelLine: 5
        } : { show: false },
        labelLine: props.showLabel && props.type === 'ring' ? {
          show: true,
          length: 10,
          length2: 10,
          lineStyle: {
            color: 'rgba(255,255,255,0.3)'
          }
        } : { show: false },
        emphasis: {
          scale: true,
          scaleSize: 10,
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }]
    }
    
    // 环形图中心文字
    if (props.type === 'ring' && props.centerText) {
      option.graphic = {
        type: 'text',
        left: 'center',
        top: 'center',
        style: {
          text: props.centerText,
          fontSize: 14,
          fontWeight: 'bold',
          textAlign: 'center',
          fill: '#ffffff',
          textVerticalAlign: 'middle'
        }
      }
    }
    
    // 根据图例位置调整图表位置
    if (props.showLegend) {
      const hasTitle = !!props.title
      
      if (props.legendPosition === 'left') {
        option.grid = {
          left: '30%',
          top: hasTitle ? '15%' : '5%',
          right: '5%',
          bottom: '5%'
        }
        option.series[0].center = ['60%', '50%']
      } else if (props.legendPosition === 'right') {
        option.grid = {
          left: '5%',
          top: hasTitle ? '15%' : '5%',
          right: '30%',
          bottom: '5%'
        }
        option.series[0].center = ['30%', '50%']
      } else if (props.legendPosition === 'top') {
        option.grid = {
          left: '5%',
          top: hasTitle ? '25%' : '20%',
          right: '5%',
          bottom: '5%'
        }
        option.series[0].center = ['50%', '55%']
      } else if (props.legendPosition === 'bottom') {
        option.grid = {
          left: '5%',
          top: hasTitle ? '15%' : '5%',
          right: '5%',
          bottom: '20%'
        }
        option.series[0].center = ['50%', '40%']
      }
    }
    
    chartInstance.setOption(option, true)
  } catch (error) {
    console.error('更新饼图失败:', error)
  }
}
</script>

<style scoped>
.pie-chart-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 160px;
}

.chart-content {
  width: 100%;
  height: 100%;
  min-height: 160px;
}
</style>