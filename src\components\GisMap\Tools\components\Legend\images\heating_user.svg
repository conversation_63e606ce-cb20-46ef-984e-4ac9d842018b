<?xml version="1.0" encoding="UTF-8"?>
<svg width="18px" height="18px" viewBox="0 0 18 18" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>供热用户</title>
    <defs>
        <circle id="path-1" cx="7" cy="7" r="7"></circle>
        <filter x="-21.4%" y="-21.4%" width="142.9%" height="142.9%" filterUnits="objectBoundingBox" id="filter-2">
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="1" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.162200418   0 0 0 0 0.568546468   0 0 0 0 0.902202219  0 0 0 0.5 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="27.9288816%" y1="8.11919591%" x2="71.2992224%" y2="91.9872084%" id="linearGradient-3">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#A5E2FF" offset="16.835118%"></stop>
            <stop stop-color="#83BEFF" offset="71.3150675%"></stop>
            <stop stop-color="#C6E3FF" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="图层&amp;图例" transform="translate(-1385.000000, -291.000000)">
            <g id="编组-13备份" transform="translate(1363.000000, 30.000000)">
                <g id="编组-45备份" transform="translate(0.000000, 65.000000)">
                    <g id="编组-2" transform="translate(24.000000, 71.000000)">
                        <g id="供热用户" transform="translate(0.000000, 127.000000)">
                            <g id="正常备份-2">
                                <g id="椭圆形">
                                    <use fill="black" fill-opacity="1" filter="url(#filter-2)" xlink:href="#path-1"></use>
                                    <use fill="#0099FF" fill-rule="evenodd" xlink:href="#path-1"></use>
                                </g>
                                <circle id="椭圆形" stroke="url(#linearGradient-3)" stroke-width="0.823529412" cx="7" cy="7" r="6.58823529"></circle>
                            </g>
                            <path d="M10.9807118,5.58797504 C10.9807118,5.43233125 10.9019518,5.28621668 10.7717566,5.19727737 L9.85556831,4.57470221 L9.85556831,3.78695406 L9.28656716,3.78695406 L9.28656716,4.18876914 L7.59402985,3.04049915 C7.51687715,2.98650028 7.41400689,2.98650028 7.33685419,3.04049915 L4.2250287,5.19727737 C4.09644087,5.28621668 4.02089552,5.43074305 4.01928817,5.58479864 L4,9.88882587 C4,9.94917754 4.04982778,10 4.110907,10 L10.889093,10 C10.9501722,10 11,9.95076574 11,9.88882587 L10.9807118,5.58797504 Z M5.22479908,5.1401021 C5.16693456,5.1401021 5.11067738,5.11310267 5.07692308,5.06386841 C5.01905855,4.98287011 5.03995408,4.87169597 5.12192882,4.81610891 L7.0619977,3.48360749 C7.14397245,3.42802042 7.2564868,3.44707884 7.31274397,3.52807714 C7.3706085,3.60907544 7.34971297,3.72024957 7.26773823,3.77583664 L5.32766935,5.10833806 C5.29552239,5.12898469 5.26016073,5.1401021 5.22479908,5.1401021 L5.22479908,5.1401021 Z M8.15820896,9.10584231 C8.57451206,8.31332955 8.25786452,7.22223483 7.54259472,6.72671583 C7.58599311,6.93953488 7.52812859,7.50493477 7.2130884,7.8591038 C7.28541906,7.49064095 7.0989667,7.26352808 7.0989667,7.26352808 C7.0989667,7.26352808 7.04110218,7.58910947 6.81285878,7.92898469 C6.59747417,8.22597845 6.45442021,8.55155984 6.726062,9.11854793 C5.85166475,8.62302893 5.62181401,8.17039138 5.75200918,7.5319342 C5.85166475,7.06500284 6.21010333,6.69653999 6.23903559,6.22960862 C6.38208955,6.48530913 6.39655568,6.65524674 6.41102181,6.90935905 C6.84179104,6.38525241 7.28541906,5.69120817 7.18576349,5.05433919 C7.18576349,5.05433919 8.00229621,5.29574589 8.33180253,6.5408962 C8.53272101,6.37095859 8.51825488,6.01678956 8.41859931,5.8039705 C8.70149254,6.00408395 10.3650976,7.8591038 8.15820896,9.10584231 L8.15820896,9.10584231 Z" id="形状" fill="#FFFFFF" fill-rule="nonzero"></path>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>