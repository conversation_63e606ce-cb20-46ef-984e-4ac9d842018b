<template>
  <el-dialog
    :title="dialogTitle"
    v-model="visible"
    width="1000px"
    :close-on-click-modal="false"
    @close="handleClose"
    class="bridge-device-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      :disabled="mode === 'view'"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="设备名称" prop="deviceName">
            <el-input v-model="formData.deviceName" placeholder="请输入设备名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="设备编码" prop="indexCode">
            <el-input v-model="formData.indexCode" placeholder="请输入设备编码" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="设备类型" prop="deviceType">
            <el-select v-model="formData.deviceType" placeholder="请选择设备类型" class="w-full">
              <el-option
                v-for="item in deviceTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="监测指标" prop="monitorIndex">
            <el-select v-model="formData.monitorIndex" placeholder="请选择监测指标" class="w-full">
              <el-option
                v-for="item in monitorIndexOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="采集频率" prop="collectFrequency">
            <div class="flex items-center">
              <el-input-number v-model="formData.collectFrequency" :min="0" class="w-full-unit" />
              <span class="unit-label">分钟/次</span>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="上传频率" prop="uploadFrequency">
            <div class="flex items-center">
              <el-input-number v-model="formData.uploadFrequency" :min="0" class="w-full-unit" />
              <span class="unit-label">分钟/次</span>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="量程" prop="measureRange">
            <div class="flex items-center">
              <el-input-number v-model="formData.measureRangeLow" :min="0" placeholder="最小值" class="measure-range-input" />
              <span class="mx-2">-</span>
              <el-input-number v-model="formData.measureRangeUp" :min="0" placeholder="最大值" class="measure-range-input" />
              <el-input v-model="formData.measureUnit" placeholder="单位" class="unit-input" />
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="监测对象" prop="monitorTarget">
            <el-select
              v-model="formData.monitorTarget"
              placeholder="请选择监测对象"
              class="w-full"
              @change="handleMonitorTargetChange"
            >
              <el-option
                v-for="item in monitorObjectOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="对象选择" prop="monitorObjectId">
            <el-select
              v-model="formData.monitorObjectId"
              placeholder="请选择对象"
              class="w-full"
              :disabled="!formData.monitorTarget || bridgeListLoading"
              :loading="bridgeListLoading"
              filterable
            >
              <el-option
                v-for="item in bridgeList"
                :key="item.id"
                :label="item.bridgeName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="设备状态" prop="onlineStatus">
            <el-select v-model="formData.onlineStatus" placeholder="请选择设备状态" class="w-full">
              <el-option
                v-for="item in deviceStatusOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="权属单位" prop="ownershipUnit">
            <el-input v-model="formData.ownershipUnit" placeholder="请输入权属单位" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="设备位置" prop="address">
            <el-input v-model="formData.address" placeholder="请输入设备安装位置" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="定位">
            <div class="flex items-center">
              <el-input v-model="formData.longitude" placeholder="经度" class="mr-2 w-32" />
              <el-input v-model="formData.latitude" placeholder="纬度" class="w-32" />
              <el-button type="primary" class="ml-2" icon="Location" circle @click="openMapPicker"></el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="备注">
            <el-input
              v-model="formData.remarks"
              type="textarea"
              :rows="3"
              placeholder="请输入备注信息"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button v-if="mode !== 'view'" type="primary" @click="handleSubmit" :loading="submitLoading">
          确 定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  DEVICE_TYPE_OPTIONS,
  MONITOR_INDEX_OPTIONS,
  DEVICE_STATUS_OPTIONS,
  MONITOR_OBJECT_OPTIONS
} from '@/constants/bridge'
import {
  savePipelineInfo,
  updatePipelineInfo,
  getPipelineInfoDetail,
  getBridgeBasicInfoList
} from '@/api/bridge'
import { collectShow } from "@/hooks/gishooks"
import bus from '@/utils/mitt'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add', // add, edit, view
    validator: (value) => ['add', 'edit', 'view'].includes(value)
  },
  id: {
    type: [String, Number],
    default: null
  }
})

const emit = defineEmits(['update:modelValue', 'success'])

// 响应式数据
const formRef = ref()
const visible = ref(false)
const submitLoading = ref(false)
const bridgeListLoading = ref(false)
const bridgeList = ref([])

// 表单数据
const formData = reactive({
  id: '',
  deviceName: '',
  indexCode: '',
  deviceType: '',
  monitorIndex: '',
  collectFrequency: 60,
  uploadFrequency: 60,
  measureRangeLow: 0,
  measureRangeUp: 100,
  measureUnit: '',
  monitorTarget: '',
  monitorObjectId: '',
  onlineStatus: '',
  address: '',
  ownershipUnit: '',
  longitude: '',
  latitude: '',
  geomText: '',
  geom3Text: '',
  remarks: ''
})

// 表单验证规则
const formRules = {
  deviceName: [
    { required: true, message: '请输入设备名称', trigger: 'blur' }
  ],
  indexCode: [
    { required: true, message: '请输入设备编码', trigger: 'blur' }
  ],
  deviceType: [
    { required: true, message: '请选择设备类型', trigger: 'change' }
  ],
  monitorIndex: [
    { required: true, message: '请选择监测指标', trigger: 'change' }
  ],
  monitorTarget: [
    { required: true, message: '请选择监测对象', trigger: 'change' }
  ],
  monitorObjectId: [
    { required: true, message: '请选择对象', trigger: 'change' }
  ],
  onlineStatus: [
    { required: true, message: '请选择设备状态', trigger: 'change' }
  ]
}

// 下拉选项
const deviceTypeOptions = DEVICE_TYPE_OPTIONS
const monitorIndexOptions = MONITOR_INDEX_OPTIONS
const deviceStatusOptions = DEVICE_STATUS_OPTIONS
const monitorObjectOptions = MONITOR_OBJECT_OPTIONS

// 计算属性
const dialogTitle = computed(() => {
  const titleMap = {
    add: '新增监测设备',
    edit: '编辑监测设备',
    view: '监测设备详情'
  }
  return titleMap[props.mode] || '监测设备信息'
})

// 监听弹窗显示状态
watch(() => props.modelValue, (val) => {
  visible.value = val
  if (val) {
    nextTick(() => {
      if (props.mode === 'edit' || props.mode === 'view') {
        loadDetail()
      } else {
        resetForm()
      }
    })
  }
})

watch(visible, (val) => {
  emit('update:modelValue', val)
})

// 监听监测对象变化
const handleMonitorTargetChange = (value) => {
  formData.monitorObjectId = ''
  if (value === '4001501') { // 桥梁
    loadBridgeList()
  } else {
    bridgeList.value = []
  }
}

// 加载桥梁列表
const loadBridgeList = async () => {
  try {
    bridgeListLoading.value = true
    const response = await getBridgeBasicInfoList()
    if (response.code === 200) {
      bridgeList.value = response.data || []
    }
  } catch (error) {
    console.error('加载桥梁列表失败:', error)
    ElMessage.error('加载桥梁列表失败')
  } finally {
    bridgeListLoading.value = false
  }
}

// 加载详情数据
const loadDetail = async () => {
  if (!props.id) return
  
  try {
    const response = await getPipelineInfoDetail(props.id)
    if (response.code === 200) {
      const data = response.data
      Object.keys(formData).forEach(key => {
        if (data.hasOwnProperty(key)) {
          formData[key] = data[key]
        }
      })
      
      // 如果有监测对象，加载对应的选项列表
      if (formData.monitorTarget === '4001501') {
        loadBridgeList()
      }
    }
  } catch (error) {
    console.error('加载详情失败:', error)
    ElMessage.error('加载详情失败')
  }
}

// 重置表单
const resetForm = () => {
  Object.keys(formData).forEach(key => {
    if (key === 'collectFrequency' || key === 'uploadFrequency') {
      formData[key] = 60
    } else if (key === 'measureRangeLow') {
      formData[key] = 0
    } else if (key === 'measureRangeUp') {
      formData[key] = 100
    } else if (typeof formData[key] === 'string') {
      formData[key] = ''
    } else if (typeof formData[key] === 'number') {
      formData[key] = 0
    }
  })
  bridgeList.value = []
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

// 打开地图选点
const openMapPicker = () => {
  collectShow.value = true // 激活采集点位窗口
  // 先移除可能存在的旧监听器
  bus.off("getCollectLocation", handleCollectLocation)
  // 添加新的监听器
  bus.on("getCollectLocation", handleCollectLocation)
}

// 地图选点事件处理函数
const handleCollectLocation = (params) => {
  nextTick(() => {
    formData.longitude = params.longitude
    formData.latitude = params.latitude
  })
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    submitLoading.value = true
    
    const submitData = { ...formData }
    
    let response
    if (props.mode === 'add') {
      response = await savePipelineInfo(submitData)
    } else {
      response = await updatePipelineInfo(submitData)
    }
    
    if (response.code === 200) {
      ElMessage.success(`${props.mode === 'add' ? '新增' : '更新'}成功`)
      emit('success')
      handleClose()
    } else {
      ElMessage.error(response.message || '操作失败')
    }
  } catch (error) {
    console.error('提交失败:', error)
    if (error.message) {
      ElMessage.error(error.message)
    }
  } finally {
    submitLoading.value = false
  }
}

// 关闭弹窗
const handleClose = () => {
  visible.value = false
  resetForm()
}

// 组件卸载时清理事件监听
onUnmounted(() => {
  bus.off("getCollectLocation", handleCollectLocation)
})
</script>

<style scoped>
.bridge-device-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-form-item__label::before) {
  content: '*';
  color: #ff4d4f;
  margin-right: 4px;
}

:deep(.el-form-item:not([required]) .el-form-item__label::before) {
  content: '';
  margin-right: 0;
}

:deep(.el-input__wrapper),
:deep(.el-select__input) {
  border-radius: 2px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  background-color: #0277FD;
  border-color: #0277FD;
}

:deep(.el-button--primary:hover) {
  background-color: #1890ff;
  border-color: #1890ff;
}

.w-full {
  width: 100%;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.ml-2 {
  margin-left: 8px;
}

.mr-2 {
  margin-right: 8px;
}

.mx-2 {
  margin-left: 8px;
  margin-right: 8px;
}

.w-32 {
  width: 140px;
}

.w-full-unit {
  width: calc(100% - 60px) !important;
}

.unit-label {
  display: inline-block;
  white-space: nowrap;
  width: 55px;
  margin-left: 5px;
}

.measure-range-input {
  width: calc(40% - 20px) !important;
}

.unit-input {
  width: 20% !important;
  margin-left: 8px;
}

.dialog-footer {
  text-align: right;
}
</style> 