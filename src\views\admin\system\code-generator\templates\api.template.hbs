/**
 * {{ pageTitle }} API
 * {{ description }}
 * <AUTHOR> author }}
 * @date {{ currentDate }}
 */
import request from '@/utils/request'

const apiPrefix = '{{ apiPrefix }}'

{{#if api.methods.list}}
/**
 * 获取{{ pageTitle }}列表
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getList(params) {
  return request({
    url: `${apiPrefix}/list`,
    method: 'get',
    params
  })
}
{{/if}}

{{#if api.methods.detail}}
/**
 * 获取{{ pageTitle }}详情
 * @param {String|Number} id - ID
 * @returns {Promise}
 */
export function getDetail(id) {
  return request({
    url: `${apiPrefix}/${id}`,
    method: 'get'
  })
}
{{/if}}

{{#if api.methods.create}}
/**
 * 创建{{ pageTitle }}
 * @param {Object} data - 创建数据
 * @returns {Promise}
 */
export function create(data) {
  return request({
    url: apiPrefix,
    method: 'post',
    data
  })
}
{{/if}}

{{#if api.methods.update}}
/**
 * 更新{{ pageTitle }}
 * @param {String|Number} id - ID
 * @param {Object} data - 更新数据
 * @returns {Promise}
 */
export function update(id, data) {
  return request({
    url: `${apiPrefix}/${id}`,
    method: 'put',
    data
  })
}
{{/if}}

{{#if api.methods.delete}}
/**
 * 删除{{ pageTitle }}
 * @param {String|Number} id - ID
 * @returns {Promise}
 */
export function remove(id) {
  return request({
    url: `${apiPrefix}/${id}`,
    method: 'delete'
  })
}
{{/if}}

{{#if hasCustomMethods}}
{{#each customMethods}}
/**
 * {{ description }}
 * {{#if params}}@param {Object} {{ params }} - 参数{{/if}}
 * @returns {Promise}
 */
export function {{ name }}({{ params }}) {
  return request({
    url: `${apiPrefix}{{ url }}`,
    method: '{{ method }}',
    {{#if_eq method "get"}}
    params
    {{else}}
    data
    {{/if_eq}}
  })
}
{{/each}}
{{/if}}
