<template>
  <div class="svg-icon-demo">
    <h3>SVG图标组件使用示例</h3>
    
    <div class="demo-section">
      <h4>1. 使用名称引用图标（从assets/icons/svg目录）</h4>
      <div class="icon-list">
        <div class="icon-item">
          <SvgIcon name="example" size="24px" />
          <span>默认颜色</span>
        </div>
        <div class="icon-item">
          <SvgIcon name="example" size="24px" color="red" />
          <span>红色</span>
        </div>
        <div class="icon-item">
          <SvgIcon name="example" size="32px" color="#2196F3" />
          <span>蓝色 (32px)</span>
        </div>
        <div class="icon-item">
          <SvgIcon name="user" size="24px" color="#4CAF50" />
          <span>用户图标</span>
        </div>
      </div>
    </div>
    
    <div class="demo-section">
      <h4>2. 使用外部SVG路径</h4>
      <div class="icon-list">
        <div class="icon-item">
          <SvgIcon src="/example-external.svg" size="24px" />
          <span>外部SVG</span>
        </div>
      </div>
      <p class="note">注意：这需要在public目录中有example-external.svg文件</p>
    </div>
    
    <div class="demo-section">
      <h4>3. 使用原始SVG字符串</h4>
      <div class="icon-list">
        <div class="icon-item">
          <SvgIcon :raw="rawSvg" size="24px" color="purple" />
          <span>内联SVG</span>
        </div>
      </div>
    </div>
    
    <div class="demo-section">
      <h4>4. 添加动画效果</h4>
      <div class="icon-list">
        <div class="icon-item">
          <SvgIcon name="example" size="24px" color="#FF9800" :spin="true" />
          <span>旋转动画</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import SvgIcon from './SvgIcon.vue';

// 原始SVG字符串示例
const rawSvg = `
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024">
  <path d="M832 464h-68V240c0-70.7-57.3-128-128-128H388c-70.7 0-128 57.3-128 128v224h-68c-17.7 0-32 14.3-32 32v384c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V496c0-17.7-14.3-32-32-32zM332 240c0-30.9 25.1-56 56-56h248c30.9 0 56 25.1 56 56v224H332V240z m460 600H232V536h560v304z"  fill="currentColor"></path>
  <path d="M484 701v53c0 4.4 3.6 8 8 8h40c4.4 0 8-3.6 8-8v-53c12.1-8.7 20-22.9 20-39 0-26.5-21.5-48-48-48s-48 21.5-48 48c0 16.1 7.9 30.3 20 39z"  fill="currentColor"></path>
</svg>
`;
</script>

<style scoped>
.svg-icon-demo {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: Arial, sans-serif;
}

h3 {
  margin-bottom: 20px;
  color: #333;
}

h4 {
  margin: 15px 0;
  color: #555;
}

.demo-section {
  margin-bottom: 30px;
  padding: 15px;
  border: 1px solid #eee;
  border-radius: 5px;
}

.icon-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin: 15px 0;
}

.icon-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.note {
  font-size: 12px;
  color: #777;
  margin-top: 8px;
}
</style> 