<template>
  <teleport to="body">
    <transition name="fade">
      <div v-if="modelValue" class="modal-overlay" @click.self="closeModal">
        <div class="modal-container">
          <div class="modal-header">
            <div class="modal-title">管线风险</div>
            <div class="close-icon" @click="closeModal">×</div>
          </div>
          <div class="modal-content">
            <div class="pipeline-detail">
              <div class="detail-row">
                <div class="detail-item">
                  <span class="info-label">风险等级：</span>
                  <span class="info-value" :style="{ color: getRiskColor(pipelineData.riskLevel) }">{{ pipelineData.riskLevel || '低风险' }}</span>
                </div>
                <div class="detail-item">
                  <span class="info-label">风险值：</span>
                  <span class="info-value">{{ pipelineData.riskValue || '98' }}</span>
                </div>
              </div>
              
              <div class="detail-row">
                <div class="detail-item">
                  <span class="info-label">风险编号：</span>
                  <span class="info-value">{{ pipelineData.riskCode || 'DFX-001' }}</span>
                </div>
                <div class="detail-item">
                  <span class="info-label">管线编码：</span>
                  <span class="info-value">{{ pipelineData.code || '18768_590845' }}</span>
                </div>
              </div>
              
              <div class="detail-row">
                <div class="detail-item">
                  <span class="info-label">压力级别：</span>
                  <span class="info-value">{{ pipelineData.pressureLevel || '高压管线' }}</span>
                </div>
                <div class="detail-item">
                  <span class="info-label">管材：</span>
                  <span class="info-value">{{ pipelineData.material || 'PE100' }}</span>
                </div>
              </div>
              
              <div class="detail-row">
                <div class="detail-item">
                  <span class="info-label">管径：</span>
                  <span class="info-value">{{ pipelineData.diameter || '400mm' }}</span>
                </div>
                <div class="detail-item">
                  <span class="info-label">管长：</span>
                  <span class="info-value">{{ pipelineData.length || '1.151km' }}</span>
                </div>
              </div>
              
              <div class="detail-row">
                <div class="detail-item full-width">
                  <span class="info-label">所在道路：</span>
                  <span class="info-value">{{ pipelineData.road || '道路名称名称' }}</span>
                </div>
              </div>
              
              <div class="detail-row">
                <div class="detail-item">
                  <span class="info-label">建设时间：</span>
                  <span class="info-value">{{ pipelineData.buildTime || '2025年4月1日' }}</span>
                </div>
                <div class="detail-item">
                  <span class="info-label">评估日期：</span>
                  <span class="info-value">{{ pipelineData.assessmentDate || '2025年4月1日' }}</span>
                </div>
              </div>
              
              <div class="detail-row">
                <div class="detail-item full-width">
                  <span class="info-label">管控状态：</span>
                  <span class="info-value">{{ pipelineData.controlStatus || '未管控' }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </transition>
  </teleport>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  pipelineData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue'])

const closeModal = () => {
  emit('update:modelValue', false)
}

// 根据风险等级获取对应的颜色
const getRiskColor = (riskLevel) => {
  switch (riskLevel) {
    case '重大风险':
      return '#FF2330'
    case '较大风险':
      return '#FF9000'
    case '一般风险':
      return '#FFD11B'
    case '低风险':
      return '#00B0FF'
    default:
      return '#00B0FF'
  }
}
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.modal-container {
  width: 550px;
  background: linear-gradient(180deg, rgba(0, 22, 72, 0.9) 0%, rgba(0, 35, 91, 0.9) 100%);
  border: 1px solid rgba(59, 141, 242, 0.5);
  box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.3);
  border-radius: 4px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid rgba(59, 141, 242, 0.3);
}

.modal-title {
  font-family: PingFangSC, 'PingFang SC';
  font-weight: 500;
  font-size: 16px;
  color: #FFFFFF;
}

.close-icon {
  font-size: 20px;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
}

.close-icon:hover {
  color: #FFFFFF;
}

.modal-content {
  padding: 15px 20px;
}

.pipeline-detail {
  width: 100%;
  padding: 10px 0;
}

.detail-row {
  display: flex;
  margin-bottom: 15px;
}

.detail-item {
  flex: 1;
  display: flex;
  align-items: center;
}

.full-width {
  flex: 2;
}

.info-label {
  font-family: PingFangSC, 'PingFang SC';
  font-weight: 500;
  font-size: 14px;
  color: #D3E5FF;
  margin-right: 5px;
  white-space: nowrap;
}

.info-value {
  font-family: PingFangSC, 'PingFang SC';
  font-weight: 500;
  font-size: 14px;
  color: #FFFFFF;
  flex: 1;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style> 