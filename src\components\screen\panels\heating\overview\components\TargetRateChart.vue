<template>
  <div class="target-rate-chart">
    <div ref="chartRef" class="chart-container"></div>
    <div class="chart-title">达标率</div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import * as echarts from 'echarts'

// 图表实例
const chartRef = ref(null)
let chartInstance = null

// 模拟数据
const targetRate = 0

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return
  
  // 创建图表实例
  chartInstance = echarts.init(chartRef.value)
  
  // 渲染图表
  updateChart()
  
  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)
}

// 更新图表
const updateChart = () => {
  const option = {
    backgroundColor: 'transparent',
    title: {
      text: `${targetRate}%`,
      x: 'center',
      y: 'center',
      textStyle: {
        fontSize: 28,
        fontWeight: 'bold',
        color: '#FFFFFF',
        fontFamily: 'D-DIN, D-DIN'
      }
    },
    polar: {
      radius: ['75%', '85%'],
      center: ['50%', '50%']
    },
    angleAxis: {
      max: 100,
      show: false,
    },
    radiusAxis: {
      type: 'category',
      show: false
    },
    series: [
      {
        type: 'bar',
        roundCap: true,
        coordinateSystem: 'polar',
        data: [targetRate],
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
            {
              offset: 0,
              color: '#0082FF'
            },
            {
              offset: 1,
              color: '#00F2F1'
            }
          ]),
          shadowColor: 'rgba(0, 255, 255, 0.5)',
          shadowBlur: 10
        }
      },
      {
        type: 'bar',
        roundCap: true,
        coordinateSystem: 'polar',
        data: [100],
        itemStyle: {
          color: 'rgba(255, 255, 255, 0.1)'
        },
        z: 1
      }
    ]
  }
  
  // 设置图表配置
  chartInstance.setOption(option)
}

// 处理窗口大小变化
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

// 组件挂载时初始化图表
onMounted(async () => {
  await nextTick()
  initChart()
})

// 组件卸载时销毁图表
onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.target-rate-chart {
  width: 100%;
  height: 100%;
  position: relative;
  background-image: url('@/assets/images/screen/heating/dabiao_bg.png');
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.chart-container {
  width: 100%;
  height: 85%;
}

.chart-title {
  font-family: SourceHanSansCN, SourceHanSansCN;
  font-weight: 400;
  font-size: 14px;
  color: #FFFFFF;
  text-align: center;
  position: absolute;
  bottom: -10px;
  width: 100%;
}
</style> 