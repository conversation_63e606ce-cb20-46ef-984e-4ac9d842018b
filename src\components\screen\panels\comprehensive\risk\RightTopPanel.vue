<template>
  <PanelBox title="桥梁风险">
    <div class="panel-content">
      <div class="risk-chart-container">
        <div class="risk-item" v-for="(item, index) in riskData" :key="index">
          <div class="risk-label-left">{{ item.type }}</div>
          <div class="risk-bar-wrapper">
            <div class="risk-bar" :style="{
              width: `${item.value}%`,
              background: getRiskGradient(item.type)
            }">
              <div class="risk-bar-circle"></div>
            </div>
          </div>
          <div class="risk-label-right">{{ item.count }} <span>座</span></div>
        </div>
      </div>
    </div>
  </PanelBox>
</template>

<script setup>
import { ref } from 'vue'
import PanelBox from '@/components/screen/PanelBox.vue'

// 综合态势总览右上面板组件

// 模拟风险数据
const riskData = ref([
  { type: '重大风险', count: 0, value: 0 },
  { type: '较大风险', count: 0, value: 0 },
  { type: '一般风险', count: 0, value: 0 },
  { type: '低风险', count: 0, value: 0 }
])

// 获取风险等级对应的渐变色
const getRiskGradient = (riskLevel) => {
  switch (riskLevel) {
    case '重大风险':
      return 'linear-gradient(90deg, #BC0000 0%, #DE0101 100%)'
    case '较大风险':
      return 'linear-gradient(90deg, #BC4E00 0%, #DE5C01 100%)'
    case '一般风险':
      return 'linear-gradient(90deg, #BC8400 0%, #DEA801 100%)'
    case '低风险':
      return 'linear-gradient(90deg, #006BBC 0%, #0138DE 100%)'
    default:
      return 'linear-gradient(90deg, #006BBC 0%, #0138DE 100%)'
  }
}
</script>

<style scoped>
.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
}

.risk-chart-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding-top: 2rem;
  padding-left: 0.3rem;
}

.risk-item {
  display: flex;
  align-items: center;
  height: 22%;
  margin-bottom: 10px;
}

.risk-label-left {
  width: 70px;
  font-family: PingFangSC, 'PingFang SC';
  font-weight: 400;
  font-size: 14px;
  color: #FFFFFF;
  margin-right: 15px;
  text-align: left;
  flex-shrink: 0;
}

.risk-bar-wrapper {
  flex: 1;
  height: 24px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 7px;
  overflow: hidden;
  margin-right: 15px;
}

.risk-bar {
  height: 100%;
  border-radius: 7px;
  position: relative;
}

.risk-bar-circle {
  position: absolute;
  top: 50%;
  right: 0;
  transform: translate(50%, -50%);
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 0 10px 2px rgba(255, 255, 255, 0.3);
}

.risk-label-right {
  width: 60px;
  font-family: 'D-DIN', 'D-DIN';
  font-weight: bold;
  font-size: 24px;
  color: #FFFFFF;
  text-align: left;
  flex-shrink: 0;
}

.risk-label-right span {
  font-size: 14px;
  font-weight: 400;
  margin-left: 2px;
}

/* 媒体查询适配不同分辨率 */
@media screen and (min-width: 1920px) and (max-width: 2560px) {
  .panel-content {
    padding: 15px;
  }
  
  .risk-item {
    margin-bottom: 15px;
  }
}

@media screen and (max-width: 1919px) {
  .panel-content {
    padding: 12px;
  }
  
  .risk-item {
    margin-bottom: 12px;
  }
  
  .risk-label-left {
    font-size: 13px;
    width: 65px;
  }
  
  .risk-label-right {
    font-size: 22px;
  }
}

/* 添加全屏模式(1080px高度)的适配 */
@media screen and (min-height: 1056px) and (max-height: 1080px) {
  .panel-content {
    padding: 15px;
  }
  
  .risk-item {
    margin-bottom: 18px;
  }
}

/* 940px-1055px高度的屏幕特别优化 */
@media screen and (min-height: 940px) and (max-height: 1055px) {
  .panel-content {
    padding: 10px;
  }
  
  .risk-item {
    margin-bottom: 10px;
  }
  
  .risk-bar-wrapper {
    height: 12px;
    border-radius: 6px;
  }
  
  .risk-bar {
    border-radius: 6px;
  }
  
  .risk-bar-circle {
    width: 18px;
    height: 18px;
  }
}

/* 910px高度的屏幕特别优化 */
@media screen and (min-height: 900px) and (max-height: 940px) {
  .panel-content {
    padding: 8px;
  }
  
  .risk-item {
    margin-bottom: 8px;
  }
  
  .risk-label-left {
    font-size: 12px;
    width: 60px;
    margin-right: 10px;
  }
  
  .risk-bar-wrapper {
    height: 18px;
    border-radius: 5px;
    margin-right: 10px;
  }
  
  .risk-bar {
    border-radius: 5px;
  }
  
  .risk-bar-circle {
    width: 16px;
    height: 16px;
  }
  
  .risk-label-right {
    font-size: 20px;
  }
  
  .risk-label-right span {
    font-size: 12px;
  }
}
</style> 