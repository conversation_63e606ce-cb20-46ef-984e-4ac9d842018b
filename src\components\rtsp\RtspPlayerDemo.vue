<template>
  <div class="rtsp-player-demo">
    <h2>RTSP视频播放器示例</h2>
    
    <div class="config-panel">
      <div class="input-group">
        <label for="wsUrl">WebSocket地址：</label>
        <input 
          type="text" 
          id="wsUrl" 
          v-model="wsUrl" 
          placeholder="例如: ws://localhost:8080/rtsp" 
          style="width:400px;"
        >
      </div>
    </div>
    
    <div class="players-container">
      <div v-for="(player, index) in players" :key="player.id" class="player-item">
        <h3>视频流 #{{ index + 1 }}</h3>
        <div class="input-group">
          <label :for="'videoId' + index">视频ID：</label>
          <input 
            :id="'videoId' + index" 
            v-model="player.videoId" 
            placeholder="输入视频ID" 
            style="width:200px;"
          >
        </div>
        
        <RtspPlayerComponent 
          v-if="wsManager"
          :wsManager="wsManager"
          :videoId="player.videoId"
          :width="640"
          :height="360"
          :showControls="true"
          :autoplay="false"
          @status-change="(status) => handleStatusChange(index, status)"
          @error="(error) => handleError(index, error)"
        />
        
        <div class="player-actions">
          <button @click="removePlayer(index)" class="remove-btn">移除播放器</button>
        </div>
      </div>
    </div>
    
    <div class="actions">
      <button @click="addPlayer" class="add-btn">添加新播放器</button>
      <button @click="connectWebSocket" class="connect-btn" :disabled="!wsUrl">
        {{ wsManager ? '重新连接' : '连接WebSocket' }}
      </button>
    </div>
  </div>
</template>

<script>
import { ref, onBeforeUnmount } from 'vue';
import { RtspWebSocketSingleton, RtspPlayerComponent } from './index';

export default {
  name: 'RtspPlayerDemo',
  components: {
    RtspPlayerComponent
  },
  setup() {
    const wsUrl = ref('ws://**************:32021/basic/jsmpeg');
    const wsManager = ref(null);
    const players = ref([
      { id: 1, videoId: 'camera1' }
    ]);
    
    // 连接WebSocket
    const connectWebSocket = () => {
      // 如果已有连接，先销毁
      if (wsManager.value) {
        RtspWebSocketSingleton.destroyInstance();
      }
      
      // 创建新的WebSocket连接
      if (wsUrl.value) {
        wsManager.value = RtspWebSocketSingleton.getInstance(wsUrl.value);
        wsManager.value.connect().catch(error => {
          console.error('WebSocket连接失败', error);
          alert('WebSocket连接失败: ' + error.message);
        });
      }
    };
    
    // 添加播放器
    const addPlayer = () => {
      const newId = players.value.length > 0 
        ? Math.max(...players.value.map(p => p.id)) + 1 
        : 1;
      
      players.value.push({
        id: newId,
        videoId: 'camera' + newId
      });
    };
    
    // 移除播放器
    const removePlayer = (index) => {
      players.value.splice(index, 1);
    };
    
    // 处理播放器状态变化
    const handleStatusChange = (index, status) => {
      console.log(`播放器 #${index + 1} 状态变化:`, status);
    };
    
    // 处理播放器错误
    const handleError = (index, error) => {
      console.error(`播放器 #${index + 1} 错误:`, error);
    };
    
    // 组件卸载前断开WebSocket连接
    onBeforeUnmount(() => {
      RtspWebSocketSingleton.destroyInstance();
    });
    
    return {
      wsUrl,
      wsManager,
      players,
      connectWebSocket,
      addPlayer,
      removePlayer,
      handleStatusChange,
      handleError
    };
  }
};
</script>

<style scoped>
.rtsp-player-demo {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

h2 {
  margin-bottom: 20px;
}

.config-panel {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f5f5f5;
  border-radius: 5px;
}

.input-group {
  margin-bottom: 10px;
  display: flex;
  align-items: center;
}

.input-group label {
  min-width: 120px;
  display: inline-block;
}

input {
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.players-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 20px;
}

.player-item {
  flex-basis: 100%;
  border: 1px solid #ddd;
  border-radius: 5px;
  padding: 15px;
  margin-bottom: 20px;
}

.player-actions {
  margin-top: 10px;
  display: flex;
  justify-content: flex-end;
}

.actions {
  display: flex;
  gap: 10px;
}

button {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  background-color: #4a90e2;
  color: white;
}

button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.add-btn {
  background-color: #5cb85c;
}

.remove-btn {
  background-color: #d9534f;
}
</style> 