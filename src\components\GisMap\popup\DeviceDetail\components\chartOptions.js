import * as echarts from "echarts";
import moment from "moment";

export const getChartOptions = (data, flag, title) => {
  return {
    color: ["#333"],
    grid: {
      left: "2%",
      right: "3%",
      bottom: "13%",
      top: "8%",
      containLabel: true,
    },
    toolbox: {
      feature: {
        dataZoom: {},
        saveAsImage: {},
      },
      iconStyle: {
        borderColor: "rgba(255, 255, 255, 0.8)",
      },
      top: 0,
      right: 10,
    },
    dataZoom: [
      {
        type: "inside",
        start: 0,
        end: 100,
        minSpan: 30,
      },
      {
        start: 0,
        end: 100,
        minSpan: 30,
        textStyle: {
          color: "transparent",
        },
      },
    ],
    xAxis: [
      {
        type: "category",
        boundaryGap: false,
        data: data.map((v) => v.time),
        axisLine: {
          show: true,
          lineStyle: {
            color: "rgba(31, 98, 163, 1)",
          },
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          fontSize: 14,
          color: "rgba(255, 255, 255, 0.8)",
          showMaxLabel: true,
          showMinLabel: true,
          formatter: (v) => {
            return flag === 1
                ? moment(v).format("HH:mm")
                : moment(v).format("DD日 HH:mm").replace(" ", "\n");
          },
        },
      },
    ],
    yAxis: [
      {
        name: title,
        nameTextStyle: {
          fontSize: 14,
          color: "rgba(255, 255, 255, 0.8)",
          padding: [0, 0, 0, 10],
        },
        type: "value",
        axisLine: {
          show: false,
        },
        splitLine: {
          show: true,
          lineStyle: {
            type: [5, 10],
            color: "rgba(255, 255, 255, 0.2)",
          },
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          fontSize: 14,
          color: "rgba(255, 255, 255, 0.8)",
        },
      },
    ],
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
      backgroundColor: "#fff",
      borderWidth: 1,
      borderColor: "#eee",
      textStyle: {
        color: "#333",
      },
      formatter: function (params) {
        const time = moment(params[0].axisValue).format("YYYY-MM-DD HH:mm");
        const returnData = time + "：" + params[0].value + params[0].data.unit;
        return returnData;
      },
    },
    series: [
      {
        type: "line",
        stack: "Total",
        smooth: true,
        showSymbol: false,
        lineStyle: {
          width: 2,
          color: "#4FFF9E",
        },
        areaStyle: {
          opacity: 0.2,
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: "#4FFF9E",
            },
            {
              offset: 1,
              color: "#4FFF9E",
            },
          ]),
        },
        emphasis: {
          focus: "series",
        },
        data: data,
      },
    ],
  };
};