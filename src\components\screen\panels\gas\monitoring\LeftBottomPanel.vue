<template>
  <PanelBox title="监测报警" class="gas-monitoring-left-bottom-panel">
    <template #extra>
      <div class="more-btn" @click="openAlarmListModal">
        更多
      </div>
    </template>
    <div class="panel-content">
      <div class="alarm-stats">
        <div class="stat-item">
          <div class="dot-wrapper">
            <div class="dot-outer red-dot"></div>
            <div class="dot-inner red-dot"></div>
          </div>
          <div class="stat-label">报警总数</div>
          <div class="stat-value red-value">{{ alarmStats.totalCount }}</div>
        </div>
        <div class="stat-item">
          <div class="dot-wrapper">
            <div class="dot-outer green-dot"></div>
            <div class="dot-inner green-dot"></div>
          </div>
          <div class="stat-label">已处置</div>
          <div class="stat-value green-value">{{ alarmStats.handledCount }}</div>
        </div>
        <div class="stat-item">
          <div class="dot-wrapper">
            <div class="dot-outer orange-dot"></div>
            <div class="dot-inner orange-dot"></div>
          </div>
          <div class="stat-label">处置中</div>
          <div class="stat-value orange-value">{{ alarmStats.processingCount }}</div>
        </div>
        <div class="stat-item">
          <div class="dot-wrapper">
            <div class="dot-outer deep-orange-dot"></div>
            <div class="dot-inner deep-orange-dot"></div>
          </div>
          <div class="stat-label">未处置</div>
          <div class="stat-value deep-orange-value">{{ alarmStats.unhandledCount }}</div>
        </div>
      </div>

      <!-- 使用滚动表格组件 -->
      <ScrollTable :columns="tableColumns" :data="alarmList" :autoScroll="true" :scrollSpeed="3000"
        :tableHeight="tableHeight" :visibleRows="4" @row-click="openDetailModal">
        <!-- 自定义等级列 -->
        <template #level="{ row }">
          <div class="level-icon-wrapper">
            <SvgIcon :raw="getAlarmLevelIcon(row.level)" :color="getAlarmLevelColor(row.level)" size="16px" />
          </div>
        </template>

        <!-- 自定义状态列 -->
        <template #status="{ row }">
          <span :class="getStatusClass(row.status)">{{ row.status }}</span>
        </template>

        <!-- 自定义位置列 -->
        <template #location>
          <SvgIcon :raw="locationIconSvg" color="#FF6D28" size="16px" />
        </template>
      </ScrollTable>
    </div>

    <!-- 设备详情弹窗 -->
    <GasMonitorDetailModal v-model="showDetailModal" :alarmId="selectedAlarmId" :deviceId="selectedDeviceId" />

    <!-- 报警列表弹窗 -->
    <AlarmListModal v-model="showAlarmListModal" />
  </PanelBox>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import PanelBox from '@/components/screen/PanelBox.vue'
import ScrollTable from '@/components/screen/common/ScrollTable.vue'
import SvgIcon from '@/components/SvgIcon.vue'
import GasMonitorDetailModal from './GasMonitorDetailModal.vue'
import AlarmListModal from './AlarmListModal.vue'

import { getMonitorAnalysisStatistics } from '@/api/gas'

// 表格列配置
const tableColumns = [
  { title: '等级', dataIndex: 'alarmLevel', width: '8%', fontSize: '13px' },
  { title: '编号', dataIndex: 'alarmCode', width: '16%', fontSize: '13px' },
  { title: '报警来源', dataIndex: 'alarmSource', width: '20%', fontSize: '13px' },
  { title: '设备名称', dataIndex: 'deviceName', width: '20%', fontSize: '13px' },
  { title: '处置状态', dataIndex: 'handleStatusName', width: '18%', fontSize: '13px', className: 'status-column' },
  { title: '位置', dataIndex: 'address', width: '8%', fontSize: '13px' }
]

// 动态计算表格高度
const tableHeight = computed(() => {
  return '200px' // 可以根据不同分辨率动态调整
})

// 位置图标SVG
const locationIconSvg = `<svg viewBox="0 0 13 16" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path d="M6.5 0C2.91 0 0 2.91 0 6.5C0 11.375 6.5 16 6.5 16C6.5 16 13 11.375 13 6.5C13 2.91 10.09 0 6.5 0ZM6.5 8.8C5.235 8.8 4.2 7.765 4.2 6.5C4.2 5.235 5.235 4.2 6.5 4.2C7.765 4.2 8.8 5.235 8.8 6.5C8.8 7.765 7.765 8.8 6.5 8.8Z" fill="currentColor"/>
</svg>`

// 报警等级图标SVG
const getAlarmLevelIcon = (level) => {
  // 使用通用的报警器图标，根据等级变换颜色
  return `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M8 0C3.58172 0 0 3.58172 0 8C0 12.4183 3.58172 16 8 16C12.4183 16 16 12.4183 16 8C16 3.58172 12.4183 0 8 0ZM8 14.5C4.41015 14.5 1.5 11.5899 1.5 8C1.5 4.41015 4.41015 1.5 8 1.5C11.5899 1.5 14.5 4.41015 14.5 8C14.5 11.5899 11.5899 14.5 8 14.5Z" fill="currentColor"/>
    <path d="M7.25 3.5V8.75H10.75V7.25H8.75V3.5H7.25Z" fill="currentColor"/>
  </svg>`
}

// 根据报警等级获取颜色
const getAlarmLevelColor = (level) => {
  switch (level) {
    case '9101': // 一级报警 - 红色
      return '#FC4949'
    case '9102': // 二级报警 - 橙色
      return '#FF6D28'
    case '9103': // 三级报警 - 黄色
      return '#FFC75A'
    case '9104': // 四级报警 - 蓝色
      return '#3B82F6'
    default:
      return '#FFFFFF'
  }
}

// 报警数据
const alarmList = ref([])

// 报警统计数据
const alarmStats = ref({
  totalCount: 0,
  handledCount: 0,
  processingCount: 0,
  unhandledCount: 0
})

// 获取处置状态对应的样式类名
const getStatusClass = (status) => {
  switch (status) {
    case '9201':
      return 'status-pending'
    case '9202':
      return 'status-resolved'
    case '9203':
      return 'status-processing'
    default:
      return ''
  }
}

// 获取报警数据
const fetchAlarmData = async () => {
  try {
    const response = await getMonitorAnalysisStatistics()
    if (response.code === 200) {
      const { totalCount, handledCount, processingCount, unhandledCount, alarms } = response.data
      
      // 更新统计数据
      alarmStats.value = {
        totalCount,
        handledCount,
        processingCount,
        unhandledCount
      }
      
      // 更新报警列表数据
      alarmList.value = alarms.records || []
    }
  } catch (error) {
    console.error('获取报警数据失败:', error)
  }
}

// 弹窗相关状态
const showDetailModal = ref(false)
const selectedAlarmId = ref('')
const selectedDeviceId = ref('')

// 打开详情弹窗
const openDetailModal = (row) => {
  selectedAlarmId.value = row.alarmId
  selectedDeviceId.value = row.deviceCode
  showDetailModal.value = true
}

// 报警列表弹窗
const showAlarmListModal = ref(false)

// 打开报警列表弹窗
const openAlarmListModal = () => {
  showAlarmListModal.value = true
}

onMounted(() => {
  fetchAlarmData()
})
</script>

<style scoped>
.gas-monitoring-left-bottom-panel {
  height: 320px;
  /* 默认高度为320px */
}

.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

/* 报警统计样式 */
.alarm-stats {
  display: flex;
  justify-content: space-around;
  align-items: center;
  margin-bottom: 10px;
}

.stat-item {
  display: flex;
  gap: 6px;
  align-items: center;
}

.dot-wrapper {
  position: relative;
  width: 9px;
  height: 9px;
}

.dot-outer {
  position: absolute;
  width: 9px;
  height: 9px;
  border-radius: 50%;
}

.dot-inner {
  position: absolute;
  width: 5px;
  height: 5px;
  border-radius: 50%;
  top: 2px;
  left: 2px;
}

/* 红色圆点 */
.red-dot.dot-outer {
  background: rgba(252, 73, 73, 0.4);
}

.red-dot.dot-inner {
  background: #FC4949;
}

/* 绿色圆点 */
.green-dot.dot-outer {
  background: rgba(63, 216, 124, 0.4);
}

.green-dot.dot-inner {
  background: #3FD87C;
}

/* 橙色圆点 */
.orange-dot.dot-outer {
  background: rgba(255, 199, 90, 0.4);
}

.orange-dot.dot-inner {
  background: #FFC75A;
}

/* 深橙色圆点 */
.deep-orange-dot.dot-outer {
  background: rgba(255, 109, 40, 0.4);
}

.deep-orange-dot.dot-inner {
  background: #FF6D28;
}

.stat-label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
}

.stat-value {
  font-family: 'D-DIN', 'D-DIN';
  font-weight: bold;
  font-size: 24px;
  line-height: 26px;
}

.red-value {
  background: linear-gradient(90deg, #FB3737 0%, #FEA6A6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.green-value {
  background: linear-gradient(90deg, #43DF81 0%, #A6FED0 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.orange-value {
  background: linear-gradient(90deg, #FFC24C 0%, #FEDFA6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.deep-orange-value {
  background: linear-gradient(90deg, #FF5717 0%, #FFCD72 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* 等级图标样式 */
.level-icon-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 状态样式 */
.status-pending {
  color: #FF6D28;
}

.status-resolved {
  color: #3FD87C;
}

.status-processing {
  color: #FFC75A;
}

/* 媒体查询适配不同分辨率 */
@media screen and (min-width: 1920px) and (max-width: 2560px) {
  .gas-monitoring-left-bottom-panel {
    height: 340px;
  }
}

@media screen and (max-width: 1919px) {
  .gas-monitoring-left-bottom-panel {
    height: 320px;
  }
}

@media screen and (min-width: 2561px) {
  .gas-monitoring-left-bottom-panel {
    height: 380px;
  }
}

@media screen and (min-height: 940px) and (max-height: 1055px) {
  .gas-monitoring-left-bottom-panel {
    height: 320px;
  }
  
  .panel-content {
    padding: 10px;
    gap: 0px;
  }
  
  .alarm-stats {
    gap: 8px;
  }
  
  .alarm-stat-item {
    padding: 8px;
  }
  
  .alarm-stat-title {
    font-size: 13px;
    margin-bottom: 3px;
  }
  
  .alarm-stat-value {
    font-size: 18px;
  }
  
  .data-table {
    margin-top: -5px;
  }
}

/* 点击行样式 */
:deep(.scroll-table tr) {
  cursor: pointer;
  transition: background-color 0.2s;
}

:deep(.scroll-table tr:hover) {
  background-color: rgba(0, 163, 255, 0.2) !important;
}

/* 更多按钮样式 */
.more-btn {
  font-family: PingFangSC, 'PingFang SC';
  font-size: 12px;
  color: #3AA1FF;
  cursor: pointer;
  text-decoration: underline;
  margin-right: 30px;
}

.more-btn:hover {
  color: #66B8FF;
}

/* 移除旧的更多按钮样式 */
.risk-more-container {
  display: none;
}

.risk-more-btn {
  display: none;
}
</style>