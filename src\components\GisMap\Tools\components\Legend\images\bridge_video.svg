<?xml version="1.0" encoding="UTF-8"?>
<svg width="18px" height="18px" viewBox="0 0 18 18" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>视频监控</title>
    <defs>
        <circle id="path-1" cx="7" cy="7" r="7"></circle>
        <filter x="-21.4%" y="-21.4%" width="142.9%" height="142.9%" filterUnits="objectBoundingBox" id="filter-2">
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="1" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.162200418   0 0 0 0 0.568546468   0 0 0 0 0.902202219  0 0 0 0.5 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="27.9288816%" y1="8.11919591%" x2="71.2992224%" y2="91.9872084%" id="linearGradient-3">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#A5E2FF" offset="16.835118%"></stop>
            <stop stop-color="#83BEFF" offset="71.3150675%"></stop>
            <stop stop-color="#C6E3FF" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="图层&amp;图例" transform="translate(-2089.000000, -562.000000)">
            <g id="编组-13备份-2" transform="translate(2067.000000, 30.000000)">
                <g id="编组-45备份" transform="translate(0.000000, 65.000000)">
                    <g id="编组-2备份-2" transform="translate(24.000000, 148.000000)">
                        <g id="视频监控" transform="translate(0.000000, 321.000000)">
                            <g id="正常备份-2">
                                <g id="椭圆形">
                                    <use fill="black" fill-opacity="1" filter="url(#filter-2)" xlink:href="#path-1"></use>
                                    <use fill="#0099FF" fill-rule="evenodd" xlink:href="#path-1"></use>
                                </g>
                                <circle id="椭圆形" stroke="url(#linearGradient-3)" stroke-width="0.823529412" cx="7" cy="7" r="6.58823529"></circle>
                            </g>
                            <g id="视频监控备份" transform="translate(3.000000, 3.000000)" fill="#FFFFFF" fill-rule="nonzero">
                                <path d="M0.853333333,5.54305882 L0.853333333,4.34305882 L0,4.2 L0,8 L0.853333333,7.68564706 L0.853333333,6.3717647 L2.21333333,6.3717647 L2.8,5.40047058 L2.05333333,4.91482352 L1.70666667,5.5717647 L0.853333333,5.5717647 L0.853333333,5.54352941 L0.853333333,5.54305882 Z M8,4.34305882 L1.89333333,0 L1.2,0 L0.24,1.62823529 L0.24,2.17129412 L5.70666667,6 L6.16,5.94305882 L8,4.34305882 L8,4.34305882 Z M0.24,2.51435294 L0.24,3.37129412 L5.76,7.14305882 L6.26666667,7.14305882 L6.50666667,6.85694118 L6.74666667,6.02870588 C6.74666667,6.02870588 6.05333333,6.57129412 6,6.57129412 C5.76,6.37129412 0.24,2.51435294 0.24,2.51435294 L0.24,2.51435294 Z" id="形状"></path>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>