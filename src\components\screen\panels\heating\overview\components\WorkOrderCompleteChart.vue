<template>
  <div class="work-order-complete-chart">
    <div class="chart-title">工单办结率</div>
    <div ref="chartRef" class="chart-container"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import * as echarts from 'echarts'

// 图表实例
const chartRef = ref(null)
let chartInstance = null

// 模拟数据
const completeRate = 80

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return
  
  // 设置一个定时器，确保DOM已经完全渲染
  setTimeout(() => {
    if (chartRef.value.clientWidth === 0 || chartRef.value.clientHeight === 0) {
      console.error('WorkOrderCompleteChart DOM尺寸为0:', chartRef.value.clientWidth, chartRef.value.clientHeight)
      // 设置一个固定的高度
      chartRef.value.style.height = '180px'
    }
    
    // 创建图表实例
    chartInstance = echarts.init(chartRef.value)
    
    // 渲染图表
    updateChart()
    
    // 监听窗口大小变化
    window.addEventListener('resize', handleResize)
  }, 0)
}

// 更新图表
const updateChart = () => {
  const option = {
    backgroundColor: 'transparent',
    grid: {
      left: '10%',
      right: '10%',
      bottom: '40%',
      top: '15%'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'none'
      },
      formatter: '{b}: {c}%'
    },
    xAxis: {
      type: 'value',
      min: 0,
      max: 100,
      axisLine: {
        show: true,
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.2)'
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        show: true,
        interval: 25,
        color: '#fff',
        fontSize: 12,
        formatter: '{value}%'
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)',
          type: 'dashed'
        }
      },
      boundaryGap: [0, 0.01]
    },
    yAxis: {
      type: 'category',
      data: ['办结率'],
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        show: false
      }
    },
    series: [
      // 背景条
      {
        type: 'bar',
        data: [100],
        barWidth: 18,
        itemStyle: {
          color: 'rgba(255, 255, 255, 0.1)',
          borderRadius: 9
        },
        z: 1
      },
      // 数据条
      {
        type: 'bar',
        data: [completeRate],
        barWidth: 18,
        barGap: '-100%', // 使数据条和背景条重叠
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
            {
              offset: 0,
              color: 'rgba(31, 204, 250, 1)'
            },
            {
              offset: 1,
              color: 'rgba(20, 110, 235, 1)'
            }
          ]),
          borderRadius: 9
        },
        label: {
          show: true,
          position: 'top',
          formatter: `{c}%`,
          fontSize: 18,
          fontWeight: 'bold',
          color: '#FFFFFF',
          fontFamily: 'D-DIN, D-DIN'
        },
        z: 2
      }
    ]
  }
  
  // 设置图表配置
  chartInstance.setOption(option)
}

// 处理窗口大小变化
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

// 组件挂载时初始化图表
onMounted(() => {
  // 使用requestAnimationFrame确保DOM已渲染
  window.requestAnimationFrame(() => {
    initChart()
  })
})

// 组件卸载时销毁图表
onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.work-order-complete-chart {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
}

.chart-title {
  font-family: SourceHanSansCN, SourceHanSansCN;
  font-weight: 400;
  font-size: 14px;
  color: #FFFFFF;
  text-align: left;
  margin-bottom: -1rem;
}

.chart-container {
  flex: 1;
  width: 100%;
  min-height: 180px; /* 设置最小高度 */
}
</style> 