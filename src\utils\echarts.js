import * as echarts from 'echarts'

// 创建useECharts函数
function createECharts(el, options) {
  let chartInstance = null
  
  const initChart = (element) => {
    if (!element && !el) return
    
    // 销毁已存在的实例
    if (chartInstance) {
      chartInstance.dispose()
    }
    
    // 初始化图表，使用深色主题
    chartInstance = echarts.init(element || el, 'dark')
    
    // 如果有配置项，则设置
    if (options) {
      chartInstance.setOption(options, true)
    }
    
    return chartInstance
  }

  const updateChart = (newOptions) => {
    if (!chartInstance) return
    
    // 设置图表配置项
    chartInstance.setOption(newOptions, true)
  }

  const resizeChart = () => {
    if (!chartInstance) return
    
    chartInstance.resize()
  }
  
  const disposeChart = () => {
    if (!chartInstance) return
    
    chartInstance.dispose()
    chartInstance = null
  }

  return {
    chartInstance,
    initChart,
    updateChart,
    resizeChart,
    disposeChart,
    echarts  // 导出echarts对象，以便在组件内部使用
  }
}

// 提供默认导出
export default createECharts

// 提供命名导出，兼容dashboard.vue中的import { useECharts } from '...'
export const useECharts = (el, options) => {
  return createECharts(el, options)
}

// 提供常用的图表主题色
export const chartColors = {
  primary: ['#00f2f1', '#0066ff', '#005ae0', '#0082fc', '#1ab3fb'],
  success: ['#00ff7f', '#00e676', '#00c853', '#00af50', '#009624'],
  warning: ['#ffa500', '#ffc107', '#ffa000', '#ff8f00', '#ff6f00'],
  danger: ['#ff4500', '#ff5252', '#ff1744', '#d50000', '#b71c1c'],
  text: ['#ffffff', '#e0f7fa', '#b2ebf2', '#80deea', '#4dd0e1', '#26c6da']
} 