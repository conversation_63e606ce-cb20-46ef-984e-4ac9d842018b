<template>
  <div class="video-player-container">
    <div ref="videoContainer" class="video-container"></div>
    <div v-if="showControls" class="video-controls">
      <div class="control-btn" @click="togglePlay">
        <i :class="isPlaying ? 'icon-pause' : 'icon-play'"></i>
      </div>
      <div class="control-btn" @click="toggleFullscreen">
        <i class="icon-fullscreen"></i>
      </div>
    </div>
    <div v-if="showStatus" class="video-status" :class="{'status-online': isOnline, 'status-offline': !isOnline}">
      {{ isOnline ? '在线' : '离线' }}
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue';
import Hls from 'hls.js';
import videojs from 'video.js';
import 'video.js/dist/video-js.css';

const props = defineProps({
  src: {
    type: String,
    required: true
  },
  autoplay: {
    type: Boolean,
    default: false
  },
  muted: {
    type: Boolean,
    default: true
  },
  loop: {
    type: Boolean,
    default: false
  },
  controls: {
    type: Boolean,
    default: false
  },
  fluid: {
    type: Boolean,
    default: true
  },
  width: {
    type: Number,
    default: 400
  },
  height: {
    type: Number,
    default: 225
  },
  type: {
    type: String,
    default: 'application/x-mpegURL'
  },
  showControls: {
    type: Boolean,
    default: true
  },
  showStatus: {
    type: Boolean,
    default: false
  },
  isOnline: {
    type: Boolean,
    default: true
  }
});

const emit = defineEmits(['play', 'pause', 'fullscreen', 'error']);

const videoContainer = ref(null);
const player = ref(null);
const isPlaying = ref(false);

// 播放/暂停切换
const togglePlay = () => {
  if (player.value) {
    if (player.value.paused()) {
      player.value.play();
      isPlaying.value = true;
      emit('play');
    } else {
      player.value.pause();
      isPlaying.value = false;
      emit('pause');
    }
  }
};

// 切换全屏
const toggleFullscreen = () => {
  if (player.value) {
    if (player.value.isFullscreen()) {
      player.value.exitFullscreen();
    } else {
      player.value.requestFullscreen();
    }
    emit('fullscreen');
  }
};

// 初始化播放器
const initPlayer = () => {
  // 创建video元素
  const videoElement = document.createElement('video');
  videoElement.className = 'video-js vjs-default-skin';
  
  // 添加到DOM
  if (videoContainer.value) {
    videoContainer.value.innerHTML = '';
    videoContainer.value.appendChild(videoElement);
  }

  // 初始化video.js播放器
  player.value = videojs(videoElement, {
    controls: props.controls,
    autoplay: props.autoplay,
    muted: props.muted,
    loop: props.loop,
    fluid: props.fluid,
    width: props.width,
    height: props.height,
    sources: [{
      src: props.src,
      type: props.type
    }]
  });

  // 设置HLS
  if (Hls.isSupported() && props.src.includes('.m3u8')) {
    const hls = new Hls();
    hls.loadSource(props.src);
    hls.attachMedia(videoElement);
    player.value.on('play', () => {
      isPlaying.value = true;
      emit('play');
    });
    
    player.value.on('pause', () => {
      isPlaying.value = false;
      emit('pause');
    });
    
    player.value.on('error', (error) => {
      console.error('视频播放错误:', error);
      emit('error', error);
    });
  }
};

// 暴露的方法
const play = () => {
  if (player.value) {
    player.value.play();
    isPlaying.value = true;
  }
};

const pause = () => {
  if (player.value) {
    player.value.pause();
    isPlaying.value = false;
  }
};

const maximize = () => {
  if (player.value && !player.value.isFullscreen()) {
    player.value.requestFullscreen();
  }
};

onMounted(() => {
  initPlayer();
});

onUnmounted(() => {
  if (player.value) {
    player.value.dispose();
  }
});

// 暴露组件方法
defineExpose({
  play,
  pause,
  maximize,
  player
});
</script>

<style scoped>
.video-player-container {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: #000;
  overflow: hidden;
  border-radius: 4px;
}

.video-container {
  width: 100%;
  height: 100%;
}

.video-controls {
  position: absolute;
  bottom: 10px;
  left: 0;
  width: 100%;
  display: flex;
  justify-content: center;
  gap: 15px;
  padding: 5px;
  background-color: rgba(0, 0, 0, 0.5);
  transition: opacity 0.3s;
  opacity: 0;
  z-index: 20;
}

.video-status {
  position: absolute;
  top: 10px;
  right: 10px;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 12px;
  font-weight: bold;
  z-index: 20;
}

.status-online {
  background-color: rgba(16, 185, 129, 0.8);
  color: white;
}

.status-offline {
  background-color: rgba(156, 163, 175, 0.8);
  color: white;
}

.video-player-container:hover .video-controls {
  opacity: 1;
}

.control-btn {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: background-color 0.3s;
}

.control-btn:hover {
  background-color: rgba(255, 255, 255, 0.4);
}

.control-btn i {
  color: #fff;
  font-size: 16px;
}

/* 图标样式 */
.icon-play:before {
  content: "▶";
}

.icon-pause:before {
  content: "⏸";
}

.icon-fullscreen:before {
  content: "⤢";
}

:deep(.video-js) {
  width: 100%;
  height: 100%;
}

:deep(.vjs-poster) {
  background-size: cover;
}

:deep(.vjs-control-bar) {
  display: none !important;
}

:deep(.vjs-big-play-button) {
  display: none !important;
}

:deep(.vjs-error-display) {
  display: none !important;
}
</style> 