// import * as Cesium from "cesium";
import WKT from "terraformer-wkt-parser";
import { layerParentId, layerQueryInfo } from "./gisInfo";

/**
 * 将笛卡尔坐标转换为经纬度
 * @param cartesian3
 */
export const cartesian3ToDegree = (cartesian3) => {
  const cartographic = Cesium.Cartographic.fromCartesian(cartesian3);
  const lng = Cesium.Math.toDegrees(cartographic.longitude);
  const lat = Cesium.Math.toDegrees(cartographic.latitude);
  return [lng, lat];
};

/**
 * 将笛卡尔坐标数组转换为经纬度数组
 */
export const cartesian3sToDegrees = (cartesian3s) => {
  return cartesian3s.map((cartesian3) => cartesian3ToDegree(cartesian3));
};

export const getColorByCss = (
  CssColor,
  flash = false
) => {
  let alpha = 1;
  if (flash) {
    return new Cesium.CallbackProperty(function (time) {
      alpha =
        time.secondsOfDay % 1 < 0.5
          ? 2 * (time.secondsOfDay % 1)
          : 2 * (1 - (time.secondsOfDay % 1));
      return Cesium.Color.fromCssColorString(CssColor).withAlpha(alpha);
    }, false);
  } else {
    return Cesium.Color.fromCssColorString(CssColor);
  }
};

// 获取最小级别的图层编号
export const formatSelectLayers = (data) => {
  const card = [
    ...layerQueryInfo["misMonitor"],
    ...layerQueryInfo["misHeatMonitor"],
    ...layerQueryInfo["misDrainWaterMonitor"],
    ...layerQueryInfo["misSupplyWaterMonitor"],
  ];
  const newCard = data.filter((item) => card.indexOf(item) > -1);
  return newCard;
};

/**
 * 检查坐标是否在有效范围内
 * @param lng
 * @param lat
 */
export const checkCoordinate = (lng, lat) => {
  return (
    (lng &&
      parseFloat(lng.toString()) > 73 &&
      parseFloat(lng.toString()) < 136 &&
      lat &&
      parseFloat(lat.toString()) > 3.8 &&
      parseFloat(lat.toString()) < 53.6)
  );
};

/**
 * 将wkt转为geojson
 * @param wkt
 */
export const wktToGeojson = (wkt) => {
  return WKT.parse(wkt);
};
/**
 * geojson转为wkt
 * @param geojson
 */
export const geojsonToWkt = (geojson) => {
  return WKT.convert(geojson);
};

export const formatDataByField = (data, field, value) => {
  const newArr = [];
  if (Array.isArray(data)) {
    data.map((item) => {
      let iconType = 1
      if (item?.alarmStatus && item?.alarmStatus !== "0") {
        iconType = 2
      } else if(item?.onlineStatus && item?.onlineStatus === 0) {
        iconType = 0
      }

      if (item.geomText) {
        const geometry = wktToGeojson(item?.geomText);
        if (checkCoordinate(geometry?.coordinates[0], geometry?.coordinates[1])) {
          item[field] = value + iconType;
          if (
              value === "gasStationRisk1" ||
              value === "gasStationRisk2" ||
              value === "gasStationRisk3" ||
              value === "gasStationRisk4" ||
              value === "misStationRisk1" ||
              value === "misStationRisk2" ||
              value === "misStationRisk3" ||
              value === "misStationRisk4"
          ) {
            item["id"] = item["stationId"];
          } else if (value === "gasEmergencyEvent") {
            item["status"] = item["dealStatus"];
          }
          // 搜索查询字段
          if (item?.stationName) {
            item["gisName"] = item["stationName"];
          } else if (item?.companyName) {
            item["gisName"] = item["companyName"];
          } else if (item?.userName) {
            item["gisName"] = item["userName"];
          } else if (item?.bottleId) {
            item["gisName"] = item["bottleId"];
          } else if (item?.adjustName) {
            item["gisName"] = item["adjustName"];
          } else if (item?.valveId) {
            item["gisName"] = item["valveId"];
          } else if (item?.resourceName) {
            item["gisName"] = item["resourceName"];
          } else if (item?.rescueName) {
            item["gisName"] = item["rescueName"];
          } else if (item?.dangerName) {
            item["gisName"] = item["dangerName"];
          } else if (item?.protectName) {
            item["gisName"] = item["protectName"];
          } else if (item?.name) {
            item["gisName"] = item["name"];
          } else if (item?.grade) {
            item["gisName"] = item["grade"];
          } else if (item?.eventName) {
            item["gisName"] = item["eventName"];
          } else if (item?.id) {
            item["gisName"] = item["id"];
          }

          newArr.push({
            ...item,
            longitude: geometry?.coordinates[0],
            latitude: geometry?.coordinates[1],
          });
        }
      } else if (checkCoordinate(item?.longitude, item?.latitude)) {
        item[field] = value + iconType;
        if (
          value === "gasStationRisk1" ||
          value === "gasStationRisk2" ||
          value === "gasStationRisk3" ||
          value === "gasStationRisk4" ||
          value === "misStationRisk1" ||
          value === "misStationRisk2" ||
          value === "misStationRisk3" ||
          value === "misStationRisk4"
        ) {
          item["id"] = item["stationId"];
        } else if (value === "gasEmergencyEvent") {
          item["status"] = item["dealStatus"];
        }
        // 搜索查询字段
        if (item?.stationName) {
          item["gisName"] = item["stationName"];
        } else if (item?.companyName) {
          item["gisName"] = item["companyName"];
        } else if (item?.userName) {
          item["gisName"] = item["userName"];
        } else if (item?.bottleId) {
          item["gisName"] = item["bottleId"];
        } else if (item?.adjustName) {
          item["gisName"] = item["adjustName"];
        } else if (item?.valveId) {
          item["gisName"] = item["valveId"];
        } else if (item?.resourceName) {
          item["gisName"] = item["resourceName"];
        } else if (item?.rescueName) {
          item["gisName"] = item["rescueName"];
        } else if (item?.dangerName) {
          item["gisName"] = item["dangerName"];
        } else if (item?.protectName) {
          item["gisName"] = item["protectName"];
        } else if (item?.name) {
          item["gisName"] = item["name"];
        } else if (item?.grade) {
          item["gisName"] = item["grade"];
        } else if (item?.eventName) {
          item["gisName"] = item["eventName"];
        } else if (item?.id) {
          item["gisName"] = item["id"];
        }

        newArr.push({
          ...item
        });
      }
    });
  }
  return newArr;
};

export const formatPolylineDataByFieldAndStyle = (data, field, value) => {
  const newArr = [];
  if (Array.isArray(data)) {
    data.map((item) => {
      if (
        // @ts-ignore
        checkCoordinate(item.longitudeStart, item.latitudeStart) &&
        // @ts-ignore
        checkCoordinate(item.longitudeEnd, item.latitudeEnd)
      ) {
        item[field] = value;
        item["degrees"] = [
          // @ts-ignore
          parseFloat(item.longitudeStart.toString()),
          // @ts-ignore
          parseFloat(item.latitudeStart.toString()),
          // @ts-ignore
          parseFloat(item.longitudeEnd.toString()),
          // @ts-ignore
          parseFloat(item.latitudeEnd.toString()),
        ];
        // pressureType = 0703高压红色，0702中压蓝色，0701低压黄色
        item["colorStr"] =
          item?.riskGrade === "150004"
            ? "#D9001B"
            : item?.riskGrade === "150003"
              ? "#F59A23"
              : item?.riskGrade === "150002"
                ? "#FFFF00"
                : item?.riskGrade === "150001"
                  ? "#0972F4"
                  : "#DA12FF";
        if (
          value === "gasPipeRisk1" ||
          value === "gasPipeRisk2" ||
          value === "gasPipeRisk3" ||
          value === "gasPipeRisk4" ||
          value === "misPipeRisk1" ||
          value === "misPipeRisk2" ||
          value === "misPipeRisk3" ||
          value === "misPipeRisk4"
        ) {
          item["id"] = item["lineDataId"];
        }
        // 搜索查询字段
        if (item?.lineId) {
          item["gisName"] = item["lineId"];
        }
        newArr.push({
          ...item,
        });
      }
    });
  }
  return newArr;
};
