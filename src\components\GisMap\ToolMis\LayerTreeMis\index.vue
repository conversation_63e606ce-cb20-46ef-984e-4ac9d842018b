<template>
  <div class="laytree-container" v-show="treeData.length > 0">
    <div class="title-header flex items-center justify-center">
      <div class="title-text">图层</div>
      <el-icon @click="HandleIsExpand()">
        <CaretTop v-show="state.isExpand" />
        <CaretBottom v-show="!state.isExpand" />
      </el-icon>
    </div>
    <div class="tree-box" v-show="state.isExpand">
      <el-tree
        ref="refCheckdata"
        :data="treeData"
        show-checkbox
        node-key="id"
        :expand-on-click-node="expandNode"
        check-on-click-node
        :props="defaultProps"
        @check="treeCheck"
        @check-change="onNodeClick"
      >
        <template #default="{ node, data }">
<!--          <svg-icon v-if="data.icon !== 'default'" :icon-class="data.icon" />-->
          <span class="label">
            {{ node.label }}
          </span>
        </template>
      </el-tree>
    </div>
  </div>
</template>

<script setup>
import { CaretTop, CaretBottom } from "@element-plus/icons-vue";
import { defaultCheckedLayers } from "@/hooks/gishooks";
import { ref, reactive, watch } from "vue";

const expandNode = ref(false);
const refCheckdata = ref(null);
const emits = defineEmits(['onClose', 'checkLayer']);
const defaultProps = ref({
  children: "children",
  label: "label",
});

const props = defineProps({
    treeData: {
        type: Array,
        required: true
    }
});

const state = reactive({
  isExpand: true, //默认展开
});

const HandleIsExpand = () => {
  state.isExpand = !state.isExpand;
};

const onNodeClick = (node, isChecked) => {
  if (!node.children) {
    emits("checkLayer", node.id, isChecked);
  }
};

const treeCheck = (data, item) => {
  defaultCheckedLayers.value = refCheckdata.value?.getCheckedKeys();
};

watch(
  () => defaultCheckedLayers.value,
  (ck) => {
    refCheckdata.value &&
      refCheckdata.value?.setCheckedKeys(defaultCheckedLayers.value);
  },
  {
    immediate: true,
    deep: true,
  }
);
</script>

<style lang="scss" scoped>
.laytree-container {
  position: relative;
  background-size: 100% 100%;
  background: rgba(255, 255, 255, 0.8);
  box-shadow: 0px 3px 4px 0px rgba(0, 0, 0, 0.2);
  border-radius: 5px;
  padding: 8px 15px;
  min-width: 110px;
  .title-header {
    .title-text {
      font-size: 14px;
      font-weight: 600;
      text-align: center;
      color: var(--el-color-primary);
    }
    :deep(.el-icon) {
      width: 16px;
      height: 16px;
      color: var(--el-color-primary);
      cursor: pointer;
      margin-left: 10px;
    }
  }
  .tree-box {
    padding-top: 10px;
  }

  :deep(.el-tree) {
    position: relative;
    cursor: default;
    background: none;
    --el-tree-expand-icon-color: var(--el-color-primary);
    --el-tree-node__expand-icon: 50px;
    .el-checkbox {
      --el-checkbox-checked-icon-color: #fff !important;
    }
    .el-tree-node__expand-icon {
      display: none;
    }

    .el-tree-node {
      //未勾选字体颜色
      .label {
        color: #000;
      }
    }
    //勾选字体颜色
    .is-checked {
      .label {
        color: #000 !important;
      }
    }
    // 节点文本属性
    .label {
      padding-left: 5px;
      font-size: 14px;
      font-weight: 500;
    }
    //鼠标悬停颜色
    .el-tree-node__content:hover {
      background-color: unset !important;
    }

    //  改变节点高度
    .el-tree-node__content {
      height: 26px;
    }

    /* 改变被点击节点背景颜色，字体颜色 */
    .el-tree-node:focus > .el-tree-node__content {
      background-color: unset !important;
    }

    // 节点 checkbox 颜色
    .el-checkbox__inner {
      border: 1px solid var(--el-color-primary) !important;
    }
  }
}

.svg-icon {
  width: 18px;
  height: 18px;
}
</style>
