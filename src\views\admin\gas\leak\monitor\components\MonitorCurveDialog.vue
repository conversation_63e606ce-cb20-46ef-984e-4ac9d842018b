<template>
  <el-dialog
    v-model="dialogVisible"
    title="监测曲线"
    width="900px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="monitor-curve-dialog"
  >
    <div class="curve-container">
      <!-- 顶部控制区域 -->
      <div class="control-container">
        <!-- 指标选择器 -->
        <div class="indicator-selector" v-if="monitorIndicators.length > 0">
          <el-select v-model="activeIndicatorId" placeholder="请选择监测指标" style="width: 180px">
            <el-option
              v-for="item in monitorIndicators"
              :key="item.id"
              :label="item.monitorIndexName"
              :value="item.id"
            >
              <span>{{ item.monitorIndexName }}</span>
              <span class="indicator-unit" v-if="item.measureUnit">({{ item.measureUnit }})</span>
            </el-option>
          </el-select>
        </div>
        
        <!-- 时间选项卡 -->
        <div class="time-tabs">
          <div
            v-for="(tab, index) in timeTabs"
            :key="index"
            :class="['time-tab', { active: activeTimeTab === index }]"
            @click="handleTimeTabChange(index)"
          >
            {{ tab.label }}
          </div>
        </div>
        
        <!-- 单位显示 -->
        <div class="unit-display" v-if="unitText">单位: {{ unitText }}</div>
      </div>

      <!-- 图表区域 -->
      <div class="chart-wrapper">
        <div ref="chartRef" class="chart-container"></div>
      </div>
      
      <!-- 无数据提示 -->
      <div class="no-data-tip" v-if="chartData.length === 0 && !chartInstance">
        <el-empty description="暂无监测数据" />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关 闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, onMounted, watch, nextTick } from 'vue';
import { getMonitorCurveData, getMonitorIndicators } from '@/api/gas';
import * as echarts from 'echarts';
import moment from 'moment';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  deviceData: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible']);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 图表引用
const chartRef = ref(null);
let chartInstance = null;

// 时间选项卡
const timeTabs = [
  { label: '最近24小时', days: 1 },
  { label: '最近7天', days: 7 },
  { label: '最近30天', days: 30 }
];
const activeTimeTab = ref(0);
const unitText = ref('');

// 图表数据
const chartData = ref([]);

// 监测指标数据
const monitorIndicators = ref([]);
// 当前选中的监测指标ID
const activeIndicatorId = ref(null);
// 当前选中的监测指标对象
const activeIndicator = computed(() => {
  if (!activeIndicatorId.value || monitorIndicators.value.length === 0) return null;
  return monitorIndicators.value.find(item => item.id === activeIndicatorId.value) || null;
});

// 颜色配置
const CHART_COLORS = [
  '#4278FF', // 蓝色
  '#36CE9E', // 绿色
  '#FFC543', // 黄色
  '#FF6B6B', // 红色
  '#8E65FF', // 紫色
  '#FF9A3C', // 橙色
  '#3ECBFF', // 天蓝色
  '#5D7092'  // 灰蓝色
];

// 状态颜色配置
const STATUS_COLORS = {
  normal: '#36CE9E', // 正常状态 - 绿色
  abnormal: '#FF6B6B' // 异常状态 - 红色
};

// 初始化图表
const initChart = () => {
  if (chartInstance) {
    chartInstance.dispose();
  }
  
  // 确保DOM元素已经渲染
  nextTick(() => {
    if (chartRef.value) {
      chartInstance = echarts.init(chartRef.value);
      updateChart();
    }
  });
};

// 更新图表
const updateChart = () => {
  if (!chartInstance || !activeIndicator.value) return;

  const xAxisData = chartData.value.map(item => formatTime(item.monitorTime));
  
  // 根据监测指标类型处理数据
  const isStatusType = activeIndicator.value.type === 0;
  const fieldName = activeIndicator.value.monitorField.toLowerCase();
  
  // 设置单位文本
  unitText.value = activeIndicator.value.measureUnit || '';
  
  let series = [];
  
  if (isStatusType) {
    // 状态型指标 - 使用散点图表示正常/异常状态
    const normalData = [];
    const abnormalData = [];
    
    chartData.value.forEach((item, index) => {
      const value = item[fieldName];
      // 0表示正常，1表示异常
      if (value === 0) {
        normalData.push([xAxisData[index], 1]); // 固定值1表示正常
      } else if (value === 1) {
        abnormalData.push([xAxisData[index], 2]); // 固定值2表示异常
      }
    });
    
    series = [
      {
        name: '正常',
        type: 'scatter',
        symbol: 'circle',
        symbolSize: 10,
        itemStyle: {
          color: STATUS_COLORS.normal
        },
        data: normalData
      },
      {
        name: '异常',
        type: 'scatter',
        symbol: 'circle',
        symbolSize: 10,
        itemStyle: {
          color: STATUS_COLORS.abnormal
        },
        data: abnormalData
      }
    ];
  } else {
    // 数值型指标 - 使用折线图
    const seriesData = chartData.value.map(item => item[fieldName]);
    
    series = [
      {
        name: activeIndicator.value.monitorIndexName,
        type: 'line',
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        sampling: 'average',
        itemStyle: {
          color: CHART_COLORS[0]
        },
        lineStyle: {
          width: 2
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: 'rgba(66, 120, 255, 0.3)'
            },
            {
              offset: 1,
              color: 'rgba(66, 120, 255, 0.05)'
            }
          ])
        },
        data: seriesData
      }
    ];
  }
  
  const option = {
    tooltip: {
      trigger: 'axis',
      formatter: function(params) {
        const data = params[0];
        let valueText = '';
        
        if (isStatusType) {
          // 状态型指标
          const statusText = data.seriesName === '正常' ? '正常' : '异常';
          valueText = `${activeIndicator.value.monitorIndexName}: ${statusText}`;
        } else {
          // 数值型指标
          valueText = `${activeIndicator.value.monitorIndexName}: ${data.value}${unitText.value}`;
        }
        
        return `${data.name}<br/>${data.marker}${valueText}`;
      }
    },
    legend: isStatusType ? {
      data: ['正常', '异常'],
      right: 10,
      top: 0
    } : null,
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: xAxisData,
      axisLine: {
        lineStyle: {
          color: '#E0E0E0'
        }
      },
      axisLabel: {
        color: '#666',
        formatter: function(value) {
          // 根据时间跨度调整显示格式
          if (activeTimeTab.value === 0) {
            return moment(value).format('HH:mm');
          } else {
            return moment(value).format('MM-DD');
          }
        }
      }
    },
    yAxis: isStatusType ? {
      type: 'value',
      min: 0,
      max: 3,
      interval: 1,
      axisLabel: {
        formatter: function(value) {
          if (value === 1) return '正常';
          if (value === 2) return '异常';
          return '';
        },
        color: '#666'
      },
      splitLine: {
        lineStyle: {
          color: '#E9E9E9',
          type: 'dashed'
        }
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: '#E0E0E0'
        }
      }
    } : {
      type: 'value',
      axisLine: {
        show: true,
        lineStyle: {
          color: '#E0E0E0'
        }
      },
      splitLine: {
        lineStyle: {
          color: '#E9E9E9',
          type: 'dashed'
        }
      },
      axisLabel: {
        color: '#666',
        formatter: function(value) {
          return value;
        }
      }
    },
    series: series
  };

  chartInstance.setOption(option);
};

// 格式化时间
const formatTime = (time) => {
  if (!time) return '';
  
  // 处理后端返回的Java时间对象
  if (typeof time === 'object' && time !== null) {
    // 根据Java时间对象的结构构建日期
    const year = time.year + 1900; // Java年份从1900开始
    const month = time.month; // Java月份是0-11
    const date = time.date;
    const hours = time.hours;
    const minutes = time.minutes;
    const seconds = time.seconds;
    
    return new Date(year, month, date, hours, minutes, seconds).toISOString();
  }
  
  return time;
};

// 处理时间选项卡变化
const handleTimeTabChange = (index) => {
  activeTimeTab.value = index;
  fetchCurveData();
};

// 获取监测指标
const fetchMonitorIndicators = async () => {
  if (!props.deviceData || !props.deviceData.id) return;
  
  try {
    const res = await getMonitorIndicators(props.deviceData.id);
    if (res && res.code === 200 && res.data && res.data.length > 0) {
      monitorIndicators.value = res.data;
      // 默认选择第一个指标
      activeIndicatorId.value = res.data[0].id;
      // 获取曲线数据
      fetchCurveData();
    }
  } catch (error) {
    console.error('获取监测指标失败', error);
  }
};

// 获取曲线数据
const fetchCurveData = async () => {
  if (!props.deviceData || !props.deviceData.id || !activeIndicator.value) return;
  
  // 计算时间范围
  const endTime = moment().format('YYYY-MM-DD HH:mm:ss');
  const startTime = moment().subtract(timeTabs[activeTimeTab.value].days, 'days').format('YYYY-MM-DD HH:mm:ss');
  
  try {
    const params = {
      deviceId: props.deviceData.id,
      startTime,
      endTime,
      type: activeTimeTab.value,
      // 添加监测指标字段，确保获取正确的指标数据
      monitorField: activeIndicator.value.monitorField
    };
    
    const res = await getMonitorCurveData(params);
    if (res && res.code === 200) {
      chartData.value = res.data || [];
      updateChart();
    }
  } catch (error) {
    console.error('获取监测曲线数据失败', error);
  }
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
};

// 监听设备数据变化
watch(() => props.deviceData, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    fetchMonitorIndicators();
  }
}, { deep: true });

// 监听对话框可见性变化
watch(() => dialogVisible.value, (val) => {
  if (val) {
    nextTick(() => {
      initChart();
    });
  }
});

// 监听当前选中的指标ID变化
watch(() => activeIndicatorId.value, () => {
  // 当指标变化时，重新获取曲线数据
  fetchCurveData();
});

// 组件挂载完成
onMounted(() => {
  if (dialogVisible.value) {
    initChart();
  }
});
</script>

<style scoped>
.monitor-curve-dialog {
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "Helvetica Neue", Arial, sans-serif;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

.curve-container {
  width: 100%;
  height: 100%;
}

.control-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.indicator-selector {
  margin-right: 16px;
}

.indicator-unit {
  color: #909399;
  margin-left: 4px;
  font-size: 12px;
}

.time-tabs {
  display: flex;
  background-color: #F5F7FA;
  border-radius: 4px;
  overflow: hidden;
}

.time-tab {
  padding: 8px 16px;
  font-size: 14px;
  color: #606266;
  cursor: pointer;
  transition: all 0.3s;
}

.time-tab.active {
  background-color: #4699FF;
  color: white;
}

.unit-display {
  font-size: 14px;
  color: #909399;
  margin-left: 16px;
}

.chart-wrapper {
  width: 100%;
  height: 350px;
  background-color: #fff;
  border-radius: 4px;
  border: 1px solid #E4E7ED;
  position: relative;
}

.chart-container {
  width: 100%;
  height: 100%;
}

.no-data-tip {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.95);
  z-index: 1;
  pointer-events: none;
}
</style>