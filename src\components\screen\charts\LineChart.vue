<template>
  <div class="line-chart-container">
    <div ref="chartRef" class="chart-content"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  series: {
    type: Array,
    required: true,
    // [{name: '系列1', data: [120, 200, 150]}, {name: '系列2', data: [130, 210, 160]}]
  },
  xAxisData: {
    type: Array,
    default: () => []
  },
  showArea: {
    type: Boolean,
    default: true
  },
  smooth: {
    type: Boolean,
    default: true
  },
  colorList: {
    type: Array,
    default: () => [
      ['#00F2F1', '#0066FF'], // 蓝色渐变
      ['#FFD24D', '#FF8F35'], // 橙色渐变
      ['#35FF6B', '#00B36B'], // 绿色渐变
      ['#FF59C8', '#FF0066'], // 粉色渐变
      ['#B36CFF', '#7700FF'], // 紫色渐变
    ]
  },
  showSymbol: {
    type: Boolean,
    default: true
  },
  showLegend: {
    type: Boolean,
    default: true
  },
  unit: {
    type: String,
    default: ''
  },
  showYAxisLabel: {
    type: Boolean,
    default: true
  },
  yAxisMax: {
    type: [Number, String],
    default: null
  },
  yAxisMin: {
    type: [Number, String],
    default: null
  }
})

const chartRef = ref(null)
let chartInstance = null

onMounted(async () => {
  await nextTick()
  if (chartRef.value) {
    initChart()
  }
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
  window.removeEventListener('resize', handleResize)
})

watch(() => props.series, () => {
  if (chartInstance) {
    updateChart()
  }
}, { deep: true })

watch(() => props.xAxisData, () => {
  if (chartInstance) {
    updateChart()
  }
}, { deep: true })

watch(() => [props.showArea, props.smooth, props.showSymbol, props.showLegend, props.title, props.unit], () => {
  if (chartInstance) {
    updateChart()
  }
}, { deep: true })

const initChart = () => {
  try {
    if (chartInstance) {
      chartInstance.dispose()
    }
    
    chartInstance = echarts.init(chartRef.value)
    
    window.addEventListener('resize', handleResize)
    
    updateChart()
    chartInstance.resize()
  } catch (error) {
    console.error('初始化折线图失败:', error)
  }
}

const handleResize = () => {
  if (chartInstance) {
    try {
      chartInstance.resize()
    } catch (error) {
      console.error('调整折线图大小失败:', error)
    }
  }
}

const updateChart = () => {
  if (!chartInstance) return
  
  try {
    // 创建本地副本
    const localSeries = JSON.parse(JSON.stringify(props.series))
    const localTitle = props.title
    const localXAxisData = JSON.parse(JSON.stringify(props.xAxisData))
    
    // 配置系列
    const series = localSeries.map((item, index) => {
      const colorPair = props.colorList[index % props.colorList.length]
      
      // 基本折线配置
      const seriesConfig = {
        name: item.name,
        type: 'line',
        data: item.data,
        smooth: props.smooth,
        symbol: props.showSymbol ? 'circle' : 'none',
        symbolSize: 6,
        itemStyle: {
          color: colorPair[0]
        },
        lineStyle: {
          color: colorPair[0],
          width: 2
        }
      }
      
      // 如果需要显示面积，添加面积样式
      if (props.showArea) {
        seriesConfig.areaStyle = {
          color: {
            type: 'linear',
            x: 0, y: 0, x2: 0, y2: 1,
            colorStops: [
              { offset: 0, color: colorPair[0].replace(')', ', 0.3)').replace('rgb', 'rgba') },
              { offset: 1, color: colorPair[1].replace(')', ', 0.05)').replace('rgb', 'rgba') }
            ]
          }
        }
      }
      
      return seriesConfig
    })
    
    // 折线图配置选项
    const option = {
      backgroundColor: 'transparent',
      title: localTitle ? {
        text: localTitle,
        left: 'center',
        top: 10,
        textStyle: {
          color: '#FFFFFF',
          fontSize: 14,
          fontWeight: 'normal',
          fontFamily: 'PingFangSC, PingFang SC'
        }
      } : null,
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'line',
          lineStyle: {
            color: 'rgba(255,255,255,0.3)',
            width: 1,
            type: 'solid'
          }
        },
        formatter: function(params) {
          let result = `${params[0].name}<br/>`
          params.forEach(param => {
            result += `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${param.color};"></span>${param.seriesName}: ${param.value}${props.unit}<br/>`
          })
          return result
        }
      },
      legend: {
        show: props.showLegend && localSeries.length > 1,
        top: props.title ? '10%' : '5%',
        right: '4%',
        itemWidth: 10,
        itemHeight: 10,
        textStyle: {
          color: 'rgba(255,255,255,0.7)',
          fontSize: 10
        },
        data: localSeries.map(item => item.name)
      },
      grid: {
        top: props.showLegend && localSeries.length > 1 ? '25%' : (props.title ? '15%' : '5%'),
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: localXAxisData,
        axisLine: {
          lineStyle: { color: 'rgba(255,255,255,0.5)' }
        },
        axisLabel: { 
          color: 'rgba(255,255,255,0.7)',
          fontSize: 10,
          rotate: localXAxisData.length > 8 ? 45 : 0
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: 'rgba(255,255,255,0.1)',
            type: 'dashed'
          }
        }
      },
      yAxis: {
        type: 'value',
        min: props.yAxisMin !== null ? props.yAxisMin : undefined,
        max: props.yAxisMax !== null ? props.yAxisMax : undefined,
        axisLine: {
          lineStyle: { color: 'rgba(255,255,255,0.5)' }
        },
        axisLabel: { 
          show: props.showYAxisLabel,
          color: 'rgba(255,255,255,0.7)',
          fontSize: 10,
          formatter: `{value}${props.unit}`
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: 'rgba(255,255,255,0.1)',
            type: 'dashed'
          }
        }
      },
      series: series
    }
    
    chartInstance.setOption(option, true)
  } catch (error) {
    console.error('更新折线图失败:', error)
  }
}
</script>

<style scoped>
.line-chart-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 160px;
}

.chart-content {
  width: 100%;
  height: 100%;
  min-height: 160px;
}
</style> 