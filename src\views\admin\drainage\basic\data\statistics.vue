<template>
  <div class="drainage-statistics-container" ref="containerRef">
    <!-- 查询条件 -->
    <div class="search-section">
      <el-form :model="searchForm" inline class="search-form">
        <div class="form-left">
          <el-form-item label="所属单位：">
            <el-select v-model="searchForm.managementUnit" placeholder="请选择" clearable style="width: 200px">
              <el-option v-for="item in enterpriseList" :key="item.id" :label="item.enterpriseName" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="所属区域：">
            <el-select v-model="searchForm.town" placeholder="请选择" clearable style="width: 200px">
              <el-option v-for="area in areaOptions" :key="area.code" :label="area.name" :value="area.code" />
              <template v-for="area in areaOptions" :key="area.code">
                <el-option v-for="child in area.children" :key="child.code" :label="`${area.name} - ${child.name}`"
                  :value="child.code" />
              </template>
            </el-select>
          </el-form-item>
        </div>
        <div class="form-right">
          <el-button type="primary" @click="exportImage" :loading="exportingImage">
            导出图片
          </el-button>
          <el-button type="primary" @click="exportExcel" :loading="exportingExcel">
            导出表格
          </el-button>
        </div>
      </el-form>
    </div>
    <div class="statistics-section-container">
      <!-- 基础数据统计 -->
      <div class="statistics-section">
        <!-- 雨水 -->
        <div class="statistics-card rain-card">
          <div class="card-header">
            <h3 class="card-title">雨水</h3>
          </div>
          <div class="statistics-content">
            <div class="stat-item">
              <span class="stat-label">雨水管网：</span>
              <span class="stat-value">{{ rainStatistics.rainPipeLength || 0 }} km</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">泵站数：</span>
              <span class="stat-value">{{ rainStatistics.rainStationCount || 0 }} 个</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">排水口：</span>
              <span class="stat-value">{{ rainStatistics.outletCount || 0 }} 个</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">雨水井：</span>
              <span class="stat-value">{{ rainStatistics.rainWellCount || 0 }} 个</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">CCTV检测：</span>
              <span class="stat-value">{{ rainStatistics.rainCctvLength || 0 }} km</span>
            </div>
          </div>
        </div>

        <!-- 污水 -->
        <div class="statistics-card sewage-card">
          <div class="card-header">
            <h3 class="card-title">污水</h3>
          </div>
          <div class="statistics-content">
            <div class="stat-item">
              <span class="stat-label">污水管网：</span>
              <span class="stat-value">{{ sewageStatistics.sewagePipeLength || 0 }} km</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">污水厂：</span>
              <span class="stat-value">{{ sewageStatistics.factoryCount || 0 }} 个</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">污水泵站：</span>
              <span class="stat-value">{{ sewageStatistics.sewageStationCount || 0 }} 个</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">污水井：</span>
              <span class="stat-value">{{ sewageStatistics.sewageWellCount || 0 }} 个</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">CCTV检测：</span>
              <span class="stat-value">{{ sewageStatistics.sewageCctvLength || 0 }} km</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 图表统计 -->
      <div class="charts-section">
        <!-- 管网管龄统计 -->
        <div class="chart-card">
          <div class="chart-header">
            <h3 class="chart-title">管网管龄统计</h3>
            <div class="chart-tabs">
              <el-button :type="ageChartType === 'rain' ? 'primary' : ''" @click="switchAgeChart('rain')" size="small">
                雨水
              </el-button>
              <el-button :type="ageChartType === 'sewage' ? 'primary' : ''" @click="switchAgeChart('sewage')"
                size="small">
                污水
              </el-button>
            </div>
          </div>
          <div class="chart-content">
            <div ref="ageChartRef" class="chart-container"></div>
          </div>
        </div>

        <!-- 管网材质统计 -->
        <div class="chart-card">
          <div class="chart-header">
            <h3 class="chart-title">管网材质统计</h3>
            <div class="chart-tabs">
              <el-button :type="materialChartType === 'rain' ? 'primary' : ''" @click="switchMaterialChart('rain')"
                size="small">
                雨水
              </el-button>
              <el-button :type="materialChartType === 'sewage' ? 'primary' : ''" @click="switchMaterialChart('sewage')"
                size="small">
                污水
              </el-button>
            </div>
          </div>
          <div class="chart-content">
            <div ref="materialChartRef" class="chart-container"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick, onBeforeUnmount } from 'vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'
import html2canvas from 'html2canvas'
import * as XLSX from 'xlsx'
import { saveAs } from 'file-saver'
import {
  getEnterpriseList,
  getRainStatistics,
  getSewageStatistics,
  getRainAgeStatistics,
  getSewageAgeStatistics,
  getRainMaterialStatistics,
  getSewageMaterialStatistics
} from '@/api/drainage'
import { AREA_OPTIONS } from '@/constants/gas'

// 响应式数据
const searchForm = reactive({
  managementUnit: '',
  town: ''
})

const containerRef = ref(null)
const enterpriseList = ref([])
const areaOptions = ref(AREA_OPTIONS)
const rainStatistics = ref({})
const sewageStatistics = ref({})
const ageChartType = ref('rain')
const materialChartType = ref('rain')
const exportingImage = ref(false)
const exportingExcel = ref(false)

// 图表实例
const ageChartRef = ref(null)
const materialChartRef = ref(null)
let ageChart = null
let materialChart = null

// 图表数据
const rainAgeData = ref([])
const sewageAgeData = ref([])
const rainMaterialData = ref([])
const sewageMaterialData = ref([])

// 获取企业列表
const fetchEnterpriseList = async () => {
  try {
    const response = await getEnterpriseList()
    if (response.code === 200) {
      enterpriseList.value = response.data || []
    }
  } catch (error) {
    console.error('获取企业列表失败:', error)
    ElMessage.error('获取企业列表失败')
  }
}

// 获取雨水统计数据
const fetchRainStatistics = async () => {
  try {
    const response = await getRainStatistics(searchForm)
    if (response.code === 200) {
      rainStatistics.value = response.data || {}
    }
  } catch (error) {
    console.error('获取雨水统计数据失败:', error)
    ElMessage.error('获取雨水统计数据失败')
  }
}

// 获取污水统计数据
const fetchSewageStatistics = async () => {
  try {
    const response = await getSewageStatistics(searchForm)
    if (response.code === 200) {
      sewageStatistics.value = response.data || {}
    }
  } catch (error) {
    console.error('获取污水统计数据失败:', error)
    ElMessage.error('获取污水统计数据失败')
  }
}

// 获取管龄统计数据
const fetchAgeStatistics = async () => {
  try {
    const [rainResponse, sewageResponse] = await Promise.all([
      getRainAgeStatistics(searchForm),
      getSewageAgeStatistics(searchForm)
    ])

    if (rainResponse.code === 200) {
      rainAgeData.value = rainResponse.data || []
    }
    if (sewageResponse.code === 200) {
      sewageAgeData.value = sewageResponse.data || []
    }

    // 更新图表
    updateAgeChart()
  } catch (error) {
    console.error('获取管龄统计数据失败:', error)
    ElMessage.error('获取管龄统计数据失败')
  }
}

// 获取材质统计数据
const fetchMaterialStatistics = async () => {
  try {
    const [rainResponse, sewageResponse] = await Promise.all([
      getRainMaterialStatistics(searchForm),
      getSewageMaterialStatistics(searchForm)
    ])

    if (rainResponse.code === 200) {
      rainMaterialData.value = rainResponse.data || []
    }
    if (sewageResponse.code === 200) {
      sewageMaterialData.value = sewageResponse.data || []
    }

    // 更新图表
    updateMaterialChart()
  } catch (error) {
    console.error('获取材质统计数据失败:', error)
    ElMessage.error('获取材质统计数据失败')
  }
}

// 初始化管龄图表
const initAgeChart = () => {
  if (ageChartRef.value) {
    ageChart = echarts.init(ageChartRef.value)
    updateAgeChart()

    // 监听窗口大小变化
    window.addEventListener('resize', () => {
      ageChart?.resize()
    })
  }
}

// 初始化材质图表
const initMaterialChart = () => {
  if (materialChartRef.value) {
    materialChart = echarts.init(materialChartRef.value)
    updateMaterialChart()

    // 监听窗口大小变化
    window.addEventListener('resize', () => {
      materialChart?.resize()
    })
  }
}

// 更新管龄图表
const updateAgeChart = () => {
  if (!ageChart) return

  const data = ageChartType.value === 'rain' ? rainAgeData.value : sewageAgeData.value
  const chartData = data.map(item => ({
    name: item.name,
    value: item.percent
  }))

  const option = {
    title: {
      text: '管龄',
      left: '37%',
      top: 'middle',
      textStyle: {
        fontSize: 20,
        fontWeight: 'bold',
        color: '#333'
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c}%'
    },
    legend: {
      orient: 'vertical',
      right: '5%',
      top: 'center',
      itemWidth: 10,
      itemHeight: 10,
      textStyle: {
        fontSize: 12
      }
    },
    series: [
      {
        type: 'pie',
        radius: ['45%', '65%'],
        center: ['40%', '50%'],
        avoidLabelOverlap: false,
        label: {
          show: false
        },
        labelLine: {
          show: false
        },
        data: chartData,
        color: ['#3DA8F5', '#36D1DC', '#5B8FF9', '#61DDAA']
      }
    ]
  }

  ageChart.setOption(option, true)
}

// 更新材质图表
const updateMaterialChart = () => {
  if (!materialChart) return

  const data = materialChartType.value === 'rain' ? rainMaterialData.value : sewageMaterialData.value
  const chartData = data.map(item => ({
    name: item.name,
    value: item.percent
  }))

  const option = {
    title: {
      text: '材质',
      left: '37%',
      top: 'middle',
      textStyle: {
        fontSize: 20,
        fontWeight: 'bold',
        color: '#333'
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c}%'
    },
    legend: {
      orient: 'vertical',
      right: '5%',
      top: 'center',
      itemWidth: 10,
      itemHeight: 10,
      textStyle: {
        fontSize: 12
      }
    },
    series: [
      {
        type: 'pie',
        radius: ['45%', '65%'],
        center: ['40%', '50%'],
        avoidLabelOverlap: false,
        label: {
          show: false
        },
        labelLine: {
          show: false
        },
        data: chartData,
        color: ['#3DA8F5', '#36D1DC', '#5B8FF9', '#61DDAA']
      }
    ]
  }

  materialChart.setOption(option, true)
}

// 切换管龄图表类型
const switchAgeChart = (type) => {
  ageChartType.value = type
  updateAgeChart()
}

// 切换材质图表类型
const switchMaterialChart = (type) => {
  materialChartType.value = type
  updateMaterialChart()
}

// 搜索
const handleSearch = () => {
  fetchAllData()
}

// 导出图片
const exportImage = async () => {
  if (!containerRef.value) return

  exportingImage.value = true
  try {
    const canvas = await html2canvas(containerRef.value, {
      backgroundColor: '#ffffff',
      scale: 2,
      useCORS: true,
      allowTaint: true,
      height: containerRef.value.scrollHeight,
      windowWidth: containerRef.value.scrollWidth,
      windowHeight: containerRef.value.scrollHeight
    })

    // 创建下载链接
    const link = document.createElement('a')
    link.download = `排水基础数据统计分析_${new Date().toLocaleDateString()}.png`
    link.href = canvas.toDataURL()
    link.click()

    ElMessage.success('图片导出成功')
  } catch (error) {
    console.error('导出图片失败:', error)
    ElMessage.error('导出图片失败')
  } finally {
    exportingImage.value = false
  }
}

// 导出表格
const exportExcel = async () => {
  exportingExcel.value = true
  try {
    const workbook = XLSX.utils.book_new()

    // 基础统计数据表
    const basicData = [
      ['项目', '雨水', '污水'],
      ['管网长度(km)', rainStatistics.value.rainPipeLength || 0, sewageStatistics.value.sewagePipeLength || 0],
      ['泵站数(个)', rainStatistics.value.rainStationCount || 0, sewageStatistics.value.sewageStationCount || 0],
      ['排水口(个)', rainStatistics.value.outletCount || 0, '-'],
      ['污水厂(个)', '-', sewageStatistics.value.factoryCount || 0],
      ['井数(个)', rainStatistics.value.rainWellCount || 0, sewageStatistics.value.sewageWellCount || 0],
      ['CCTV检测(km)', rainStatistics.value.rainCctvLength || 0, sewageStatistics.value.sewageCctvLength || 0]
    ]
    const basicSheet = XLSX.utils.aoa_to_sheet(basicData)
    XLSX.utils.book_append_sheet(workbook, basicSheet, '基础统计数据')

    // 雨水管龄统计表
    const rainAgeTableData = [
      ['管龄', '长度(km)', '占比(%)'],
      ...rainAgeData.value.map(item => [item.name, item.length || 0, item.percent || 0])
    ]
    const rainAgeSheet = XLSX.utils.aoa_to_sheet(rainAgeTableData)
    XLSX.utils.book_append_sheet(workbook, rainAgeSheet, '雨水管龄统计')

    // 污水管龄统计表
    const sewageAgeTableData = [
      ['管龄', '长度(km)', '占比(%)'],
      ...sewageAgeData.value.map(item => [item.name, item.length || 0, item.percent || 0])
    ]
    const sewageAgeSheet = XLSX.utils.aoa_to_sheet(sewageAgeTableData)
    XLSX.utils.book_append_sheet(workbook, sewageAgeSheet, '污水管龄统计')

    // 雨水材质统计表
    const rainMaterialTableData = [
      ['材质', '长度(km)', '占比(%)'],
      ...rainMaterialData.value.map(item => [item.name, item.length || 0, item.percent || 0])
    ]
    const rainMaterialSheet = XLSX.utils.aoa_to_sheet(rainMaterialTableData)
    XLSX.utils.book_append_sheet(workbook, rainMaterialSheet, '雨水材质统计')

    // 污水材质统计表
    const sewageMaterialTableData = [
      ['材质', '长度(km)', '占比(%)'],
      ...sewageMaterialData.value.map(item => [item.name, item.length || 0, item.percent || 0])
    ]
    const sewageMaterialSheet = XLSX.utils.aoa_to_sheet(sewageMaterialTableData)
    XLSX.utils.book_append_sheet(workbook, sewageMaterialSheet, '污水材质统计')

    // 导出文件
    const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' })
    const data = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
    saveAs(data, `排水基础数据统计分析_${new Date().toLocaleDateString()}.xlsx`)

    ElMessage.success('表格导出成功')
  } catch (error) {
    console.error('导出表格失败:', error)
    ElMessage.error('导出表格失败')
  } finally {
    exportingExcel.value = false
  }
}

// 获取所有数据
const fetchAllData = async () => {
  await Promise.all([
    fetchRainStatistics(),
    fetchSewageStatistics(),
    fetchAgeStatistics(),
    fetchMaterialStatistics()
  ])
}

// 组件挂载后执行
onMounted(async () => {
  await fetchEnterpriseList()
  await fetchAllData()

  await nextTick()
  initAgeChart()
  initMaterialChart()
})

// 组件销毁前清理
onBeforeUnmount(() => {
  ageChart?.dispose()
  materialChart?.dispose()
})
</script>

<style scoped>
.drainage-statistics-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 400px);
}

.search-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-form {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
}

.form-left {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.form-right {
  display: flex;
  gap: 10px;
}

.statistics-section-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.statistics-section {
  display: flex;
  gap: 20px;
}

.statistics-card {
  flex: 1;
  background: white;
  border-radius: 8px;
  padding: 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.rain-card .card-header {
  background: linear-gradient(135deg, #74b9ff, #0984e3);
}

.sewage-card .card-header {
  background: linear-gradient(135deg, #00b894, #00a085);
}

.card-header {
  padding: 15px 20px;
  border-bottom: 1px solid #eee;
}

.card-title {
  margin: 0;
  color: white;
  font-size: 18px;
  font-weight: bold;
}

.statistics-content {
  padding: 20px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.stat-item:last-child {
  border-bottom: none;
}

.stat-label {
  color: #666;
  font-size: 14px;
}

.stat-value {
  color: #333;
  font-weight: bold;
  font-size: 16px;
}

.rain-card .stat-value {
  color: #0984e3;
}

.sewage-card .stat-value {
  color: #00a085;
}

.charts-section {
  display: flex;
  gap: 20px;
}

.chart-card {
  flex: 1;
  background: white;
  border-radius: 8px;
  padding: 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  min-height: 400px;
}

.chart-header {
  padding: 15px 20px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-title {
  margin: 0;
  color: #333;
  font-size: 16px;
  font-weight: bold;
}

.chart-tabs {
  display: flex;
  gap: 8px;
}

.chart-content {
  padding: 20px;
}

.chart-container {
  width: 100%;
  height: 300px;
}

/* 高度适配：910px-1079px范围内启用滚动 */
@media (min-height: 910px) and (max-height: 1079px) {
  .statistics-section-container {
    max-height: calc(100vh - 400px);
    overflow-y: auto;
  }

  .charts-section::-webkit-scrollbar {
    width: 0px;
  }

  .charts-section::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 0px;
  }

  .charts-section::-webkit-scrollbar-thumb {
    background: transparent;
  }

  .charts-section::-webkit-scrollbar-thumb:hover {
    background: transparent;
  }
}

/* 响应式布局 */
@media (max-width: 1200px) {
  .statistics-section {
    flex-direction: column;
  }

  .charts-section {
    flex-direction: column;
  }
}

@media (max-width: 768px) {
  .drainage-statistics-container {
    padding: 10px;
  }

  .search-form {
    flex-direction: column;
    align-items: stretch;
  }

  .form-left,
  .form-right {
    width: 100%;
    justify-content: center;
    margin-bottom: 10px;
  }

  .form-left .el-form-item {
    margin-bottom: 15px;
  }

  .chart-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .chart-container {
    height: 280px;
  }
}
</style>