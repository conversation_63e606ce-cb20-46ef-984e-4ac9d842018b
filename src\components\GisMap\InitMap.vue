<template>
  <div ref="cesiumDiv" class="cesiumDiv"></div>
</template>

<script setup>
// 地图方法层导入
import { initStates, resetStates } from "./mapStates";
import { shallowRef, onMounted, onUnmounted } from "vue";
import positiveX from "./images/earth-bg/px1.png";
import positiveY from "./images/earth-bg/pz.png";
import positiveZ from "./images/earth-bg/py.png";
import negativeX from "./images/earth-bg/nx1.png";
import negativeY from "./images/earth-bg/nz1.png";
import negativeZ from "./images/earth-bg/ny1.png";
const cesiumDiv = shallowRef(null);
let viewer = null;

const adjustmentPixel = () => {
  const supportImageRenderingPixelated =
    viewer.cesiumWidget._supportsImageRenderingPixelated;
  if (supportImageRenderingPixelated) {
    let vtxf_dpr = window.devicePixelRatio;
    while (vtxf_dpr >= 2.0) {
      vtxf_dpr /= 2.0;
    }
    viewer.resolutionScale = vtxf_dpr;
  }
};
const initMap = () => {
  viewer = new Cesium.Viewer(cesiumDiv.value, {
    shouldAnimate: false, // 开启时间轴，图标闪烁使用
    geocoder: false, // 是否显示 geocoder 小器件，右上角查询按钮
    sceneModePicker: false, // 是否显示 3D/2D 选择器
    baseLayerPicker: false, // 是否显示图层选择器
    navigationHelpButton: false, // 是否显示右上角的帮助按钮
    selectionIndicator: false,
    animation: false, // 是否创建动画小器件，左下角仪表
    timeline: false, // 是否显示时间轴
    homeButton: false, // 是否显示 Home 按钮
    vrButton: false, // VR
    infoBox: false, // 是否显示信息框
    fullscreenButton: false, // 是否显示全屏按钮
    imageryProvider: new Cesium.SingleTileImageryProvider({
      url: "data:image/gif;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVQImWNgYGBgAAAABQABh6FO1AAAAABJRU5ErkJggg==",
    }), // 默认图层，需要 icon token。去掉。
    sceneMode: Cesium.SceneMode.SCENE3D, // 初始场景模式
    skyBox: false, // 天空盒
    skyAtmosphere: false, // 大气层
    requestRenderMode: false, // 启用请求渲染模式
    contextOptions: {
      requestWebgl2: true,
    },
    // msaaSamples: 4
  });
  // 去掉左下角 logo 图标
  viewer.cesiumWidget.creditContainer.style.display = "none";
  // 注销默认鼠标点击事件
  viewer.cesiumWidget.screenSpaceEventHandler.removeInputAction(
    Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK
  );
  viewer.cesiumWidget.screenSpaceEventHandler.removeInputAction(
    Cesium.ScreenSpaceEventType.LEFT_CLICK
  );
  // 开启或关闭地形遮盖
  viewer.scene.globe.depthTestAgainstTerrain = true;
  // 模拟真实光照
  // viewer.scene.globe.enableLighting = true;

  // 3DTilesInspector 控制器
  // viewer.extend(Cesium.viewerCesium3DTilesInspectorMixin);

  //判断是否支持图像渲染像素化处理
   if (Cesium.FeatureDetection.supportsImageRenderingPixelated()) {
    viewer.resolutionScale = window.devicePixelRatio; // 屏幕分辨率;
  }

  // viewer.scene.screenSpaceCameraController.enableCollisionDetection = false  // 关闭相机碰撞检测

  // viewer.useBrowserRecommendedResolution = false; // 使用浏览器推荐的分辨率
  viewer.scene.fxaa = false; // 开启 fxaa 能够启用图片抗锯齿，可能会导致图片清晰度降低
  viewer.scene.postProcessStages.fxaa.enabled = false //// 抗锯齿，是：文字清晰

  if (viewer.scene.highDynamicRangeSupported) {
    viewer.scene.highDynamicRange = false; // 高动态范围
  }

  viewer.scene.skyBox = new Cesium.SkyBox({
    sources: {
      positiveX: positiveX,
      negativeX: negativeX,
      positiveY: positiveY,
      negativeY: negativeY,
      positiveZ: positiveZ,
      negativeZ: negativeZ,
    },
  });

  initStates({ viewer });
  console.log("viewer------->>>complete");
};

// 销毁地图
const destroyMap = () => {
  if (Cesium.defined(viewer)) {
    if (viewer.tileset) {
      viewer.tileset.destroy();
    }
    viewer.entities.removeAll();
    viewer.imageryLayers.removeAll();
    viewer.dataSources.removeAll();
    viewer.scene.layers.removeAll();

    // viewer.scene.primitives.removeAll(); //有bug

    let gl = viewer.scene.context._originalGLContext;
    gl.canvas.width = 1;
    gl.canvas.height = 1;
    gl.getExtension("WEBGL_lose_context").loseContext();
    gl = null;
    viewer.destroy(); // 销毁Viewer实例
    viewer = null;
    const cesiumContainer = document.getElementById("cesiumContainer");
    if (cesiumContainer) {
      cesiumContainer.remove(); // 移除与地图相关的DOM元素
    }
  }
};

onMounted(() => {
  try {
    initMap();
  } catch (error) {
    console.error(error);
  }
});
onUnmounted(() => {
  resetStates();
  destroyMap();
  console.log("viewer------->>>destroy");
});
defineExpose({});
</script>

<style lang="scss" scoped>
.cesiumDiv {
  position: relative;
  width: 100%;
  height: 100%;
  z-index: 1;
}

:deep(.geniuns-coordinates) {
  display: none !important;
}

:deep(.cesium-viewer-timelineContainer) {
  display: none !important;
}
</style>
