/**
 *    ████████                   ██                   ██       ██                  ██      ██
 *   ██░░░░░░██                 ░░                   ░██      ░██                 ░██     ░██
 *  ██      ░░   █████  ███████  ██ ██   ██  ██████  ░██   █  ░██  ██████  ██████ ░██     ░██
 * ░██          ██░░░██░░██░░░██░██░██  ░██ ██░░░░   ░██  ███ ░██ ██░░░░██░░██░░█ ░██  ██████
 * ░██    █████░███████ ░██  ░██░██░██  ░██░░█████   ░██ ██░██░██░██   ░██ ░██ ░  ░██ ██░░░██
 * ░░██  ░░░░██░██░░░░  ░██  ░██░██░██  ░██ ░░░░░██  ░████ ░░████░██   ░██ ░██    ░██░██  ░██
 *  ░░████████ ░░██████ ███  ░██░██░░██████ ██████   ░██░   ░░░██░░██████ ░███    ███░░██████
 *   ░░░░░░░░   ░░░░░░ ░░░   ░░ ░░  ░░░░░░ ░░░░░░    ░░       ░░  ░░░░░░  ░░░    ░░░  ░░░░░░
 *
 * @license
 * GeniusWorld Web SDK - http://zydlxx.cn:1234/
 * Version 4.0.0.B#develop-d3ff2330@202405231107
 *
 *
 * Copyright © 2020 - 2023 正元地理信息集团股份有限公司. All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
define(["./Matrix3-79d15570","./defaultValue-7b61670d","./EllipseGeometry-158fdbc2","./VertexFormat-6d750b6e","./Math-6acd1674","./Transforms-6a5d79d3","./Matrix2-d550732e","./RuntimeError-7dc4ea5a","./combine-bc3d0d90","./ComponentDatatype-e95dda25","./WebGLConstants-68839929","./EllipseGeometryLibrary-48098a0e","./GeometryAttribute-d24f9032","./GeometryAttributes-410c425f","./GeometryInstance-b3218e0a","./GeometryOffsetAttribute-4f73a4af","./GeometryPipeline-ab8f5560","./AttributeCompression-aa106b76","./EncodedCartesian3-e2f2e578","./IndexDatatype-7c192505","./IntersectionTests-044bd161","./Plane-e4eb0e88"],(function(e,t,i,r,o,n,a,s,l,d,m,u,c,p,y,_,x,G,h,f,g,E){"use strict";function b(e){const r=(e=t.defaultValue(e,t.defaultValue.EMPTY_OBJECT)).radius,o={center:e.center,semiMajorAxis:r,semiMinorAxis:r,ellipsoid:e.ellipsoid,height:e.height,extrudedHeight:e.extrudedHeight,granularity:e.granularity,vertexFormat:e.vertexFormat,stRotation:e.stRotation,shadowVolume:e.shadowVolume};this._ellipseGeometry=new i.EllipseGeometry(o),this._workerName="createCircleGeometry"}b.packedLength=i.EllipseGeometry.packedLength,b.pack=function(e,t,r){return i.EllipseGeometry.pack(e._ellipseGeometry,t,r)};const w=new i.EllipseGeometry({center:new e.Cartesian3,semiMajorAxis:1,semiMinorAxis:1}),A={center:new e.Cartesian3,radius:void 0,ellipsoid:e.Ellipsoid.clone(e.Ellipsoid.UNIT_SPHERE),height:void 0,extrudedHeight:void 0,granularity:void 0,vertexFormat:new r.VertexFormat,stRotation:void 0,semiMajorAxis:void 0,semiMinorAxis:void 0,shadowVolume:void 0};return b.unpack=function(o,n,a){const s=i.EllipseGeometry.unpack(o,n,w);return A.center=e.Cartesian3.clone(s._center,A.center),A.ellipsoid=e.Ellipsoid.clone(s._ellipsoid,A.ellipsoid),A.height=s._height,A.extrudedHeight=s._extrudedHeight,A.granularity=s._granularity,A.vertexFormat=r.VertexFormat.clone(s._vertexFormat,A.vertexFormat),A.stRotation=s._stRotation,A.shadowVolume=s._shadowVolume,t.defined(a)?(A.semiMajorAxis=s._semiMajorAxis,A.semiMinorAxis=s._semiMinorAxis,a._ellipseGeometry=new i.EllipseGeometry(A),a):(A.radius=s._semiMajorAxis,new b(A))},b.createGeometry=function(e){return i.EllipseGeometry.createGeometry(e._ellipseGeometry)},b.createShadowVolume=function(e,t,i){const o=e._ellipseGeometry._granularity,n=e._ellipseGeometry._ellipsoid,a=t(o,n),s=i(o,n);return new b({center:e._ellipseGeometry._center,radius:e._ellipseGeometry._semiMajorAxis,ellipsoid:n,stRotation:e._ellipseGeometry._stRotation,granularity:o,extrudedHeight:a,height:s,vertexFormat:r.VertexFormat.POSITION_ONLY,shadowVolume:!0})},Object.defineProperties(b.prototype,{rectangle:{get:function(){return this._ellipseGeometry.rectangle}},textureCoordinateRotationPoints:{get:function(){return this._ellipseGeometry.textureCoordinateRotationPoints}}}),function(i,r){return t.defined(r)&&(i=b.unpack(i,r)),i._ellipseGeometry._center=e.Cartesian3.clone(i._ellipseGeometry._center),i._ellipseGeometry._ellipsoid=e.Ellipsoid.clone(i._ellipseGeometry._ellipsoid),b.createGeometry(i)}}));
