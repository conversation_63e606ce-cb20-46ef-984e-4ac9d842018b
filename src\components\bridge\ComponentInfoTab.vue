<template>
  <div class="component-info-tab">
    <div class="header-actions">
      <el-button type="primary" @click="handleAdd" :disabled="readonly">新建</el-button>
    </div>

    <el-table :data="componentList" border style="width: 100%">
      <el-table-column prop="segmentType" label="桥段类型" width="120">
        <template #default="{ row }">
          {{ getSegmentTypeName(row.segmentType) }}
        </template>
      </el-table-column>
      <el-table-column prop="segmentName" label="桥段名称" width="150" />
      <el-table-column prop="segmentCode" label="桥段编号" width="150" />
      <el-table-column prop="startLocation" label="起始位置" width="150" />
      <el-table-column prop="endLocation" label="终止位置" width="150" />
      <el-table-column prop="laneCount" label="车道数量" width="100" />
      <el-table-column label="操作" width="150" fixed="right">
        <template #default="{ row, $index }">
          <el-button 
            type="primary" 
            link 
            @click="handleEdit(row, $index)"
            :disabled="readonly"
          >
            修改
          </el-button>
          <el-button 
            type="danger" 
            link 
            @click="handleDelete($index)"
            :disabled="readonly"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 新建/编辑弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="800px"
      :close-on-click-modal="false"
      :before-close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="桥段类型" prop="segmentType" required>
              <el-select v-model="formData.segmentType" placeholder="请选择" style="width: 100%">
                <el-option
                  v-for="option in segmentTypeOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="桥段名称" prop="segmentName" required>
              <el-input v-model="formData.segmentName" placeholder="请输入桥段名称" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="起始位置" prop="startLocation" required>
              <el-input v-model="formData.startLocation" placeholder="请输入起始位置" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="桥段编号" prop="segmentCode" required>
              <el-input v-model="formData.segmentCode" placeholder="请输入桥段编号" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="终止位置" prop="endLocation" required>
              <el-input v-model="formData.endLocation" placeholder="请输入终止位置" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="车道数量" prop="laneCount" required>
              <el-input-number 
                v-model="formData.laneCount" 
                :min="1" 
                :max="20" 
                style="width: 100%" 
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 联跨信息表格 -->
        <el-form-item label="联跨信息">
          <div class="link-info-section">
            <div class="link-info-header">
              <span>联跨信息</span>
              <el-button type="text" @click="addLinkInfo">新增</el-button>
            </div>
            <el-table :data="linkInfoList" border size="small">
              <el-table-column prop="linkNumber" label="联编号" width="100">
                <template #default="{ row, $index }">
                  <el-input v-model="row.linkNumber" size="small" />
                </template>
              </el-table-column>
              <el-table-column prop="crossNumber" label="跨编号" width="100">
                <template #default="{ row, $index }">
                  <el-input v-model="row.crossNumber" size="small" />
                </template>
              </el-table-column>
              <el-table-column prop="startSpanNumber" label="起始桥跨编号" width="120">
                <template #default="{ row, $index }">
                  <el-input v-model="row.startSpanNumber" size="small" />
                </template>
              </el-table-column>
              <el-table-column prop="endSpanNumber" label="终止桥跨编号" width="120">
                <template #default="{ row, $index }">
                  <el-input v-model="row.endSpanNumber" size="small" />
                </template>
              </el-table-column>
              <el-table-column prop="span" label="跨径" width="100">
                <template #default="{ row, $index }">
                  <el-input v-model="row.span" size="small" />
                </template>
              </el-table-column>
              <el-table-column label="操作" width="80">
                <template #default="{ row, $index }">
                  <el-button type="danger" link @click="removeLinkInfo($index)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCancel">取消</el-button>
          <el-button type="primary" @click="handleSave">保存</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { SEGMENT_TYPE_OPTIONS, SEGMENT_TYPE_MAP } from '@/constants/bridge'

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  },
  readonly: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue'])

const componentList = ref([])
const dialogVisible = ref(false)
const dialogTitle = ref('')
const editIndex = ref(-1)
const formRef = ref()

// 下拉选项数据
const segmentTypeOptions = ref(SEGMENT_TYPE_OPTIONS)

// 表单数据
const formData = reactive({
  segmentType: '',
  segmentName: '',
  segmentCode: '',
  startLocation: '',
  endLocation: '',
  laneCount: 1
})

// 联跨信息
const linkInfoList = ref([])

// 表单验证规则
const rules = {
  segmentType: [
    { required: true, message: '请选择桥段类型', trigger: 'change' }
  ],
  segmentName: [
    { required: true, message: '请输入桥段名称', trigger: 'blur' }
  ],
  segmentCode: [
    { required: true, message: '请输入桥段编号', trigger: 'blur' }
  ],
  startLocation: [
    { required: true, message: '请输入起始位置', trigger: 'blur' }
  ],
  endLocation: [
    { required: true, message: '请输入终止位置', trigger: 'blur' }
  ],
  laneCount: [
    { required: true, message: '请输入车道数量', trigger: 'blur' }
  ]
}

// 获取桥段类型名称
const getSegmentTypeName = (type) => {
  return SEGMENT_TYPE_MAP[type] || ''
}

// 监听props变化
watch(() => props.modelValue, (newVal) => {
  if (newVal && Array.isArray(newVal)) {
    componentList.value = [...newVal]
  }
}, { immediate: true, deep: true })

// 监听组件列表变化
watch(componentList, (newVal) => {
  emit('update:modelValue', [...newVal])
}, { deep: true })

// 新增
const handleAdd = () => {
  dialogTitle.value = '新建'
  editIndex.value = -1
  resetForm()
  dialogVisible.value = true
}

// 编辑
const handleEdit = (row, index) => {
  dialogTitle.value = '修改'
  editIndex.value = index
  Object.assign(formData, row)
  
  // 处理联跨信息
  if (row.linkInfo && typeof row.linkInfo === 'object') {
    linkInfoList.value = Array.isArray(row.linkInfo) ? [...row.linkInfo] : []
  } else {
    linkInfoList.value = []
  }
  
  dialogVisible.value = true
}

// 删除
const handleDelete = async (index) => {
  try {
    await ElMessageBox.confirm('确定要删除这条记录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    componentList.value.splice(index, 1)
    ElMessage.success('删除成功')
  } catch {
    // 用户取消删除
  }
}

// 保存
const handleSave = async () => {
  try {
    await formRef.value?.validate()
    
    const data = {
      ...formData,
      linkInfo: [...linkInfoList.value],
      segmentTypeName: getSegmentTypeName(formData.segmentType)
    }
    
    if (editIndex.value >= 0) {
      // 编辑
      componentList.value[editIndex.value] = data
      ElMessage.success('修改成功')
    } else {
      // 新增
      componentList.value.push(data)
      ElMessage.success('新增成功')
    }
    
    dialogVisible.value = false
  } catch (error) {
    console.error('保存失败:', error)
  }
}

// 取消
const handleCancel = () => {
  dialogVisible.value = false
}

// 弹窗关闭前处理
const handleDialogClose = (done) => {
  done()
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    segmentType: '',
    segmentName: '',
    segmentCode: '',
    startLocation: '',
    endLocation: '',
    laneCount: 1
  })
  linkInfoList.value = []
  formRef.value?.resetFields()
}

// 新增联跨信息
const addLinkInfo = () => {
  linkInfoList.value.push({
    linkNumber: '',
    crossNumber: '',
    startSpanNumber: '',
    endSpanNumber: '',
    span: ''
  })
}

// 删除联跨信息
const removeLinkInfo = (index) => {
  linkInfoList.value.splice(index, 1)
}

// 表单验证方法
const validate = () => {
  return Promise.resolve(true)
}

// 重置组件
const resetComponent = () => {
  componentList.value = []
}

// 暴露方法给父组件
defineExpose({
  validate,
  resetForm: resetComponent
})
</script>

<style scoped>
.component-info-tab {
  padding: 20px;
}

.header-actions {
  margin-bottom: 20px;
}

.link-info-section {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 15px;
}

.link-info-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  font-weight: bold;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-table .el-input) {
  border: none;
}

:deep(.el-table .el-input .el-input__wrapper) {
  box-shadow: none;
  background: transparent;
}
</style> 