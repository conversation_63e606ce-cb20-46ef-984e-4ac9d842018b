<template>
  <PanelBox title="报警信息">
    <template #extra>
      <div class="com-select">
        <CommonSelect v-model="timeRange" :options="timeOptions" @change="handleTimeChange" />
      </div>
    </template>
    <div class="panel-content">
      <!-- 报警数量统计区域 -->
      <div class="stats-row">
        <div class="stat-item">
          <div class="stat-info">
            <span class="stat-dot"><span class="stat-dot-inner red"></span></span>
            <span class="stat-label">报警总数</span>
          </div>
          <span class="stat-value red-gradient">{{ statsData.total }}</span>
        </div>
        <div class="stat-item">
          <div class="stat-info">
            <span class="stat-dot"><span class="stat-dot-inner orange"></span></span>
            <span class="stat-label">待处置</span>
          </div>
          <span class="stat-value orange-gradient">{{ statsData.unhandled }}</span>
        </div>
        <div class="stat-item">
          <div class="stat-info">
            <span class="stat-dot"><span class="stat-dot-inner yellow"></span></span>
            <span class="stat-label">处置中</span>
          </div>
          <span class="stat-value yellow-gradient">{{ statsData.handling }}</span>
        </div>
        <div class="stat-item">
          <div class="stat-info">
            <span class="stat-dot"><span class="stat-dot-inner green"></span></span>
            <span class="stat-label">已处置</span>
          </div>
          <span class="stat-value green-gradient">{{ statsData.handled }}</span>
        </div>
      </div>

      <!-- 报警列表区域 -->
      <ScrollTable
        :columns="tableColumns"
        :data="alarmList"
        :autoScroll="true"
        :scrollSpeed="2000"
        :tableHeight="tableHeight"
        :visibleRows="2"
        :hiddenHeader="true"
      >
        <template #custom="{ row }">
          <div class="alarm-row" @click="openAlarmDetail(row)">
            <div class="alarm-main">
              <span class="alarm-type">报警类型：{{ row.type }}</span>
              <div class="alarm-level">
                <span class="level-tag" :style="{ background: getLevelColor(row.level) }">{{ row.level }}</span>
              </div>
            </div>
            <div class="alarm-location">
              <img src="@/assets/images/screen/common/location.svg" alt="location" class="location-icon" />
              <span class="location-text">{{ row.location }}</span>
            </div>
            <div class="alarm-time">
              <span class="time-text">{{ row.time }}</span>
            </div>
            <div class="alarm-divider"></div>
          </div>
        </template>
      </ScrollTable>
    </div>
    
    <!-- 报警详情弹窗预留 -->
  </PanelBox>
</template>

<script setup>
import { ref, computed } from 'vue'
import PanelBox from '@/components/screen/PanelBox.vue'
import CommonSelect from '@/components/screen/CommonSelect.vue'
import ScrollTable from '@/components/screen/common/ScrollTable.vue'

// 时间选择
const timeRange = ref('week')
const timeOptions = [
  { label: '近一周', value: 'week' },
  { label: '近一月', value: 'month' },
  { label: '近一年', value: 'year' }
]

// 统计数据
const statsData = ref({
  total: 360,
  unhandled: 20,
  handling: 90,
  handled: 270
})

// 不同时间范围的数据
const timeRangeData = {
  week: {
    total: 360,
    unhandled: 20,
    handling: 90,
    handled: 270
  },
  month: {
    total: 520,
    unhandled: 35,
    handling: 125,
    handled: 360
  },
  year: {
    total: 1280,
    unhandled: 80,
    handling: 320,
    handled: 880
  }
}

// 不同时间范围的报警列表数据
const alarmListData = {
  week: [
    {
      type: '水位报警',
      level: '三级报警',
      location: 'XX省XX市XX区XX路XXX号',
      time: '2025-04-08 00:12:20'
    },
    {
      type: '水位报警',
      level: '三级报警',
      location: 'XX省XX市XX区XX路XXX号',
      time: '2025-04-08 00:12:20'
    },
    {
      type: '水位报警',
      level: '三级报警',
      location: 'XX省XX市XX区XX路XXX号',
      time: '2025-04-08 00:12:20'
    },
    {
      type: '水质报警',
      level: '二级报警',
      location: 'XX省XX市XX区XX路XXX号',
      time: '2025-04-08 00:10:15'
    },
    {
      type: '设备故障',
      level: '一级报警',
      location: 'XX省XX市XX区XX路XXX号',
      time: '2025-04-07 23:45:30'
    },
    {
      type: '水位报警',
      level: '四级报警',
      location: 'XX省XX市XX区XX路XXX号',
      time: '2025-04-07 22:30:10'
    }
  ],
  month: [
    {
      type: '水质报警',
      level: '一级报警',
      location: 'XX省XX市XX区XX路XXX号',
      time: '2025-03-25 14:12:20'
    },
    {
      type: '设备故障',
      level: '二级报警',
      location: 'XX省XX市XX区XX路XXX号',
      time: '2025-03-20 09:35:40'
    },
    {
      type: '水位报警',
      level: '三级报警',
      location: 'XX省XX市XX区XX路XXX号',
      time: '2025-03-15 18:22:10'
    },
    {
      type: '水质报警',
      level: '四级报警',
      location: 'XX省XX市XX区XX路XXX号',
      time: '2025-03-10 11:05:30'
    }
  ],
  year: [
    {
      type: '设备故障',
      level: '一级报警',
      location: 'XX省XX市XX区XX路XXX号',
      time: '2024-12-15 10:22:20'
    },
    {
      type: '水位报警',
      level: '二级报警',
      location: 'XX省XX市XX区XX路XXX号',
      time: '2024-10-05 08:45:15'
    },
    {
      type: '水质报警',
      level: '三级报警',
      location: 'XX省XX市XX区XX路XXX号',
      time: '2024-07-20 16:30:40'
    },
    {
      type: '设备故障',
      level: '四级报警',
      location: 'XX省XX市XX区XX路XXX号',
      time: '2024-05-10 12:15:30'
    }
  ]
}

// 表格配置
const tableColumns = [
  { title: '报警信息', dataIndex: 'custom', width: '100%' }
]

// 动态计算表格高度
const tableHeight = computed(() => {
  if (window.innerHeight === 910) {
    return '200px'
  } else if (window.innerHeight >= 940 && window.innerHeight <= 1055) {
    return '220px'
  } else if (window.innerWidth >= 2561) {
    return '260px'
  } else if (window.innerWidth >= 1920 && window.innerWidth <= 2560) {
    return '210px'
  } else {
    return '180px'
  }
})

// 报警列表数据，初始为一周的数据
const alarmList = ref(alarmListData.week)

// 获取报警等级对应的颜色
const getLevelColor = (level) => {
  const colorMap = {
    '一级报警': '#FB3737', // 红色
    '二级报警': '#FF6D28', // 橙色
    '三级报警': '#EAA01B', // 黄色
    '四级报警': '#3FD87C'  // 绿色
  }
  return colorMap[level] || '#3FD87C'
}

// 处理时间范围变化
const handleTimeChange = (value) => {
  console.log('时间范围变更为:', value)
  // 更新统计数据
  statsData.value = { ...timeRangeData[value] }
  // 更新报警列表数据
  alarmList.value = alarmListData[value]
}

// 打开报警详情弹窗
const openAlarmDetail = (row) => {
  console.log('打开报警详情:', row)
  // 预留报警详情弹窗功能
}
</script>

<style scoped>
.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.com-select {
  margin-right: 20px;
}

/* 统计数据样式 */
.stats-row {
  display: flex;
  justify-content: space-around;
  padding: 0 5px 0px;
}

.stat-item {
  display: flex;
  align-items: center;
  position: relative;
  gap: 8px;
}

.stat-dot {
  position: relative;
  width: 9px;
  height: 9px;
  margin-bottom: 2px;
}

.stat-dot {
  width: 9px;
  height: 9px;
  border-radius: 50%;
  position: relative;
}

.stat-dot-inner {
  width: 5px;
  height: 5px;
  border-radius: 50%;
  position: absolute;
  top: 2px;
  left: 2px;
}

.stat-dot-inner.red { 
  background: #FC4949;
}

.stat-dot-inner.orange { 
  background: #FF6D28;
}

.stat-dot-inner.yellow { 
  background: #FFC75A;
}

.stat-dot-inner.green { 
  background: #3FD87C;
}

.stat-dot:has(.stat-dot-inner.red) {
  background: rgba(252, 73, 73, 0.4);
}

.stat-dot:has(.stat-dot-inner.orange) {
  background: rgba(255, 109, 40, 0.4);
}

.stat-dot:has(.stat-dot-inner.yellow) {
  background: rgba(255, 199, 90, 0.4);
}

.stat-dot:has(.stat-dot-inner.green) {
  background: rgba(63, 216, 124, 0.4);
}

.stat-info {
  display: flex;
  align-items: center;
  gap: 4px;
}

.stat-label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
}

.stat-value {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 24px;
  line-height: 26px;
  font-style: normal;
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  text-align: center;
}

.red-gradient {
  background: linear-gradient(90deg, #FB3737 0%, #FEA6A6 100%);
  -webkit-background-clip: text;
}

.orange-gradient {
  background: linear-gradient(90deg, #FF5717 0%, #FFCD72 100%);
  -webkit-background-clip: text;
}

.yellow-gradient {
  background: linear-gradient(90deg, #FFC24C 0%, #FEDFA6 100%);
  -webkit-background-clip: text;
}

.green-gradient {
  background: linear-gradient(90deg, #43DF81 0%, #A6FED0 100%);
  -webkit-background-clip: text;
}

/* 报警列表样式 */
.alarm-row {
  padding: 5px 0;
  width: 100%;
  position: relative;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  cursor: pointer;
  transition: background-color 0.3s;
}

.alarm-row:hover {
  background-color: rgba(59, 141, 242, 0.1);
}

.alarm-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.alarm-type {
  font-family: PingFangSC, PingFang SC;
  font-weight: 600;
  font-size: 14px;
  color: #FFFFFF;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.alarm-level {
  display: flex;
  flex-shrink: 0;
}

.level-tag {
  width: 60px;
  height: 24px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 12px;
  color: #FFFFFF;
}

.alarm-location {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 4px;
}

.location-icon {
  width: 12px;
  height: 12px;
}

.location-text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
  opacity: 0.6;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.alarm-time {
  display: flex;
  justify-content: flex-end;
  margin-top: 0px;
}

.time-text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
  opacity: 0.8;
}

.alarm-divider {
  display: none;
}

/* 响应式布局适配 */
@media screen and (min-width: 1920px) and (max-width: 2560px) {
  .panel-content {
    padding: 15px;
  }
}

@media screen and (max-width: 1919px) {
  .panel-content {
    padding: 12px;
  }
}

@media screen and (min-width: 2561px) {
  .panel-content {
    padding: 18px;
  }
}

/* 添加全屏模式(1080px高度)的适配 */
@media screen and (min-height: 1056px) and (max-height: 1080px) {
  .panel-content {
    padding: 15px;
  }
  
  .stat-value {
    font-size: 24px;
    line-height: 26px;
  }
}

@media screen and (min-height: 940px) and (max-height: 1055px) {
  .panel-content {
    padding: 10px;
    gap: 10px;
  }

  .stat-value {
    font-size: 20px;
    line-height: 22px;
  }
}

@media screen and (min-height: 900px) and (max-height: 940px) {
  .panel-content {
    padding: 8px;
    gap: 8px;
  }

  .stat-value {
    font-size: 18px;
    line-height: 20px;
  }
}
</style> 