<template>
  <div class="work-order-type-chart">
    <div class="chart-title">工单类型</div>
    <div ref="chartRef" class="chart-container"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import * as echarts from 'echarts'

// 图表实例
const chartRef = ref(null)
let chartInstance = null

// 模拟数据
const chartData = [
  { name: '报修', value: 100, percentage: '46%', color: '#FF6450' },
  { name: '咨询', value: 100, percentage: '46%', color: '#367EFF' },
  { name: '投诉', value: 100, percentage: '46%', color: '#FFD200' },
  { name: '其它', value: 100, percentage: '46%', color: '#26D6A1' }
]

// 计算总数
const totalValue = chartData.reduce((sum, item) => sum + item.value, 0)

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return
  
  // 设置一个定时器，确保DOM已经完全渲染
  setTimeout(() => {
    if (chartRef.value.clientWidth === 0 || chartRef.value.clientHeight === 0) {
      console.error('WorkOrderTypeChart DOM尺寸为0:', chartRef.value.clientWidth, chartRef.value.clientHeight)
      // 设置一个固定的高度
      chartRef.value.style.height = '180px'
    }
    
    // 创建图表实例
    chartInstance = echarts.init(chartRef.value)
    
    // 渲染图表
    updateChart()
    
    // 监听窗口大小变化
    window.addEventListener('resize', handleResize)
  }, 0)
}

// 更新图表
const updateChart = () => {
  const option = {
    backgroundColor: 'transparent',
    color: chartData.map(item => item.color),
    title: {
      text: `${totalValue}`,
      subtext: '',
      left: '20%',
      top: 'center',
      textStyle: {
        fontSize: 20,
        fontWeight: 'bold',
        color: '#FFFFFF',
        fontFamily: 'D-DIN, D-DIN'
      }
    },
    tooltip: {
      show: false
    },
    legend: {
      orient: 'vertical',
      right: '5%',
      top: 'middle',
      itemWidth: 8,
      itemHeight: 8,
      icon: 'rect',
      formatter: (name) => {
        const item = chartData.find(i => i.name === name)
        return `{name|${name}}`
      },
      textStyle: {
        rich: {
          name: {
            color: '#FFFFFF',
            fontSize: 12,
            width: 40
          },
          value: {
            color: '#FFFFFF',
            fontSize: 12,
            width: 40
          }
        }
      }
    },
    series: [
      {
        name: '工单类型',
        type: 'pie',
        radius: ['25%', '40%'],
        avoidLabelOverlap: false,
        center: ['30%', '50%'],
        label: {
          show: true,
          position: 'outside',
          formatter: '{d}%',
          fontSize: 10,
          color: '#FFFFFF',
          fontFamily: 'D-DIN, D-DIN'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 14,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: true,
          length: 10,
          length2: 5,
          smooth: true,
          lineStyle: {
            width: 1,
            type: 'solid'
          }
        },
        itemStyle: {
          borderColor: 'rgba(0, 0, 0, 0.1)',
          borderWidth: 1
        },
        data: chartData.map(item => ({
          name: item.name,
          value: item.value
        }))
      }
    ]
  }
  
  // 设置图表配置
  chartInstance.setOption(option)
}

// 处理窗口大小变化
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

// 组件挂载时初始化图表
onMounted(() => {
  // 使用requestAnimationFrame确保DOM已渲染
  window.requestAnimationFrame(() => {
    initChart()
  })
})

// 组件卸载时销毁图表
onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.work-order-type-chart {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
}

.chart-title {
  font-family: SourceHanSansCN, SourceHanSansCN;
  font-weight: 400;
  font-size: 14px;
  color: #FFFFFF;
  text-align: left;
  margin-bottom: -1rem;
}

.chart-container {
  flex: 1;
  width: 100%;
  min-height: 180px; /* 设置最小高度 */
}
</style> 