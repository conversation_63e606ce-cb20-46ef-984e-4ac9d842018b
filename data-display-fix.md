# 数据回显问题修复方案

## 问题分析

根据接口返回数据和界面显示的对比，发现以下问题：

### 1. `isEnabled` 字段类型不匹配
- **接口返回**: `true`/`false` (布尔值)
- **界面选项**: `1`/`0` (数字)
- **结果**: 界面显示 "true" 而不是 "是"

### 2. 异步数据加载时序问题
- **监测指标**: 需要先加载对应设备类型的指标选项，再回显值
- **设备选择**: 需要先加载对应设备类型的设备列表，再回显值

## 修复方案

### 1. isEnabled 数据类型转换

```javascript
// 特殊处理isEnabled字段：将布尔值转换为数字
if (typeof data.isEnabled === 'boolean') {
  formData.isEnabled = data.isEnabled ? 1 : 0;
} else if (data.isEnabled !== undefined) {
  formData.isEnabled = data.isEnabled;
}
```

### 2. 优化数据加载时序

```javascript
const fetchThresholdDetail = async (id) => {
  // 1. 先确保设备类型选项已加载
  if (deviceTypeOptions.value.length === 0) {
    await fetchDeviceTypes();
  }
  
  // 2. 根据返回的设备类型，预先加载监测指标和设备列表
  if (data.deviceType) {
    await fetchMonitorIndexes(data.deviceType);
    await fetchDeviceList(data.deviceType);
  }
  
  // 3. 等待选项更新完成
  await new Promise(resolve => setTimeout(resolve, 100));
  
  // 4. 再进行数据回显
  // ...数据回显逻辑
};
```

### 3. 优化监听器逻辑

```javascript
// 监听设备类型变化时，避免在编辑模式下清空已回显的数据
watch(() => formData.deviceType, (newVal, oldVal) => {
  if (newVal && newVal !== oldVal) {
    fetchMonitorIndexes(newVal);
    fetchDeviceList(newVal);
    
    // 只在新增模式或确实改变时才清空
    if (props.mode === 'add' || (oldVal && oldVal !== newVal)) {
      formData.monitorIndex = '';
      formData.deviceIds = [];
    }
  }
});
```

### 4. 数组数据处理优化

```javascript
// 支持字符串和数组两种格式的设备ID
if (typeof data.deviceIds === 'string' && data.deviceIds) {
  formData.deviceIds = data.deviceIds.split(',').map(id => id.trim()).filter(id => id);
} else if (Array.isArray(data.deviceIds)) {
  formData.deviceIds = data.deviceIds;
}
```

### 5. 添加调试日志

```javascript
console.log('回显数据:', {
  原始数据: data,
  处理后: formData,
  设备类型选项: deviceTypeOptions.value,
  监测指标选项: monitorIndexOptions.value,
  设备选项: deviceOptions.value
});
```

## 测试方法

1. 打开浏览器开发者工具的 Console 面板
2. 编辑一条已有记录
3. 检查控制台输出的调试信息：
   - 确认设备类型选项已正确加载
   - 确认监测指标选项已根据设备类型正确加载
   - 确认设备列表已正确加载
   - 确认数据转换结果正确

## 预期效果

修复后应该看到：
- ✅ "是否生效" 正确显示为 "是" 或 "否"
- ✅ "监测指标" 正确显示对应的指标名称
- ✅ "设备选择" 正确显示已选择的设备

## 如果问题仍然存在

请检查以下几点：
1. 接口 `/drain/usmMonitorIndicators/list` 是否正常返回数据
2. 接口 `/drain/usmMonitorDevice/list` 是否正常返回数据
3. 控制台是否有错误信息
4. 数据格式是否与预期一致 