<template>
  <div class="api-config-step">
    <div class="step-header">
      <h3>API配置</h3>
    </div>
    
    <div class="api-methods">
      <h4>标准API方法</h4>
      <el-form :model="config.api.methods" label-width="120px">
        <el-form-item label="获取列表">
          <el-switch v-model="config.api.methods.list" />
        </el-form-item>
        
        <el-form-item label="获取详情">
          <el-switch v-model="config.api.methods.detail" />
        </el-form-item>
        
        <el-form-item label="创建">
          <el-switch v-model="config.api.methods.create" />
        </el-form-item>
        
        <el-form-item label="更新">
          <el-switch v-model="config.api.methods.update" />
        </el-form-item>
        
        <el-form-item label="删除">
          <el-switch v-model="config.api.methods.delete" />
        </el-form-item>
      </el-form>
    </div>
    
    <div class="custom-methods">
      <div class="custom-methods-header">
        <h4>自定义API方法</h4>
        <el-button type="primary" size="small" @click="addCustomMethod">添加方法</el-button>
      </div>
      
      <el-empty v-if="!config.api.customMethods.length" description="暂无自定义API方法，可以添加" />
      
      <el-card v-for="(method, index) in config.api.customMethods" :key="index" class="method-card">
        <template #header>
          <div class="method-header">
            <span>方法 {{ index + 1 }}: {{ method.name || '未命名' }}</span>
            <div class="method-actions">
              <el-button type="danger" size="small" circle @click="removeCustomMethod(index)">
                <el-icon><Delete /></el-icon>
              </el-button>
            </div>
          </div>
        </template>
        
        <el-form :model="method" label-width="100px">
          <el-form-item label="方法名称" required>
            <el-input v-model="method.name" placeholder="请输入方法名称，如：exportData" />
          </el-form-item>
          
          <el-form-item label="请求方法" required>
            <el-select v-model="method.method" placeholder="请选择请求方法">
              <el-option label="GET" value="get" />
              <el-option label="POST" value="post" />
              <el-option label="PUT" value="put" />
              <el-option label="DELETE" value="delete" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="URL路径" required>
            <el-input v-model="method.url" placeholder="请输入URL路径，如：/export" />
          </el-form-item>
          
          <el-form-item label="参数">
            <el-input v-model="method.params" placeholder="请输入参数，如：id, data" />
          </el-form-item>
          
          <el-form-item label="描述">
            <el-input v-model="method.description" placeholder="请输入方法描述" />
          </el-form-item>
        </el-form>
      </el-card>
    </div>
    
    <div class="step-actions">
      <el-button @click="handlePrev">上一步</el-button>
      <el-button type="primary" @click="handleFinish">完成</el-button>
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue'
import { Delete } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  config: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update:config', 'next', 'prev'])

// 添加自定义方法
const addCustomMethod = () => {
  if (!props.config.api.customMethods) {
    props.config.api.customMethods = []
  }
  
  props.config.api.customMethods.push({
    name: '',
    method: 'get',
    url: '',
    params: '',
    description: ''
  })
}

// 移除自定义方法
const removeCustomMethod = (index) => {
  props.config.api.customMethods.splice(index, 1)
}

// 上一步
const handlePrev = () => {
  emit('prev')
}

// 完成
const handleFinish = () => {
  // 验证自定义API方法
  for (let i = 0; i < (props.config.api.customMethods || []).length; i++) {
    const method = props.config.api.customMethods[i]
    if (!method.name) {
      ElMessage.warning(`第 ${i + 1} 个自定义方法的名称不能为空`)
      return
    }
    if (!method.method) {
      ElMessage.warning(`第 ${i + 1} 个自定义方法的请求方法不能为空`)
      return
    }
    if (!method.url) {
      ElMessage.warning(`第 ${i + 1} 个自定义方法的URL路径不能为空`)
      return
    }
  }
  
  // 验证至少启用了一个标准API方法
  const { methods } = props.config.api
  if (!methods.list && !methods.detail && !methods.create && !methods.update && !methods.delete) {
    ElMessage.warning('请至少启用一个标准API方法')
    return
  }
  
  emit('next')
}
</script>

<style scoped>
.api-config-step {
  padding: 0 20px;
}

.step-header {
  margin-bottom: 20px;
}

.api-methods {
  margin-bottom: 30px;
}

.custom-methods {
  margin-bottom: 20px;
}

.custom-methods-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.method-card {
  margin-bottom: 16px;
}

.method-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.method-actions {
  display: flex;
  gap: 8px;
}

.step-actions {
  margin-top: 30px;
  display: flex;
  justify-content: space-between;
}
</style>
