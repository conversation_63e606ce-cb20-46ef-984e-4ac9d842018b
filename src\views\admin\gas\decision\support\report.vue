<template>
  <div class="gas-decision-support-report">
    <!-- 搜索区域 -->
    <GasReportSearch @search="handleSearch" @reset="handleReset" />
    
    <!-- 表格区域 -->
    <div class="table-container">
      <el-table
        :data="tableData"
        style="width: 100%"
        :header-cell-style="headerCellStyle"
        :row-class-name="tableRowClassName"
        @row-click="handleRowClick"
        height="calc(100vh - 280px)"
        v-loading="loading"
      >
        <el-table-column label="序号" min-width="60">
          <template #default="{ $index }">
            {{ (currentPage - 1) * pageSize + $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="reportCode" label="报告编码" min-width="120" />
        <el-table-column prop="reportName" label="报告名称" min-width="150" />
        <el-table-column prop="reportType" label="报告类型" min-width="100">
          <template #default="{ row }">
            {{ getReportTypeName(row.reportType) }}
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="生成时间" min-width="150">
          <template #default="{ row }">
            {{ formatDateTime(row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" min-width="180" fixed="right" align="center">
          <template #default="scope">
            <div class="operation-btns">
              <div class="operation-btn-row">
                <span class="operation-btn-text" @click.stop="handleDownload(scope.row)">下载</span>
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 30, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :pager-count="5"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { ElTable, ElTableColumn, ElPagination, ElMessage } from 'element-plus';
import GasReportSearch from './components/GasReportSearch.vue';
import { getSecurityReportPage, downloadSecurityReport } from '@/api/gas';
import { REPORT_TYPE_MAP } from '@/constants/gas';
import moment from 'moment';

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const tableData = ref([]);
const loading = ref(false);

// 查询参数
const queryParams = ref({});

// 获取报告类型名称
const getReportTypeName = (type) => {
  return REPORT_TYPE_MAP[type] || '未知类型';
};

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return '';
  if (typeof dateTime === 'object' && dateTime.time) {
    return moment(dateTime.time).format('YYYY-MM-DD HH:mm:ss');
  }
  return moment(dateTime).format('YYYY-MM-DD HH:mm:ss');
};

// 处理搜索
const handleSearch = (formData) => {
  queryParams.value = formData;
  currentPage.value = 1;
  fetchReportData();
};

// 处理重置
const handleReset = () => {
  queryParams.value = {};
  currentPage.value = 1;
  fetchReportData();
};

// 获取报告数据
const fetchReportData = async () => {
  try {
    loading.value = true;
    const response = await getSecurityReportPage(
      currentPage.value,
      pageSize.value,
      queryParams.value
    );
    if (response && response.code === 200) {
      tableData.value = response.data.records || [];
      total.value = response.data.total || 0;
    } else {
      ElMessage.error('获取报告数据失败');
    }
  } catch (error) {
    console.error('获取报告数据异常:', error);
    ElMessage.error('获取报告数据异常');
  } finally {
    loading.value = false;
  }
};

// 处理分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val;
  currentPage.value = 1;
  fetchReportData();
};

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val;
  fetchReportData();
};

// 表头样式
const headerCellStyle = {
  background: '#F4F4F4',
  fontFamily: 'PingFangSC, PingFang SC',
  fontWeight: '500',
  fontSize: '14px',
  color: '#282828',
  height: '64px'
};

// 斑马纹和选中行样式
const tableRowClassName = ({ rowIndex }) => {
  if (rowIndex % 2 === 1) {
    return 'zebra-row';
  }
  return '';
};

// 处理行点击
const handleRowClick = (row) => {
  console.log('行数据:', row);
};

// 处理下载
const handleDownload = async (row) => {
  if (!row.fileUrl) {
    ElMessage.warning('文件链接不存在');
    return;
  }
  
  try {
    // await downloadSecurityReport(row.fileUrl);
    console.log(row.fileUrl);
    window.open(row.fileUrl, '_blank');
  } catch (error) {
    console.error('下载文件失败:', error);
    ElMessage.error('下载文件失败');
  }
};

onMounted(() => {
  fetchReportData();
});
</script>

<style scoped>
.gas-decision-support-report {
  width: 100%;
  height: 95%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  background-color: white;
}

/* 表格样式 */
.table-container {
  margin-bottom: 16px;
  flex: 1;
  width: 100%;
  overflow: hidden;
}

:deep(.el-table) {
  overflow-x: hidden !important;
}

:deep(.el-table th) {
  background-color: #F4F4F4;
  height: 64px;
}

:deep(.el-table tr) {
  height: 48px;
}

:deep(.zebra-row) {
  background-color: #F8FAFD;
}

:deep(.el-table__row:hover) {
  background: #EEF5FF !important;
}

:deep(.el-table__row.selected-row) {
  background: #EEF5FF !important;
}

.operation-btns {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.operation-btn-row {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 4px 0;
}

.operation-btn-text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #0086FF;
  cursor: pointer;
}

.operation-divider {
  margin: 0 4px;
  color: #CED3DA;
}

/* 分页样式 */
.pagination-container {
  display: flex;
  justify-content: flex-end;
  padding: 0;
  margin-top: 16px;
  min-height: 32px;
}

:deep(.el-pagination) {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #222222;
  padding-right: 0;
}

:deep(.el-pagination .el-pager li) {
  width: 24px;
  height: 24px;
  background: rgba(255,255,255,0.99);
  border-radius: 2px;
  border: 1px solid #EEEEEE;
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.el-pagination .el-pager li.is-active) {
  width: 24px;
  height: 24px;
  background: #0086FF;
  border-radius: 2px;
  color: #FFFFFF;
  border: none;
}

/* 防止表格过长时遮挡分页组件 */
:deep(.el-table__body-wrapper) {
  overflow-y: auto !important;
  min-height: 200px;
}

/* 确保操作列文字不换行 */
:deep(.cell) {
  white-space: nowrap;
}
</style>