<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="1000px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="drainage-device-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      :disabled="mode === 'view'"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="设备名称" prop="deviceName">
            <el-input v-model="formData.deviceName" placeholder="请输入设备名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="设备编号" prop="indexCode">
            <el-input v-model="formData.indexCode" placeholder="请输入设备编号" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="设备类型" prop="deviceType">
            <el-select v-model="formData.deviceType" placeholder="请选择" class="w-full">
              <el-option v-for="item in deviceTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="监测指标" prop="monitorIndex">
            <el-select v-model="formData.monitorIndex" placeholder="请选择" class="w-full">
              <el-option v-for="item in monitorIndexOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="采集频率" prop="collectFrequency">
            <div class="flex items-center">
              <el-input-number v-model="formData.collectFrequency" :min="0" class="w-full-unit" />
              <span class="unit-label">分钟/次</span>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="上传频率" prop="uploadFrequency">
            <div class="flex items-center">
              <el-input-number v-model="formData.uploadFrequency" :min="0" class="w-full-unit" />
              <span class="unit-label">分钟/次</span>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="量程" prop="measureRange">
            <div class="flex items-center">
              <el-input-number v-model="formData.measureRangeLow" :min="0" placeholder="最小值" class="measure-range-input" />
              <span class="mx-2">-</span>
              <el-input-number v-model="formData.measureRangeUp" :min="0" placeholder="最大值" class="measure-range-input" />
              <span class="unit-label">单位</span>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="监测对象" prop="monitorTarget">
            <el-select v-model="formData.monitorTarget" placeholder="请选择" class="w-full" @change="handleMonitorTargetChange">
              <el-option v-for="item in monitorObjectOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="对象选择" prop="monitorObjectId">
            <el-select v-model="formData.monitorObjectId" placeholder="请选择" class="w-full" :loading="objectLoading">
              <el-option v-for="item in objectOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="设备状态" prop="onlineStatus">
            <el-select v-model="formData.onlineStatus" placeholder="请选择" class="w-full">
              <el-option v-for="item in onlineStatusOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="权属单位" prop="ownershipUnit">
            <el-select v-model="formData.ownershipUnit" placeholder="请选择" class="w-full">
              <el-option v-for="unit in managementUnits" :key="unit.id" :label="unit.enterpriseName" :value="unit.enterpriseName" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="位置" prop="region">
            <div class="flex items-center">
              <el-cascader
                v-model="formData.regionCode"
                :options="areaOptions"
                :props="{
                  value: 'code',
                  label: 'name',
                  children: 'children'
                }"
                placeholder="请选择所属区域"
                class="w-full mr-2"
                @change="handleAreaChange"
              />
              <el-input v-model="formData.address" placeholder="详细地址" class="w-full" />
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="定位">
            <div class="flex items-center">
              <el-input v-model="formData.longitude" placeholder="经度" class="mr-2 w-32" />
              <el-input v-model="formData.latitude" placeholder="纬度" class="w-32" />
              <el-button type="primary" class="ml-2" icon="Location" circle @click="openMapPicker"></el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="备注">
            <el-input v-model="formData.remarks" type="textarea" :rows="3" placeholder="请输入备注信息" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="mode !== 'view'">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted, onUnmounted, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import { 
  getEnterpriseList, 
  saveMonitorDevice, 
  updateMonitorDevice, 
  getPipelineList, 
  getSewageFactoryList,
  getPumpStationList,
  getWellList
} from '@/api/drainage';
import { 
  DEVICE_TYPE_OPTIONS, 
  ONLINE_STATUS_OPTIONS,
  MONITOR_INDEX_OPTIONS,
  MONITOR_OBJECT_OPTIONS
} from '@/constants/drainage';
import { AREA_OPTIONS } from '@/constants/gas';
import { collectShow } from "@/hooks/gishooks";
import bus from '@/utils/mitt';

// 过滤掉选项中的"全部"选项
const deviceTypeOptions = DEVICE_TYPE_OPTIONS.filter(item => item.value !== '');
const onlineStatusOptions = ONLINE_STATUS_OPTIONS.filter(item => item.value !== '');
const monitorIndexOptions = MONITOR_INDEX_OPTIONS.filter(item => item.value !== '');
const monitorObjectOptions = MONITOR_OBJECT_OPTIONS.filter(item => item.value !== '');

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add', // 'add', 'edit', 'view'
    validator: (value) => ['add', 'edit', 'view'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'success']);

// 表单引用
const formRef = ref(null);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 对话框标题
const dialogTitle = computed(() => {
  const titles = {
    add: '新增监测设备',
    edit: '编辑监测设备',
    view: '监测设备详情'
  };
  return titles[props.mode] || '监测设备信息';
});

// 权属单位列表
const managementUnits = ref([]);
// 行政区划选项
const areaOptions = ref(AREA_OPTIONS);
// 对象选项
const objectOptions = ref([]);
// 对象加载状态
const objectLoading = ref(false);

// 表单数据
const formData = reactive({
  id: '',
  deviceName: '',
  indexCode: '',
  deviceType: '',
  deviceTypeName: '',
  monitorIndex: '',
  monitorIndexName: '',
  collectFrequency: 60,
  uploadFrequency: 60,
  measureRangeLow: 0,
  measureRangeUp: 100,
  measureUnit: '',
  monitorTarget: '',
  monitorTargetName: '',
  monitorObjectId: '',
  monitorObjectName: '',
  onlineStatus: 1,
  ownershipUnit: '',
  ownershipUnitName: '',
  regionCode: '',
  regionName: '',
  regionPath: '',
  regionPathName: '',
  address: '',
  longitude: '',
  latitude: '',
  remarks: '',
  geomText: ''
});

// 表单验证规则
const formRules = {
  deviceName: [{ required: true, message: '请输入设备名称', trigger: 'blur' }],
  indexCode: [{ required: true, message: '请输入设备编号', trigger: 'blur' }],
  deviceType: [{ required: true, message: '请选择设备类型', trigger: 'change' }],
  monitorIndex: [{ required: true, message: '请选择监测指标', trigger: 'change' }],
  collectFrequency: [{ required: true, message: '请输入采集频率', trigger: 'blur' }],
  uploadFrequency: [{ required: true, message: '请输入上传频率', trigger: 'blur' }],
  monitorTarget: [{ required: true, message: '请选择监测对象', trigger: 'change' }],
  monitorObjectId: [{ required: true, message: '请选择对象', trigger: 'change' }],
  onlineStatus: [{ required: true, message: '请选择设备状态', trigger: 'change' }],
  ownershipUnit: [{ required: true, message: '请选择权属单位', trigger: 'change' }]
};

// 重置表单
const resetForm = () => {
  // 重置表单数据
  Object.keys(formData).forEach(key => {
    if (key === 'collectFrequency' || key === 'uploadFrequency') {
      formData[key] = 60;
    } else if (key === 'measureRangeLow') {
      formData[key] = 0;
    } else if (key === 'measureRangeUp') {
      formData[key] = 100;
    } else if (key === 'onlineStatus') {
      formData[key] = 1;
    } else if (typeof formData[key] === 'number') {
      formData[key] = 0;
    } else {
      formData[key] = '';
    }
  });
  
  // 清空对象选项
  objectOptions.value = [];
};

// 更新各字段的名称，基于选中的值
const updateNamesByValues = () => {
  // 设备类型
  const selectedDeviceType = deviceTypeOptions.find(item => item.value === formData.deviceType);
  if (selectedDeviceType) {
    formData.deviceTypeName = selectedDeviceType.label;
  }
  
  // 监测指标
  const selectedMonitorIndex = monitorIndexOptions.find(item => item.value === formData.monitorIndex);
  if (selectedMonitorIndex) {
    formData.monitorIndexName = selectedMonitorIndex.label;
  }
  
  // 监测对象
  const selectedMonitorTarget = monitorObjectOptions.find(item => item.value === formData.monitorTarget);
  if (selectedMonitorTarget) {
    formData.monitorTargetName = selectedMonitorTarget.label;
  }
  
  // 设备状态
  const selectedOnlineStatus = onlineStatusOptions.find(item => item.value === formData.onlineStatus);
  if (selectedOnlineStatus) {
    formData.onlineStatusName = selectedOnlineStatus.label === '在线' ? '使用中' : '使用中';
  }

  // 权属单位 - 现在使用名称作为值，直接赋值
  formData.ownershipUnitName = formData.ownershipUnit;
};

// 监听props.data变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    // 复制数据到表单
    Object.keys(formData).forEach(key => {
      if (newVal[key] !== undefined) {
        formData[key] = newVal[key];
      }
    });
    
    // 处理设备类型回显
    if (newVal.deviceType) {
      const selectedDeviceType = deviceTypeOptions.find(item => item.value === parseInt(newVal.deviceType));
      if (selectedDeviceType) {
        formData.deviceType = selectedDeviceType.value;
        formData.deviceTypeName = selectedDeviceType.label;
      }
    }
    
    // 处理监测指标回显
    if (newVal.monitorIndex) {
      const selectedMonitorIndex = monitorIndexOptions.find(item => item.value === parseInt(newVal.monitorIndex));
      if (selectedMonitorIndex) {
        formData.monitorIndex = selectedMonitorIndex.value;
        formData.monitorIndexName = selectedMonitorIndex.label;
      }
    }
    
    // 处理监测对象回显
    if (newVal.monitorTarget) {
      const selectedMonitorTarget = monitorObjectOptions.find(item => item.value === parseInt(newVal.monitorTarget));
      if (selectedMonitorTarget) {
        formData.monitorTarget = selectedMonitorTarget.value;
        formData.monitorTargetName = selectedMonitorTarget.label;
        // 加载对象选项
        loadObjectOptions(selectedMonitorTarget.value);
      }
    }
    
    // 处理设备状态回显
    if (newVal.onlineStatus !== undefined) {
      const selectedOnlineStatus = onlineStatusOptions.find(item => item.value === parseInt(newVal.onlineStatus));
      if (selectedOnlineStatus) {
        formData.onlineStatus = selectedOnlineStatus.value;
      }
    }
    
    // 处理权属单位回显
    if (newVal.ownershipUnit) {
      formData.ownershipUnit = newVal.ownershipUnit;
      formData.ownershipUnitName = newVal.ownershipUnit;
    }
    
    // 处理区域回显
    if (newVal.regionPath) {
      try {
        // 尝试将字符串转换为数组
        if (typeof newVal.regionPath === 'string') {
          formData.regionPath = newVal.regionPath.split(',').map(code => parseInt(code));
        } else if (Array.isArray(newVal.regionPath)) {
          formData.regionPath = [...newVal.regionPath];
        }
        
        // 如果有区域代码，但没有区域路径，尝试自动构建
        if (newVal.regionCode && !newVal.regionPath) {
          const area = findAreaByCode(areaOptions.value, parseInt(newVal.regionCode));
          if (area) {
            const path = buildAreaPath(areaOptions.value, parseInt(newVal.regionCode));
            if (path.length > 0) {
              formData.regionPath = path;
            }
          }
        }
      } catch (error) {
        console.error('处理区域回显失败:', error);
      }
    }
  } else if (props.mode === 'add') {
    // 新增模式清空表单，保留默认值
    resetForm();
  }
}, { immediate: true, deep: true });

// 监听下拉框值变化，自动更新对应的名称
watch(() => formData.deviceType, (val) => {
  if (val) {
    const selected = deviceTypeOptions.find(item => item.value === val);
    if (selected) {
      formData.deviceTypeName = selected.label;
    }
  }
});

watch(() => formData.monitorIndex, (val) => {
  if (val) {
    const selected = monitorIndexOptions.find(item => item.value === val);
    if (selected) {
      formData.monitorIndexName = selected.label;
    }
  }
});

watch(() => formData.monitorTarget, (val) => {
  if (val) {
    const selected = monitorObjectOptions.find(item => item.value === val);
    if (selected) {
      formData.monitorTargetName = selected.label;
    }
  }
});

watch(() => formData.onlineStatus, (val) => {
  if (val !== undefined) {
    const selected = onlineStatusOptions.find(item => item.value === val);
    if (selected) {
      formData.onlineStatusName = selected.label === '在线' ? '使用中' : '使用中';
    }
  }
});

watch(() => formData.ownershipUnit, (val) => {
  if (val) {
    formData.ownershipUnitName = val;
  }
});

// 监测对象变化时加载对应的选项
const handleMonitorTargetChange = (value) => {
  formData.monitorObjectId = '';
  formData.monitorObjectName = '';
  loadObjectOptions(value);
};

// 根据监测对象类型加载选项数据
const loadObjectOptions = async (targetType) => {
  if (!targetType) return;
  
  objectLoading.value = true;
  objectOptions.value = [];
  
  try {
    let res;
    // 根据选中的监测对象类型加载对应的选项
    switch (parseInt(targetType)) {
      case 3002201: // 管线
        res = await getPipelineList({});
        if (res && res.data) {
          objectOptions.value = res.data.map(item => ({
            label: item.pipelineTypeName,
            value: item.id
          }));
        }
        break;
      case 3002202: // 水厂
        res = await getSewageFactoryList({});
        if (res && res.data) {
          objectOptions.value = res.data.map(item => ({
            label: item.factoryName,
            value: item.id
          }));
        }
        break;
      case 3002203: // 泵站
        res = await getPumpStationList({});
        if (res && res.data) {
          objectOptions.value = res.data.map(item => ({
            label: item.stationName,
            value: item.id
          }));
        }
        break;
      case 3002204: // 窨井
        res = await getWellList({});
        if (res && res.data) {
          objectOptions.value = res.data.map(item => ({
            label: `${item.wellTypeName}${item.wellCode}`,
            value: item.id
          }));
        }
        break;
      default:
        break;
    }
    
    // 如果有回显数据，需要选中对应的项
    if (formData.monitorObjectId && props.data.monitorObjectId) {
      formData.monitorObjectId = props.data.monitorObjectId;
      const selectedObject = objectOptions.value.find(item => item.value === props.data.monitorObjectId);
      if (selectedObject) {
        formData.monitorObjectName = selectedObject.label;
      }
    }
  } catch (error) {
    console.error('加载对象选项失败', error);
    ElMessage.error('加载对象选项失败');
  } finally {
    objectLoading.value = false;
  }
};

// 获取权属单位列表
const fetchManagementUnits = async () => {
  try {
    const res = await getEnterpriseList();
    if (res && res.data) {
      managementUnits.value = res.data || [];
    }
  } catch (error) {
    console.error('获取权属单位列表失败', error);
  }
};

// 处理区域选择变化
const handleAreaChange = (value) => {
  if (value && value.length > 0) {
    formData.regionCode = value[value.length - 1];
    // 更新区域路径
    formData.regionPath = value.join(',');
    // 更新区域名称
    const selectedArea = findAreaByCode(areaOptions.value, formData.regionCode);
    if (selectedArea) {
      formData.regionName = selectedArea.name;
      
      // 构建区域路径名称
      const pathNames = [];
      let currentLevel = areaOptions.value;
      for (const code of value) {
        const area = findAreaByCode(currentLevel, code);
        if (area) {
          pathNames.push(area.name);
          currentLevel = area.children || [];
        }
      }
      formData.regionPathName = pathNames.join('/');
    }
  }
};

// 根据区域代码查找区域信息
const findAreaByCode = (areas, code) => {
  for (const area of areas) {
    if (area.code === code) {
      return area;
    }
    if (area.children) {
      const found = findAreaByCode(area.children, code);
      if (found) return found;
    }
  }
  return null;
};

// 构建区域路径
const buildAreaPath = (areas, targetCode) => {
  const path = [];
  const find = (areas, targetCode, currentPath) => {
    for (const area of areas) {
      const newPath = [...currentPath, area.code];
      if (area.code === targetCode) {
        path.push(...newPath);
        return true;
      }
      if (area.children && find(area.children, targetCode, newPath)) {
        return true;
      }
    }
    return false;
  };
  find(areas, targetCode, []);
  return path;
};

// 打开地图选点
const openMapPicker = () => {
  collectShow.value = true; // 激活采集点位窗口
  // 先移除可能存在的旧监听器
  bus.off("getCollectLocation", handleCollectLocation);
  // 添加新的监听器
  bus.on("getCollectLocation", handleCollectLocation);
};

// 地图选点事件处理函数
const handleCollectLocation = (params) => {
  nextTick(() => {
    formData.longitude = params.longitude;
    formData.latitude = params.latitude;
  });
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  // 重置表单
  if (formRef.value) {
    formRef.value.resetFields();
  }
  resetForm();
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;
  
  try {
    await formRef.value.validate();
    
    // 更新表单中的名称字段
    updateNamesByValues();
    
    // 准备提交数据
    const submitData = { ...formData };
    
    // 提交数据
    let res;
    if (props.mode === 'add') {
      res = await saveMonitorDevice(submitData);
    } else if (props.mode === 'edit') {
      res = await updateMonitorDevice(submitData);
    }
    
    if (res && res.code === 200) {
      ElMessage.success(props.mode === 'add' ? '新增成功' : '更新成功');
      emit('success');
      handleClose();
    } else {
      ElMessage.error(res?.msg || (props.mode === 'add' ? '新增失败' : '更新失败'));
    }
  } catch (error) {
    console.error('表单验证失败', error);
  }
};

// 组件卸载时清理事件监听
onUnmounted(() => {
  bus.off("getCollectLocation", handleCollectLocation);
});

// 组件挂载时获取数据
onMounted(() => {
  fetchManagementUnits();
});
</script>

<style scoped>
.drainage-device-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-form-item__label::before) {
  content: '*';
  color: #ff4d4f;
  margin-right: 4px;
}

:deep(.el-form-item:not([required]) .el-form-item__label::before) {
  content: '';
  margin-right: 0;
}

:deep(.el-input__wrapper),
:deep(.el-select__input) {
  border-radius: 2px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  background-color: #0277FD;
  border-color: #0277FD;
}

:deep(.el-button--primary:hover) {
  background-color: #1890ff;
  border-color: #1890ff;
}

.w-full {
  width: 100%;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.ml-2 {
  margin-left: 8px;
}

.mr-2 {
  margin-right: 8px;
}

.mx-2 {
  margin-left: 8px;
  margin-right: 8px;
}

.w-32 {
  width: 140px;
}

.w-full-unit {
  width: calc(100% - 60px) !important;
}

.unit-label {
  display: inline-block;
  white-space: nowrap;
  width: 55px;
  margin-left: 5px;
}

.measure-range-input {
  width: calc(50% - 25px) !important;
}
</style> 