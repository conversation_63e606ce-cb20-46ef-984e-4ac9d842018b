<template>
  <PanelBox title="管网风险" class="right-top-panel">
    <template #extra>
      <div class="tab-buttons">
        <div 
          class="tab-btn" 
          :class="{ active: activeTab === 'pipeline' }" 
          @click="changeTab('pipeline')"
        >
          管网
        </div>
        <div class="divider"></div>
        <div 
          class="tab-btn" 
          :class="{ active: activeTab === 'station' }" 
          @click="changeTab('station')"
        >
          场站
        </div>
      </div>
    </template>
    <div class="panel-content">
      <div class="content-wrapper">
        <div class="risk-chart">
          <div class="chart-container" ref="chartRef"></div>
          <div class="center-text">
            <div class="risk-total">{{activeTab === 'pipeline' ? totalRisk.toFixed(2) : totalRisk}}</div>
            <div class="unit"> {{ activeTab === 'pipeline' ? 'KM' : '个' }}</div>
          </div>
        </div>
        <div class="risk-list">
          <div class="risk-item" v-for="(item, index) in riskItems" :key="index">
            <div class="risk-indicator" :style="{ background: item.color }"></div>
            <div class="risk-name">{{ item.name }}</div>
            <div class="risk-value">{{ activeTab === 'pipeline' ? item.value.toFixed(2) : item.value }} 
              <span class="unit-text">{{ activeTab === 'pipeline' ? 'KM' : '个' }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </PanelBox>
</template>

<script setup>
import { ref, onMounted, nextTick, computed, watch } from 'vue'
import * as echarts from 'echarts'
import PanelBox from '@/components/screen/PanelBox.vue'
import { getPipelineRiskStatistics, getStationRiskStatistics } from '@/api/gas' // 引入 API 函数

// 综合态势总览右上面板组件

// 定义数据源
const activeTab = ref('pipeline') // 默认选择管网
const chartRef = ref(null)
let chartInstance = null

// 风险等级颜色映射
const riskColorMap = {
  '重大风险': '#EC1616',
  '较大风险': '#FA9700',
  '一般风险': '#FFE100',
  '低风险': '#23CAFF'
}

// 动态数据
const pipelineData = ref([])
const stationData = ref([])

// 计算当前展示数据
const riskItems = computed(() => {
  const data = activeTab.value === 'pipeline' ? pipelineData.value : stationData.value
  // 确保数据按特定顺序（重大、较大、一般、低）显示，如果API不保证顺序
  const order = ['重大风险', '较大风险', '一般风险', '低风险']
  return data.sort((a, b) => order.indexOf(a.name) - order.indexOf(b.name))
})

// 计算总风险值 (使用 length 字段)
const totalRisk = computed(() => {
  // 将 length 转换为数字再累加
  return riskItems.value.reduce((sum, item) => sum + Number(item.value || 0), 0)
})

// 监听数据变化，更新图表
watch([riskItems], () => {
  if (chartInstance) {
    updateChart()
  }
})

// 切换标签页
const changeTab = (tab) => {
  activeTab.value = tab
  fetchRiskData(tab)
}

// 接口请求方法
const fetchRiskData = async (tabType) => {
  try {
    let response
    if (tabType === 'pipeline') {
      response = await getPipelineRiskStatistics()
    } else {
      response = await getStationRiskStatistics()
    }

    if (response.code === 200 && response.data && response.data.riskLevelStatistics) {
      const formattedData = response.data.riskLevelStatistics.map(item => ({
        name: item.name,
        value: item.length, // 使用 length 字段作为值
        color: riskColorMap[item.name] || '#ccc' // 使用映射获取颜色
      }))
      if (tabType === 'pipeline') {
        pipelineData.value = formattedData
      } else {
        stationData.value = formattedData
      }
    } else {
      console.error(`获取${tabType === 'pipeline' ? '管网' : '场站'}风险数据失败:`, response)
      // 清空数据或显示错误提示
      if (tabType === 'pipeline') {
        pipelineData.value = []
      } else {
        stationData.value = []
      }
    }
  } catch (error) {
    console.error(`获取${tabType === 'pipeline' ? '管网' : '场站'}风险数据失败:`, error)
     // 清空数据或显示错误提示
     if (tabType === 'pipeline') {
        pipelineData.value = []
      } else {
        stationData.value = []
      }
  }
}

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return
  
  chartInstance = echarts.init(chartRef.value)
  updateChart() // 初始绘制空图或基于默认tab的数据
  
  window.addEventListener('resize', () => {
    chartInstance && chartInstance.resize()
  })
}

// 更新图表数据
const updateChart = () => {
  if (!chartInstance) return

  const data = riskItems.value
  const colorList = data.map(item => item.color)
  // 确保 value 是数字类型
  const valueList = data.map(item => Number(item.value || 0))

  const option = {
    backgroundColor: 'transparent',
    series: [{
      type: 'pie',
      radius: ['75%', '85%'], // 修改为10px厚度的环形图
      center: ['50%', '50%'],
      startAngle: 0,
      itemStyle: {
        borderRadius: 0,
        borderColor: 'transparent',
        borderWidth: 0
      },
      label: {
        show: false
      },
      silent: true,
      data: valueList.map((value, index) => ({
        value,
        name: data[index].name,
        itemStyle: {
          color: colorList[index]
        }
      }))
    }]
  }
  
  chartInstance.setOption(option, true) // 使用 true 清除旧图表配置
}

onMounted(async () => {
  await nextTick()
  initChart()
  // 初始化时调用接口获取默认标签页数据
  await fetchRiskData(activeTab.value)
})
</script>

<style scoped>
.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
}

.content-wrapper {
  display: flex;
  align-items: center;
  gap: 20px;
  height: 100%;
}

.tab-buttons {
  display: flex;
  align-items: center;
  margin-right: 30px;
}

.divider {
  width: 1px;
  height: 16px;
  background-color: rgba(255, 255, 255, 0.8);
  margin: 0 10px;
}

.tab-btn {
  cursor: pointer;
  font-family: PingFangSC, 'PingFang SC';
  font-weight: 500;
  font-size: 16px;
  color: rgba(255, 255, 255, 0.3);
  line-height: 22px;
}

.tab-btn.active {
  color: rgba(255, 255, 255, 0.8);
}

.risk-chart {
  width: 192px;
  height: 192px;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.chart-container {
  width: 192px;
  height: 192px;
  background-image: url('@/assets/images/screen/gas/guanwangfengxian.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  position: relative;
}

.center-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #fff;
}

.risk-total {
  font-size: 24px;
  font-weight: bold;
  color: #22CBFF;
  margin-bottom: 4px;
}

.unit {
  font-size: 12px;
  color: #85A5C3;
}

.risk-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.risk-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.risk-indicator {
  width: 6px;
  height: 16px;
  border-radius: 3px;
}

.risk-name {
  font-family: PingFangSC, 'PingFang SC';
  font-weight: 500;
  font-size: 14px;
  color: #FFFFFF;
  white-space: nowrap;
  width: 70px;
}

.risk-value {
  font-family: 'DIN Alternate', sans-serif;
  font-weight: bold;
  font-size: 16px;
  color: #FFFFFF;
}

.unit-text {
  font-size: 12px;
  font-weight: normal;
  color: rgba(255, 255, 255, 0.6);
  margin-left: 4px;
}

/* 940px左右高度的屏幕特别优化 */
@media (min-height: 940px) and (max-height: 1055px){
  .panel-content {
    padding: 10px;
  }
  
  .content-wrapper {
    gap: 15px;
  }
  
  /* 调整标签页按钮 */
  .tab-buttons {
    margin-right: 26px;
  }
  
  .tab-btn {
    font-size: 14px;
  }
  
  .divider {
    height: 14px;
    margin: 0 8px;
  }
  
  /* 调整图表尺寸 */
  .risk-chart {
    width: 160px;
    height: 160px;
  }
  
  .chart-container {
    width: 160px;
    height: 160px;
  }
  
  .risk-total {
    font-size: 22px;
    margin-bottom: 2px;
  }
  
  .unit {
    font-size: 10px;
  }
  
  /* 调整列表项 */
  .risk-list {
    gap: 5px;
  }
  
  .risk-item {
    gap: 8px;
  }
  
  .risk-indicator {
    width: 5px;
    height: 14px;
  }
  
  .risk-name {
    font-size: 13px;
    width: 65px;
  }
  
  .risk-value {
    font-size: 14px;
  }
  
  .unit-text {
    font-size: 11px;
    margin-left: 2px;
  }
}
</style>