/* 添加 REM 适配配置 */

/* 基准值设置 - 1920px宽度下 1rem = 16px */
html {
  font-size: 16px;
}

:root {
  --screen-width: 1920px;
}

/* 媒体查询适配不同屏幕尺寸 */
/* 小于1366px的屏幕 */
@media screen and (max-width: 1366px) {
  html {
    font-size: 11.4px; /* 16 * (1366 / 1920) */
  }
}

/* 1366px - 1600px屏幕 */
@media screen and (min-width: 1367px) and (max-width: 1600px) {
  html {
    font-size: 13.3px; /* 16 * (1600 / 1920) */
  }
}

/* 1601px - 1920px屏幕 */
@media screen and (min-width: 1601px) and (max-width: 1920px) {
  html {
    font-size: 16px;
  }
}

/* 1921px - 2560px屏幕 - 2K及以上 */
@media screen and (min-width: 1921px) and (max-width: 2560px) {
  html {
    font-size: 21.3px; /* 16 * (2560 / 1920) */
  }
}

/* 2561px - 3440px屏幕 - 超宽屏 */
@media screen and (min-width: 2561px) and (max-width: 3440px) {
  html {
    font-size: 28.7px; /* 16 * (3440 / 1920) */
  }
}

/* 3840px及以上 - 4K */
@media screen and (min-width: 3441px) {
  html {
    font-size: 32px; /* 16 * (3840 / 1920) */
  }
}

/* 低高度屏幕适配 */
@media screen and (max-height: 900px) {
  html {
    /* 保持水平缩放比例，但稍微压缩垂直空间 */
    --vertical-scale: 0.9;
  }
}

/* 940px左右高度的屏幕特别优化 */
@media screen and (min-height: 940px) and (max-height: 1055px) {
  html {
    --vertical-scale: 0.95;
  }
}

/* 非全屏大屏情况下的自适应 */
@media screen and (min-device-width: 1366px) {
  @media (orientation: landscape) {
    html {
      /* 动态计算font-size，使其根据视口宽度进行缩放 */
      font-size: calc(16 * 100vw / 1920 * 1px);
    }
  }
} 