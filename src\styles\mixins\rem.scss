// REM适配工具函数和混合宏
// 基于1920*1080的设计稿

// 引入sass模块
@use "sass:math";
@use "sass:meta";

// 基准值：1rem = 16px (1920px设计稿)
$base-font-size: 16px;

// px转rem函数
@function rem($px) {
  @if meta.type-of($px) == number {
    @return math.div($px, $base-font-size) * 1rem;
  } @else {
    @error "rem函数的参数必须是数字类型";
    @return 0;
  }
}

// 响应式字体大小混合宏
@mixin font-size($px) {
  font-size: rem($px);
}

// 响应式宽度混合宏
@mixin width($px) {
  width: rem($px);
}

// 响应式高度混合宏
@mixin height($px) {
  height: rem($px);
}

// 响应式内边距混合宏
@mixin padding($top, $right: $top, $bottom: $top, $left: $right) {
  padding: rem($top) rem($right) rem($bottom) rem($left);
}

// 响应式外边距混合宏
@mixin margin($top, $right: $top, $bottom: $top, $left: $right) {
  margin: rem($top) rem($right) rem($bottom) rem($left);
}

// 响应式定位混合宏
@mixin position($position, $top: null, $right: null, $bottom: null, $left: null) {
  position: $position;
  
  @if $top != null {
    top: rem($top);
  }
  
  @if $right != null {
    right: rem($right);
  }
  
  @if $bottom != null {
    bottom: rem($bottom);
  }
  
  @if $left != null {
    left: rem($left);
  }
}

// 最大宽度混合宏
@mixin max-width($px) {
  max-width: rem($px);
}

// 最小宽度混合宏
@mixin min-width($px) {
  min-width: rem($px);
}

// 圆角混合宏
@mixin border-radius($radius) {
  border-radius: rem($radius);
}

// 生成常用的尺寸变量
$spacings: (
  4: rem(4px),
  8: rem(8px),
  12: rem(12px),
  16: rem(16px),
  20: rem(20px),
  24: rem(24px),
  32: rem(32px),
  40: rem(40px),
  48: rem(48px),
  56: rem(56px),
  64: rem(64px),
  80: rem(80px),
  100: rem(100px)
);

// 生成常用字体大小变量
$font-sizes: (
  12: rem(12px),
  14: rem(14px),
  16: rem(16px),
  18: rem(18px),
  20: rem(20px),
  24: rem(24px),
  28: rem(28px),
  32: rem(32px),
  36: rem(36px),
  40: rem(40px),
  48: rem(48px)
);

// 使用示例：
// .example {
//   width: rem(100px);
//   height: rem(200px);
//   font-size: rem(16px);
//   padding: rem(10px) rem(20px);
//   margin-bottom: rem(30px);
//   @include position(absolute, 10px, 20px, 30px, 40px);
// } 