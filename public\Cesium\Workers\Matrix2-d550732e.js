/**
 *    ████████                   ██                   ██       ██                  ██      ██
 *   ██░░░░░░██                 ░░                   ░██      ░██                 ░██     ░██
 *  ██      ░░   █████  ███████  ██ ██   ██  ██████  ░██   █  ░██  ██████  ██████ ░██     ░██
 * ░██          ██░░░██░░██░░░██░██░██  ░██ ██░░░░   ░██  ███ ░██ ██░░░░██░░██░░█ ░██  ██████
 * ░██    █████░███████ ░██  ░██░██░██  ░██░░█████   ░██ ██░██░██░██   ░██ ░██ ░  ░██ ██░░░██
 * ░░██  ░░░░██░██░░░░  ░██  ░██░██░██  ░██ ░░░░░██  ░████ ░░████░██   ░██ ░██    ░██░██  ░██
 *  ░░████████ ░░██████ ███  ░██░██░░██████ ██████   ░██░   ░░░██░░██████ ░███    ███░░██████
 *   ░░░░░░░░   ░░░░░░ ░░░   ░░ ░░  ░░░░░░ ░░░░░░    ░░       ░░  ░░░░░░  ░░░    ░░░  ░░░░░░
 *
 * @license
 * GeniusWorld Web SDK - http://zydlxx.cn:1234/
 * Version 4.0.0.B#develop-d3ff2330@202405231107
 *
 *
 * Copyright © 2020 - 2023 正元地理信息集团股份有限公司. All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
define(["exports","./Matrix3-79d15570","./defaultValue-7b61670d","./Math-6acd1674","./RuntimeError-7dc4ea5a"],(function(t,e,n,a,r){"use strict";function u(t){let e;this.name="DeveloperError",this.message=t;try{throw new Error}catch(t){e=t.stack}this.stack=e}n.defined(Object.create)&&(u.prototype=Object.create(Error.prototype),u.prototype.constructor=u),u.prototype.toString=function(){let t=`${this.name}: ${this.message}`;return n.defined(this.stack)&&(t+=`\n${this.stack.toString()}`),t},u.throwInstantiationError=function(){throw new u("This function defines an interface and should not be called directly.")};const o={};function i(t,e,n){return`Expected ${n} to be typeof ${e}, actual typeof was ${t}`}o.typeOf={},o.defined=function(t,e){if(!n.defined(e))throw new u(function(t){return`${t} is required, actual value was undefined`}(t))},o.typeOf.func=function(t,e){if("function"!=typeof e)throw new u(i(typeof e,"function",t))},o.typeOf.string=function(t,e){if("string"!=typeof e)throw new u(i(typeof e,"string",t))},o.typeOf.number=function(t,e){if("number"!=typeof e)throw new u(i(typeof e,"number",t))},o.typeOf.number.lessThan=function(t,e,n){if(o.typeOf.number(t,e),e>=n)throw new u(`Expected ${t} to be less than ${n}, actual value was ${e}`)},o.typeOf.number.lessThanOrEquals=function(t,e,n){if(o.typeOf.number(t,e),e>n)throw new u(`Expected ${t} to be less than or equal to ${n}, actual value was ${e}`)},o.typeOf.number.greaterThan=function(t,e,n){if(o.typeOf.number(t,e),e<=n)throw new u(`Expected ${t} to be greater than ${n}, actual value was ${e}`)},o.typeOf.number.greaterThanOrEquals=function(t,e,n){if(o.typeOf.number(t,e),e<n)throw new u(`Expected ${t} to be greater than or equal to ${n}, actual value was ${e}`)},o.typeOf.object=function(t,e){if("object"!=typeof e)throw new u(i(typeof e,"object",t))},o.typeOf.bool=function(t,e){if("boolean"!=typeof e)throw new u(i(typeof e,"boolean",t))},o.typeOf.bigint=function(t,e){if("bigint"!=typeof e)throw new u(i(typeof e,"bigint",t))},o.typeOf.number.equals=function(t,e,n,a){if(o.typeOf.number(t,n),o.typeOf.number(e,a),n!==a)throw new u(`${t} must be equal to ${e}, the actual values are ${n} and ${a}`)};var s=o;function c(t,e,a,r){this.x=n.defaultValue(t,0),this.y=n.defaultValue(e,0),this.z=n.defaultValue(a,0),this.w=n.defaultValue(r,0)}c.fromElements=function(t,e,a,r,u){return n.defined(u)?(u.x=t,u.y=e,u.z=a,u.w=r,u):new c(t,e,a,r)},c.fromColor=function(t,e){return n.defined(e)?(e.x=t.red,e.y=t.green,e.z=t.blue,e.w=t.alpha,e):new c(t.red,t.green,t.blue,t.alpha)},c.clone=function(t,e){if(n.defined(t))return n.defined(e)?(e.x=t.x,e.y=t.y,e.z=t.z,e.w=t.w,e):new c(t.x,t.y,t.z,t.w)},c.packedLength=4,c.pack=function(t,e,a){return a=n.defaultValue(a,0),e[a++]=t.x,e[a++]=t.y,e[a++]=t.z,e[a]=t.w,e},c.unpack=function(t,e,a){return e=n.defaultValue(e,0),n.defined(a)||(a=new c),a.x=t[e++],a.y=t[e++],a.z=t[e++],a.w=t[e],a},c.packArray=function(t,e){const a=t.length,r=4*a;n.defined(e)?(Array.isArray(e)||e.length===r)&&e.length!==r&&(e.length=r):e=new Array(r);for(let n=0;n<a;++n)c.pack(t[n],e,4*n);return e},c.unpackArray=function(t,e){const a=t.length;n.defined(e)?e.length=a/4:e=new Array(a/4);for(let n=0;n<a;n+=4){const a=n/4;e[a]=c.unpack(t,n,e[a])}return e},c.fromArray=c.unpack,c.maximumComponent=function(t){return Math.max(t.x,t.y,t.z,t.w)},c.minimumComponent=function(t){return Math.min(t.x,t.y,t.z,t.w)},c.minimumByComponent=function(t,e,n){return n.x=Math.min(t.x,e.x),n.y=Math.min(t.y,e.y),n.z=Math.min(t.z,e.z),n.w=Math.min(t.w,e.w),n},c.maximumByComponent=function(t,e,n){return n.x=Math.max(t.x,e.x),n.y=Math.max(t.y,e.y),n.z=Math.max(t.z,e.z),n.w=Math.max(t.w,e.w),n},c.clamp=function(t,e,n,r){const u=a.CesiumMath.clamp(t.x,e.x,n.x),o=a.CesiumMath.clamp(t.y,e.y,n.y),i=a.CesiumMath.clamp(t.z,e.z,n.z),s=a.CesiumMath.clamp(t.w,e.w,n.w);return r.x=u,r.y=o,r.z=i,r.w=s,r},c.magnitudeSquared=function(t){return t.x*t.x+t.y*t.y+t.z*t.z+t.w*t.w},c.magnitude=function(t){return Math.sqrt(c.magnitudeSquared(t))};const l=new c;c.distance=function(t,e){return c.subtract(t,e,l),c.magnitude(l)},c.distanceSquared=function(t,e){return c.subtract(t,e,l),c.magnitudeSquared(l)},c.normalize=function(t,e){const n=c.magnitude(t);return e.x=t.x/n,e.y=t.y/n,e.z=t.z/n,e.w=t.w/n,e},c.dot=function(t,e){return t.x*e.x+t.y*e.y+t.z*e.z+t.w*e.w},c.multiplyComponents=function(t,e,n){return n.x=t.x*e.x,n.y=t.y*e.y,n.z=t.z*e.z,n.w=t.w*e.w,n},c.divideComponents=function(t,e,n){return n.x=t.x/e.x,n.y=t.y/e.y,n.z=t.z/e.z,n.w=t.w/e.w,n},c.add=function(t,e,n){return n.x=t.x+e.x,n.y=t.y+e.y,n.z=t.z+e.z,n.w=t.w+e.w,n},c.subtract=function(t,e,n){return n.x=t.x-e.x,n.y=t.y-e.y,n.z=t.z-e.z,n.w=t.w-e.w,n},c.multiplyByScalar=function(t,e,n){return n.x=t.x*e,n.y=t.y*e,n.z=t.z*e,n.w=t.w*e,n},c.divideByScalar=function(t,e,n){return n.x=t.x/e,n.y=t.y/e,n.z=t.z/e,n.w=t.w/e,n},c.negate=function(t,e){return e.x=-t.x,e.y=-t.y,e.z=-t.z,e.w=-t.w,e},c.abs=function(t,e){return e.x=Math.abs(t.x),e.y=Math.abs(t.y),e.z=Math.abs(t.z),e.w=Math.abs(t.w),e};const f=new c;c.lerp=function(t,e,n,a){return c.multiplyByScalar(e,n,f),a=c.multiplyByScalar(t,1-n,a),c.add(f,a,a)};const h=new c;c.mostOrthogonalAxis=function(t,e){const n=c.normalize(t,h);return c.abs(n,n),e=n.x<=n.y?n.x<=n.z?n.x<=n.w?c.clone(c.UNIT_X,e):c.clone(c.UNIT_W,e):n.z<=n.w?c.clone(c.UNIT_Z,e):c.clone(c.UNIT_W,e):n.y<=n.z?n.y<=n.w?c.clone(c.UNIT_Y,e):c.clone(c.UNIT_W,e):n.z<=n.w?c.clone(c.UNIT_Z,e):c.clone(c.UNIT_W,e)},c.equals=function(t,e){return t===e||n.defined(t)&&n.defined(e)&&t.x===e.x&&t.y===e.y&&t.z===e.z&&t.w===e.w},c.equalsArray=function(t,e,n){return t.x===e[n]&&t.y===e[n+1]&&t.z===e[n+2]&&t.w===e[n+3]},c.equalsEpsilon=function(t,e,r,u){return t===e||n.defined(t)&&n.defined(e)&&a.CesiumMath.equalsEpsilon(t.x,e.x,r,u)&&a.CesiumMath.equalsEpsilon(t.y,e.y,r,u)&&a.CesiumMath.equalsEpsilon(t.z,e.z,r,u)&&a.CesiumMath.equalsEpsilon(t.w,e.w,r,u)},c.ZERO=Object.freeze(new c(0,0,0,0)),c.ONE=Object.freeze(new c(1,1,1,1)),c.UNIT_X=Object.freeze(new c(1,0,0,0)),c.UNIT_Y=Object.freeze(new c(0,1,0,0)),c.UNIT_Z=Object.freeze(new c(0,0,1,0)),c.UNIT_W=Object.freeze(new c(0,0,0,1)),c.prototype.clone=function(t){return c.clone(this,t)},c.prototype.equals=function(t){return c.equals(this,t)},c.prototype.equalsEpsilon=function(t,e,n){return c.equalsEpsilon(this,t,e,n)},c.prototype.toString=function(){return`(${this.x}, ${this.y}, ${this.z}, ${this.w})`};const d=new Float32Array(1),y=new Uint8Array(d.buffer),m=new Uint32Array([287454020]),w=68===new Uint8Array(m.buffer)[0];function p(t,e,a,r,u,o,i,s,c,l,f,h,d,y,m,w){this[0]=n.defaultValue(t,0),this[1]=n.defaultValue(u,0),this[2]=n.defaultValue(c,0),this[3]=n.defaultValue(d,0),this[4]=n.defaultValue(e,0),this[5]=n.defaultValue(o,0),this[6]=n.defaultValue(l,0),this[7]=n.defaultValue(y,0),this[8]=n.defaultValue(a,0),this[9]=n.defaultValue(i,0),this[10]=n.defaultValue(f,0),this[11]=n.defaultValue(m,0),this[12]=n.defaultValue(r,0),this[13]=n.defaultValue(s,0),this[14]=n.defaultValue(h,0),this[15]=n.defaultValue(w,0)}c.packFloat=function(t,e){return n.defined(e)||(e=new c),d[0]=t,w?(e.x=y[0],e.y=y[1],e.z=y[2],e.w=y[3]):(e.x=y[3],e.y=y[2],e.z=y[1],e.w=y[0]),e},c.unpackFloat=function(t){return w?(y[0]=t.x,y[1]=t.y,y[2]=t.z,y[3]=t.w):(y[0]=t.w,y[1]=t.z,y[2]=t.y,y[3]=t.x),d[0]},p.packedLength=16,p.pack=function(t,e,a){return a=n.defaultValue(a,0),e[a++]=t[0],e[a++]=t[1],e[a++]=t[2],e[a++]=t[3],e[a++]=t[4],e[a++]=t[5],e[a++]=t[6],e[a++]=t[7],e[a++]=t[8],e[a++]=t[9],e[a++]=t[10],e[a++]=t[11],e[a++]=t[12],e[a++]=t[13],e[a++]=t[14],e[a]=t[15],e},p.unpack=function(t,e,a){return e=n.defaultValue(e,0),n.defined(a)||(a=new p),a[0]=t[e++],a[1]=t[e++],a[2]=t[e++],a[3]=t[e++],a[4]=t[e++],a[5]=t[e++],a[6]=t[e++],a[7]=t[e++],a[8]=t[e++],a[9]=t[e++],a[10]=t[e++],a[11]=t[e++],a[12]=t[e++],a[13]=t[e++],a[14]=t[e++],a[15]=t[e],a},p.packArray=function(t,e){const a=t.length,r=16*a;n.defined(e)?(Array.isArray(e)||e.length===r)&&e.length!==r&&(e.length=r):e=new Array(r);for(let n=0;n<a;++n)p.pack(t[n],e,16*n);return e},p.unpackArray=function(t,e){const a=t.length;n.defined(e)?e.length=a/16:e=new Array(a/16);for(let n=0;n<a;n+=16){const a=n/16;e[a]=p.unpack(t,n,e[a])}return e},p.clone=function(t,e){if(n.defined(t))return n.defined(e)?(e[0]=t[0],e[1]=t[1],e[2]=t[2],e[3]=t[3],e[4]=t[4],e[5]=t[5],e[6]=t[6],e[7]=t[7],e[8]=t[8],e[9]=t[9],e[10]=t[10],e[11]=t[11],e[12]=t[12],e[13]=t[13],e[14]=t[14],e[15]=t[15],e):new p(t[0],t[4],t[8],t[12],t[1],t[5],t[9],t[13],t[2],t[6],t[10],t[14],t[3],t[7],t[11],t[15])},p.fromArray=p.unpack,p.fromColumnMajorArray=function(t,e){return p.clone(t,e)},p.fromRowMajorArray=function(t,e){return n.defined(e)?(e[0]=t[0],e[1]=t[4],e[2]=t[8],e[3]=t[12],e[4]=t[1],e[5]=t[5],e[6]=t[9],e[7]=t[13],e[8]=t[2],e[9]=t[6],e[10]=t[10],e[11]=t[14],e[12]=t[3],e[13]=t[7],e[14]=t[11],e[15]=t[15],e):new p(t[0],t[1],t[2],t[3],t[4],t[5],t[6],t[7],t[8],t[9],t[10],t[11],t[12],t[13],t[14],t[15])},p.fromRotationTranslation=function(t,a,r){return a=n.defaultValue(a,e.Cartesian3.ZERO),n.defined(r)?(r[0]=t[0],r[1]=t[1],r[2]=t[2],r[3]=0,r[4]=t[3],r[5]=t[4],r[6]=t[5],r[7]=0,r[8]=t[6],r[9]=t[7],r[10]=t[8],r[11]=0,r[12]=a.x,r[13]=a.y,r[14]=a.z,r[15]=1,r):new p(t[0],t[3],t[6],a.x,t[1],t[4],t[7],a.y,t[2],t[5],t[8],a.z,0,0,0,1)},p.fromTranslationQuaternionRotationScale=function(t,e,a,r){n.defined(r)||(r=new p);const u=a.x,o=a.y,i=a.z,s=e.x*e.x,c=e.x*e.y,l=e.x*e.z,f=e.x*e.w,h=e.y*e.y,d=e.y*e.z,y=e.y*e.w,m=e.z*e.z,w=e.z*e.w,x=e.w*e.w,M=s-h-m+x,g=2*(c-w),C=2*(l+y),b=2*(c+w),z=-s+h-m+x,O=2*(d-f),V=2*(l-y),E=2*(d+f),T=-s-h+m+x;return r[0]=M*u,r[1]=b*u,r[2]=V*u,r[3]=0,r[4]=g*o,r[5]=z*o,r[6]=E*o,r[7]=0,r[8]=C*i,r[9]=O*i,r[10]=T*i,r[11]=0,r[12]=t.x,r[13]=t.y,r[14]=t.z,r[15]=1,r},p.fromTranslationRotationScale=function(t,e){return p.fromTranslationQuaternionRotationScale(t.translation,t.rotation,t.scale,e)},p.fromTranslation=function(t,n){return p.fromRotationTranslation(e.Matrix3.IDENTITY,t,n)},p.fromScale=function(t,e){return n.defined(e)?(e[0]=t.x,e[1]=0,e[2]=0,e[3]=0,e[4]=0,e[5]=t.y,e[6]=0,e[7]=0,e[8]=0,e[9]=0,e[10]=t.z,e[11]=0,e[12]=0,e[13]=0,e[14]=0,e[15]=1,e):new p(t.x,0,0,0,0,t.y,0,0,0,0,t.z,0,0,0,0,1)},p.fromUniformScale=function(t,e){return n.defined(e)?(e[0]=t,e[1]=0,e[2]=0,e[3]=0,e[4]=0,e[5]=t,e[6]=0,e[7]=0,e[8]=0,e[9]=0,e[10]=t,e[11]=0,e[12]=0,e[13]=0,e[14]=0,e[15]=1,e):new p(t,0,0,0,0,t,0,0,0,0,t,0,0,0,0,1)},p.fromRotation=function(t,e){return n.defined(e)||(e=new p),e[0]=t[0],e[1]=t[1],e[2]=t[2],e[3]=0,e[4]=t[3],e[5]=t[4],e[6]=t[5],e[7]=0,e[8]=t[6],e[9]=t[7],e[10]=t[8],e[11]=0,e[12]=0,e[13]=0,e[14]=0,e[15]=1,e};const x=new e.Cartesian3,M=new e.Cartesian3,g=new e.Cartesian3;p.fromCamera=function(t,a){const r=t.position,u=t.direction,o=t.up;e.Cartesian3.normalize(u,x),e.Cartesian3.normalize(e.Cartesian3.cross(x,o,M),M),e.Cartesian3.normalize(e.Cartesian3.cross(M,x,g),g);const i=M.x,s=M.y,c=M.z,l=x.x,f=x.y,h=x.z,d=g.x,y=g.y,m=g.z,w=r.x,C=r.y,b=r.z,z=i*-w+s*-C+c*-b,O=d*-w+y*-C+m*-b,V=l*w+f*C+h*b;return n.defined(a)?(a[0]=i,a[1]=d,a[2]=-l,a[3]=0,a[4]=s,a[5]=y,a[6]=-f,a[7]=0,a[8]=c,a[9]=m,a[10]=-h,a[11]=0,a[12]=z,a[13]=O,a[14]=V,a[15]=1,a):new p(i,s,c,z,d,y,m,O,-l,-f,-h,V,0,0,0,1)},p.computePerspectiveFieldOfView=function(t,e,n,a,r){const u=1/Math.tan(.5*t),o=u/e,i=(a+n)/(n-a),s=2*a*n/(n-a);return r[0]=o,r[1]=0,r[2]=0,r[3]=0,r[4]=0,r[5]=u,r[6]=0,r[7]=0,r[8]=0,r[9]=0,r[10]=i,r[11]=-1,r[12]=0,r[13]=0,r[14]=s,r[15]=0,r},p.computeOrthographicOffCenter=function(t,e,n,a,r,u,o){let i=1/(e-t),s=1/(a-n),c=1/(u-r);const l=-(e+t)*i,f=-(a+n)*s,h=-(u+r)*c;return i*=2,s*=2,c*=-2,o[0]=i,o[1]=0,o[2]=0,o[3]=0,o[4]=0,o[5]=s,o[6]=0,o[7]=0,o[8]=0,o[9]=0,o[10]=c,o[11]=0,o[12]=l,o[13]=f,o[14]=h,o[15]=1,o},p.computePerspectiveOffCenter=function(t,e,n,a,r,u,o){const i=2*r/(e-t),s=2*r/(a-n),c=(e+t)/(e-t),l=(a+n)/(a-n),f=-(u+r)/(u-r),h=-2*u*r/(u-r);return o[0]=i,o[1]=0,o[2]=0,o[3]=0,o[4]=0,o[5]=s,o[6]=0,o[7]=0,o[8]=c,o[9]=l,o[10]=f,o[11]=-1,o[12]=0,o[13]=0,o[14]=h,o[15]=0,o},p.computeInfinitePerspectiveOffCenter=function(t,e,n,a,r,u){const o=2*r/(e-t),i=2*r/(a-n),s=(e+t)/(e-t),c=(a+n)/(a-n),l=-2*r;return u[0]=o,u[1]=0,u[2]=0,u[3]=0,u[4]=0,u[5]=i,u[6]=0,u[7]=0,u[8]=s,u[9]=c,u[10]=-1,u[11]=-1,u[12]=0,u[13]=0,u[14]=l,u[15]=0,u},p.computeViewportTransformation=function(t,e,a,r){n.defined(r)||(r=new p),t=n.defaultValue(t,n.defaultValue.EMPTY_OBJECT);const u=n.defaultValue(t.x,0),o=n.defaultValue(t.y,0),i=n.defaultValue(t.width,0),s=n.defaultValue(t.height,0);e=n.defaultValue(e,0);const c=.5*i,l=.5*s,f=.5*((a=n.defaultValue(a,1))-e),h=c,d=l,y=f,m=u+c,w=o+l,x=e+f;return r[0]=h,r[1]=0,r[2]=0,r[3]=0,r[4]=0,r[5]=d,r[6]=0,r[7]=0,r[8]=0,r[9]=0,r[10]=y,r[11]=0,r[12]=m,r[13]=w,r[14]=x,r[15]=1,r},p.computeView=function(t,n,a,r,u){return u[0]=r.x,u[1]=a.x,u[2]=-n.x,u[3]=0,u[4]=r.y,u[5]=a.y,u[6]=-n.y,u[7]=0,u[8]=r.z,u[9]=a.z,u[10]=-n.z,u[11]=0,u[12]=-e.Cartesian3.dot(r,t),u[13]=-e.Cartesian3.dot(a,t),u[14]=e.Cartesian3.dot(n,t),u[15]=1,u},p.toArray=function(t,e){return n.defined(e)?(e[0]=t[0],e[1]=t[1],e[2]=t[2],e[3]=t[3],e[4]=t[4],e[5]=t[5],e[6]=t[6],e[7]=t[7],e[8]=t[8],e[9]=t[9],e[10]=t[10],e[11]=t[11],e[12]=t[12],e[13]=t[13],e[14]=t[14],e[15]=t[15],e):[t[0],t[1],t[2],t[3],t[4],t[5],t[6],t[7],t[8],t[9],t[10],t[11],t[12],t[13],t[14],t[15]]},p.getElementIndex=function(t,e){return 4*t+e},p.getColumn=function(t,e,n){const a=4*e,r=t[a],u=t[a+1],o=t[a+2],i=t[a+3];return n.x=r,n.y=u,n.z=o,n.w=i,n},p.setColumn=function(t,e,n,a){const r=4*e;return(a=p.clone(t,a))[r]=n.x,a[r+1]=n.y,a[r+2]=n.z,a[r+3]=n.w,a},p.getRow=function(t,e,n){const a=t[e],r=t[e+4],u=t[e+8],o=t[e+12];return n.x=a,n.y=r,n.z=u,n.w=o,n},p.setRow=function(t,e,n,a){return(a=p.clone(t,a))[e]=n.x,a[e+4]=n.y,a[e+8]=n.z,a[e+12]=n.w,a},p.setTranslation=function(t,e,n){return n[0]=t[0],n[1]=t[1],n[2]=t[2],n[3]=t[3],n[4]=t[4],n[5]=t[5],n[6]=t[6],n[7]=t[7],n[8]=t[8],n[9]=t[9],n[10]=t[10],n[11]=t[11],n[12]=e.x,n[13]=e.y,n[14]=e.z,n[15]=t[15],n};const C=new e.Cartesian3;p.setScale=function(t,e,n){const a=p.getScale(t,C),r=e.x/a.x,u=e.y/a.y,o=e.z/a.z;return n[0]=t[0]*r,n[1]=t[1]*r,n[2]=t[2]*r,n[3]=t[3],n[4]=t[4]*u,n[5]=t[5]*u,n[6]=t[6]*u,n[7]=t[7],n[8]=t[8]*o,n[9]=t[9]*o,n[10]=t[10]*o,n[11]=t[11],n[12]=t[12],n[13]=t[13],n[14]=t[14],n[15]=t[15],n};const b=new e.Cartesian3;p.setUniformScale=function(t,e,n){const a=p.getScale(t,b),r=e/a.x,u=e/a.y,o=e/a.z;return n[0]=t[0]*r,n[1]=t[1]*r,n[2]=t[2]*r,n[3]=t[3],n[4]=t[4]*u,n[5]=t[5]*u,n[6]=t[6]*u,n[7]=t[7],n[8]=t[8]*o,n[9]=t[9]*o,n[10]=t[10]*o,n[11]=t[11],n[12]=t[12],n[13]=t[13],n[14]=t[14],n[15]=t[15],n};const z=new e.Cartesian3;p.getScale=function(t,n){return n.x=e.Cartesian3.magnitude(e.Cartesian3.fromElements(t[0],t[1],t[2],z)),n.y=e.Cartesian3.magnitude(e.Cartesian3.fromElements(t[4],t[5],t[6],z)),n.z=e.Cartesian3.magnitude(e.Cartesian3.fromElements(t[8],t[9],t[10],z)),n};const O=new e.Cartesian3;p.getMaximumScale=function(t){return p.getScale(t,O),e.Cartesian3.maximumComponent(O)};const V=new e.Cartesian3;p.setRotation=function(t,e,n){const a=p.getScale(t,V);return n[0]=e[0]*a.x,n[1]=e[1]*a.x,n[2]=e[2]*a.x,n[3]=t[3],n[4]=e[3]*a.y,n[5]=e[4]*a.y,n[6]=e[5]*a.y,n[7]=t[7],n[8]=e[6]*a.z,n[9]=e[7]*a.z,n[10]=e[8]*a.z,n[11]=t[11],n[12]=t[12],n[13]=t[13],n[14]=t[14],n[15]=t[15],n};const E=new e.Cartesian3;p.getRotation=function(t,e){const n=p.getScale(t,E);return e[0]=t[0]/n.x,e[1]=t[1]/n.x,e[2]=t[2]/n.x,e[3]=t[4]/n.y,e[4]=t[5]/n.y,e[5]=t[6]/n.y,e[6]=t[8]/n.z,e[7]=t[9]/n.z,e[8]=t[10]/n.z,e},p.multiply=function(t,e,n){const a=t[0],r=t[1],u=t[2],o=t[3],i=t[4],s=t[5],c=t[6],l=t[7],f=t[8],h=t[9],d=t[10],y=t[11],m=t[12],w=t[13],p=t[14],x=t[15],M=e[0],g=e[1],C=e[2],b=e[3],z=e[4],O=e[5],V=e[6],E=e[7],T=e[8],A=e[9],q=e[10],I=e[11],S=e[12],U=e[13],N=e[14],P=e[15],_=a*M+i*g+f*C+m*b,R=r*M+s*g+h*C+w*b,W=u*M+c*g+d*C+p*b,$=o*M+l*g+y*C+x*b,k=a*z+i*O+f*V+m*E,L=r*z+s*O+h*V+w*E,v=u*z+c*O+d*V+p*E,j=o*z+l*O+y*V+x*E,B=a*T+i*A+f*q+m*I,X=r*T+s*A+h*q+w*I,Y=u*T+c*A+d*q+p*I,Z=o*T+l*A+y*q+x*I,D=a*S+i*U+f*N+m*P,F=r*S+s*U+h*N+w*P,G=u*S+c*U+d*N+p*P,H=o*S+l*U+y*N+x*P;return n[0]=_,n[1]=R,n[2]=W,n[3]=$,n[4]=k,n[5]=L,n[6]=v,n[7]=j,n[8]=B,n[9]=X,n[10]=Y,n[11]=Z,n[12]=D,n[13]=F,n[14]=G,n[15]=H,n},p.add=function(t,e,n){return n[0]=t[0]+e[0],n[1]=t[1]+e[1],n[2]=t[2]+e[2],n[3]=t[3]+e[3],n[4]=t[4]+e[4],n[5]=t[5]+e[5],n[6]=t[6]+e[6],n[7]=t[7]+e[7],n[8]=t[8]+e[8],n[9]=t[9]+e[9],n[10]=t[10]+e[10],n[11]=t[11]+e[11],n[12]=t[12]+e[12],n[13]=t[13]+e[13],n[14]=t[14]+e[14],n[15]=t[15]+e[15],n},p.subtract=function(t,e,n){return n[0]=t[0]-e[0],n[1]=t[1]-e[1],n[2]=t[2]-e[2],n[3]=t[3]-e[3],n[4]=t[4]-e[4],n[5]=t[5]-e[5],n[6]=t[6]-e[6],n[7]=t[7]-e[7],n[8]=t[8]-e[8],n[9]=t[9]-e[9],n[10]=t[10]-e[10],n[11]=t[11]-e[11],n[12]=t[12]-e[12],n[13]=t[13]-e[13],n[14]=t[14]-e[14],n[15]=t[15]-e[15],n},p.multiplyTransformation=function(t,e,n){const a=t[0],r=t[1],u=t[2],o=t[4],i=t[5],s=t[6],c=t[8],l=t[9],f=t[10],h=t[12],d=t[13],y=t[14],m=e[0],w=e[1],p=e[2],x=e[4],M=e[5],g=e[6],C=e[8],b=e[9],z=e[10],O=e[12],V=e[13],E=e[14],T=a*m+o*w+c*p,A=r*m+i*w+l*p,q=u*m+s*w+f*p,I=a*x+o*M+c*g,S=r*x+i*M+l*g,U=u*x+s*M+f*g,N=a*C+o*b+c*z,P=r*C+i*b+l*z,_=u*C+s*b+f*z,R=a*O+o*V+c*E+h,W=r*O+i*V+l*E+d,$=u*O+s*V+f*E+y;return n[0]=T,n[1]=A,n[2]=q,n[3]=0,n[4]=I,n[5]=S,n[6]=U,n[7]=0,n[8]=N,n[9]=P,n[10]=_,n[11]=0,n[12]=R,n[13]=W,n[14]=$,n[15]=1,n},p.multiplyByMatrix3=function(t,e,n){const a=t[0],r=t[1],u=t[2],o=t[4],i=t[5],s=t[6],c=t[8],l=t[9],f=t[10],h=e[0],d=e[1],y=e[2],m=e[3],w=e[4],p=e[5],x=e[6],M=e[7],g=e[8],C=a*h+o*d+c*y,b=r*h+i*d+l*y,z=u*h+s*d+f*y,O=a*m+o*w+c*p,V=r*m+i*w+l*p,E=u*m+s*w+f*p,T=a*x+o*M+c*g,A=r*x+i*M+l*g,q=u*x+s*M+f*g;return n[0]=C,n[1]=b,n[2]=z,n[3]=0,n[4]=O,n[5]=V,n[6]=E,n[7]=0,n[8]=T,n[9]=A,n[10]=q,n[11]=0,n[12]=t[12],n[13]=t[13],n[14]=t[14],n[15]=t[15],n},p.multiplyByTranslation=function(t,e,n){const a=e.x,r=e.y,u=e.z,o=a*t[0]+r*t[4]+u*t[8]+t[12],i=a*t[1]+r*t[5]+u*t[9]+t[13],s=a*t[2]+r*t[6]+u*t[10]+t[14];return n[0]=t[0],n[1]=t[1],n[2]=t[2],n[3]=t[3],n[4]=t[4],n[5]=t[5],n[6]=t[6],n[7]=t[7],n[8]=t[8],n[9]=t[9],n[10]=t[10],n[11]=t[11],n[12]=o,n[13]=i,n[14]=s,n[15]=t[15],n},p.multiplyByScale=function(t,e,n){const a=e.x,r=e.y,u=e.z;return 1===a&&1===r&&1===u?p.clone(t,n):(n[0]=a*t[0],n[1]=a*t[1],n[2]=a*t[2],n[3]=t[3],n[4]=r*t[4],n[5]=r*t[5],n[6]=r*t[6],n[7]=t[7],n[8]=u*t[8],n[9]=u*t[9],n[10]=u*t[10],n[11]=t[11],n[12]=t[12],n[13]=t[13],n[14]=t[14],n[15]=t[15],n)},p.multiplyByUniformScale=function(t,e,n){return n[0]=t[0]*e,n[1]=t[1]*e,n[2]=t[2]*e,n[3]=t[3],n[4]=t[4]*e,n[5]=t[5]*e,n[6]=t[6]*e,n[7]=t[7],n[8]=t[8]*e,n[9]=t[9]*e,n[10]=t[10]*e,n[11]=t[11],n[12]=t[12],n[13]=t[13],n[14]=t[14],n[15]=t[15],n},p.multiplyByVector=function(t,e,n){const a=e.x,r=e.y,u=e.z,o=e.w,i=t[0]*a+t[4]*r+t[8]*u+t[12]*o,s=t[1]*a+t[5]*r+t[9]*u+t[13]*o,c=t[2]*a+t[6]*r+t[10]*u+t[14]*o,l=t[3]*a+t[7]*r+t[11]*u+t[15]*o;return n.x=i,n.y=s,n.z=c,n.w=l,n},p.multiplyByPointAsVector=function(t,e,n){const a=e.x,r=e.y,u=e.z,o=t[0]*a+t[4]*r+t[8]*u,i=t[1]*a+t[5]*r+t[9]*u,s=t[2]*a+t[6]*r+t[10]*u;return n.x=o,n.y=i,n.z=s,n},p.multiplyByPoint=function(t,e,n){const a=e.x,r=e.y,u=e.z,o=t[0]*a+t[4]*r+t[8]*u+t[12],i=t[1]*a+t[5]*r+t[9]*u+t[13],s=t[2]*a+t[6]*r+t[10]*u+t[14];return n.x=o,n.y=i,n.z=s,n},p.multiplyByScalar=function(t,e,n){return n[0]=t[0]*e,n[1]=t[1]*e,n[2]=t[2]*e,n[3]=t[3]*e,n[4]=t[4]*e,n[5]=t[5]*e,n[6]=t[6]*e,n[7]=t[7]*e,n[8]=t[8]*e,n[9]=t[9]*e,n[10]=t[10]*e,n[11]=t[11]*e,n[12]=t[12]*e,n[13]=t[13]*e,n[14]=t[14]*e,n[15]=t[15]*e,n},p.negate=function(t,e){return e[0]=-t[0],e[1]=-t[1],e[2]=-t[2],e[3]=-t[3],e[4]=-t[4],e[5]=-t[5],e[6]=-t[6],e[7]=-t[7],e[8]=-t[8],e[9]=-t[9],e[10]=-t[10],e[11]=-t[11],e[12]=-t[12],e[13]=-t[13],e[14]=-t[14],e[15]=-t[15],e},p.transpose=function(t,e){const n=t[1],a=t[2],r=t[3],u=t[6],o=t[7],i=t[11];return e[0]=t[0],e[1]=t[4],e[2]=t[8],e[3]=t[12],e[4]=n,e[5]=t[5],e[6]=t[9],e[7]=t[13],e[8]=a,e[9]=u,e[10]=t[10],e[11]=t[14],e[12]=r,e[13]=o,e[14]=i,e[15]=t[15],e},p.abs=function(t,e){return e[0]=Math.abs(t[0]),e[1]=Math.abs(t[1]),e[2]=Math.abs(t[2]),e[3]=Math.abs(t[3]),e[4]=Math.abs(t[4]),e[5]=Math.abs(t[5]),e[6]=Math.abs(t[6]),e[7]=Math.abs(t[7]),e[8]=Math.abs(t[8]),e[9]=Math.abs(t[9]),e[10]=Math.abs(t[10]),e[11]=Math.abs(t[11]),e[12]=Math.abs(t[12]),e[13]=Math.abs(t[13]),e[14]=Math.abs(t[14]),e[15]=Math.abs(t[15]),e},p.equals=function(t,e){return t===e||n.defined(t)&&n.defined(e)&&t[12]===e[12]&&t[13]===e[13]&&t[14]===e[14]&&t[0]===e[0]&&t[1]===e[1]&&t[2]===e[2]&&t[4]===e[4]&&t[5]===e[5]&&t[6]===e[6]&&t[8]===e[8]&&t[9]===e[9]&&t[10]===e[10]&&t[3]===e[3]&&t[7]===e[7]&&t[11]===e[11]&&t[15]===e[15]},p.equalsEpsilon=function(t,e,a){return a=n.defaultValue(a,0),t===e||n.defined(t)&&n.defined(e)&&Math.abs(t[0]-e[0])<=a&&Math.abs(t[1]-e[1])<=a&&Math.abs(t[2]-e[2])<=a&&Math.abs(t[3]-e[3])<=a&&Math.abs(t[4]-e[4])<=a&&Math.abs(t[5]-e[5])<=a&&Math.abs(t[6]-e[6])<=a&&Math.abs(t[7]-e[7])<=a&&Math.abs(t[8]-e[8])<=a&&Math.abs(t[9]-e[9])<=a&&Math.abs(t[10]-e[10])<=a&&Math.abs(t[11]-e[11])<=a&&Math.abs(t[12]-e[12])<=a&&Math.abs(t[13]-e[13])<=a&&Math.abs(t[14]-e[14])<=a&&Math.abs(t[15]-e[15])<=a},p.getTranslation=function(t,e){return e.x=t[12],e.y=t[13],e.z=t[14],e},p.getMatrix3=function(t,e){return e[0]=t[0],e[1]=t[1],e[2]=t[2],e[3]=t[4],e[4]=t[5],e[5]=t[6],e[6]=t[8],e[7]=t[9],e[8]=t[10],e};const T=new e.Matrix3,A=new e.Matrix3,q=new c,I=new c(0,0,0,1);p.inverse=function(t,n){const u=t[0],o=t[4],i=t[8],s=t[12],l=t[1],f=t[5],h=t[9],d=t[13],y=t[2],m=t[6],w=t[10],x=t[14],M=t[3],g=t[7],C=t[11],b=t[15];let z=w*b,O=x*C,V=m*b,E=x*g,S=m*C,U=w*g,N=y*b,P=x*M,_=y*C,R=w*M,W=y*g,$=m*M;const k=z*f+E*h+S*d-(O*f+V*h+U*d),L=O*l+N*h+R*d-(z*l+P*h+_*d),v=V*l+P*f+W*d-(E*l+N*f+$*d),j=U*l+_*f+$*h-(S*l+R*f+W*h),B=O*o+V*i+U*s-(z*o+E*i+S*s),X=z*u+P*i+_*s-(O*u+N*i+R*s),Y=E*u+N*o+$*s-(V*u+P*o+W*s),Z=S*u+R*o+W*i-(U*u+_*o+$*i);z=i*d,O=s*h,V=o*d,E=s*f,S=o*h,U=i*f,N=u*d,P=s*l,_=u*h,R=i*l,W=u*f,$=o*l;const D=z*g+E*C+S*b-(O*g+V*C+U*b),F=O*M+N*C+R*b-(z*M+P*C+_*b),G=V*M+P*g+W*b-(E*M+N*g+$*b),H=U*M+_*g+$*C-(S*M+R*g+W*C),Q=V*w+U*x+O*m-(S*x+z*m+E*w),J=_*x+z*y+P*w-(N*w+R*x+O*y),K=N*m+$*x+E*y-(W*x+V*y+P*m),tt=W*w+S*y+R*m-(_*m+$*w+U*y);let et=u*k+o*L+i*v+s*j;if(Math.abs(et)<a.CesiumMath.EPSILON21){if(e.Matrix3.equalsEpsilon(p.getMatrix3(t,T),A,a.CesiumMath.EPSILON7)&&c.equals(p.getRow(t,3,q),I))return n[0]=0,n[1]=0,n[2]=0,n[3]=0,n[4]=0,n[5]=0,n[6]=0,n[7]=0,n[8]=0,n[9]=0,n[10]=0,n[11]=0,n[12]=-t[12],n[13]=-t[13],n[14]=-t[14],n[15]=1,n;throw new r.RuntimeError("matrix is not invertible because its determinate is zero.")}return et=1/et,n[0]=k*et,n[1]=L*et,n[2]=v*et,n[3]=j*et,n[4]=B*et,n[5]=X*et,n[6]=Y*et,n[7]=Z*et,n[8]=D*et,n[9]=F*et,n[10]=G*et,n[11]=H*et,n[12]=Q*et,n[13]=J*et,n[14]=K*et,n[15]=tt*et,n},p.inverseTransformation=function(t,e){const n=t[0],a=t[1],r=t[2],u=t[4],o=t[5],i=t[6],s=t[8],c=t[9],l=t[10],f=t[12],h=t[13],d=t[14],y=-n*f-a*h-r*d,m=-u*f-o*h-i*d,w=-s*f-c*h-l*d;return e[0]=n,e[1]=u,e[2]=s,e[3]=0,e[4]=a,e[5]=o,e[6]=c,e[7]=0,e[8]=r,e[9]=i,e[10]=l,e[11]=0,e[12]=y,e[13]=m,e[14]=w,e[15]=1,e};const S=new p;function U(t,e,a,r){this.west=n.defaultValue(t,0),this.south=n.defaultValue(e,0),this.east=n.defaultValue(a,0),this.north=n.defaultValue(r,0)}p.inverseTranspose=function(t,e){return p.inverse(p.transpose(t,S),e)},p.IDENTITY=Object.freeze(new p(1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1)),p.ZERO=Object.freeze(new p(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0)),p.COLUMN0ROW0=0,p.COLUMN0ROW1=1,p.COLUMN0ROW2=2,p.COLUMN0ROW3=3,p.COLUMN1ROW0=4,p.COLUMN1ROW1=5,p.COLUMN1ROW2=6,p.COLUMN1ROW3=7,p.COLUMN2ROW0=8,p.COLUMN2ROW1=9,p.COLUMN2ROW2=10,p.COLUMN2ROW3=11,p.COLUMN3ROW0=12,p.COLUMN3ROW1=13,p.COLUMN3ROW2=14,p.COLUMN3ROW3=15,Object.defineProperties(p.prototype,{length:{get:function(){return p.packedLength}}}),p.prototype.clone=function(t){return p.clone(this,t)},p.prototype.equals=function(t){return p.equals(this,t)},p.equalsArray=function(t,e,n){return t[0]===e[n]&&t[1]===e[n+1]&&t[2]===e[n+2]&&t[3]===e[n+3]&&t[4]===e[n+4]&&t[5]===e[n+5]&&t[6]===e[n+6]&&t[7]===e[n+7]&&t[8]===e[n+8]&&t[9]===e[n+9]&&t[10]===e[n+10]&&t[11]===e[n+11]&&t[12]===e[n+12]&&t[13]===e[n+13]&&t[14]===e[n+14]&&t[15]===e[n+15]},p.prototype.equalsEpsilon=function(t,e){return p.equalsEpsilon(this,t,e)},p.prototype.toString=function(){return`(${this[0]}, ${this[4]}, ${this[8]}, ${this[12]})\n(${this[1]}, ${this[5]}, ${this[9]}, ${this[13]})\n(${this[2]}, ${this[6]}, ${this[10]}, ${this[14]})\n(${this[3]}, ${this[7]}, ${this[11]}, ${this[15]})`},p.multiplyByPlane=function(t,n,a){s.typeOf.object("matrix",t),s.typeOf.object("plane",n),s.typeOf.object("result",a);const r=new p,u=new p;p.inverse(t,r),p.transpose(r,u);const o=new c(n.normal.x,n.normal.y,n.normal.z,n.distance);p.multiplyByVector(u,o,o),a.normal.x=o.x,a.normal.y=o.y,a.normal.z=o.z;const i=e.Cartesian3.magnitude(a.normal);return e.Cartesian3.normalize(a.normal,a.normal),a.distance=o.w/i,a},Object.defineProperties(U.prototype,{width:{get:function(){return U.computeWidth(this)}},height:{get:function(){return U.computeHeight(this)}}}),U.packedLength=4,U.pack=function(t,e,a){return a=n.defaultValue(a,0),e[a++]=t.west,e[a++]=t.south,e[a++]=t.east,e[a]=t.north,e},U.unpack=function(t,e,a){return e=n.defaultValue(e,0),n.defined(a)||(a=new U),a.west=t[e++],a.south=t[e++],a.east=t[e++],a.north=t[e],a},U.computeWidth=function(t){let e=t.east;const n=t.west;return e<n&&(e+=a.CesiumMath.TWO_PI),e-n},U.computeHeight=function(t){return t.north-t.south},U.fromDegrees=function(t,e,r,u,o){return t=a.CesiumMath.toRadians(n.defaultValue(t,0)),e=a.CesiumMath.toRadians(n.defaultValue(e,0)),r=a.CesiumMath.toRadians(n.defaultValue(r,0)),u=a.CesiumMath.toRadians(n.defaultValue(u,0)),n.defined(o)?(o.west=t,o.south=e,o.east=r,o.north=u,o):new U(t,e,r,u)},U.fromRadians=function(t,e,a,r,u){return n.defined(u)?(u.west=n.defaultValue(t,0),u.south=n.defaultValue(e,0),u.east=n.defaultValue(a,0),u.north=n.defaultValue(r,0),u):new U(t,e,a,r)},U.fromCartographicArray=function(t,e){let r=Number.MAX_VALUE,u=-Number.MAX_VALUE,o=Number.MAX_VALUE,i=-Number.MAX_VALUE,s=Number.MAX_VALUE,c=-Number.MAX_VALUE;for(let e=0,n=t.length;e<n;e++){const n=t[e];r=Math.min(r,n.longitude),u=Math.max(u,n.longitude),s=Math.min(s,n.latitude),c=Math.max(c,n.latitude);const l=n.longitude>=0?n.longitude:n.longitude+a.CesiumMath.TWO_PI;o=Math.min(o,l),i=Math.max(i,l)}return u-r>i-o&&(r=o,u=i,u>a.CesiumMath.PI&&(u-=a.CesiumMath.TWO_PI),r>a.CesiumMath.PI&&(r-=a.CesiumMath.TWO_PI)),n.defined(e)?(e.west=r,e.south=s,e.east=u,e.north=c,e):new U(r,s,u,c)},U.fromCartesianArray=function(t,r,u){r=n.defaultValue(r,e.Ellipsoid.WGS84);let o=Number.MAX_VALUE,i=-Number.MAX_VALUE,s=Number.MAX_VALUE,c=-Number.MAX_VALUE,l=Number.MAX_VALUE,f=-Number.MAX_VALUE;for(let e=0,n=t.length;e<n;e++){const n=r.cartesianToCartographic(t[e]);o=Math.min(o,n.longitude),i=Math.max(i,n.longitude),l=Math.min(l,n.latitude),f=Math.max(f,n.latitude);const u=n.longitude>=0?n.longitude:n.longitude+a.CesiumMath.TWO_PI;s=Math.min(s,u),c=Math.max(c,u)}return i-o>c-s&&(o=s,i=c,i>a.CesiumMath.PI&&(i-=a.CesiumMath.TWO_PI),o>a.CesiumMath.PI&&(o-=a.CesiumMath.TWO_PI)),n.defined(u)?(u.west=o,u.south=l,u.east=i,u.north=f,u):new U(o,l,i,f)},U.clone=function(t,e){if(n.defined(t))return n.defined(e)?(e.west=t.west,e.south=t.south,e.east=t.east,e.north=t.north,e):new U(t.west,t.south,t.east,t.north)},U.equalsEpsilon=function(t,e,a){return a=n.defaultValue(a,0),t===e||n.defined(t)&&n.defined(e)&&Math.abs(t.west-e.west)<=a&&Math.abs(t.south-e.south)<=a&&Math.abs(t.east-e.east)<=a&&Math.abs(t.north-e.north)<=a},U.prototype.clone=function(t){return U.clone(this,t)},U.prototype.equals=function(t){return U.equals(this,t)},U.equals=function(t,e){return t===e||n.defined(t)&&n.defined(e)&&t.west===e.west&&t.south===e.south&&t.east===e.east&&t.north===e.north},U.prototype.equalsEpsilon=function(t,e){return U.equalsEpsilon(this,t,e)},U.validate=function(t){},U.southwest=function(t,a){return n.defined(a)?(a.longitude=t.west,a.latitude=t.south,a.height=0,a):new e.Cartographic(t.west,t.south)},U.northwest=function(t,a){return n.defined(a)?(a.longitude=t.west,a.latitude=t.north,a.height=0,a):new e.Cartographic(t.west,t.north)},U.northeast=function(t,a){return n.defined(a)?(a.longitude=t.east,a.latitude=t.north,a.height=0,a):new e.Cartographic(t.east,t.north)},U.southeast=function(t,a){return n.defined(a)?(a.longitude=t.east,a.latitude=t.south,a.height=0,a):new e.Cartographic(t.east,t.south)},U.center=function(t,r){let u=t.east;const o=t.west;u<o&&(u+=a.CesiumMath.TWO_PI);const i=a.CesiumMath.negativePiToPi(.5*(o+u)),s=.5*(t.south+t.north);return n.defined(r)?(r.longitude=i,r.latitude=s,r.height=0,r):new e.Cartographic(i,s)},U.intersection=function(t,e,r){let u=t.east,o=t.west,i=e.east,s=e.west;u<o&&i>0?u+=a.CesiumMath.TWO_PI:i<s&&u>0&&(i+=a.CesiumMath.TWO_PI),u<o&&s<0?s+=a.CesiumMath.TWO_PI:i<s&&o<0&&(o+=a.CesiumMath.TWO_PI);const c=a.CesiumMath.negativePiToPi(Math.max(o,s)),l=a.CesiumMath.negativePiToPi(Math.min(u,i));if((t.west<t.east||e.west<e.east)&&l<=c)return;const f=Math.max(t.south,e.south),h=Math.min(t.north,e.north);return f>=h?void 0:n.defined(r)?(r.west=c,r.south=f,r.east=l,r.north=h,r):new U(c,f,l,h)},U.simpleIntersection=function(t,e,a){const r=Math.max(t.west,e.west),u=Math.max(t.south,e.south),o=Math.min(t.east,e.east),i=Math.min(t.north,e.north);if(!(u>=i||r>=o))return n.defined(a)?(a.west=r,a.south=u,a.east=o,a.north=i,a):new U(r,u,o,i)},U.union=function(t,e,r){n.defined(r)||(r=new U);let u=t.east,o=t.west,i=e.east,s=e.west;u<o&&i>0?u+=a.CesiumMath.TWO_PI:i<s&&u>0&&(i+=a.CesiumMath.TWO_PI),u<o&&s<0?s+=a.CesiumMath.TWO_PI:i<s&&o<0&&(o+=a.CesiumMath.TWO_PI);const c=a.CesiumMath.negativePiToPi(Math.min(o,s)),l=a.CesiumMath.negativePiToPi(Math.max(u,i));return r.west=c,r.south=Math.min(t.south,e.south),r.east=l,r.north=Math.max(t.north,e.north),r},U.expand=function(t,e,a){return n.defined(a)||(a=new U),a.west=Math.min(t.west,e.longitude),a.south=Math.min(t.south,e.latitude),a.east=Math.max(t.east,e.longitude),a.north=Math.max(t.north,e.latitude),a},U.contains=function(t,e){let n=e.longitude;const r=e.latitude,u=t.west;let o=t.east;return o<u&&(o+=a.CesiumMath.TWO_PI,n<0&&(n+=a.CesiumMath.TWO_PI)),(n>u||a.CesiumMath.equalsEpsilon(n,u,a.CesiumMath.EPSILON14))&&(n<o||a.CesiumMath.equalsEpsilon(n,o,a.CesiumMath.EPSILON14))&&r>=t.south&&r<=t.north};const N=new e.Cartographic;U.subsample=function(t,r,u,o){r=n.defaultValue(r,e.Ellipsoid.WGS84),u=n.defaultValue(u,0),n.defined(o)||(o=[]);let i=0;const s=t.north,c=t.south,l=t.east,f=t.west,h=N;h.height=u,h.longitude=f,h.latitude=s,o[i]=r.cartographicToCartesian(h,o[i]),i++,h.longitude=l,o[i]=r.cartographicToCartesian(h,o[i]),i++,h.latitude=c,o[i]=r.cartographicToCartesian(h,o[i]),i++,h.longitude=f,o[i]=r.cartographicToCartesian(h,o[i]),i++,h.latitude=s<0?s:c>0?c:0;for(let e=1;e<8;++e)h.longitude=-Math.PI+e*a.CesiumMath.PI_OVER_TWO,U.contains(t,h)&&(o[i]=r.cartographicToCartesian(h,o[i]),i++);return 0===h.latitude&&(h.longitude=f,o[i]=r.cartographicToCartesian(h,o[i]),i++,h.longitude=l,o[i]=r.cartographicToCartesian(h,o[i]),i++),o.length=i,o},U.subsection=function(t,e,r,u,o,i){if(n.defined(i)||(i=new U),t.west<=t.east){const n=t.east-t.west;i.west=t.west+e*n,i.east=t.west+u*n}else{const n=a.CesiumMath.TWO_PI+t.east-t.west;i.west=a.CesiumMath.negativePiToPi(t.west+e*n),i.east=a.CesiumMath.negativePiToPi(t.west+u*n)}const s=t.north-t.south;return i.south=t.south+r*s,i.north=t.south+o*s,1===e&&(i.west=t.east),1===u&&(i.east=t.east),1===r&&(i.south=t.north),1===o&&(i.north=t.north),i},U.MAX_VALUE=Object.freeze(new U(-Math.PI,-a.CesiumMath.PI_OVER_TWO,Math.PI,a.CesiumMath.PI_OVER_TWO));const P=new e.Cartographic;function _(t,e){this.x=n.defaultValue(t,0),this.y=n.defaultValue(e,0)}U.prototype.contains=function(t){return U.contains(this,U.southwest(t,P))&&U.contains(this,U.northwest(t,P))&&U.contains(this,U.southeast(t,P))&&U.contains(this,U.northeast(t,P))},_.fromElements=function(t,e,a){return n.defined(a)?(a.x=t,a.y=e,a):new _(t,e)},_.clone=function(t,e){if(n.defined(t))return n.defined(e)?(e.x=t.x,e.y=t.y,e):new _(t.x,t.y)},_.fromCartesian3=_.clone,_.fromCartesian4=_.clone,_.packedLength=2,_.pack=function(t,e,a){return a=n.defaultValue(a,0),e[a++]=t.x,e[a]=t.y,e},_.unpack=function(t,e,a){return e=n.defaultValue(e,0),n.defined(a)||(a=new _),a.x=t[e++],a.y=t[e],a},_.packArray=function(t,e){const a=t.length,r=2*a;n.defined(e)?(Array.isArray(e)||e.length===r)&&e.length!==r&&(e.length=r):e=new Array(r);for(let n=0;n<a;++n)_.pack(t[n],e,2*n);return e},_.unpackArray=function(t,e){const a=t.length;n.defined(e)?e.length=a/2:e=new Array(a/2);for(let n=0;n<a;n+=2){const a=n/2;e[a]=_.unpack(t,n,e[a])}return e},_.fromArray=_.unpack,_.maximumComponent=function(t){return Math.max(t.x,t.y)},_.minimumComponent=function(t){return Math.min(t.x,t.y)},_.minimumByComponent=function(t,e,n){return n.x=Math.min(t.x,e.x),n.y=Math.min(t.y,e.y),n},_.maximumByComponent=function(t,e,n){return n.x=Math.max(t.x,e.x),n.y=Math.max(t.y,e.y),n},_.clamp=function(t,e,n,r){const u=a.CesiumMath.clamp(t.x,e.x,n.x),o=a.CesiumMath.clamp(t.y,e.y,n.y);return r.x=u,r.y=o,r},_.magnitudeSquared=function(t){return t.x*t.x+t.y*t.y},_.magnitude=function(t){return Math.sqrt(_.magnitudeSquared(t))};const R=new _;_.distance=function(t,e){return _.subtract(t,e,R),_.magnitude(R)},_.distanceSquared=function(t,e){return _.subtract(t,e,R),_.magnitudeSquared(R)},_.normalize=function(t,e){const n=_.magnitude(t);return e.x=t.x/n,e.y=t.y/n,e},_.dot=function(t,e){return t.x*e.x+t.y*e.y},_.cross=function(t,e){return t.x*e.y-t.y*e.x},_.multiplyComponents=function(t,e,n){return n.x=t.x*e.x,n.y=t.y*e.y,n},_.divideComponents=function(t,e,n){return n.x=t.x/e.x,n.y=t.y/e.y,n},_.add=function(t,e,n){return n.x=t.x+e.x,n.y=t.y+e.y,n},_.subtract=function(t,e,n){return n.x=t.x-e.x,n.y=t.y-e.y,n},_.multiplyByScalar=function(t,e,n){return n.x=t.x*e,n.y=t.y*e,n},_.divideByScalar=function(t,e,n){return n.x=t.x/e,n.y=t.y/e,n},_.negate=function(t,e){return e.x=-t.x,e.y=-t.y,e},_.abs=function(t,e){return e.x=Math.abs(t.x),e.y=Math.abs(t.y),e};const W=new _;_.lerp=function(t,e,n,a){return _.multiplyByScalar(e,n,W),a=_.multiplyByScalar(t,1-n,a),_.add(W,a,a)};const $=new _,k=new _;_.angleBetween=function(t,e){return _.normalize(t,$),_.normalize(e,k),a.CesiumMath.acosClamped(_.dot($,k))};const L=new _;function v(t,e,a,r){this[0]=n.defaultValue(t,0),this[1]=n.defaultValue(a,0),this[2]=n.defaultValue(e,0),this[3]=n.defaultValue(r,0)}_.mostOrthogonalAxis=function(t,e){const n=_.normalize(t,L);return _.abs(n,n),e=n.x<=n.y?_.clone(_.UNIT_X,e):_.clone(_.UNIT_Y,e)},_.equals=function(t,e){return t===e||n.defined(t)&&n.defined(e)&&t.x===e.x&&t.y===e.y},_.equalsArray=function(t,e,n){return t.x===e[n]&&t.y===e[n+1]},_.equalsEpsilon=function(t,e,r,u){return t===e||n.defined(t)&&n.defined(e)&&a.CesiumMath.equalsEpsilon(t.x,e.x,r,u)&&a.CesiumMath.equalsEpsilon(t.y,e.y,r,u)},_.ZERO=Object.freeze(new _(0,0)),_.ONE=Object.freeze(new _(1,1)),_.UNIT_X=Object.freeze(new _(1,0)),_.UNIT_Y=Object.freeze(new _(0,1)),_.prototype.clone=function(t){return _.clone(this,t)},_.prototype.equals=function(t){return _.equals(this,t)},_.prototype.equalsEpsilon=function(t,e,n){return _.equalsEpsilon(this,t,e,n)},_.prototype.toString=function(){return`(${this.x}, ${this.y})`},v.packedLength=4,v.pack=function(t,e,a){return a=n.defaultValue(a,0),e[a++]=t[0],e[a++]=t[1],e[a++]=t[2],e[a++]=t[3],e},v.unpack=function(t,e,a){return e=n.defaultValue(e,0),n.defined(a)||(a=new v),a[0]=t[e++],a[1]=t[e++],a[2]=t[e++],a[3]=t[e++],a},v.packArray=function(t,e){const a=t.length,r=4*a;n.defined(e)?(Array.isArray(e)||e.length===r)&&e.length!==r&&(e.length=r):e=new Array(r);for(let n=0;n<a;++n)v.pack(t[n],e,4*n);return e},v.unpackArray=function(t,e){const a=t.length;n.defined(e)?e.length=a/4:e=new Array(a/4);for(let n=0;n<a;n+=4){const a=n/4;e[a]=v.unpack(t,n,e[a])}return e},v.clone=function(t,e){if(n.defined(t))return n.defined(e)?(e[0]=t[0],e[1]=t[1],e[2]=t[2],e[3]=t[3],e):new v(t[0],t[2],t[1],t[3])},v.fromArray=v.unpack,v.fromColumnMajorArray=function(t,e){return v.clone(t,e)},v.fromRowMajorArray=function(t,e){return n.defined(e)?(e[0]=t[0],e[1]=t[2],e[2]=t[1],e[3]=t[3],e):new v(t[0],t[1],t[2],t[3])},v.fromScale=function(t,e){return n.defined(e)?(e[0]=t.x,e[1]=0,e[2]=0,e[3]=t.y,e):new v(t.x,0,0,t.y)},v.fromUniformScale=function(t,e){return n.defined(e)?(e[0]=t,e[1]=0,e[2]=0,e[3]=t,e):new v(t,0,0,t)},v.fromRotation=function(t,e){const a=Math.cos(t),r=Math.sin(t);return n.defined(e)?(e[0]=a,e[1]=r,e[2]=-r,e[3]=a,e):new v(a,-r,r,a)},v.toArray=function(t,e){return n.defined(e)?(e[0]=t[0],e[1]=t[1],e[2]=t[2],e[3]=t[3],e):[t[0],t[1],t[2],t[3]]},v.getElementIndex=function(t,e){return 2*t+e},v.getColumn=function(t,e,n){const a=2*e,r=t[a],u=t[a+1];return n.x=r,n.y=u,n},v.setColumn=function(t,e,n,a){const r=2*e;return(a=v.clone(t,a))[r]=n.x,a[r+1]=n.y,a},v.getRow=function(t,e,n){const a=t[e],r=t[e+2];return n.x=a,n.y=r,n},v.setRow=function(t,e,n,a){return(a=v.clone(t,a))[e]=n.x,a[e+2]=n.y,a};const j=new _;v.setScale=function(t,e,n){const a=v.getScale(t,j),r=e.x/a.x,u=e.y/a.y;return n[0]=t[0]*r,n[1]=t[1]*r,n[2]=t[2]*u,n[3]=t[3]*u,n};const B=new _;v.setUniformScale=function(t,e,n){const a=v.getScale(t,B),r=e/a.x,u=e/a.y;return n[0]=t[0]*r,n[1]=t[1]*r,n[2]=t[2]*u,n[3]=t[3]*u,n};const X=new _;v.getScale=function(t,e){return e.x=_.magnitude(_.fromElements(t[0],t[1],X)),e.y=_.magnitude(_.fromElements(t[2],t[3],X)),e};const Y=new _;v.getMaximumScale=function(t){return v.getScale(t,Y),_.maximumComponent(Y)};const Z=new _;v.setRotation=function(t,e,n){const a=v.getScale(t,Z);return n[0]=e[0]*a.x,n[1]=e[1]*a.x,n[2]=e[2]*a.y,n[3]=e[3]*a.y,n};const D=new _;v.getRotation=function(t,e){const n=v.getScale(t,D);return e[0]=t[0]/n.x,e[1]=t[1]/n.x,e[2]=t[2]/n.y,e[3]=t[3]/n.y,e},v.multiply=function(t,e,n){const a=t[0]*e[0]+t[2]*e[1],r=t[0]*e[2]+t[2]*e[3],u=t[1]*e[0]+t[3]*e[1],o=t[1]*e[2]+t[3]*e[3];return n[0]=a,n[1]=u,n[2]=r,n[3]=o,n},v.add=function(t,e,n){return n[0]=t[0]+e[0],n[1]=t[1]+e[1],n[2]=t[2]+e[2],n[3]=t[3]+e[3],n},v.subtract=function(t,e,n){return n[0]=t[0]-e[0],n[1]=t[1]-e[1],n[2]=t[2]-e[2],n[3]=t[3]-e[3],n},v.multiplyByVector=function(t,e,n){const a=t[0]*e.x+t[2]*e.y,r=t[1]*e.x+t[3]*e.y;return n.x=a,n.y=r,n},v.multiplyByScalar=function(t,e,n){return n[0]=t[0]*e,n[1]=t[1]*e,n[2]=t[2]*e,n[3]=t[3]*e,n},v.multiplyByScale=function(t,e,n){return n[0]=t[0]*e.x,n[1]=t[1]*e.x,n[2]=t[2]*e.y,n[3]=t[3]*e.y,n},v.multiplyByUniformScale=function(t,e,n){return n[0]=t[0]*e,n[1]=t[1]*e,n[2]=t[2]*e,n[3]=t[3]*e,n},v.negate=function(t,e){return e[0]=-t[0],e[1]=-t[1],e[2]=-t[2],e[3]=-t[3],e},v.transpose=function(t,e){const n=t[0],a=t[2],r=t[1],u=t[3];return e[0]=n,e[1]=a,e[2]=r,e[3]=u,e},v.abs=function(t,e){return e[0]=Math.abs(t[0]),e[1]=Math.abs(t[1]),e[2]=Math.abs(t[2]),e[3]=Math.abs(t[3]),e},v.equals=function(t,e){return t===e||n.defined(t)&&n.defined(e)&&t[0]===e[0]&&t[1]===e[1]&&t[2]===e[2]&&t[3]===e[3]},v.equalsArray=function(t,e,n){return t[0]===e[n]&&t[1]===e[n+1]&&t[2]===e[n+2]&&t[3]===e[n+3]},v.equalsEpsilon=function(t,e,a){return a=n.defaultValue(a,0),t===e||n.defined(t)&&n.defined(e)&&Math.abs(t[0]-e[0])<=a&&Math.abs(t[1]-e[1])<=a&&Math.abs(t[2]-e[2])<=a&&Math.abs(t[3]-e[3])<=a},v.IDENTITY=Object.freeze(new v(1,0,0,1)),v.ZERO=Object.freeze(new v(0,0,0,0)),v.COLUMN0ROW0=0,v.COLUMN0ROW1=1,v.COLUMN1ROW0=2,v.COLUMN1ROW1=3,Object.defineProperties(v.prototype,{length:{get:function(){return v.packedLength}}}),v.prototype.clone=function(t){return v.clone(this,t)},v.prototype.equals=function(t){return v.equals(this,t)},v.prototype.equalsEpsilon=function(t,e){return v.equalsEpsilon(this,t,e)},v.prototype.toString=function(){return`(${this[0]}, ${this[2]})\n(${this[1]}, ${this[3]})`},t.Cartesian2=_,t.Cartesian4=c,t.Check=s,t.DeveloperError=u,t.Matrix2=v,t.Matrix4=p,t.Rectangle=U}));
