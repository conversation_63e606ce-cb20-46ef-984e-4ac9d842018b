<template>
  <div class="column-chart-container">
    <div ref="chartRef" class="chart-content"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  title: {
    type: String,
    required: true
  },
  series: {
    type: Array,
    required: true,
    // [{name: '系列1', data: [120, 200, 150]}, {name: '系列2', data: [130, 210, 160]}]
  },
  xAxisData: {
    type: Array,
    default: () => []
  },
  isHorizontal: {
    type: Boolean,
    default: false
  },
  colorList: {
    type: Array,
    default: () => [
      ['#00F2F1', '#0066FF'], // 蓝色渐变
      ['#FFD24D', '#FF8F35'], // 橙色渐变
      ['#35FF6B', '#00B36B'], // 绿色渐变
      ['#FF59C8', '#FF0066'], // 粉色渐变
      ['#B36CFF', '#7700FF'], // 紫色渐变
    ]
  },
  showLabel: {
    type: Boolean,
    default: false
  },
  showLegend: {
    type: Boolean,
    default: true
  },
  unit: {
    type: String,
    default: ''
  }
})

const chartRef = ref(null)
let chartInstance = null

onMounted(async () => {
  await nextTick()
  if (chartRef.value) {
    console.log('初始化多列图表:', props.title)
    initChart()
  }
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
  window.removeEventListener('resize', handleResize)
})

watch(() => props.series, () => {
  if (chartInstance) {
    console.log('数据系列变化，更新图表:', props.title)
    updateChart()
  }
}, { deep: true })

watch(() => props.xAxisData, () => {
  if (chartInstance) {
    console.log('x轴数据变化，更新图表:', props.title)
    updateChart()
  }
}, { deep: true })

watch(() => props.isHorizontal, () => {
  if (chartInstance) {
    console.log('方向变化，更新图表:', props.title)
    updateChart()
  }
})

const initChart = () => {
  try {
    if (chartInstance) {
      chartInstance.dispose()
    }
    
    console.log('创建echarts实例，容器大小:', chartRef.value.offsetWidth, chartRef.value.offsetHeight)
    chartInstance = echarts.init(chartRef.value)
    
    window.addEventListener('resize', handleResize)
    
    updateChart()
    chartInstance.resize()
  } catch (error) {
    console.error('初始化多列图表失败:', error)
  }
}

const handleResize = () => {
  if (chartInstance) {
    try {
      chartInstance.resize()
    } catch (error) {
      console.error('调整图表大小失败:', error)
    }
  }
}

const updateChart = () => {
  if (!chartInstance) {
    console.warn('图表实例不存在，无法更新')
    return
  }
  
  try {
    // 创建本地副本
    const localSeries = JSON.parse(JSON.stringify(props.series))
    const localTitle = String(props.title)
    const localXAxisData = JSON.parse(JSON.stringify(props.xAxisData))
    
    // 配置系列
    const series = localSeries.map((item, index) => {
      const colorPair = props.colorList[index % props.colorList.length]
      
      return {
        name: item.name,
        type: 'bar',
        data: item.data,
        barGap: '0%',
        barCategoryGap: '20%',
        itemStyle: {
          color: {
            type: 'linear',
            x: 0, y: 0, x2: props.isHorizontal ? 1 : 0, y2: props.isHorizontal ? 0 : 1,
            colorStops: [{
              offset: 0,
              color: colorPair[0]
            }, {
              offset: 1,
              color: colorPair[1]
            }]
          },
          borderRadius: 4
        },
        label: {
          show: props.showLabel,
          position: props.isHorizontal ? 'right' : 'top',
          color: '#fff',
          fontSize: 10,
          formatter: `{c}${props.unit}`
        }
      }
    })
    
    // 横向或纵向柱状图配置
    const option = {
      backgroundColor: 'transparent',
      title: {
        text: localTitle,
        left: 'center',
        top: 10,
        textStyle: {
          color: '#FFFFFF',
          fontSize: 14,
          fontWeight: 'normal',
          fontFamily: 'PingFangSC, PingFang SC'
        }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        formatter: function(params) {
          let result = `${params[0].name}<br/>`
          params.forEach(param => {
            const colorSpan = `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${param.color.colorStops[0].color};"></span>`
            result += `${colorSpan}${param.seriesName}: ${param.value}${props.unit}<br/>`
          })
          return result
        }
      },
      legend: {
        show: props.showLegend && localSeries.length > 1,
        top: props.title ? '10%' : '5%',
        right: '4%',
        itemWidth: 10,
        itemHeight: 10,
        textStyle: {
          color: 'rgba(255,255,255,0.7)',
          fontSize: 10
        },
        data: localSeries.map(item => item.name)
      },
      grid: {
        top: props.showLegend ? '25%' : (props.title ? '15%' : '5%'),
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: props.isHorizontal ? {
        type: 'value',
        axisLine: {
          lineStyle: { color: 'rgba(255,255,255,0.5)' }
        },
        axisLabel: { 
          color: 'rgba(255,255,255,0.7)',
          fontSize: 10
        },
        splitLine: {
          lineStyle: { color: 'rgba(255,255,255,0.1)' }
        }
      } : {
        type: 'category',
        data: localXAxisData,
        axisLine: {
          lineStyle: { color: 'rgba(255,255,255,0.5)' }
        },
        axisLabel: { 
          color: 'rgba(255,255,255,0.7)',
          fontSize: 10,
          rotate: localXAxisData.length > 5 ? 45 : 0
        }
      },
      yAxis: props.isHorizontal ? {
        type: 'category',
        data: localXAxisData,
        axisLine: {
          lineStyle: { color: 'rgba(255,255,255,0.5)' }
        },
        axisLabel: { 
          color: 'rgba(255,255,255,0.7)',
          fontSize: 10
        }
      } : {
        type: 'value',
        axisLine: {
          lineStyle: { color: 'rgba(255,255,255,0.5)' }
        },
        axisLabel: { 
          color: 'rgba(255,255,255,0.7)',
          fontSize: 10
        },
        splitLine: {
          lineStyle: { color: 'rgba(255,255,255,0.1)' }
        }
      },
      series: series
    }
    
    console.log('设置多列图表选项:', props.title)
    chartInstance.setOption(option, true)
  } catch (error) {
    console.error('更新多列图表失败:', error, props.title)
  }
}
</script>

<style scoped>
.column-chart-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 160px;
}

.chart-content {
  width: 100%;
  height: 100%;
  min-height: 160px;
}
</style> 