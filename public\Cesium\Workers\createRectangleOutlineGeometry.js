/**
 *    ████████                   ██                   ██       ██                  ██      ██
 *   ██░░░░░░██                 ░░                   ░██      ░██                 ░██     ░██
 *  ██      ░░   █████  ███████  ██ ██   ██  ██████  ░██   █  ░██  ██████  ██████ ░██     ░██
 * ░██          ██░░░██░░██░░░██░██░██  ░██ ██░░░░   ░██  ███ ░██ ██░░░░██░░██░░█ ░██  ██████
 * ░██    █████░███████ ░██  ░██░██░██  ░██░░█████   ░██ ██░██░██░██   ░██ ░██ ░  ░██ ██░░░██
 * ░░██  ░░░░██░██░░░░  ░██  ░██░██░██  ░██ ░░░░░██  ░████ ░░████░██   ░██ ░██    ░██░██  ░██
 *  ░░████████ ░░██████ ███  ░██░██░░██████ ██████   ░██░   ░░░██░░██████ ░███    ███░░██████
 *   ░░░░░░░░   ░░░░░░ ░░░   ░░ ░░  ░░░░░░ ░░░░░░    ░░       ░░  ░░░░░░  ░░░    ░░░  ░░░░░░
 *
 * @license
 * GeniusWorld Web SDK - http://zydlxx.cn:1234/
 * Version 4.0.0.B#develop-d3ff2330@202405231107
 *
 *
 * Copyright © 2020 - 2023 正元地理信息集团股份有限公司. All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
define(["./defaultValue-7b61670d","./Matrix3-79d15570","./Matrix2-d550732e","./Transforms-6a5d79d3","./ComponentDatatype-e95dda25","./GeometryAttribute-d24f9032","./GeometryAttributes-410c425f","./GeometryOffsetAttribute-4f73a4af","./IndexDatatype-7c192505","./Math-6acd1674","./PolygonPipeline-de89f886","./RectangleGeometryLibrary-06911a47","./RuntimeError-7dc4ea5a","./combine-bc3d0d90","./WebGLConstants-68839929","./EllipsoidRhumbLine-997e9b1a"],(function(e,t,i,n,o,a,r,l,s,u,c,d,p,f,g,h){"use strict";const y=new n.BoundingSphere,m=new n.BoundingSphere,b=new t.Cartesian3,_=new i.Rectangle;function E(e,t){const i=e._ellipsoid,n=t.height,l=t.width,u=t.northCap,c=t.southCap;let p=n,f=2,g=0,h=4;u&&(f-=1,p-=1,g+=1,h-=2),c&&(f-=1,p-=1,g+=1,h-=2),g+=f*l+2*p-h;const y=new Float64Array(3*g);let m,_=0,E=0;const A=b;if(u)d.RectangleGeometryLibrary.computePosition(t,i,!1,E,0,A),y[_++]=A.x,y[_++]=A.y,y[_++]=A.z;else for(m=0;m<l;m++)d.RectangleGeometryLibrary.computePosition(t,i,!1,E,m,A),y[_++]=A.x,y[_++]=A.y,y[_++]=A.z;for(m=l-1,E=1;E<n;E++)d.RectangleGeometryLibrary.computePosition(t,i,!1,E,m,A),y[_++]=A.x,y[_++]=A.y,y[_++]=A.z;if(E=n-1,!c)for(m=l-2;m>=0;m--)d.RectangleGeometryLibrary.computePosition(t,i,!1,E,m,A),y[_++]=A.x,y[_++]=A.y,y[_++]=A.z;for(m=0,E=n-2;E>0;E--)d.RectangleGeometryLibrary.computePosition(t,i,!1,E,m,A),y[_++]=A.x,y[_++]=A.y,y[_++]=A.z;const G=y.length/3*2,R=s.IndexDatatype.createTypedArray(y.length/3,G);let P=0;for(let e=0;e<y.length/3-1;e++)R[P++]=e,R[P++]=e+1;R[P++]=y.length/3-1,R[P++]=0;const L=new a.Geometry({attributes:new r.GeometryAttributes,primitiveType:a.PrimitiveType.LINES});return L.attributes.position=new a.GeometryAttribute({componentDatatype:o.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:y}),L.indices=R,L}function A(n){const o=(n=e.defaultValue(n,e.defaultValue.EMPTY_OBJECT)).rectangle,a=e.defaultValue(n.granularity,u.CesiumMath.RADIANS_PER_DEGREE),r=e.defaultValue(n.ellipsoid,t.Ellipsoid.WGS84),l=e.defaultValue(n.rotation,0),s=e.defaultValue(n.height,0),c=e.defaultValue(n.extrudedHeight,s);this._rectangle=i.Rectangle.clone(o),this._granularity=a,this._ellipsoid=r,this._surfaceHeight=Math.max(s,c),this._rotation=l,this._extrudedHeight=Math.min(s,c),this._offsetAttribute=n.offsetAttribute,this._workerName="createRectangleOutlineGeometry"}A.packedLength=i.Rectangle.packedLength+t.Ellipsoid.packedLength+5,A.pack=function(n,o,a){return a=e.defaultValue(a,0),i.Rectangle.pack(n._rectangle,o,a),a+=i.Rectangle.packedLength,t.Ellipsoid.pack(n._ellipsoid,o,a),a+=t.Ellipsoid.packedLength,o[a++]=n._granularity,o[a++]=n._surfaceHeight,o[a++]=n._rotation,o[a++]=n._extrudedHeight,o[a]=e.defaultValue(n._offsetAttribute,-1),o};const G=new i.Rectangle,R=t.Ellipsoid.clone(t.Ellipsoid.UNIT_SPHERE),P={rectangle:G,ellipsoid:R,granularity:void 0,height:void 0,rotation:void 0,extrudedHeight:void 0,offsetAttribute:void 0};A.unpack=function(n,o,a){o=e.defaultValue(o,0);const r=i.Rectangle.unpack(n,o,G);o+=i.Rectangle.packedLength;const l=t.Ellipsoid.unpack(n,o,R);o+=t.Ellipsoid.packedLength;const s=n[o++],u=n[o++],c=n[o++],d=n[o++],p=n[o];return e.defined(a)?(a._rectangle=i.Rectangle.clone(r,a._rectangle),a._ellipsoid=t.Ellipsoid.clone(l,a._ellipsoid),a._surfaceHeight=u,a._rotation=c,a._extrudedHeight=d,a._offsetAttribute=-1===p?void 0:p,a):(P.granularity=s,P.height=u,P.rotation=c,P.extrudedHeight=d,P.offsetAttribute=-1===p?void 0:p,new A(P))};const L=new t.Cartographic;return A.createGeometry=function(t){const i=t._rectangle,r=t._ellipsoid,p=d.RectangleGeometryLibrary.computeOptions(i,t._granularity,t._rotation,0,_,L);let f,g;if(u.CesiumMath.equalsEpsilon(i.north,i.south,u.CesiumMath.EPSILON10)||u.CesiumMath.equalsEpsilon(i.east,i.west,u.CesiumMath.EPSILON10))return;const h=t._surfaceHeight,b=t._extrudedHeight;let A;if(!u.CesiumMath.equalsEpsilon(h,b,0,u.CesiumMath.EPSILON2)){if(f=function(e,t){const i=e._surfaceHeight,n=e._extrudedHeight,o=e._ellipsoid,a=n,r=i,l=E(e,t),u=t.height,d=t.width,p=c.PolygonPipeline.scaleToGeodeticHeight(l.attributes.position.values,r,o,!1);let f=p.length;const g=new Float64Array(2*f);g.set(p);const h=c.PolygonPipeline.scaleToGeodeticHeight(l.attributes.position.values,a,o);g.set(h,f),l.attributes.position.values=g;const y=t.northCap,m=t.southCap;let b=4;y&&(b-=1),m&&(b-=1);const _=2*(g.length/3+b),A=s.IndexDatatype.createTypedArray(g.length/3,_);f=g.length/6;let G,R=0;for(let e=0;e<f-1;e++)A[R++]=e,A[R++]=e+1,A[R++]=e+f,A[R++]=e+f+1;if(A[R++]=f-1,A[R++]=0,A[R++]=f+f-1,A[R++]=f,A[R++]=0,A[R++]=f,y)G=u-1;else{const e=d-1;A[R++]=e,A[R++]=e+f,G=d+u-2}if(A[R++]=G,A[R++]=G+f,!m){const e=d+G-1;A[R++]=e,A[R]=e+f}return l.indices=A,l}(t,p),e.defined(t._offsetAttribute)){const e=f.attributes.position.values.length/3;let i=new Uint8Array(e);t._offsetAttribute===l.GeometryOffsetAttribute.TOP?i=i.fill(1,0,e/2):(A=t._offsetAttribute===l.GeometryOffsetAttribute.NONE?0:1,i=i.fill(A)),f.attributes.applyOffset=new a.GeometryAttribute({componentDatatype:o.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:i})}const u=n.BoundingSphere.fromRectangle3D(i,r,h,m),d=n.BoundingSphere.fromRectangle3D(i,r,b,y);g=n.BoundingSphere.union(u,d)}else{if(f=E(t,p),f.attributes.position.values=c.PolygonPipeline.scaleToGeodeticHeight(f.attributes.position.values,h,r,!1),e.defined(t._offsetAttribute)){const e=f.attributes.position.values.length;A=t._offsetAttribute===l.GeometryOffsetAttribute.NONE?0:1;const i=new Uint8Array(e/3).fill(A);f.attributes.applyOffset=new a.GeometryAttribute({componentDatatype:o.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:i})}g=n.BoundingSphere.fromRectangle3D(i,r,h)}return new a.Geometry({attributes:f.attributes,indices:f.indices,primitiveType:a.PrimitiveType.LINES,boundingSphere:g,offsetAttribute:t._offsetAttribute})},function(n,o){return e.defined(o)&&(n=A.unpack(n,o)),n._ellipsoid=t.Ellipsoid.clone(n._ellipsoid),n._rectangle=i.Rectangle.clone(n._rectangle),A.createGeometry(n)}}));
