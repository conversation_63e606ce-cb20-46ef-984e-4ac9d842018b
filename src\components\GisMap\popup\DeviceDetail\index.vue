<template>
  <PopupFrame
    :title="'查看详情'"
    :hasArrow="true"
    :showTitle="showTitle"
    :data="data"
    :tabList="tabList"
    @change-tab="tabIndex = $event"
    @close="handleClose"
    :isAlarm="isWarning"
  >
    <component
      :is="currentCom"
      :data="data"
      :tabIndex="tabIndex"
      :baseInfo="baseInfo"
    />
  </PopupFrame>
</template>

<script setup>
import { ref, computed, watch } from "vue";
import PopupFrame from "../PopupFrame.vue";
import AlarmInfo from "./components/AlarmInfo.vue";
import MonitorCurve from "./components/MonitorCurve.vue";
import DisposalProcess from "./components/DisposalProcess.vue";
import BaseInfo from "../BaseInfo.vue";
import {popupApiInfo} from "@/components/GisMap/popup/popupApi.js";
import {popupConfigInfo} from "@/components/GisMap/popup/NormalDetail/config.js";
import {mapStates} from "@/components/GisMap/mapStates.js";

const props = defineProps({
    showTitle: {
        type: Boolean,
        default: true,
    },
    data: {
        type: Object,
        default: () => ({}),
    },
    closeEvent: {
        type: Function,
        required: false
    }
});

const alarmId = computed(() => props?.data?.alarmStatus !=="0" ?? "");

// 是否为告警状态
const isWarning = computed(() => !!alarmId.value);

// tab
const tabList = computed(() => {
    let defaultTab = [
        {
            id: 1,
            name: "设备信息",
            com: BaseInfo,
        },
        {
            id: 2,
            name: "监测曲线",
            com: MonitorCurve,
        },
    ];
    const alarmTab1 = [
        { id: 4, name: "报警信息", com: AlarmInfo },
    ];
    const alarmTab2 = [
        { id: 5, name: "报警记录", com: DisposalProcess },
    ];
    return isWarning.value ? [...alarmTab1, ...defaultTab, ...alarmTab2] : defaultTab;
});
const tabIndex = ref(1);

const currentCom = computed(() => {
    return tabList.value.find((v) => v.id === tabIndex.value)?.com;
});

const baseInfo = ref([]);

// 获取基础信息
const getBaseInfo = async () => {
    const { data } = await popupApiInfo[props.data?.layerId](props.data?.id);
    baseInfo.value = popupConfigInfo[props.data?.layerId].map((v) => {
        return {
            ...v,
            value: v.props === "onlineStatus" && data[v.props] === 1 ? "在线"
                  : v.props === "onlineStatus" && data[v.props] === 0 ? "离线"
                  : data[v.props] || "",
        };
    });
};

const handleClose = () => {
    props.closeEvent?.();
    //清除高亮
    mapStates.earth.entity.clearHighlight();
};

watch(
    () => props.data,
    () => {
        if (isWarning.value) tabIndex.value = 4;
        getBaseInfo();
    },
    {
        deep: true,
        immediate: true,
    }
);
</script>

<style lang="scss" scoped>
:deep(.el-table) {
  --el-table-border-color: none;
  --el-table-bg-color: none;
  tbody tr {
    &:hover {
      td {
        background: #12254c !important;
      }
    }
  }
}
</style>
