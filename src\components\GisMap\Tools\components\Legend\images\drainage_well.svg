<?xml version="1.0" encoding="UTF-8"?>
<svg width="18px" height="18px" viewBox="0 0 18 18" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>窨井</title>
    <defs>
        <circle id="path-1" cx="7" cy="7" r="7"></circle>
        <filter x="-21.4%" y="-21.4%" width="142.9%" height="142.9%" filterUnits="objectBoundingBox" id="filter-2">
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="1" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.162200418   0 0 0 0 0.568546468   0 0 0 0 0.902202219  0 0 0 0.5 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="27.9288816%" y1="8.11919591%" x2="71.2992224%" y2="91.9872084%" id="linearGradient-3">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#A5E2FF" offset="16.835118%"></stop>
            <stop stop-color="#83BEFF" offset="71.3150675%"></stop>
            <stop stop-color="#C6E3FF" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="图层&amp;图例" transform="translate(-705.000000, -271.000000)">
            <g id="编组-13" transform="translate(683.000000, 30.000000)">
                <g id="编组-45备份" transform="translate(0.000000, 65.000000)">
                    <g id="编组-2" transform="translate(24.000000, 71.000000)">
                        <g id="窨井" transform="translate(0.000000, 107.000000)">
                            <g id="正常备份-2">
                                <g id="椭圆形">
                                    <use fill="black" fill-opacity="1" filter="url(#filter-2)" xlink:href="#path-1"></use>
                                    <use fill="#0099FF" fill-rule="evenodd" xlink:href="#path-1"></use>
                                </g>
                                <circle id="椭圆形" stroke="url(#linearGradient-3)" stroke-width="0.823529412" cx="7" cy="7" r="6.58823529"></circle>
                            </g>
                            <g id="编组-41备份-15" transform="translate(3.000000, 3.000000)" fill="#FFFFFF" fill-rule="nonzero">
                                <path d="M4,0 C1.78333334,0 0,1.78333334 0,4 L0,4.08333334 C0.05,6.26666666 1.81666666,8 4,8 C6.18333334,8 7.95,6.26666666 8,4.08333334 L8,4 C8,1.78333334 6.21666666,0 4,0 Z M3.46666666,1.28333334 L3.58333334,1.85 C2.86666667,2 2.26666667,2.48333334 1.98333334,3.16666666 C1.93333334,3.3 1.88333334,3.43333333 1.86666666,3.58333334 L1.3,3.46666666 C1.51666666,2.36666666 2.36666666,1.5 3.46666666,1.28333334 L3.46666666,1.28333334 Z M3.46666666,6.71666666 C2.36666666,6.5 1.51666666,5.63333333 1.3,4.55 L1.86666666,4.43333334 C1.95,4.86666667 2.16666666,5.25 2.46666666,5.55 C2.76666666,5.85 3.16666666,6.06666666 3.58333334,6.15 L3.46666666,6.71666666 L3.46666666,6.71666666 Z M3.05,4 C3.05,3.46666666 3.48333334,3.05 4,3.05 C4.53333334,3.05 4.95,3.48333334 4.95,4 C4.95,4.53333334 4.51666666,4.95 4,4.95 C3.46666666,4.95 3.05,4.53333334 3.05,4 Z M4.53333334,6.71666666 L4.41666666,6.15 C5.13333333,6 5.73333333,5.51666666 6.01666666,4.83333334 C6.06666666,4.7 6.11666666,4.56666667 6.13333334,4.41666666 L6.7,4.53333334 C6.48333334,5.63333334 5.63333334,6.5 4.53333334,6.71666666 L4.53333334,6.71666666 Z M6.15,3.56666666 C5.98333334,2.7 5.3,2.01666666 4.43333334,1.83333334 L4.55,1.26666666 C5.08333334,1.36666666 5.58333334,1.63333333 5.96666666,2.03333334 C6.35,2.41666667 6.61666666,2.91666667 6.71666666,3.45 L6.15,3.56666666 Z" id="形状"></path>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>