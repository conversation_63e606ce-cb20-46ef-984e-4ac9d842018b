<template>
  <PanelBox title="风险清单" class="drainage-risk-left-bottom">
    <template #extra>
      <div class="com-select">
        <CommonSelect v-model="selectedPipeType" :options="pipeTypeOptions" @change="handlePipeTypeChange" />
      </div>
    </template>
    <div class="panel-content">
      <!-- 使用滚动表格组件 -->
      <ScrollTable :columns="tableColumns" :data="pipelineList" :autoScroll="true" :scrollSpeed="3000"
        :tableHeight="tableHeight" :visibleRows="4" @row-click="openPipelineDetail">
        <!-- 自定义风险等级列 -->
        <template #riskLevel="{ row }">
          <span :style="{ color: getRiskColor(row.riskLevel) }">{{ row.riskLevel }}</span>
        </template>
      </ScrollTable>
      <!-- 更多按钮放置在右下角 -->
        <div class="more-btn" @click="handleMoreClick">更多</div>
    </div>
  </PanelBox>
  <PipelineDetailModal v-model="pipelineDetailVisible" :pipeline-data="selectedPipeline" />
  <RiskListModal v-model="riskListVisible" :pipe-type="riskListPipeType" />
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import PanelBox from '@/components/screen/PanelBox.vue'
import CommonSelect from '@/components/screen/CommonSelect.vue'
import ScrollTable from '@/components/screen/common/ScrollTable.vue'
import PipelineDetailModal from './PipelineDetailModal.vue'
import RiskListModal from './RiskListModal.vue'

// 管线类型选项 - 与 LeftTopPanel.vue 保持一致
const pipeTypeOptions = [
  { value: 'rain', label: '管线' },
  { value: 'plant', label: '热源厂' },
  { value: 'station', label: '换热站' }
]

// 默认选择雨水管线
const selectedPipeType = ref('rain')

// 表格列配置
const tableColumns = computed(() => {
  if (selectedPipeType.value === 'plant') {
    return [
      { title: '热源厂名称', dataIndex: 'name', width: '40%', fontSize: '13px' },
      { title: '供热建筑面积', dataIndex: 'area', width: '30%', fontSize: '13px' },
      { title: '风险等级', dataIndex: 'riskLevel', width: '30%', fontSize: '13px', slot: 'riskLevel' }
    ]
  } else if (selectedPipeType.value === 'station') {
    return [
      { title: '换热站名称', dataIndex: 'name', width: '40%', fontSize: '13px' },
      { title: '供热建筑面积', dataIndex: 'area', width: '30%', fontSize: '13px' },
      { title: '风险等级', dataIndex: 'riskLevel', width: '30%', fontSize: '13px', slot: 'riskLevel' }
    ]
  } else {
    // 默认为管线
    return [
      { title: '管线编码', dataIndex: 'code', width: '25%', fontSize: '13px' },
      { title: '管龄', dataIndex: 'age', width: '15%', fontSize: '13px' },
      { title: '材质', dataIndex: 'material', width: '15%', fontSize: '13px' },
      { title: '管径', dataIndex: 'diameter', width: '15%', fontSize: '13px' },
      { title: '风险等级', dataIndex: 'riskLevel', width: '30%', fontSize: '13px', slot: 'riskLevel' }
    ]
  }
})

// 动态计算表格高度
const tableHeight = computed(() => {
  // 根据不同分辨率动态调整表格高度
  if (window.innerHeight === 910) {
    return '180px' // 910px高度的屏幕使用更小的表格高度
  } else if (window.innerHeight >= 940 && window.innerHeight <= 1055) {
    return '180px' // 940px-1055px高度的屏幕
  } else if (window.innerWidth >= 2561) {
    return '220px' // 超宽屏幕
  } else if (window.innerWidth >= 1920 && window.innerWidth <= 2560) {
    return '220px' // 标准宽屏
  } else {
    return '180px' // 默认高度
  }
})



// 弹窗控制
const pipelineDetailVisible = ref(false)
const selectedPipeline = ref({
  riskCode: 'DFX-001',
  code: '18768_590845',
  type: '雨水管网',
  material: 'PE100',
  diameter: '400mm',
  length: '1.151km',
  location: '*******',
  buildTime: '2020年1月5日',
  riskLevel: '低风险',
  riskValue: '98',
  controlStatus: '未管控'
})
const riskListVisible = ref(false)
const riskListPipeType = computed(() => selectedPipeType.value)

// 模拟管网风险数据 - 根据图片内容创建
const mockData = {
  rain: [
    { code: 'GX10322', age: '3年', material: 'PE', diameter: '0.5M', riskLevel: '重大风险' },
    { code: 'GX10322', age: '5年', material: 'PE', diameter: '0.5M', riskLevel: '较大风险' },
    { code: 'GX10322', age: '8年', material: 'PE', diameter: '0.5M', riskLevel: '较大风险' },
    { code: 'GX10322', age: '5年', material: 'PE', diameter: '0.5M', riskLevel: '较大风险' },
    { code: 'GX10322', age: '8年', material: 'PE', diameter: '0.5M', riskLevel: '较大风险' },
    { code: 'GX10322', age: '6年', material: 'PE', diameter: '0.5M', riskLevel: '一般风险' },
    { code: 'GX10322', age: '6年', material: 'PE', diameter: '0.5M', riskLevel: '一般风险' }
  ],
  plant: [
    { name: '热源厂A', area: '10000 m²', riskLevel: '低风险' },
    { name: '热源厂B', area: '12000 m²', riskLevel: '一般风险' },
    { name: '热源厂C', area: '8000 m²', riskLevel: '较大风险' },
    { name: '热源厂D', area: '15000 m²', riskLevel: '重大风险' }
  ],
  station: [
    { name: '换热站X', area: '500 m²', riskLevel: '低风险' },
    { name: '换热站Y', area: '600 m²', riskLevel: '一般风险' },
    { name: '换热站Z', area: '450 m²', riskLevel: '较大风险' },
    { name: '换热站W', area: '700 m²', riskLevel: '低风险' }
  ]
}

// 计算当前展示数据
const pipelineList = computed(() => {
  return mockData[selectedPipeType.value] || []
})

// 处理管线类型变化
const handlePipeTypeChange = () => {
  console.log('管线类型变更为:', selectedPipeType.value)
  // 这里可以调用接口获取对应类型的风险数据
  fetchRiskData(selectedPipeType.value)
}

// 打开管线详情弹窗
const openPipelineDetail = (row) => {
  if (selectedPipeType.value === 'plant' || selectedPipeType.value === 'station') {
    // TODO: 热源厂或换热站的详情处理
    console.log('TODO: Open detail for plant/station', row)
    // 示例：可以直接将行数据赋值给 selectedPipeline，或者根据需要获取更详细的数据
    selectedPipeline.value = {
      riskCode: row.name, // 假设用名称作为风险编号
      code: row.name, // 假设用名称作为编码
      type: selectedPipeType.value === 'plant' ? '热源厂' : '换热站',
      area: row.area,
      riskLevel: row.riskLevel,
      // 其他字段可以根据实际情况填充或留空
      material: '-',
      diameter: '-',
      length: '-',
      location: '*******',
      buildTime: 'N/A',
      riskValue: '-',
      controlStatus: '未管控'
    }
    pipelineDetailVisible.value = true
    return
  }

  // 在实际项目中，这里应该调用接口获取详细信息
  // const response = await api.getPipelineDetail(row.code)
  // selectedPipeline.value = response.data

  // 目前使用模拟数据
  selectedPipeline.value = {
    ...selectedPipeline.value,
    code: row.code,
    material: row.material,
    riskLevel: row.riskLevel
  }
  pipelineDetailVisible.value = true
}

// 点击更多按钮处理
const handleMoreClick = () => {
  if (selectedPipeType.value === 'plant' || selectedPipeType.value === 'station') {
    // TODO: 热源厂或换热站的更多操作处理
    console.log('TODO: Handle more click for plant/station')
    // 示例：可以打开一个不同的列表弹窗，或者执行其他操作
    // riskListVisible.value = true; // 如果也用同一个弹窗，确保 RiskListModal 能处理不同类型
    return
  }
  riskListVisible.value = true
}

// 根据风险等级获取对应的颜色
const getRiskColor = (riskLevel) => {
  switch (riskLevel) {
    case '重大风险':
      return '#FF2330'
    case '较大风险':
      return '#FF9000'
    case '一般风险':
      return '#FFD11B'
    case '低风险':
      return '#00B0FF'
    default:
      return '#FFFFFF'
  }
}

// 从后端获取数据的方法
const fetchRiskData = async (pipeType) => {
  try {
    // TODO: 实际项目中替换为API调用
    // const response = await api.getRiskData(pipeType)
    // if (response.code === 200 && response.data) {
    //   mockData[pipeType] = response.data
    // }

    // 目前使用模拟数据
    console.log(`获取${pipeType}风险数据`)
  } catch (error) {
    console.error('获取风险数据失败:', error)
  }
}

// 监听管线类型变化
watch(selectedPipeType, (newValue) => {
  fetchRiskData(newValue)
})

onMounted(() => {
  // 初始化时获取数据
  fetchRiskData(selectedPipeType.value)
})
</script>

<style scoped>
.drainage-risk-left-bottom {
  height: 20rem;
}

.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.com-select {
  margin-right: 20px;
}

/* 点击行样式 */
:deep(.scroll-table tr) {
  cursor: pointer;
  transition: background-color 0.2s;
}

:deep(.scroll-table td) {
  cursor: pointer;
  transition: background-color 0.2s;
  line-height: 2.3rem;
}

:deep(.scroll-table tr:hover) {
  background-color: rgba(0, 163, 255, 0.2) !important;
}

/* 更多按钮容器样式 */
.more-btn-container {
  position: relative;
  width: 100%;
  height: 0;
}

/* 更多按钮样式 */
.more-btn {
  position: absolute;
  right: 5px;
  bottom: -5px;
  font-family: PingFangSC, 'PingFang SC';
  font-size: 12px;
  color: #3AA1FF;
  cursor: pointer;
  text-decoration: underline;
  z-index: 10;
}

.more-btn:hover {
  color: #66B8FF;
}

/* 媒体查询适配不同分辨率 */
@media screen and (min-width: 1920px) and (max-width: 2560px) {
  .drainage-risk-left-bottom {
    height: 310px;
  }
}

@media screen and (max-width: 1919px) {
  .drainage-risk-left-bottom {
    height: 310px;
  }
}

@media screen and (min-width: 2561px) {
  .drainage-risk-left-bottom {
    height: 310px;
  }
}

/* 添加全屏模式(1080px高度)的适配 */
@media screen and (min-height: 1056px) and (max-height: 1080px) {
  .drainage-risk-left-bottom {
    height: 310px;
  }
  .panel-content {
    padding: 15px;
    gap: 15px;
  }
}

/* 940px-1055px高度的屏幕特别优化 */
@media screen and (min-height: 940px) and (max-height: 1055px) {
  .drainage-risk-left-bottom {
    height: 310px;
  }

  .panel-content {
    padding: 10px;
    gap: 10px;
  }

  .more-btn-container {
    height: 0;
  }

  .more-btn {
    font-size: 11px;
    padding: 4px;
    bottom: 5px;
  }
}

/* 910px高度的屏幕特别优化 */
@media screen and (min-height: 900px) and (max-height: 940px) {
  .drainage-risk-left-bottom {
    height: 252px;
  }

  .panel-content {
    padding: 8px;
    gap: 0px;
  }

  .more-btn-container {
    height: 0;
  }

  .more-btn {
    bottom: 10px;
    z-index: 10;
  }
}
</style>