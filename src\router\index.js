import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '@/stores/user'
import Screen from '@/views/screen/index.vue'
import { adminRoutes } from './admin.js'

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/login/index.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/',
    name: 'Home',
    component: () => import('@/views/Home.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/screen',
    name: 'Screen',
    component: () => import('@/views/screen/index.vue'),
    meta: { requiresAuth: true }
  },
  // 重定向路由，用于刷新页面功能
  {
    path: '/redirect',
    component: () => import('@/views/redirect/index.vue'),
    children: [
      {
        path: '/redirect/:path(.*)',
        component: () => import('@/views/redirect/index.vue')
      }
    ]
  },
  // 将adminRoutes合并到主路由配置中
  ...adminRoutes,
  {
    path: '/',
    redirect: '/comprehensive/overview'
  },
  {
    path: '/bridge',
    name: 'Bridge',
    component: Screen,
    meta: { requiresAuth: true },
    props: route => ({
      primaryTab: 'bridge',
      secondaryTab: 'overview'
    })
  },
  {
    path: '/:primaryTab/:secondaryTab',
    name: 'Screen',
    component: Screen,
    props: true,
    beforeEnter: (to, from, next) => {
      const { primaryTab, secondaryTab } = to.params
      
      const validPrimaryTabs = ['comprehensive', 'gas', 'drainage', 'heating']
      
      const validSecondaryTabs = {
        comprehensive: ['overview', 'risk', 'monitoring', 'coordination', 'emergency'],
        gas: ['overview', 'network-risk', 'monitoring', 'decision'],
        drainage: ['overview', 'risk', 'flooding-risk', 'monitoring', 'decision'],
        heating: ['overview', 'risk', 'monitoring', 'decision']
      }
      
      if (!validPrimaryTabs.includes(primaryTab)) {
        next({ path: '/comprehensive/overview' })
        return
      }
      
      if (!validSecondaryTabs[primaryTab].includes(secondaryTab)) {
        next({ path: `/${primaryTab}/${validSecondaryTabs[primaryTab][0]}` })
        return
      }
      
      next()
    }
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/comprehensive/overview'
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

router.beforeEach((to, from, next) => {
  // 获取token，确保在路由守卫中读取到最新的token
  const TOKEN = 'Admin-Token'
  const token = localStorage.getItem(TOKEN)

  if (to.meta.requiresAuth && !token) {
    next('/login')
  } else if (to.path === '/login' && token) {
    next('/')
  } else {
    next()
  }
})

export default router