<template>
  <div class="search-container">
    <el-autocomplete
      v-model="searchString"
      :fetch-suggestions="querySearch"
      :popper-append-to-body="false"
      :clearable="true"
      class="inline-input"
      popper-class="mapSearch"
      placeholder="请输入名称"
      :trigger-on-focus="true"
      value-key="gisName"
      @select="onSelect"
    >
      <template #suffix> </template>
    </el-autocomplete>
  </div>
</template>

<script setup>
import bus from "@/utils/mitt";
import {ref} from "vue";
const emits = defineEmits(["onSelect"]);

const searchData = ref([]);

const searchString = ref(""); //搜索框字符串绑定v-model
const onSelect = (selected) => {
  bus.emit("screenSlideFocusToGisPoint", {
    layerQueryId: selected.layerId,
    id: selected.id,
  });
};

const querySearch = (queryString, cb) => {
  // let results = [{ name: 'bbb' }]
  let results = [];
  if (queryString) {
    //todo 检索地图数据
    results = searchData.value.filter(createFilter(queryString));
  }
  console.log(results);
  cb(results);
};
const createFilter = (queryString) => {
  return (obj) => {
    return (
      obj.gisName &&
      obj.gisName.toLowerCase().indexOf(queryString.toLowerCase()) >= 0
    );
  };
};

bus.on("searchDataChanged", (data) => {
  const keys = Object.keys(data);
  const mapData = keys
    .map((v) => {
      return data[v].map((a) => {
        return {
          ...a,
          layerId: v,
          gisName: a?.properties?.gisName ?? "",
        };
      });
    })
    .flat(2);
  searchData.value = mapData;
});
</script>

<style lang="scss" scoped>
.search-container {
  :deep(.el-autocomplete) {
    position: relative;
    .el-input__wrapper {
      width: 200px;
      height: 32px;
      background: rgba(13, 37, 82, 0.8);
      border-radius: 4px;
      border: 1px solid #324256;
      box-shadow: none;
    }
    .el-input__inner {
      color: #fff;
      width: 100%;
      font-size: 14px;
    }
  }

  .icon-search {
    width: 18px;
    height: 18px;
    margin-top: 6px;
    color: cadetblue;
    cursor: pointer;
  }
}

:deep(.el-autocomplete-suggestion) {
  background-color: rgba(13, 37, 82, 0.8) !important;
}
</style>
<style lang="scss">
.el-popper.mapSearch {
  background: rgba(13, 37, 82, 0.8);
  border: 1px solid #324256;
  color: #fff;
  width: 250px;
  .el-autocomplete-suggestion li {
    color: #fff;
    &:hover {
      background-color: #0d4c92;
    }
  }
  .el-popper__arrow {
    &::before {
      background-color: rgba(13, 37, 82, 0.8);
      border-left: 1px solid #324256;
      border-top: 1px solid #324256;
      transform: rotate(45deg);
    }
  }
}
</style>
