<?xml version="1.0" encoding="UTF-8"?>
<svg width="25px" height="25px" viewBox="0 0 25 25" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>放大</title>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="一张图总览（鼠标点击交互）" transform="translate(-1361, -863)">
            <g id="编组-4" transform="translate(1357, 859)">
                <g id="放大" transform="translate(4.3333, 4.3333)">
                    <rect id="矩形备份-28" x="0" y="0" width="24" height="24" rx="4"></rect>
                    <path d="M5.95238095,10.7142857 L17.3809524,10.7142857 C17.9069379,10.7142857 18.3333333,11.162001 18.3333333,11.7142857 C18.3333333,12.2665705 17.9069379,12.7142857 17.3809524,12.7142857 L5.95238095,12.7142857 C5.42639548,12.7142857 5,12.2665705 5,11.7142857 C5,11.162001 5.42639548,10.7142857 5.95238095,10.7142857 Z" id="路径" fill="#FFFFFF" fill-rule="nonzero"></path>
                    <path d="M12.7142857,5.95238095 L12.7142857,17.3809524 C12.7142857,17.9069379 12.2665705,18.3333333 11.7142857,18.3333333 C11.162001,18.3333333 10.7142857,17.9069379 10.7142857,17.3809524 L10.7142857,5.95238095 C10.7142857,5.42639548 11.162001,5 11.7142857,5 C12.2665705,5 12.7142857,5.42639548 12.7142857,5.95238095 Z" id="路径" fill="#FFFFFF" fill-rule="nonzero"></path>
                </g>
            </g>
        </g>
    </g>
</svg>