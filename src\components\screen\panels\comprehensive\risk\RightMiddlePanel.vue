<template>
  <PanelBox title="隐患信息">
    <template #extra>
      <div class="extra-container">
        <div class="com-select">
          <CommonSelect v-model="selectedType" :options="typeOptions" @change="handleTypeChange" />
        </div>
        <div class="com-select">
          <CommonSelect v-model="selectedTime" :options="timeOptions" @change="handleTimeChange" />
        </div>
      </div>
    </template>
    <div class="panel-content">
      <!-- 隐患数量信息 -->
      <div class="hazard-stats-container">
        <div class="hazard-stats-box">
          <div class="hazard-item">
            <div class="hazard-value">{{ hazardStats.total }}</div>
            <div class="hazard-label">隐患总数</div>
          </div>
          <div class="hazard-item">
            <div class="hazard-value">{{ hazardStats.handled }}</div>
            <div class="hazard-label">已整改</div>
          </div>
          <div class="hazard-item">
            <div class="hazard-value">{{ hazardStats.pending }}</div>
            <div class="hazard-label">待整改</div>
          </div>
          <div class="hazard-item">
            <div class="hazard-value">{{ hazardStats.inProgress }}</div>
            <div class="hazard-label">整改中</div>
          </div>
          <div class="hazard-item">
            <div class="hazard-value">{{ hazardStats.pendingReview }}</div>
            <div class="hazard-label">待验收</div>
          </div>
        </div>
      </div>

      <!-- 隐患列表 -->
      <div class="hazard-list">
        <ScrollTable 
          :columns="tableColumns" 
          :data="hazardList" 
          :autoScroll="true" 
          :scrollSpeed="3000"
          :tableHeight="tableHeight" 
          :visibleRows="5" 
          @row-click="handleRowClick"
        >
          <!-- 状态列的自定义渲染 -->
          <template #status="{ row }">
            <span :class="getStatusClass(row.status)">{{ row.status }}</span>
          </template>
        </ScrollTable>
        <!-- 更多按钮 -->
        <div class="more-btn-container">
          <div class="more-btn" @click="handleMoreClick">更多</div>
        </div>
      </div>
    </div>
  </PanelBox>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import PanelBox from '@/components/screen/PanelBox.vue'
import CommonSelect from '@/components/screen/CommonSelect.vue'
import ScrollTable from '@/components/screen/common/ScrollTable.vue'

// 综合态势总览右中面板组件

// 专项类型选择
const selectedType = ref('all')
const typeOptions = [
  { label: '全部', value: 'all' },
  { label: '燃气', value: 'gas' },
  { label: '排水', value: 'water' },
  { label: '桥梁', value: 'bridge' },
  { label: '供热', value: 'heating' }
]

// 时间选择
const selectedTime = ref('year')
const timeOptions = [
  { label: '近一周', value: 'week' },
  { label: '近一月', value: 'month' },
  { label: '近一年', value: 'year' }
]

// 隐患统计数据
const hazardStats = reactive({
  total: 54990,
  handled: 45000,
  pending: 900,
  inProgress: 15,
  pendingReview: 15
})

// 表格列配置
const tableColumns = [
  { title: '隐患名称', dataIndex: 'name', width: '25%', fontSize: '12px' },
  { title: '隐患等级', dataIndex: 'level', width: '20%', fontSize: '12px' },
  { title: '上报时间', dataIndex: 'reportTime', width: '35%', fontSize: '12px' },
  { title: '整改状态', dataIndex: 'status', width: '20%', fontSize: '12px', slot: 'status' }
]

// 模拟隐患数据
const mockHazardData = [
  { name: '燃气管网老旧', level: '三级隐患', reportTime: '2024-12-8 14:26:28', status: '待整改' },
  { name: '燃气管网老旧', level: '三级隐患', reportTime: '2024-12-8 14:26:28', status: '已整改' },
  { name: '燃气管网老旧', level: '三级隐患', reportTime: '2024-12-8 14:26:28', status: '已整改' },
  { name: '燃气管网老旧', level: '三级隐患', reportTime: '2024-12-8 14:26:28', status: '已整改' },
  { name: '燃气管网老旧', level: '三级隐患', reportTime: '2024-12-8 14:26:28', status: '已整改' },
  { name: '燃气管网老旧', level: '三级隐患', reportTime: '2024-12-8 14:26:28', status: '已整改' }
]

// 表格数据
const hazardList = ref([...mockHazardData])

// 表格高度
const tableHeight = computed(() => {
  // 根据不同分辨率动态调整表格高度
  if (window.innerHeight >= 900 && window.innerHeight <= 940) {
    return '160px' // 较小屏幕
  } else if (window.innerHeight >= 940 && window.innerHeight <= 1055) {
    return '180px' // 中等屏幕
  } else if (window.innerHeight >= 1056) {
    return '220px' // 较大屏幕
  } else {
    return '180px' // 默认高度
  }
})

// 根据状态获取对应的样式类
const getStatusClass = (status) => {
  switch (status) {
    case '待整改':
      return 'status-pending'
    case '已整改':
      return 'status-handled'
    case '整改中':
      return 'status-in-progress'
    case '待验收':
      return 'status-pending-review'
    default:
      return 'status-default'
  }
}

// 处理专项类型变化
const handleTypeChange = (value) => {
  console.log('专项类型变更为:', value)
  fetchHazardData(value, selectedTime.value)
}

// 处理时间范围变化
const handleTimeChange = (value) => {
  console.log('时间范围变更为:', value)
  fetchHazardData(selectedType.value, value)
}

// 行点击事件
const handleRowClick = (row) => {
  console.log('点击隐患行:', row)
  // TODO: 实现隐患详情展示逻辑
}

// 点击更多按钮
const handleMoreClick = () => {
  console.log('查看更多隐患')
  // TODO: 实现查看更多隐患的逻辑
}

// 从后端获取数据的方法
const fetchHazardData = async (type, time) => {
  try {
    // TODO: 实际项目中替换为API调用
    // const response = await api.getHazardData(type, time)
    // if (response.code === 200 && response.data) {
    //   hazardList.value = response.data.list
    //   Object.assign(hazardStats, response.data.stats)
    // }

    // 目前使用模拟数据
    console.log(`获取${type}类型，${time}时间范围的隐患数据`)
    
    // 模拟数据变化
    if (type === 'gas') {
      hazardStats.total = 25000
      hazardStats.handled = 20000
    } else if (type === 'water') {
      hazardStats.total = 15000
      hazardStats.handled = 12000
    } else if (type === 'heating') {
      hazardStats.total = 10000
      hazardStats.handled = 8000
    } else if (type === 'bridge') {
      hazardStats.total = 5000
      hazardStats.handled = 4000
    } else {
      hazardStats.total = 54990
      hazardStats.handled = 45000
    }
    
    // 计算其他统计数据
    hazardStats.pending = Math.floor(hazardStats.total * 0.02)
    hazardStats.inProgress = Math.floor(hazardStats.total * 0.0003)
    hazardStats.pendingReview = hazardStats.inProgress
  } catch (error) {
    console.error('获取隐患数据失败:', error)
  }
}

onMounted(() => {
  // 初始化时获取数据
  fetchHazardData(selectedType.value, selectedTime.value)
})
</script>

<style scoped>
.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.extra-container {
  display: flex;
  gap: 15px;
}

.com-select {
  margin-right: 5px;
}

/* 隐患统计样式 */
.hazard-stats-container {
  display: flex;
  justify-content: center;
  margin: 5px 0;
}

.hazard-stats-box {
  width: 100%;
  height: 60px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 15px;
  background: rgba(0, 80, 179, 0.3);
  border: 1px solid rgba(0, 141, 255, 0.3);
}

.hazard-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 3px;
}

.hazard-label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #FFFFFF;
}

.hazard-value {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 18px;
  line-height: 20px;
  text-align: left;
  font-style: normal;
  background: linear-gradient(90deg, #FFFFFF 0%, #3CF3FF 98%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 隐患列表样式 */
.hazard-list {
  flex: 1;
  position: relative;
}

/* 状态样式 */
.status-pending {
  color: #FFC400;
}

.status-handled {
  color: #3CF3FF;
}

.status-in-progress {
  color: #36F281;
}

.status-pending-review {
  color: #D9E7FF;
}

/* 更多按钮容器样式 */
.more-btn-container {
  position: relative;
  width: 100%;
  height: 0;
}

/* 更多按钮样式 */
.more-btn {
  position: absolute;
  right: 10px;
  bottom: 10px;
  font-family: PingFangSC, 'PingFang SC';
  font-size: 12px;
  color: #3AA1FF;
  cursor: pointer;
  text-decoration: underline;
  z-index: 10;
}

.more-btn:hover {
  color: #66B8FF;
}

/* 响应式布局 */
@media screen and (min-height: 900px) and (max-height: 940px) {
  .panel-content {
    padding: 8px;
    gap: 5px;
  }
  
  .hazard-stats-box {
    height: 50px;
    padding: 0 10px;
  }
  
  .hazard-label {
    font-size: 12px;
  }
  
  .hazard-value {
    font-size: 16px;
    line-height: 18px;
  }
  
  .more-btn {
    bottom: 8px;
    right: 8px;
  }
}

@media screen and (min-height: 940px) and (max-height: 1055px) {
  .panel-content {
    padding: 10px;
    gap: 2px;
  }
  
  .hazard-stats-box {
    height: 55px;
  }
}
</style> 