<template>
  <el-dialog
    v-model="dialogVisible"
    title="运行记录"
    width="900px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="operation-record-dialog"
  >
    <div class="record-container">
      <!-- 顶部时间选择区域 -->
      <div class="date-range-container">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="YYYY/MM/DD"
          value-format="YYYY-MM-DD"
          :shortcuts="dateShortcuts"
          @change="handleDateRangeChange"
        />
      </div>

      <!-- Tab切换区域 -->
      <el-tabs v-model="activeTab" @tab-change="handleTabChange">
        <el-tab-pane label="历史数据" name="historyData">
          <!-- 历史数据表格 -->
          <el-table
            :data="processedHistoryData"
            :header-cell-style="headerCellStyle"
            :row-class-name="tableRowClassName"
            style="width: 100%"
            height="400px"
          >
            <el-table-column type="index" label="序号" width="60" align="center" />
            <el-table-column label="时间" prop="monitorTime" width="180" align="center">
              <template #default="scope">
                {{ formatJavaTime(scope.row.monitorTime) }}
              </template>
            </el-table-column>
            <el-table-column label="监测指标" prop="monitorIndex" width="120" align="center" />
            <el-table-column label="监测值" prop="monitorValue" width="120" align="center" />
            <el-table-column label="状态" prop="monitorStatusName" width="100" align="center">
              <template #default="scope">
                <el-tag 
                  :type="scope.row.monitorStatus === 0 ? 'success' : 'danger'" 
                  size="small"
                >
                  {{ scope.row.monitorStatusName || (scope.row.monitorStatus === 0 ? '正常' : '异常') }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
          
          <!-- 分页区域 -->
          <div class="pagination-container">
            <el-pagination
              v-model:current-page="historyPagination.currentPage"
              v-model:page-size="historyPagination.pageSize"
              :page-sizes="[10, 20, 30, 50]"
              layout="total, prev, pager, next, jumper, sizes"
              :total="historyPagination.total"
              @size-change="handleHistorySizeChange"
              @current-change="handleHistoryCurrentChange"
            />
          </div>
        </el-tab-pane>

        <el-tab-pane label="离线记录" name="offlineRecord">
          <!-- 离线记录表格 -->
          <el-table
            :data="offlineData"
            :header-cell-style="headerCellStyle"
            :row-class-name="tableRowClassName"
            style="width: 100%"
            height="400px"
          >
            <el-table-column type="index" label="序号" width="60" align="center" />
            <el-table-column label="离线时间" prop="offlineTime" width="180" align="center">
              <template #default="scope">
                {{ formatJavaTime(scope.row.offlineTime) }}
              </template>
            </el-table-column>
            <el-table-column label="恢复时间" prop="recoveryTime" width="180" align="center">
              <template #default="scope">
                {{ formatJavaTime(scope.row.recoveryTime) }}
              </template>
            </el-table-column>
            <el-table-column label="离线时长" prop="offlineDuration" min-width="120" align="center" />
          </el-table>
          
          <!-- 分页区域 -->
          <div class="pagination-container">
            <el-pagination
              v-model:current-page="offlinePagination.currentPage"
              v-model:page-size="offlinePagination.pageSize"
              :page-sizes="[10, 20, 30, 50]"
              layout="total, prev, pager, next, jumper, sizes"
              :total="offlinePagination.total"
              @size-change="handleOfflineSizeChange"
              @current-change="handleOfflineCurrentChange"
            />
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关 闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted } from 'vue';
import { getMonitorCurvePageData, getOfflineRecordsData, getMonitorIndicators } from '@/api/gas';
import moment from 'moment';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  deviceData: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible']);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 当前活动的选项卡
const activeTab = ref('historyData');

// 日期范围
const dateRange = ref([
  moment().subtract(30, 'days').format('YYYY-MM-DD'),
  moment().format('YYYY-MM-DD')
]);

// 日期快捷选项
const dateShortcuts = [
  {
    text: '最近一周',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    },
  },
  {
    text: '最近一个月',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    },
  },
  {
    text: '最近三个月',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
      return [start, end];
    },
  },
];

// 历史数据
const historyData = ref([]);

// 处理后的历史数据（用于显示）
const processedHistoryData = ref([]);

// 监测指标列表
const monitorIndicators = ref([]);

// 历史数据分页
const historyPagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
});

// 离线数据
const offlineData = ref([]);

// 离线数据分页
const offlinePagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
});

// 表头样式
const headerCellStyle = {
  background: '#F5F8FA',
  color: '#333333',
  fontWeight: 'bold',
  height: '40px',
  padding: '0',
  borderBottom: '1px solid #EBEEF5'
};

// 表格行样式
const tableRowClassName = ({ rowIndex }) => {
  return rowIndex % 2 === 0 ? 'even-row' : 'odd-row';
};

// 格式化Java时间对象
const formatJavaTime = (time) => {
  if (!time) return '-';
  
  // 处理后端返回的Java时间对象
  if (typeof time === 'object' && time !== null) {
    // 根据Java时间对象的结构构建日期
    const year = time.year + 1900; // Java年份从1900开始
    const month = time.month; // Java月份是0-11
    const date = time.date;
    const hours = time.hours;
    const minutes = time.minutes;
    const seconds = time.seconds;
    
    const dateObj = new Date(year, month, date, hours, minutes, seconds);
    return moment(dateObj).format('YYYY-MM-DD HH:mm:ss');
  }
  
  return time;
};

// 处理日期范围变化
const handleDateRangeChange = () => {
  // 重置分页
  if (activeTab.value === 'historyData') {
    historyPagination.currentPage = 1;
    fetchHistoryData();
  } else {
    offlinePagination.currentPage = 1;
    fetchOfflineData();
  }
};

// 处理选项卡变化
const handleTabChange = (tab) => {
  if (tab === 'historyData') {
    fetchHistoryData();
  } else {
    fetchOfflineData();
  }
};

// 获取监测指标
const fetchMonitorIndicators = async () => {
  if (!props.deviceData || !props.deviceData.id) return;
  
  try {
    const res = await getMonitorIndicators(props.deviceData.id);
    if (res && res.code === 200 && res.data) {
      monitorIndicators.value = res.data;
    }
  } catch (error) {
    console.error('获取监测指标失败', error);
  }
};

// 获取历史数据
const fetchHistoryData = async () => {
  if (!props.deviceData || !props.deviceData.id) return;
  
  try {
    // 先获取监测指标
    if (monitorIndicators.value.length === 0) {
      await fetchMonitorIndicators();
    }
    
    const [startDate, endDate] = dateRange.value;
    
    const params = {
      deviceId: props.deviceData.id,
      startTime: startDate + ' 00:00:00',
      endTime: endDate + ' 23:59:59',
      type: 0 // 使用默认类型
    };
    
    const res = await getMonitorCurvePageData(
      historyPagination.currentPage,
      historyPagination.pageSize,
      params
    );
    
    if (res && res.code === 200) {
      historyData.value = res.data.records || [];
      historyPagination.total = res.data.total || 0;
      
      // 处理数据，将每个时间点的多个指标拆分为多行
      processHistoryData();
    }
  } catch (error) {
    console.error('获取历史数据失败', error);
  }
};

// 处理历史数据，将每个时间点的多个指标拆分为多行
const processHistoryData = () => {
  const processed = [];
  
  // 如果没有数据或没有指标，直接返回
  if (historyData.value.length === 0 || monitorIndicators.value.length === 0) {
    processedHistoryData.value = [];
    return;
  }
  
  // 创建指标字段映射表
  const fieldMappings = {};
  monitorIndicators.value.forEach(indicator => {
    // 从monitorField中提取关键部分，例如从mon_wd提取wd
    const fieldKey = indicator.monitorField.split('_').pop().toLowerCase();
    // 存储映射关系：字段关键部分 -> 完整指标信息
    fieldMappings[fieldKey] = indicator;
    
    // 同时添加不带下划线的映射，例如monJd -> 角度
    // 这样可以处理没有下划线的字段名
    if (indicator.monitorField.startsWith('mon')) {
      const directKey = indicator.monitorField.substring(3).toLowerCase(); // 去掉'mon'前缀
      fieldMappings[directKey] = indicator;
    }
  });
  
  // 添加一些常见的监测指标映射，确保能正确显示
  const commonIndicators = {
    'jd': '角度',
    'sjzt': '水浸状态',
    'wd': '温度'
  };
  
  // 将常见指标添加到映射中（如果没有对应的指标信息）
  Object.keys(commonIndicators).forEach(key => {
    if (!fieldMappings[key]) {
      fieldMappings[key] = {
        monitorIndexName: commonIndicators[key],
        measureUnit: ''
      };
    }
  });
  
  // 调试日志
  console.log('监测指标映射表:', fieldMappings);
  if (historyData.value.length > 0) {
    console.log('历史数据示例:', historyData.value[0]);
  }
  
  historyData.value.forEach(record => {
    let hasAnyIndicator = false; // 标记是否找到任何指标
    
    // 遍历记录中的所有字段
    Object.keys(record).forEach(key => {
      // 只处理以mon开头的字段，并排除一些特定字段
      if (key.toLowerCase().startsWith('mon') && 
          !['monitortime', 'monitorstatus', 'monitorstatusname', 'monitortimestamp', 'monitorid'].includes(key.toLowerCase())) {
        // 提取字段关键部分，例如从monWd提取wd
        const fieldKey = key.replace(/^mon/i, '').toLowerCase();
        const indicator = fieldMappings[fieldKey];
        
        // 如果找到匹配的指标，并且值不为null
        if (indicator && record[key] !== null && record[key] !== undefined) {
          hasAnyIndicator = true;
          // 特殊处理井盖状态和水浸状态的值
          let displayValue = record[key];
          
          // 井盖状态(0正常;1打开)
          if (key.toLowerCase() === 'monjgzt') {
            displayValue = record[key] === 0 ? '正常' : '打开';
          }
          // 水浸状态(0正常;1水浸)
          else if (key.toLowerCase() === 'monsjzt') {
            displayValue = record[key] === 0 ? '正常' : '水浸';
          }
          // 其他指标添加单位
          else {
            displayValue = record[key] + (indicator.measureUnit ? ' ' + indicator.measureUnit : '');
          }
          
          processed.push({
            ...record,
            monitorIndex: indicator.monitorIndexName, // 监测指标名称
            monitorValue: displayValue, // 转换后的监测值
            monitorStatus: record.monitorStatus, // 状态
            _originalRecord: record, // 保存原始记录以便需要时使用
            _matchedField: key // 用于调试，记录匹配到的字段名
          });
        } else if (record[key] !== null && record[key] !== undefined) {
          // 如果没有找到匹配的指标，但字段有值，也显示出来
          hasAnyIndicator = true;
          
          // 提取字段名（去掉mon前缀）
          const fieldName = key.replace(/^mon/i, '');
          const lowerFieldName = fieldName.toLowerCase();
          
          // 尝试从commonIndicators中查找对应的指标名称
          let displayName = fieldName;
          
          // 检查是否有对应的常见指标名称
          if (commonIndicators[lowerFieldName]) {
            displayName = commonIndicators[lowerFieldName];
          }
          
          // 特殊处理井盖状态和水浸状态的值
          let displayValue = record[key];
          
          // 井盖状态(0正常;1打开)
          if (key.toLowerCase() === 'monjgzt') {
            displayValue = record[key] === 0 ? '正常' : '打开';
          }
          // 水浸状态(0正常;1水浸)
          else if (key.toLowerCase() === 'monsjzt') {
            displayValue = record[key] === 0 ? '正常' : '水浸';
          }
          
          processed.push({
            ...record,
            monitorIndex: displayName, // 使用映射的指标名或字段名
            monitorValue: displayValue, // 转换后的监测值
            monitorStatus: record.monitorStatus, // 状态
            _originalRecord: record, // 保存原始记录以便需要时使用
            _matchedField: key // 用于调试，记录匹配到的字段名
          });
        }
      }
    });
    
    // 如果没有找到任何指标，添加一个默认行
    if (!hasAnyIndicator) {
      processed.push({
        ...record,
        monitorIndex: '未知指标',
        monitorValue: '-',
        monitorStatus: record.monitorStatus,
        _originalRecord: record
      });
    }
    
    // 调试日志，查看处理后的数据
    if (processed.length > 0) {
      console.log('处理后的历史数据示例:', processed[0]);
    }
  });
  
  // 按时间和指标名称排序
  processed.sort((a, b) => {
    // 首先按时间降序排序
    const timeA = a.monitorTime ? new Date(a.monitorTime).getTime() : 0;
    const timeB = b.monitorTime ? new Date(b.monitorTime).getTime() : 0;
    if (timeB !== timeA) {
      return timeB - timeA; // 降序
    }
    // 时间相同则按指标名称排序
    return a.monitorIndex.localeCompare(b.monitorIndex);
  });
  
  processedHistoryData.value = processed;
};


// 获取离线记录
const fetchOfflineData = async () => {
  if (!props.deviceData || !props.deviceData.id) return;
  
  try {
    const [startDate, endDate] = dateRange.value;
    
    const params = {
      deviceId: props.deviceData.id,
      startTime: startDate + ' 00:00:00',
      endTime: endDate + ' 23:59:59',
      type: 0 // 使用默认类型
    };
    
    const res = await getOfflineRecordsData(
      offlinePagination.currentPage,
      offlinePagination.pageSize,
      params
    );
    
    if (res && res.code === 200) {
      offlineData.value = res.data.records || [];
      offlinePagination.total = res.data.total || 0;
    }
  } catch (error) {
    console.error('获取离线记录失败', error);
  }
};

// 历史数据分页大小变化
const handleHistorySizeChange = (val) => {
  historyPagination.pageSize = val;
  historyPagination.currentPage = 1;
  fetchHistoryData();
};

// 历史数据页码变化
const handleHistoryCurrentChange = (val) => {
  historyPagination.currentPage = val;
  fetchHistoryData();
};

// 离线记录分页大小变化
const handleOfflineSizeChange = (val) => {
  offlinePagination.pageSize = val;
  offlinePagination.currentPage = 1;
  fetchOfflineData();
};

// 离线记录页码变化
const handleOfflineCurrentChange = (val) => {
  offlinePagination.currentPage = val;
  fetchOfflineData();
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
};

// 监听设备数据变化
watch(() => props.deviceData, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    if (activeTab.value === 'historyData') {
      fetchHistoryData();
    } else {
      fetchOfflineData();
    }
  }
}, { deep: true });

// 监听对话框可见性变化
watch(() => dialogVisible.value, (val) => {
  if (val && props.deviceData && props.deviceData.id) {
    if (activeTab.value === 'historyData') {
      fetchHistoryData();
    } else {
      fetchOfflineData();
    }
  }
});

// 组件挂载完成
onMounted(() => {
  if (dialogVisible.value && props.deviceData && props.deviceData.id) {
    fetchMonitorIndicators().then(() => {
      fetchHistoryData();
    });
  }
});
</script>

<style scoped>
.operation-record-dialog {
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "Helvetica Neue", Arial, sans-serif;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

.record-container {
  width: 100%;
}

.date-range-container {
  margin-bottom: 16px;
  display: flex;
  justify-content: flex-end;
}

:deep(.el-tabs__header) {
  margin-bottom: 16px;
}

:deep(.el-tabs__nav-wrap::after) {
  height: 1px;
  background-color: #E4E7ED;
}

:deep(.el-tabs__active-bar) {
  height: 2px;
  background-color: #0277FD;
}

:deep(.el-tabs__item) {
  height: 40px;
  line-height: 40px;
  font-size: 14px;
  color: #606266;
}

:deep(.el-tabs__item.is-active) {
  color: #0277FD;
  font-weight: 500;
}

:deep(.el-tabs__item:hover) {
  color: #0277FD;
}

:deep(.el-table) {
  --el-table-border-color: #EBEEF5;
  --el-table-header-bg-color: #F5F8FA;
}

:deep(.el-table .even-row) {
  background-color: #FFFFFF;
}

:deep(.el-table .odd-row) {
  background-color: #F5F8FA;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
}

:deep(.el-pagination .el-pagination__total) {
  margin-right: 16px;
}

:deep(.el-pagination .el-input__wrapper) {
  box-shadow: 0 0 0 1px #CED3DA inset;
}

:deep(.el-pagination .el-select .el-input) {
  margin: 0 8px;
}

:deep(.el-pagination button:disabled) {
  background-color: #F5F8FA;
}

:deep(.el-pagination .el-pagination__jump) {
  margin-left: 16px;
}
</style>