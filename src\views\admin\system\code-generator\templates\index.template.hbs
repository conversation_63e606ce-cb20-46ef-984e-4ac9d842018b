<template>
  <div class="{{ moduleName }}-container">
    <div class="page-header">
      <h2 class="page-title">{{ pageTitle }}</h2>
      <div class="page-description">{{ description }}</div>
    </div>

    <!-- 搜索区域 -->
    {{#if hasSearch}}
    <SearchForm @search="handleSearch" @reset="handleReset" />
    {{/if}}

    <!-- 表格区域 -->
    <div class="table-container">
      <div class="table-header">
        {{#if api.methods.create}}
        <el-button type="primary" @click="handleAdd">新增</el-button>
        {{/if}}
        {{#if hasCustomButtons}}
        {{#each customButtons}}
        <el-button type="{{ type }}" @click="{{ handler }}">{{ label }}</el-button>
        {{/each}}
        {{/if}}
      </div>

      <el-table
        :data="tableData"
        v-loading="loading"
        style="width: 100%"
        :header-cell-style="headerCellStyle"
        :row-class-name="tableRowClassName"
        @row-click="handleRowClick"
        height="calc(100vh - 380px)"
      >
        <el-table-column label="序号" min-width="60">
          <template #default="{ $index }">
            {{ calculate_index currentPage pageSize $index }}
          </template>
        </el-table-column>

        {{#each tableColumns}}
        <el-table-column
          label="{{ label }}"
          prop="{{ prop }}"
          {{#if width}}min-width="{{ width }}"{{/if}}
          {{#if align}}align="{{ align }}"{{/if}}
          {{#if sortable}}sortable{{/if}}
          {{#if fixed}}fixed="{{ fixed }}"{{/if}}
        />
        {{/each}}

        <!-- 操作列 -->
        <el-table-column label="操作" min-width="150" fixed="right">
          <template #default="{ row }">
            {{#if api.methods.detail}}
            <span class="operation-btn-text" @click.stop="handleView(row)">查看</span>
            {{/if}}
            {{#if api.methods.update}}
            <span class="operation-btn-text" @click.stop="handleEdit(row)">编辑</span>
            {{/if}}
            {{#if api.methods.delete}}
            <span class="operation-btn-text" @click.stop="handleDelete(row)">删除</span>
            {{/if}}
            {{#if hasCustomOperations}}
            {{#each customOperations}}
            <span class="operation-btn-text" @click.stop="{{ handler }}(row)">{{ label }}</span>
            {{/each}}
            {{/if}}
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 表单弹窗 -->
    {{#if hasForm}}
    <EditForm
      v-model:visible="formVisible"
      :form-data="formData"
      :is-edit="isEdit"
      @success="handleFormSuccess"
    />
    {{/if}}
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
{{#if hasSearch}}
import SearchForm from './components/SearchForm.vue'
{{/if}}
{{#if hasForm}}
import EditForm from './components/EditForm.vue'
{{/if}}
import {
  {{#if api.methods.list}}getList, {{/if}}
  {{#if api.methods.detail}}getDetail, {{/if}}
  {{#if api.methods.create}}create, {{/if}}
  {{#if api.methods.update}}update, {{/if}}
  {{#if api.methods.delete}}remove{{/if}}
  {{#if hasCustomMethods}}
  {{#if (or api.methods.list api.methods.detail api.methods.create api.methods.update api.methods.delete)}},{{/if}}
  {{#each customMethods}}
  {{name}}{{#unless @last}},{{/unless}}
  {{/each}}
  {{/if}}
} from '@/api/{{ moduleName }}'

// 表格数据
const tableData = ref([])
const loading = ref(false)

// 分页参数
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 查询参数
const queryParams = ref({})

// 表单相关
const formVisible = ref(false)
const formData = ref({})
const isEdit = ref(false)

// 表格样式
const headerCellStyle = {
  background: '#f5f7fa',
  color: '#606266',
  fontWeight: 'bold'
}

// 表格行样式
const tableRowClassName = ({ rowIndex }) => {
  return rowIndex % 2 === 0 ? 'light-row' : 'dark-row'
}

// 获取数据列表
const getDataList = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      limit: pageSize.value,
      ...queryParams.value
    }
    const res = await getList(params)
    tableData.value = res.data.list || []
    total.value = res.data.total || 0
  } catch (error) {
    console.error('获取数据失败', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 处理搜索
const handleSearch = (params) => {
  queryParams.value = params
  currentPage.value = 1
  getDataList()
}

// 处理重置
const handleReset = () => {
  queryParams.value = {}
  currentPage.value = 1
  getDataList()
}

// 处理页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page
  getDataList()
}

// 处理每页条数变化
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  getDataList()
}

// 处理行点击
const handleRowClick = (row) => {
  console.log('行点击', row)
}

// 处理查看
const handleView = async (row) => {
  try {
    const res = await getDetail(row.id)
    console.log('查看详情', res.data)
    // 这里可以实现查看详情的逻辑
  } catch (error) {
    console.error('获取详情失败', error)
    ElMessage.error('获取详情失败')
  }
}

// 处理新增
const handleAdd = () => {
  formData.value = {}
  isEdit.value = false
  formVisible.value = true
}

// 处理编辑
const handleEdit = async (row) => {
  try {
    const res = await getDetail(row.id)
    formData.value = res.data
    isEdit.value = true
    formVisible.value = true
  } catch (error) {
    console.error('获取详情失败', error)
    ElMessage.error('获取详情失败')
  }
}

// 处理删除
const handleDelete = (row) => {
  ElMessageBox.confirm('确认删除该记录吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await remove(row.id)
      ElMessage.success('删除成功')
      getDataList()
    } catch (error) {
      console.error('删除失败', error)
      ElMessage.error('删除失败')
    }
  }).catch(() => {
    // 取消删除
  })
}

// 处理表单提交成功
const handleFormSuccess = () => {
  formVisible.value = false
  getDataList()
}

// 组件挂载时获取数据
onMounted(() => {
  getDataList()
})
</script>

<style scoped>
.{{ moduleName }}-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-title {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 8px;
}

.page-description {
  color: #666;
  font-size: 14px;
}

.table-container {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.table-header {
  margin-bottom: 16px;
  display: flex;
  justify-content: flex-start;
  gap: 10px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

:deep(.light-row) {
  background-color: #ffffff;
}

:deep(.dark-row) {
  background-color: #f5f7fa;
}

.operation-btn-text {
  color: #409eff;
  cursor: pointer;
  margin-right: 10px;
}

.operation-btn-text:hover {
  color: #66b1ff;
}
</style>
