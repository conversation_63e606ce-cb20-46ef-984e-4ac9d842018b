<template>
  <div class="component-tools">
    <div class="tool-left">
      <div class="tool-left-top">
          <el-tooltip
             v-for="item in leftTopTools"
             :key="item.id"
             :content="item.label"
             placement="left"
             popper-class="gis-tool-tooltip"
          >
              <div
                 class="tool-item-btn"
                 :class="{ active: item.id === leftActiveId }"
                 @click="handleLeftSelectTool(item)"
              >
                <img :src="item.icon" alt="" />
              </div>
          </el-tooltip>
          <GasModel v-if="showGasModel"/>
      </div>
      <Legend v-if="showLegend" />
    </div>
    <div class="tool-right">
      <div class="tool-right-top">
        <div class="tool-item" v-for="item in topTools" :key="item.id">
          <el-popover
            placement="left-start"
            :width="'auto'"
            :show-arrow="false"
            popper-class="gis-tool-popover"
            :visible="item.id === topActiveId"
            v-if="!!item.component"
          >
            <template #reference>
              <div>
                <el-tooltip
                  :content="item.label"
                  placement="left"
                  popper-class="gis-tool-tooltip"
                >
                  <div
                    class="tool-item-btn"
                    :class="{ active: item.id === topActiveId }"
                    @click="handleTopSelectTool(item)"
                  >
                    <img :src="item.icon" alt="" />
                  </div>
                </el-tooltip>
              </div>
            </template>
            <component :is="item.component" />
          </el-popover>
          <el-tooltip
            v-else
            :content="item.label"
            placement="left"
            popper-class="gis-tool-tooltip"
          >
            <div
              class="tool-item-btn"
              :class="{active: item.id === topActiveId}"
              @click="handleTopSelectTool(item)"
            >
              <img :src="item.icon" alt="" />
            </div>
          </el-tooltip>
        </div>
      </div>
      <div class="tool-right-bottom">
        <el-tooltip
          v-for="item in bottomTools"
          :key="item.id"
          :content="item.label"
          placement="left"
          popper-class="gis-tool-tooltip"
        >
          <div
            class="tool-item-btn"
            :class="{
              active: item.id === '7' ? showLegend : item.id === bottomActiveId,
            }"
            @click="handleBottomSelectTool(item)"
          >
            <img :src="item.icon" alt="" />
          </div>
        </el-tooltip>
      </div>
    </div>
  </div>
</template>

<script setup>
import {computed, ref, watch} from "vue";
import GasModel from "./components/GasModel/index.vue";
import Legend from "./components/Legend/index.vue";
import LayerTree from "./components/LayerTree/index.vue";
import Search from "./components/Search/index.vue";
import EyesEarth from "./components/EyesEarth/index.vue";
import { mapStates } from "@/components/GisMap/mapStates";
import bus from "@/utils/mitt";
import { useRoute } from "vue-router";
const route = useRoute();
const leftActiveId = ref("");
const topActiveId = ref("");
const bottomActiveId = ref("");
const showLegend = ref(false);
const showPane = ref(true);
const showGasModel = ref(false);

watch(
  () => route,
  (val) => {
    leftActiveId.value = "";
    topActiveId.value = "";
    bottomActiveId.value = "";
    showLegend.value = false;
    showPane.value = true;
    showGasModel.value = false;
    if (route.path ==="/gas/decision-screen") {
      showGasModel.value = true; // 显示燃气模型
    }
  },
  {
    deep: true,
  }
);

const leftTopTools = ref([
    {
        id: "8",
        icon: new URL(
            `./svg/fullScreen.svg`,
            import.meta.url
        ).href,
        label: "全屏",
    }
]);

const topTools = computed(() => {
  return [
    {
      id: "1",
      icon: new URL(
          `./svg/search.svg`,
          import.meta.url
      ).href,
      label: "搜索",
      component: Search,
    },
    {
      id: "2",
      icon: new URL(
          `./svg/layerTree.svg`,
          import.meta.url
      ).href,
      label: "图层",
      component: LayerTree,
    },
    {
      id: "3",
      icon: new URL(
           `./svg/eyesEarth.svg`,
           import.meta.url
      ).href,
      label: "透视地图",
      component: EyesEarth,
    },
   /* {
      id: "3",
      // icon: showPane.value ? "show" : "hide",
      icon: new URL(
          `./svg/show.svg`,
          import.meta.url
      ).href,
      label: showPane.value ? "收起" : "展开",
    },*/
  ];
});
const bottomTools = ref([
  {
    id: "4",
    icon: new URL(
        `./svg/zoomOut.svg`,
        import.meta.url
    ).href,
    label: "放大",
  },
  {
    id: "5",
    icon: new URL(
        `./svg/zoomIn.svg`,
        import.meta.url
    ).href,
    label: "缩小",
  },
  {
    id: "6",
    icon: new URL(
        `./svg/reset.svg`,
        import.meta.url
    ).href,
    label: "复位",
  },
  {
    id: "7",
    icon: new URL(
        `./svg/legend.svg`,
        import.meta.url
    ).href,
    label: "图例",
  },
]);

const resetPostion = () => {
    mapStates.earth.camera.flyTo({
        lon: 115.097,
        lat: 35.288,
        height: 8000,
        orientation: {
            heading: 0,
            pitch: -90, //-45
            roll: 0,
        },
    });
};

const handleLeftSelectTool = (tool) => {
    if (tool.id === leftActiveId.value){
        leftActiveId.value = "";
        document.exitFullscreen();
    } else {
        leftActiveId.value = tool.id;
        document.documentElement.requestFullscreen();
    }
  /*if (tool.id === "8") {
      leftActiveId.value = tool.id;
      if (document.fullscreenElement) {
          document.exitFullscreen();
      } else {
          document.documentElement.requestFullscreen();
      }
  }*/
};
const handleTopSelectTool = (tool) => {
    if (tool.id === topActiveId.value){
        topActiveId.value = "";
        mapStates.viewer.scene.globe.translucency.enabled = false;
        mapStates.viewer.scene.globe.translucency.frontFaceAlpha = 1.0;
        mapStates.viewer.scene.globe.translucency.backFaceAlpha = 1.0;
        const osgbLayers = mapStates.viewer.scene.layers._layers.filter(layer => ["倾斜摄影","桥梁"].includes(layer.name));
        osgbLayers.forEach(layer => {
            layer.transparent = 100;
        })
    } else {
        topActiveId.value = tool.id;
        if (tool.id === "3") {
            bus.emit("resetEyesEarth")
        } else {
            mapStates.viewer.scene.globe.translucency.enabled = false;
            mapStates.viewer.scene.globe.translucency.frontFaceAlpha = 1.0;
            mapStates.viewer.scene.globe.translucency.backFaceAlpha = 1.0;
            const osgbLayers = mapStates.viewer.scene.layers._layers.filter(layer => ["倾斜摄影","桥梁"].includes(layer.name));
            osgbLayers.forEach(layer => {
                layer.transparent = 100;
            })
        }
    }
  /*  switch (tool.id) {
        // 收起
        case "3":
            showPane.value = !showPane.value;
            bus.emit("togglePane", showPane.value);
            break;
    }*/
};
const handleBottomSelectTool = (tool) => {
  if (tool.id === "7") {
      bottomActiveId.value = tool.id;
  }
  switch (tool.id) {
    // 放大
    case "4":
        mapStates.earth.camera.onMapZoomIn();
      break;
    // 缩小
    case "5":
        mapStates.earth.camera.onMapZoomOut();
      break;
    // 复位
    case "6":
        resetPostion();
        bus.emit("resetGisPopup");
      break;
    // 图例
    case "7":
      showLegend.value = !showLegend.value;
      break;
  }
};
</script>

<style lang="scss" scoped>
.component-tools {
  position: absolute;
  top: 154px;
  bottom: 60px;
  left: 530px;
  right: 530px;
  display: flex;
  justify-content: space-between;
  z-index: 999;
  cursor: pointer;
  pointer-events: none;
  .tool-right,
  .tool-center,
  .tool-left {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    pointer-events: auto;
  }
  .tool-item-btn {
    pointer-events: all;
    width: 32px;
    height: 32px;
    background: rgba(13, 37, 82, 0.8);
    border: 1px solid #182d54;
    border-radius: 4px;
    margin-bottom: 8px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;

    img {
      width: 32px;
      height: 32px;
    }
    &.active {
      background: rgba(26, 142, 231, 0.8);
      border: 1px solid rgba(26, 142, 231, 1);
    }
  }
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  &::-webkit-scrollbar-thumb {
    background: #263e58; /* 滑块颜色 */
  }
  &::-webkit-scrollbar-track {
    background: #162744; /* 轨道颜色 */
  }
}
</style>

<style lang="scss">
.el-popover.el-popper.gis-tool-popover {
  background: transparent;
  border: none;
  padding: 0;
  border-radius: 0;
  z-index: 998 !important;
}
</style>
