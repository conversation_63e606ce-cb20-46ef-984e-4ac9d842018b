<template>
  <div class="multi-rtsp-player">
    <div class="video-container">
      <canvas ref="videoCanvas" :width="width" :height="height"></canvas>
      <!-- 状态指示器 - 只在播放中时显示，位置在右上角 -->
      <div v-if="isPlaying" class="status-indicator playing">
        ● 播放中
      </div>
      <!-- 错误信息显示在中间 -->
      <div v-if="statusMessage && isError" class="status-overlay error">
        {{ statusMessage }}
      </div>
    </div>
    <div v-if="showControls" class="controls">
      <button @click="handlePlay" :disabled="isPlaying">播放</button>
      <button @click="handleStop" :disabled="!isPlaying">停止</button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue';

const props = defineProps({
  // 视频ID，对应RTSP流标识
  videoId: {
    type: String,
    required: true
  },
  // WebSocket服务器地址 - 新版本使用jsmpeg2端点
  wsUrl: {
    type: String,
    default: 'ws://**************:32021/basic/jsmpeg2'  // 专注jsmpeg2服务
  },
  // RTSP流地址（如果需要的话）
  rtspUrl: {
    type: String,
    default: ''
  },
  // 播放器尺寸
  width: {
    type: Number,
    default: 320
  },
  height: {
    type: Number,
    default: 180
  },
  // 是否显示控制按钮
  showControls: {
    type: Boolean,
    default: false
  },
  // 是否自动播放
  autoplay: {
    type: Boolean,
    default: true
  }
});

const emit = defineEmits(['error', 'play', 'stop']);

// 引用和状态
const videoCanvas = ref(null);
const player = ref(null);
const isPlaying = ref(false);
const statusMessage = ref('');
const isError = ref(false);
const jsMpegLoaded = ref(false);
const websocket = ref(null);
const shouldAutoplay = ref(false);

// 添加数据缓冲相关状态
const dataBuffer = ref(new Uint8Array(0)); // 数据缓冲区
const lastWriteTime = ref(0); // 上次写入时间
const writeInterval = 50; // 写入间隔（毫秒）
const maxBufferSize = 1024 * 1024; // 最大缓冲区大小 1MB
const minWriteSize = 1024; // 最小写入数据大小

// 页面可见性状态
const isPageVisible = ref(true);

// 定时写入机制
const writeTimer = ref(null);

// 🔥 新增：数据流缓冲和管理
const videoDataBuffer = ref(new Uint8Array(0)); // 视频数据缓冲区
const lastFlushTime = ref(0); // 上次刷新时间
const flushInterval = 100; // 刷新间隔(ms)
const maxBufferSize2 = 512 * 1024; // 最大缓冲区 512KB

// 启动定时写入
const startBufferWriter = () => {
  if (writeTimer.value) {
    clearInterval(writeTimer.value);
  }
  
  writeTimer.value = setInterval(() => {
    if (isPlaying.value && dataBuffer.value.length > 0) {
      writeBufferedDataToPlayer();
    }
  }, writeInterval);
  
  console.log('定时写入机制已启动');
};

// 停止定时写入
const stopBufferWriter = () => {
  if (writeTimer.value) {
    clearInterval(writeTimer.value);
    writeTimer.value = null;
  }
  console.log('定时写入机制已停止');
};

// 清理缓冲区
const clearBuffer = () => {
  dataBuffer.value = new Uint8Array(0);
  lastWriteTime.value = 0;
  console.log('缓冲区已清空');
};

// 监听页面可见性变化
const handleVisibilityChange = () => {
  isPageVisible.value = !document.hidden;
  
  if (document.hidden) {
    // 页面不可见时，断开连接
    console.log('页面不可见，断开WebSocket连接');
    if (isPlaying.value) {
      shouldAutoplay.value = true;
    }
    handleStop();
  } else {
    // 页面重新可见时，如果之前在播放则重新连接
    console.log('页面重新可见');
    if (shouldAutoplay.value || props.autoplay) {
      setTimeout(() => {
        console.log('恢复视频播放');
        handlePlay();
      }, 1000);
    }
  }
};

// 加载JSMpeg库
const loadJSMpeg = () => {
  return new Promise((resolve, reject) => {
    // 首先检查是否已经加载
    if (window.JSMpeg) {
      jsMpegLoaded.value = true;
      console.log('JSMpeg已存在');
      resolve();
      return;
    }

    console.log('开始加载JSMpeg库...');
    const script = document.createElement('script');
    script.src = 'https://cdn.jsdelivr.net/gh/phoboslab/jsmpeg@master/jsmpeg.min.js';
    
    script.onload = () => {
      console.log('JSMpeg脚本加载完成');
      
      // 等待一下确保库完全初始化
      setTimeout(() => {
        if (window.JSMpeg) {
          jsMpegLoaded.value = true;
          console.log('JSMpeg库加载成功');
          resolve();
        } else {
          console.error('JSMpeg库加载后不可用');
          reject(new Error('JSMpeg库加载后不可用'));
        }
      }, 500);
    };
    
    script.onerror = (error) => {
      console.error('JSMpeg脚本加载失败:', error);
      reject(new Error('JSMpeg脚本加载失败'));
    };
    
    document.head.appendChild(script);
  });
};

// 更新状态
const updateStatus = (message, error = false) => {
  statusMessage.value = message;
  isError.value = error;
  if (error) {
    console.error(message);
  } else {
    console.log(message);
  }
};

// 将二进制数据转换为字符串
const binaryToString = (buffer) => {
  try {
    // 使用TextDecoder进行转换，更可靠
    const decoder = new TextDecoder('utf-8');
    return decoder.decode(buffer);
  } catch (error) {
    // 如果TextDecoder失败，回退到传统方法
    console.warn('TextDecoder失败，使用传统方法:', error);
    const uint8Array = new Uint8Array(buffer);
    let str = '';
    for (let i = 0; i < uint8Array.length; i++) {
      str += String.fromCharCode(uint8Array[i]);
    }
    return str;
  }
};

// 数据缓冲管理函数
const appendToBuffer = (newData) => {
  if (!newData || newData.length === 0) return;
  
  // 检查缓冲区大小，防止内存溢出
  if (dataBuffer.value.length + newData.length > maxBufferSize) {
    console.warn('数据缓冲区即将溢出，清空旧数据');
    dataBuffer.value = new Uint8Array(0);
  }
  
  // 合并数据
  const combined = new Uint8Array(dataBuffer.value.length + newData.length);
  combined.set(dataBuffer.value);
  combined.set(newData, dataBuffer.value.length);
  dataBuffer.value = combined;
  
  console.log('数据已添加到缓冲区，当前缓冲区大小:', dataBuffer.value.length);
};

// 受控的数据写入函数
const writeBufferedDataToPlayer = () => {
  const now = Date.now();
  
  // 检查写入间隔和数据大小
  if (now - lastWriteTime.value < writeInterval) {
    console.log('写入间隔未到，跳过本次写入');
    return;
  }
  
  if (dataBuffer.value.length < minWriteSize) {
    console.log('缓冲区数据不足最小写入大小，等待更多数据');
    return;
  }
  
  if (!player.value || !player.value.write) {
    console.warn('播放器未准备好，无法写入数据');
    return;
  }
  
  try {
    // 检查数据是否以MPEG同步字节开头
    const startsWithSync = dataBuffer.value[0] === 0x47;
    
    if (startsWithSync) {
      // 尝试找到完整的MPEG-TS包（188字节）
      let writeSize = 0;
      const packetSize = 188;
      
      // 计算完整包的数量
      const completePackets = Math.floor(dataBuffer.value.length / packetSize);
      
      if (completePackets > 0) {
        writeSize = completePackets * packetSize;
        const dataToWrite = dataBuffer.value.slice(0, writeSize);
        
        console.log('写入完整MPEG-TS包到播放器:', {
          packetsCount: completePackets,
          writeSize: writeSize,
          remainingBuffer: dataBuffer.value.length - writeSize
        });
        
        // 安全写入数据
        player.value.write(dataToWrite);
        lastWriteTime.value = now;
        
        // 移除已写入的数据
        dataBuffer.value = dataBuffer.value.slice(writeSize);
        
        if (!isPlaying.value) {
          isPlaying.value = true;
          emit('play');
        }
      } else {
        console.log('缓冲区中没有完整的MPEG-TS包，等待更多数据');
      }
    } else {
      // 如果不是标准MPEG格式，直接写入但限制大小
      const writeSize = Math.min(dataBuffer.value.length, 4096); // 限制单次写入4KB
      const dataToWrite = dataBuffer.value.slice(0, writeSize);
      
      console.log('写入非MPEG格式数据到播放器:', {
        writeSize: writeSize,
        remainingBuffer: dataBuffer.value.length - writeSize
      });
      
      player.value.write(dataToWrite);
      lastWriteTime.value = now;
      
      // 移除已写入的数据
      dataBuffer.value = dataBuffer.value.slice(writeSize);
      
      if (!isPlaying.value) {
        isPlaying.value = true;
        emit('play');
      }
    }
    
  } catch (writeError) {
    console.error('写入数据到播放器失败:', writeError);
    
    // 如果写入失败，清空缓冲区防止数据堆积
    dataBuffer.value = new Uint8Array(0);
    
    // 尝试重新创建播放器
    if (writeError.message.includes('memory') || writeError.message.includes('bounds')) {
      console.log('检测到内存错误，尝试重新创建播放器');
      setTimeout(async () => {
        await recreatePlayer();
      }, 1000);
    }
  }
};

// 重新创建播放器
const recreatePlayer = async () => {
  try {
    console.log('重新创建播放器...');
    
    // 销毁旧播放器
    if (player.value) {
      try {
        player.value.destroy();
      } catch (e) {
        console.warn('销毁旧播放器时出错:', e);
      }
      player.value = null;
    }
    
    // 清空缓冲区
    dataBuffer.value = new Uint8Array(0);
    lastWriteTime.value = 0;
    
    // 重新创建播放器
    await createJSMpegPlayer();
    
    console.log('播放器重新创建完成');
    
  } catch (error) {
    console.error('重新创建播放器失败:', error);
    updateStatus('播放器重建失败', true);
  }
};

// 播放视频 - 重构版本，严格按照新逻辑
const handlePlay = async () => {
  // 如果页面不可见，不开始播放
  if (!isPageVisible.value) {
    console.log('页面不可见，暂停播放');
    return;
  }

  if (!jsMpegLoaded.value) {
    updateStatus('正在加载 JSMpeg...');
    try {
      await loadJSMpeg();
    } catch (error) {
      updateStatus('JSMpeg 加载失败', true);
      emit('error', error);
      return;
    }
  }

  if (!videoCanvas.value) {
    updateStatus('视频画布未准备好', true);
    return;
  }

  try {
    // 如果已有播放器实例，先销毁
    if (player.value) {
      player.value.destroy();
      player.value = null;
    }

    // 如果已有WebSocket连接，先关闭
    if (websocket.value) {
      websocket.value.close();
      websocket.value = null;
    }

    updateStatus('正在连接视频流...');

    // 建立WebSocket连接
    const ws = new WebSocket(props.wsUrl);
    ws.binaryType = 'arraybuffer';
    websocket.value = ws;

    let isPlayerCreated = false;

    ws.onopen = () => {
      console.log('WebSocket连接成功，发送播放请求');
      updateStatus('连接成功，请求视频流...');
      
      // 清空缓冲区
      clearBuffer();
      
      // 启动定时写入机制
      startBufferWriter();
      
      // 发送JSON播放请求 - 与SimpleRtspPlayer保持一致
      const playRequest = {
        type: 110,
        data: props.videoId
      };
      console.log('发送播放请求:', playRequest);
      ws.send(JSON.stringify(playRequest));
    };

    ws.onmessage = async (event) => {
      try {
        // 如果页面不可见，不处理数据
        if (!isPageVisible.value) {
          return;
        }

        console.log('收到WebSocket消息，数据类型:', event.data.constructor.name);
        
        // 严格按照新逻辑：只处理Blob数据
        if (event.data instanceof Blob) {
          console.log(`收到Blob数据，大小: ${event.data.size} 字节`);
          
          // 步骤1：将Blob转换为字符串
          const arrayBuffer = await event.data.arrayBuffer();
          await processVideoData(arrayBuffer);
          
        } else if (typeof event.data === 'string') {
          // 处理字符串消息（连接状态等）
          console.log('收到字符串消息:', event.data);
          try {
            const response = JSON.parse(event.data);
            console.log('解析字符串JSON响应:', response);
          } catch (e) {
            console.log('普通文本消息:', event.data);
          }
        } else if (event.data instanceof ArrayBuffer) {
          // 如果直接收到ArrayBuffer
          console.log('收到ArrayBuffer数据，直接处理');
          await processVideoData(event.data);
        } else {
          console.warn('未知的数据类型:', typeof event.data);
        }
        
      } catch (error) {
        console.error('处理WebSocket消息错误:', error);
        updateStatus('数据处理错误: ' + error.message, true);
      }
    };

    // 🔥 新增：统一的视频数据处理函数
    const processVideoData = async (arrayBuffer) => {
      try {
        const uint8Array = new Uint8Array(arrayBuffer);
        console.log('🔍 [jsmpeg2服务] 处理视频数据:', {
          大小: arrayBuffer.byteLength,
          前10字节: Array.from(uint8Array.slice(0, 10)),
          前10字节HEX: Array.from(uint8Array.slice(0, 10)).map(b => `0x${b.toString(16).padStart(2, '0')}`).join(' ')
        });
        
        // 检查是否是JSON数据（以 { 开头）
        const startsWithJSON = uint8Array[0] === 123; // 123 = '{'
        
        if (startsWithJSON) {
          console.log('🔍 [jsmpeg2服务] 检测到JSON格式数据，提取视频流');
          
          // 🔥 简化JSON解析 - 直接字符串操作提取Base64数据
          const stringData = binaryToString(arrayBuffer);
          
          // 使用字符串操作直接提取array字段的值
          const arrayStartMarker = '"array":"';
          const arrayStartIndex = stringData.indexOf(arrayStartMarker);
          
          if (arrayStartIndex !== -1) {
            const dataStartIndex = arrayStartIndex + arrayStartMarker.length;
            const dataEndIndex = stringData.indexOf('"', dataStartIndex);
            
            if (dataEndIndex !== -1) {
              const base64Data = stringData.substring(dataStartIndex, dataEndIndex);
              console.log('🔍 [jsmpeg2服务] 直接提取Base64数据:', {
                长度: base64Data.length,
                前50字符: base64Data.substring(0, 50),
                是否纯Base64: /^[A-Za-z0-9+/]*={0,2}$/.test(base64Data)
              });
              
              // 转换为二进制并直接写入播放器
              const binaryData = convertVideoStreamToBinary(base64Data);
              if (binaryData) {
                console.log('🔍 [jsmpeg2服务] Base64转换成功:', {
                  原始Base64长度: base64Data.length,
                  转换后二进制长度: binaryData.length,
                  前20字节: Array.from(binaryData.slice(0, 20)),
                  前20字节HEX: Array.from(binaryData.slice(0, 20)).map(b => `0x${b.toString(16).padStart(2, '0')}`).join(' '),
                  是否以MPEG同步字节开头: binaryData[0] === 0x47,
                  数据类型分析: analyzeBinaryData(binaryData)
                });
                await writeToPlayer(binaryData);
              } else {
                console.error('🔍 [jsmpeg2服务] Base64转换失败');
              }
            } else {
              console.warn('🔍 [jsmpeg2服务] 未找到Base64数据结束位置');
            }
          } else {
            console.warn('🔍 [jsmpeg2服务] 未找到array字段');
          }
          
        } else {
          // 直接的二进制视频流
          console.log('🔍 [jsmpeg2服务] 直接的二进制视频流，size:', arrayBuffer.byteLength);
          await writeToPlayer(uint8Array);
        }
        
      } catch (error) {
        console.error('🔍 [jsmpeg2服务] 处理视频数据失败:', error);
      }
    };

    // 🔥 新增：分析二进制数据类型
    const analyzeBinaryData = (data) => {
      if (!data || data.length === 0) return '空数据';
      
      const firstByte = data[0];
      const analysis = {
        firstByte: `0x${firstByte.toString(16).padStart(2, '0')}`,
        type: '未知'
      };
      
      if (firstByte === 0x47) {
        analysis.type = 'MPEG-TS包';
      } else if (firstByte === 0x00 && data.length > 3) {
        if (data[1] === 0x00 && data[2] === 0x00 && data[3] === 0x01) {
          analysis.type = 'H.264 NAL单元';
        } else if (data[1] === 0x00 && data[2] === 0x01) {
          analysis.type = 'MPEG视频流';
        } else {
          analysis.type = '可能是视频帧数据';
        }
      } else if (firstByte >= 0x01 && firstByte <= 0x09) {
        analysis.type = '可能是H.264 NAL';
      } else if (firstByte === 0xFF) {
        analysis.type = '填充数据或音频帧';
      } else {
        analysis.type = '压缩视频数据';
      }
      
      return analysis;
    };

    // 🔥 新增：统一的播放器写入函数
    const writeToPlayer = async (videoData) => {
      try {
        // 第一次收到数据时创建播放器
        if (!player.value) {
          console.log('🔍 [jsmpeg2服务] 首次收到数据，创建播放器');
          await createJSMpegPlayer();
          updateStatus('');
          // 启动缓冲区刷新机制
          startBufferFlushing();
        }
        
        const dataAnalysis = {
          大小: videoData.length,
          前10字节: Array.from(videoData.slice(0, 10)),
          前10字节HEX: Array.from(videoData.slice(0, 10)).map(b => `0x${b.toString(16).padStart(2, '0')}`).join(' '),
          是否以MPEG同步字节开头: videoData[0] === 0x47,
          数据类型: analyzeBinaryData(videoData)
        };
        
        console.log('🔍 [jsmpeg2服务] 收到视频数据，加入缓冲区:', dataAnalysis);
        
        // 🔥 关键修改：将数据加入缓冲区而不是直接写入
        appendToVideoBuffer(videoData);
        
      } catch (error) {
        console.error('🔍 [jsmpeg2服务] 处理视频数据失败:', error);
      }
    };

    ws.onerror = (error) => {
      updateStatus('WebSocket连接错误', true);
      emit('error', error);
      console.error('WebSocket错误:', error);
    };

    ws.onclose = (event) => {
      console.log('WebSocket连接关闭:', event);
      if (isPlaying.value) {
        isPlaying.value = false;
        emit('stop');
      }
      websocket.value = null;
    };

    // 创建JSMpeg播放器 - 使用与SimpleRtspPlayer.vue相同的逻辑
    const createJSMpegPlayer = async () => {
      try {
        console.log('创建JSMpeg播放器（使用SimpleRtspPlayer逻辑）...');
        
        // 清理旧的播放器
        if (player.value) {
          try {
            player.value.destroy();
          } catch (e) {
            console.warn('清理旧播放器时出错:', e);
          }
          player.value = null;
        }
        
        // 检查JSMpeg是否正确加载
        if (!window.JSMpeg) {
          throw new Error('JSMpeg库未正确加载');
        }
        
        console.log('JSMpeg库检查通过，使用SimpleRtspPlayer的创建方式');
        
        // 使用JSMpeg的VideoDecoder直接解码方式（与SimpleRtspPlayer相同）
        if (window.JSMpeg && window.JSMpeg.VideoDecoder) {
          console.log('使用VideoDecoder方式创建播放器');
          
          // 创建视频解码器
          const decoder = new JSMpeg.VideoDecoder();
          
          // 创建播放器，但不使用WebSocket source
          player.value = {
            canvas: videoCanvas.value,
            decoder: decoder,
            write: function(data) {
              try {
                console.log('VideoDecoder播放器接收数据:', data.length, '字节');
                // 直接解码数据到canvas
                const decoded = this.decoder.decode(data);
                if (decoded && this.canvas) {
                  const ctx = this.canvas.getContext('2d');
                  if (ctx && decoded.width && decoded.height) {
                    // 创建ImageData并绘制
                    const imageData = new ImageData(decoded.y, decoded.width, decoded.height);
                    ctx.putImageData(imageData, 0, 0);
                    console.log('解码并绘制成功，尺寸:', decoded.width, 'x', decoded.height);
                  }
                }
              } catch (decodeError) {
                console.warn('VideoDecoder解码错误:', decodeError);
              }
            },
            destroy: function() {
              if (this.decoder) {
                this.decoder = null;
              }
              this.canvas = null;
            }
          };
          
          console.log('VideoDecoder播放器创建成功');
          
        } else {
          console.log('VideoDecoder不可用，使用传统JSMpeg.Player方式');
          
          // 如果没有VideoDecoder，使用传统方式（与SimpleRtspPlayer相同）
          const dummyUrl = `ws://localhost:0/${props.videoId}`;
          
          player.value = new JSMpeg.Player(dummyUrl, {
            canvas: videoCanvas.value,
            autoplay: true,
            audio: false,
            videoBufferSize: 1024 * 1024,
            onConnectionCreate: () => {
              console.log('创建模拟WebSocket连接');
              // 返回一个自定义的WebSocket对象
              const mockWs = {
                onopen: null,
                onclose: null,
                onmessage: null,
                onerror: null,
                send: () => {},
                close: () => {},
                readyState: 1,
                constructor: { prototype: { OPEN: 1 } }
              };
              
              // 添加自定义write方法
              mockWs.write = (data) => {
                if (mockWs.onmessage) {
                  mockWs.onmessage({ data: data });
                }
              };
              
              return mockWs;
            }
          });
          
          // 重写player的write方法（与SimpleRtspPlayer相同）
          if (player.value && player.value.source) {
            const originalSource = player.value.source;
            player.value.write = function(data) {
              try {
                console.log('传统播放器接收数据:', data.length, '字节');
                if (originalSource && originalSource.socket && originalSource.socket.onmessage) {
                  originalSource.socket.onmessage({ data: data });
                }
              } catch (e) {
                console.warn('写入数据到传统播放器失败:', e);
              }
            };
            
            // 模拟连接建立
            originalSource.established = true;
            originalSource.completed = false;
            
            console.log('传统播放器创建并配置完成');
          }
        }
        
        console.log('播放器创建完成，类型:', player.value.constructor.name || 'CustomPlayer');
        
      } catch (error) {
        console.error('创建播放器失败:', error);
        updateStatus('创建播放器失败: ' + error.message, true);
        
        // 创建最基础的播放器作为后备
        player.value = {
          canvas: videoCanvas.value,
          write: function(data) {
            console.log('后备播放器收到数据:', data.length, '字节');
          },
          destroy: function() {
            this.canvas = null;
          }
        };
      }
    };

  } catch (error) {
    updateStatus('播放失败: ' + error.message, true);
    emit('error', error);
    console.error('播放失败:', error);
  }
};

// 将视频流字符串转换为二进制数据 - 简化版本
const convertVideoStreamToBinary = (videoStreamString) => {
  try {
    if (typeof videoStreamString !== 'string' || !videoStreamString) {
      console.warn('视频流数据无效:', typeof videoStreamString);
      return null;
    }
    
    const cleanString = videoStreamString.trim();
    console.log('开始转换视频流，长度:', cleanString.length);
    
    // 如果是data URL格式
    if (cleanString.startsWith('data:')) {
      console.log('处理data URL格式');
      const parts = cleanString.split(',');
      if (parts.length >= 2) {
        try {
          const binaryString = atob(parts[1]);
          const uint8Array = new Uint8Array(binaryString.length);
          for (let i = 0; i < binaryString.length; i++) {
            uint8Array[i] = binaryString.charCodeAt(i);
          }
          console.log('data URL解码成功，大小:', uint8Array.length);
          return uint8Array;
        } catch (e) {
          console.warn('data URL解码失败:', e);
          return null;
        }
      }
    }
    
    // 尝试Base64解码
    try {
      console.log('尝试Base64解码...');
      const binaryString = atob(cleanString);
      const uint8Array = new Uint8Array(binaryString.length);
      for (let i = 0; i < binaryString.length; i++) {
        uint8Array[i] = binaryString.charCodeAt(i);
      }
      
      console.log('Base64解码成功:', {
        原始长度: cleanString.length,
        解码后长度: uint8Array.length,
        前10字节: Array.from(uint8Array.slice(0, 10)),
        最后10字节: Array.from(uint8Array.slice(-10))
      });
      
      return uint8Array;
      
    } catch (base64Error) {
      console.warn('Base64解码失败，尝试其他方法:', base64Error.message);
      
      // 如果Base64解码失败，尝试直接转换字符码
      try {
        console.log('尝试直接字符码转换...');
        const uint8Array = new Uint8Array(cleanString.length);
        for (let i = 0; i < cleanString.length; i++) {
          uint8Array[i] = cleanString.charCodeAt(i) & 0xFF;
        }
        
        console.log('字符码转换完成:', {
          长度: uint8Array.length,
          前10字节: Array.from(uint8Array.slice(0, 10))
        });
        
        return uint8Array;
      } catch (charError) {
        console.error('字符码转换也失败:', charError);
        return null;
      }
    }
    
  } catch (error) {
    console.error('视频流转换失败:', error);
    return null;
  }
};

// 停止播放
const handleStop = () => {
  // 停止定时写入
  stopBufferWriter();
  
  // 清空缓冲区
  clearBuffer();
  
  // 关闭WebSocket连接
  if (websocket.value) {
    websocket.value.close();
    websocket.value = null;
  }
  
  // 销毁播放器
  if (player.value) {
    try {
      player.value.destroy();
    } catch (e) {
      console.warn('销毁播放器时出错:', e);
    }
    player.value = null;
  }
  
  isPlaying.value = false;
  updateStatus('');
  emit('stop');
};

// 监听videoId变化
watch(() => props.videoId, () => {
  if (isPlaying.value) {
    handleStop();
    setTimeout(() => {
      handlePlay();
    }, 500);
  }
});

// 组件挂载
onMounted(async () => {
  console.log('🔍 [jsmpeg2服务] MultiRtspPlayer组件挂载');
  
  // 添加页面可见性监听
  document.addEventListener('visibilitychange', handleVisibilityChange);
  
  // 启动缓冲区刷新机制
  startBufferFlushing();
  
  // 加载JSMpeg
  try {
    await loadJSMpeg();
    
    // 如果自动播放，延迟一点时间开始播放
    if (props.autoplay) {
      setTimeout(() => {
        console.log('🔍 [jsmpeg2服务] 自动播放启动');
        handlePlay();
      }, 1000);
    }
  } catch (error) {
    updateStatus('初始化失败: ' + error.message, true);
  }
});

// 组件卸载
onUnmounted(() => {
  // 移除页面可见性监听
  document.removeEventListener('visibilitychange', handleVisibilityChange);
  
  // 停止定时写入机制
  stopBufferWriter();
  
  // 清空缓冲区
  clearBuffer();
  
  // 断开连接并清理资源
  handleStop();
});

// 暴露方法给父组件
defineExpose({
  play: handlePlay,
  stop: handleStop,
  destroy: handleStop
});

// 🔥 新增：数据流拼接函数
const appendToVideoBuffer = (newData) => {
  try {
    // 防止缓冲区过大
    if (videoDataBuffer.value.length + newData.length > maxBufferSize2) {
      console.warn('🔍 [jsmpeg2服务] 缓冲区将溢出，清空旧数据');
      videoDataBuffer.value = new Uint8Array(0);
    }
    
    // 拼接新数据
    const combined = new Uint8Array(videoDataBuffer.value.length + newData.length);
    combined.set(videoDataBuffer.value);
    combined.set(newData, videoDataBuffer.value.length);
    videoDataBuffer.value = combined;
    
    console.log('🔍 [jsmpeg2服务] 数据已加入缓冲区:', {
      新数据大小: newData.length,
      缓冲区总大小: videoDataBuffer.value.length,
      前10字节: Array.from(videoDataBuffer.value.slice(0, 10)).map(b => `0x${b.toString(16).padStart(2, '0')}`).join(' ')
    });
    
  } catch (error) {
    console.error('🔍 [jsmpeg2服务] 数据拼接失败:', error);
  }
};

// 🔥 新增：定时刷新缓冲区到播放器
const flushBufferToPlayer = () => {
  const now = Date.now();
  if (now - lastFlushTime.value < flushInterval) {
    return; // 未到刷新时间
  }
  
  if (videoDataBuffer.value.length === 0) {
    return; // 没有数据
  }
  
  if (!player.value || !player.value.write) {
    console.warn('🔍 [jsmpeg2服务] 播放器未准备好，延迟刷新');
    return;
  }
  
  try {
    const dataToWrite = new Uint8Array(videoDataBuffer.value);
    
    console.log('🔍 [jsmpeg2服务] 刷新缓冲区到播放器:', {
      数据大小: dataToWrite.length,
      前20字节: Array.from(dataToWrite.slice(0, 20)).map(b => `0x${b.toString(16).padStart(2, '0')}`).join(' '),
      包含MPEG同步字节: Array.from(dataToWrite).filter(b => b === 0x47).length
    });
    
    // 写入播放器
    player.value.write(dataToWrite);
    
    // 清空缓冲区并更新时间
    videoDataBuffer.value = new Uint8Array(0);
    lastFlushTime.value = now;
    
    if (!isPlaying.value) {
      isPlaying.value = true;
      emit('play');
      console.log('🔍 [jsmpeg2服务] 开始播放状态');
    }
    
  } catch (error) {
    console.error('🔍 [jsmpeg2服务] 刷新缓冲区失败:', error);
    // 发生错误时清空缓冲区
    videoDataBuffer.value = new Uint8Array(0);
  }
};

// 🔥 新增：启动定时刷新机制
const startBufferFlushing = () => {
  const flushTimer = setInterval(() => {
    if (isPlaying.value || videoDataBuffer.value.length > 0) {
      flushBufferToPlayer();
    }
  }, flushInterval);
  
  // 组件卸载时清理定时器
  onUnmounted(() => {
    clearInterval(flushTimer);
  });
  
  console.log('🔍 [jsmpeg2服务] 缓冲区定时刷新已启动');
};
</script>

<style scoped>
.multi-rtsp-player {
  width: 100%;
  height: 100%;
}

.video-container {
  position: relative;
  width: 100%;
  height: 100%;
}

canvas {
  width: 100%;
  height: 100%;
  background-color: #000;
  display: block;
}

.status-indicator {
  position: absolute;
  top: 8px;
  right: 8px;
  padding: 4px 8px;
  background: rgba(34, 197, 94, 0.9);
  color: white;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  pointer-events: none;
  z-index: 15;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(4px);
}

.status-indicator.playing {
  background: rgba(34, 197, 94, 0.9);
  color: white;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 0.9; }
  50% { opacity: 0.7; }
  100% { opacity: 0.9; }
}

.status-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(220, 38, 38, 0.9);
  color: white;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 12px;
  pointer-events: none;
  z-index: 20;
  max-width: 80%;
  text-align: center;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.status-overlay.error {
  background: rgba(255, 0, 0, 0.7);
  color: white;
}

.controls {
  margin-top: 8px;
  text-align: center;
}

.controls button {
  margin: 0 4px;
  padding: 4px 12px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.controls button:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.controls button:hover:not(:disabled) {
  background: #0056b3;
}
</style> 