<template>
  <el-dialog
    v-model="dialogVisible"
    title="报警确认"
    width="600px"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    class="heating-alarm-confirm-dialog"
  >
    <el-form :model="confirmForm" :rules="rules" ref="formRef" label-width="100px">
      <el-form-item label="确认结果:" prop="confirmResult" required>
        <el-radio-group v-model="confirmForm.confirmResult">
          <el-radio :value="2002001">真实报警</el-radio>
          <el-radio :value="2002002">误报</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="确认描述:" prop="description">
        <el-input
          v-model="confirmForm.description"
          type="textarea"
          :rows="4"
          placeholder="输入描述内容（100字以内）"
          maxlength="100"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :loading="loading">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch } from 'vue';
import { ElDialog, ElForm, ElFormItem, ElRadioGroup, ElRadio, ElInput, ElButton, ElMessage } from 'element-plus';
import { confirmHeatingAlarm } from '@/api/heating';

// 接收父组件传递的参数
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  alarmData: {
    type: Object,
    default: () => ({})
  }
});

// 向父组件发送事件
const emit = defineEmits(['update:visible', 'success']);

// 弹窗显示状态
const dialogVisible = ref(false);
const loading = ref(false);
const formRef = ref(null);

// 监听visible属性变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal;
  if (newVal) {
    resetForm();
  }
});

// 监听弹窗显示状态变化
watch(() => dialogVisible.value, (newVal) => {
  emit('update:visible', newVal);
});

// 确认表单数据
const confirmForm = reactive({
  confirmResult: 2002001, // 默认选择真实报警
  description: ''
});

// 表单验证规则
const rules = {
  confirmResult: [
    { required: true, message: '请选择确认结果', trigger: 'change' }
  ]
};

// 重置表单
const resetForm = () => {
  confirmForm.confirmResult = 2002001;
  confirmForm.description = '';
  if (formRef.value) {
    formRef.value.clearValidate();
  }
};

// 处理确认
const handleConfirm = async () => {
  if (!formRef.value) return;
  
  try {
    await formRef.value.validate();
    loading.value = true;

    const params = {
      alarmId: props.alarmData.id,
      confirmResult: confirmForm.confirmResult,
      description: confirmForm.description,
      handleUser: '当前用户' // 这里应该从用户信息中获取
    };

    const res = await confirmHeatingAlarm(params);
    if (res.code === 200) {
      ElMessage.success('确认成功');
      emit('success');
      handleCancel();
    } else {
      ElMessage.error(res.message || '确认失败');
    }
  } catch (error) {
    console.error('确认报警失败:', error);
    ElMessage.error('确认失败');
  } finally {
    loading.value = false;
  }
};

// 处理取消
const handleCancel = () => {
  dialogVisible.value = false;
  resetForm();
};
</script>

<style scoped>
.heating-alarm-confirm-dialog {
  :deep(.el-dialog__body) {
    padding: 20px;
  }
}

:deep(.el-form-item__label) {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #606266;
}

:deep(.el-radio__label) {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #606266;
}

:deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px #CED3DA inset;
}

:deep(.el-textarea__inner) {
  box-shadow: 0 0 0 1px #CED3DA inset;
}

:deep(.el-button--primary) {
  background-color: #0086FF;
  border-color: #0086FF;
}

:deep(.el-button--primary:hover) {
  background-color: #40A9FF;
  border-color: #40A9FF;
}

.dialog-footer {
  text-align: right;
}
</style> 