# 桥梁管理系统修复说明

## 修复内容

### 1. 上传接口统一使用 uploadFile

**修改文件：**
- `src/components/bridge/BasicInfoTab.vue`
- `src/components/bridge/AttachmentTab.vue`

**修改内容：**
- 将原有的 `:action="uploadAction"` 改为 `:auto-upload="false"`
- 使用 `@/api/upload` 中的 `uploadFile` 方法进行文件上传
- 在 `handleFileChange` 方法中处理文件上传逻辑
- 添加文件大小和类型验证

**代码示例：**
```javascript
import { uploadFile } from '@/api/upload'

const handleFileChange = async (file, fileList) => {
  // 检查文件大小
  const isLt20M = file.size / 1024 / 1024 < 20
  if (!isLt20M) {
    ElMessage.error('上传图片大小不能超过 20MB!')
    return
  }

  try {
    // 上传文件
    const response = await uploadFile(file.raw)
    if (response.code === 200) {
      // 处理上传成功逻辑
    }
  } catch (error) {
    ElMessage.error('上传失败')
  }
}
```

### 2. 所属区域按照 GasNetworkPointDialog 写法

**修改文件：**
- `src/components/bridge/BasicInfoTab.vue`
- `src/constants/bridge.js`

**修改内容：**
- 在 `bridge.js` 中添加 `AREA_OPTIONS` 常量，包含完整的行政区划数据
- 将原有的简单下拉选择改为级联选择器
- 添加 `handleAreaChange` 方法处理区域选择变化
- 添加 `findAreaByCode` 方法查找区域信息

**代码示例：**
```vue
<el-cascader 
  v-model="formData.town" 
  :options="areaOptions" 
  :props="{
    value: 'code',
    label: 'name',
    children: 'children'
  }" 
  placeholder="请选择所属区域" 
  style="width: 100%" 
  @change="handleAreaChange" 
/>
```

### 3. 修复组成信息弹窗问题

**修改文件：**
- `src/components/bridge/ComponentInfoTab.vue`

**修改内容：**
- 添加 `handleCancel` 方法处理取消按钮点击
- 添加 `handleDialogClose` 方法处理弹窗关闭前的逻辑
- 修复弹窗的 `before-close` 属性绑定
- 使用常量中的桥段类型选项

**修复的问题：**
- 取消按钮无法正常关闭弹窗
- 弹窗关闭时可能出现的控制台错误
- 桥段类型选项硬编码问题

### 4. 下拉选项常量统一管理

**修改文件：**
- `src/constants/bridge.js`
- `src/components/bridge/BasicInfoTab.vue`
- `src/components/bridge/ProfileCardTab.vue`
- `src/components/bridge/ComponentInfoTab.vue`
- `src/components/bridge/AttachmentTab.vue`

**新增常量：**
```javascript
// 区域选项
export const AREA_OPTIONS = [...]

// 桥段类型选项
export const SEGMENT_TYPE_OPTIONS = [...]
export const SEGMENT_TYPE_MAP = {...}

// 主梁类型选项
export const MAIN_BEAM_TYPE_OPTIONS = [...]
export const MAIN_BEAM_TYPE_MAP = {...}

// 支座类型选项
export const SUPPORT_TYPE_OPTIONS = [...]
export const SUPPORT_TYPE_MAP = {...}

// 伸缩缝类型选项
export const SHRINKAGE_JOINT_TYPE_OPTIONS = [...]
export const SHRINKAGE_JOINT_TYPE_MAP = {...}

// 桥墩类型选项
export const PIER_TYPE_OPTIONS = [...]
export const PIER_TYPE_MAP = {...}

// 桥台类型选项
export const ABUTMENT_TYPE_OPTIONS = [...]
export const ABUTMENT_TYPE_MAP = {...}

// 护岸类型选项
export const BANK_PROTECTION_TYPE_OPTIONS = [...]
export const BANK_PROTECTION_TYPE_MAP = {...}

// 引桥扶壁类型选项
export const CABLE_ANCHOR_WALL_TYPE_OPTIONS = [...]
export const CABLE_ANCHOR_WALL_TYPE_MAP = {...}

// 附件类型选项
export const ATTACHMENT_TYPE_OPTIONS = [...]
export const ATTACHMENT_TYPE_MAP = {...}
```

**修改内容：**
- 将所有硬编码的下拉选项抽离到常量文件中
- 为每个选项组添加清晰的注释说明用途
- 提供选项数组和映射对象两种形式
- 在各个组件中引用常量替换硬编码

## 修复后的功能特点

### 1. 统一的文件上传
- 所有文件上传都使用统一的 `uploadFile` 接口
- 统一的文件大小和类型验证
- 一致的错误处理和用户提示

### 2. 完善的区域选择
- 支持三级级联选择（市-区县-街道/乡镇）
- 与燃气管网点保持一致的交互体验
- 自动更新区域名称字段

### 3. 稳定的弹窗交互
- 修复了组成信息弹窗的关闭问题
- 添加了完善的事件处理机制
- 避免了控制台错误

### 4. 规范的常量管理
- 所有下拉选项都有明确的注释
- 便于维护和扩展
- 避免了重复定义

## 测试建议

1. **文件上传测试**
   - 测试各种文件类型的上传
   - 测试文件大小限制
   - 测试上传失败的处理

2. **区域选择测试**
   - 测试三级级联选择
   - 测试区域名称的自动更新
   - 测试必填验证

3. **弹窗交互测试**
   - 测试组成信息的新增、编辑、删除
   - 测试弹窗的取消和关闭
   - 测试表单验证

4. **常量引用测试**
   - 确认所有下拉选项正常显示
   - 确认选项值和显示文本的映射正确
   - 确认没有硬编码残留

## 注意事项

1. 确保 `@/api/upload` 中的 `uploadFile` 方法可用
2. 确保服务器端支持文件上传接口
3. 根据实际需求调整文件大小限制
4. 根据实际行政区划调整 `AREA_OPTIONS` 数据

---

**修复完成时间：** 2024年12月
**修复内容：** 文件上传、区域选择、弹窗交互、常量管理
**状态：** 已完成 