<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="1000px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="sensor-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      :disabled="mode === 'view'"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="设备名称" prop="deviceName">
            <el-input v-model="formData.deviceName" placeholder="请输入设备名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="设备编码" prop="indexCode">
            <el-input v-model="formData.indexCode" placeholder="请输入设备编码" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="设备类型" prop="deviceType">
            <el-select v-model="formData.deviceType" placeholder="请选择" class="w-full" @change="handleDeviceTypeChange">
              <el-option v-for="item in DEVICE_TYPES" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="监测指标" prop="monitorIndex">
            <el-select v-model="formData.monitorIndex" placeholder="请选择" class="w-full" @change="handleMonitorIndexChange">
              <el-option v-for="item in MONITOR_INDEXES" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="采集频率" prop="collectFrequency">
            <div class="flex items-center">
              <el-input v-model="formData.collectFrequency" placeholder="请输入数值" class="w-32" />
              <span class="ml-2">分钟/次</span>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="监测对象" prop="monitorTarget">
            <el-select v-model="formData.monitorTarget" placeholder="请选择" class="w-full" @change="handleMonitorTargetChange">
              <el-option v-for="item in MONITOR_TARGET_OPTIONS" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="对象选择" prop="monitorObjectId">
            <el-select v-model="formData.monitorObjectId" placeholder="请选择" class="w-full" @change="handleObjectChange">
              <el-option v-for="item in objectOptions" :key="item.id" :label="getObjectLabel(item)" :value="item.id" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="设备状态" prop="onlineStatus">
            <el-select v-model="formData.onlineStatus" placeholder="请选择" class="w-full">
              <el-option v-for="item in DEVICE_STATUS_OPTIONS" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="权属单位" prop="ownershipUnit">
            <el-select v-model="formData.ownershipUnit" placeholder="请选择" class="w-full" @change="handleOwnershipChange">
              <el-option v-for="item in enterpriseOptions" :key="item.id" :label="item.enterpriseName" :value="item.id" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="所属位置">
            <div class="flex items-center">
              <el-cascader
                v-model="formData.regionPath"
                :options="areaOptions"
                :props="{
                  value: 'code',
                  label: 'name',
                  children: 'children'
                }"
                placeholder="请选择所属区域"
                class="w-full"
                @change="handleAreaChange"
              />
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="16">
          <el-form-item label="" prop="address" class="w-full">
            <el-input v-model="formData.address" placeholder="输入详细地址" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="位置坐标">
            <div class="flex items-center">
              <el-input v-model="formData.longitude" placeholder="经度" class="mr-2 w-32" />
              <el-input v-model="formData.latitude" placeholder="纬度" class="w-32" />
              <el-button type="primary" class="ml-2" icon="Location" circle @click="openMapPicker"></el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="备注">
            <el-input
              v-model="formData.remarks"
              type="textarea"
              :rows="4"
              placeholder="请输入备注"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="mode !== 'view'">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted, onUnmounted, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import {
  getAllEnterpriseList,
  saveSensorDevice,
  updateSensorDevice,
  getPipelineList,
  getHeatFactoryList,
  getAllHeatStationList,
  getUserList,
  getWellList,
} from '@/api/heating';
import { AREA_OPTIONS } from '@/constants/gas';
import {
  DEVICE_TYPES,
  DEVICE_TYPE_MAP,
  MONITOR_INDEXES,
  MONITOR_INDEX_MAP,
  MONITOR_TARGET_OPTIONS,
  MONITOR_TARGET_MAP,
  DEVICE_STATUS_OPTIONS,
  DEVICE_STATUS_MAP
} from '@/constants/heating';
import { collectShow } from "@/hooks/gishooks";
import bus from '@/utils/mitt';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add', // 'add', 'edit', 'view'
    validator: (value) => ['add', 'edit', 'view'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'success']);

// 表单引用
const formRef = ref(null);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 对话框标题
const dialogTitle = computed(() => {
  const titles = {
    add: '新增传感器',
    edit: '编辑传感器',
    view: '传感器详情'
  };
  return titles[props.mode] || '传感器信息';
});

// 企业选项
const enterpriseOptions = ref([]);
// 对象选项
const objectOptions = ref([]);
// 行政区划选项
const areaOptions = ref(AREA_OPTIONS);

// 表单数据
const formData = reactive({
  id: '',
  deviceName: '',
  indexCode: '',
  deviceType: '',
  deviceTypeName: '',
  monitorIndex: '',
  monitorIndexName: '',
  collectFrequency: '',
  monitorTarget: '',
  monitorTargetName: '',
  monitorObjectId: '',
  monitorObjectName: '',
  onlineStatus: '',
  ownershipUnit: '',
  ownershipUnitName: '',
  regionCode: '',
  regionName: '',
  regionPath: '',
  regionPathName: '',
  address: '',
  longitude: '',
  latitude: '',
  remarks: ''
});

// 表单验证规则
const formRules = {
  deviceName: [{ required: true, message: '请输入设备名称', trigger: 'blur' }],
  indexCode: [{ required: true, message: '请输入设备编码', trigger: 'blur' }],
  deviceType: [{ required: true, message: '请选择设备类型', trigger: 'change' }],
  monitorIndex: [{ required: true, message: '请选择监测指标', trigger: 'change' }],
  collectFrequency: [{ required: true, message: '请输入采集频率', trigger: 'blur' }],
  monitorTarget: [{ required: true, message: '请选择监测对象', trigger: 'change' }],
  monitorObjectId: [{ required: true, message: '请选择对象', trigger: 'change' }],
  onlineStatus: [{ required: true, message: '请选择设备状态', trigger: 'change' }],
  ownershipUnit: [{ required: true, message: '请选择权属单位', trigger: 'change' }]
};

// 获取企业列表
const fetchEnterpriseOptions = async () => {
  try {
    const res = await getAllEnterpriseList();
    if (res && res.code === 200) {
      enterpriseOptions.value = res.data || [];
    }
  } catch (error) {
    console.error('获取企业列表失败:', error);
    ElMessage.error('获取企业列表失败');
  }
};

// 根据监测对象获取相应的选项列表
const fetchObjectOptions = async () => {
  if (!formData.monitorTarget) return;
  
  try {
    let res;
    
    switch (Number(formData.monitorTarget)) {
      case 2001501: // 管线
        res = await getPipelineList();
        break;
      case 2001502: // 热源
        res = await getHeatFactoryList();
        break;
      case 2001503: // 换热站
        res = await getAllHeatStationList();
        break;
      case 2001504: // 用户
        res = await getUserList();
        break;
      case 2001505: // 窨井
        res = await getWellList();
        break;
      default:
        objectOptions.value = [];
        return;
    }
    
    if (res && res.code === 200) {
      objectOptions.value = res.data || [];
    } else {
      objectOptions.value = [];
    }
  } catch (error) {
    console.error('获取对象列表失败:', error);
    ElMessage.error('获取对象列表失败');
    objectOptions.value = [];
  }
};

// 获取对象标签
const getObjectLabel = (item) => {
  if (!item) return '';
  
  switch (Number(formData.monitorTarget)) {
    case 2001501: // 管线
      return item.pipelineName || item.name || '';
    case 2001502: // 热源
      return item.factoryName || item.name || '';
    case 2001503: // 换热站
      return item.stationName || item.name || '';
    case 2001504: // 用户
      return item.userName || item.name || '';
    case 2001505: // 窨井
      return item.wellName || item.name || '';
    default:
      return '';
  }
};

// 设备类型变更处理
const handleDeviceTypeChange = (value) => {
  if (value) {
    formData.deviceTypeName = DEVICE_TYPE_MAP[value] || '';
  }
};

// 监测指标变更处理
const handleMonitorIndexChange = (value) => {
  if (value) {
    formData.monitorIndexName = MONITOR_INDEX_MAP[value] || '';
  }
};

// 监测对象变更处理
const handleMonitorTargetChange = (value) => {
  if (value) {
    formData.monitorTargetName = MONITOR_TARGET_MAP[value] || '';
    // 清空已选择的对象
    formData.monitorObjectId = '';
    formData.monitorObjectName = '';
    // 获取新的对象选项
    fetchObjectOptions();
  }
};

// 对象选择变更处理
const handleObjectChange = (value) => {
  if (value) {
    const selected = objectOptions.value.find(item => item.id === value);
    if (selected) {
      formData.monitorObjectName = getObjectLabel(selected);
    }
  }
};

// 权属单位变更处理
const handleOwnershipChange = (value) => {
  if (value) {
    const selected = enterpriseOptions.value.find(item => item.id === value);
    if (selected) {
      formData.ownershipUnitName = selected.enterpriseName;
    }
  }
};

// 处理区域选择变化
const handleAreaChange = (value) => {
  if (value && value.length > 0) {
    formData.regionCode = value[value.length - 1];
    // 更新区域名称
    const selectedArea = findAreaByCode(areaOptions.value, formData.regionCode);
    if (selectedArea) {
      formData.regionName = selectedArea.name;
      
      // 构建区域路径名称
      const pathNames = [];
      for (let i = 0; i < value.length; i++) {
        const areaCode = value[i];
        let area;
        
        if (i === 0) {
          area = areaOptions.value.find(a => a.code === areaCode);
        } else {
          let parent = findAreaByCode(areaOptions.value, value[i-1]);
          area = parent.children.find(a => a.code === areaCode);
        }
        
        if (area) {
          pathNames.push(area.name);
        }
      }
      
      formData.regionPathName = pathNames.join('/');
    }
  }
};

// 根据区域代码查找区域信息
const findAreaByCode = (areas, code) => {
  for (const area of areas) {
    if (area.code === code) {
      return area;
    }
    if (area.children) {
      const found = findAreaByCode(area.children, code);
      if (found) return found;
    }
  }
  return null;
};

// 重置表单
const resetForm = () => {
  // 重置表单数据
  Object.keys(formData).forEach(key => {
    formData[key] = '';
  });
  
  // 重置对象选项
  objectOptions.value = [];
};

// 确保数值为数字
const ensureNumber = (val) => {
  if (val === undefined || val === null || val === '') return '';
  return Number(val);
};

// 监听props.data变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    // 复制数据到表单
    Object.keys(formData).forEach(key => {
      if (newVal[key] !== undefined) {
        // 确保数字字段的值是数字类型
        if (key === 'deviceType' || key === 'monitorIndex' || key === 'monitorTarget' || key === 'onlineStatus') {
          formData[key] = ensureNumber(newVal[key]);
        } else {
          formData[key] = newVal[key];
        }
      }
    });

    // 确保各类型名称正确设置
    if (formData.deviceType) {
      formData.deviceTypeName = DEVICE_TYPE_MAP[formData.deviceType] || '';
    }
    if (formData.monitorIndex) {
      formData.monitorIndexName = MONITOR_INDEX_MAP[formData.monitorIndex] || '';
    }
    if (formData.monitorTarget) {
      formData.monitorTargetName = MONITOR_TARGET_MAP[formData.monitorTarget] || '';
      // 获取对象选项
      fetchObjectOptions();
    }
    if (formData.ownershipUnit && enterpriseOptions.value.length > 0) {
      const selected = enterpriseOptions.value.find(item => item.id === formData.ownershipUnit);
      if (selected) {
        formData.ownershipUnitName = selected.enterpriseName;
      }
    }
  } else if (props.mode === 'add') {
    // 新增模式清空表单
    resetForm();
  }
}, { immediate: true, deep: true });

// 地图选点事件处理函数
const handleCollectLocation = (params) => {
  nextTick(() => {
    formData.longitude = params.longitude || 0;
    formData.latitude = params.latitude || 0;
  });
};

// 打开地图选点
const openMapPicker = () => {
  collectShow.value = true; // 激活采集点位窗口
  // 先移除可能存在的旧监听器
  bus.off("getCollectLocation", handleCollectLocation);
  // 添加新的监听器
  bus.on("getCollectLocation", handleCollectLocation);
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  // 重置表单
  if (formRef.value) {
    formRef.value.resetFields();
  }
  resetForm();
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;
  
  try {
    await formRef.value.validate();
    
    // 准备提交数据
    const submitData = { ...formData };
    
    // 确保数值字段正确
    if (submitData.collectFrequency) {
      submitData.collectFrequency = Number(submitData.collectFrequency);
    }
    if (submitData.longitude) {
      submitData.longitude = Number(submitData.longitude);
    }
    if (submitData.latitude) {
      submitData.latitude = Number(submitData.latitude);
    }
    // 确保ID类型字段是数字
    submitData.deviceType = Number(submitData.deviceType);
    submitData.monitorIndex = Number(submitData.monitorIndex);
    submitData.monitorTarget = Number(submitData.monitorTarget);
    submitData.onlineStatus = Number(submitData.onlineStatus);
    
    // 提交数据
    let res;
    if (props.mode === 'add') {
      res = await saveSensorDevice(submitData);
    } else if (props.mode === 'edit') {
      res = await updateSensorDevice(submitData);
    }
    
    if (res && res.code === 200) {
      ElMessage.success(props.mode === 'add' ? '新增成功' : '更新成功');
      emit('success');
      handleClose();
    } else {
      ElMessage.error(res?.msg || (props.mode === 'add' ? '新增失败' : '更新失败'));
    }
  } catch (error) {
    console.error('表单验证失败', error);
  }
};

// 组件卸载时清理事件监听
onUnmounted(() => {
  bus.off("getCollectLocation", handleCollectLocation);
});

// 组件挂载时初始化
onMounted(async () => {
  await fetchEnterpriseOptions();
});
</script>

<style scoped>
.sensor-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-form-item__label::before) {
  content: '*';
  color: #ff4d4f;
  margin-right: 4px;
}

:deep(.el-form-item:not([required]) .el-form-item__label::before) {
  content: '';
  margin-right: 0;
}

:deep(.el-input__wrapper),
:deep(.el-select__input) {
  border-radius: 2px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  background-color: #0277FD;
  border-color: #0277FD;
}

:deep(.el-button--primary:hover) {
  background-color: #1890ff;
  border-color: #1890ff;
}

.w-full {
  width: 100%;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.ml-2 {
  margin-left: 8px;
}

.mr-2 {
  margin-right: 8px;
}

.w-32 {
  width: 140px;
}
</style> 