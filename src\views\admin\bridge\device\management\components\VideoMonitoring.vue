<template>
  <div class="video-monitoring-container">
    <div class="bg-white p-6 rounded-lg shadow">
      <div class="text-center text-gray-500">
        <el-icon size="64" class="mb-4">
          <VideoCamera />
        </el-icon>
        <h3 class="text-xl font-medium mb-2">视频监控</h3>
        <p class="text-gray-400">视频监控功能开发中...</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { VideoCamera } from '@element-plus/icons-vue'
</script>

<style scoped>
.video-monitoring-container {
  padding: 20px;
}

.text-center {
  text-align: center;
}

.text-gray-500 {
  color: #6b7280;
}

.text-gray-400 {
  color: #9ca3af;
}

.text-xl {
  font-size: 1.25rem;
}

.font-medium {
  font-weight: 500;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mb-4 {
  margin-bottom: 1rem;
}
</style> 