<template>
  <div class="tags-view-container">
    <div class="tags-view-wrapper">
      <el-scrollbar class="tags-view-scrollbar">
        <div class="tags-view-item-wrapper">
          <!-- 标签页列表，按当前一级菜单过滤 -->
          <span
            v-for="tag in currentFirstLevelTags"
            :key="tag.path"
            :class="['tags-view-item', isActive(tag) ? 'active' : '']"
            @click="handleClick(tag)"
            @contextmenu.prevent="openMenu(tag, $event)"
          >
            {{ tag.title }}
            <el-icon 
              class="close-icon"
              @click.stop="closeSelectedTag(tag)"
              v-if="!isAffix(tag)"
            >
              <Close />
            </el-icon>
          </span>
        </div>
      </el-scrollbar>
    </div>
    
    <!-- 右键菜单 -->
    <ul v-show="visible" :style="{left: left+'px', top: top+'px'}" class="contextmenu">
      <li @click="refreshSelectedTag(selectedTag)">刷新页面</li>
      <li @click="closeSelectedTag(selectedTag)" v-if="!isAffix(selectedTag)">关闭当前</li>
      <li @click="closeOthersTags">关闭其他</li>
      <li @click="closeAllTags(selectedTag)">关闭所有</li>
    </ul>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onBeforeUnmount } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Close } from '@element-plus/icons-vue'

// 路由相关
const route = useRoute()
const router = useRouter()

// 访问过的视图
const visitedViews = ref([])

// 右键菜单相关
const visible = ref(false)
const top = ref(0)
const left = ref(0)
const selectedTag = ref({})

// 获取当前路由的一级菜单
const currentFirstLevel = computed(() => {
  const path = route.path
  const firstLevel = path.split('/')[1]
  return firstLevel || 'comprehensive'
})

// 按当前一级菜单过滤标签
const currentFirstLevelTags = computed(() => {
  return visitedViews.value.filter(tag => {
    const tagFirstLevel = tag.path.split('/')[1]
    return tagFirstLevel === currentFirstLevel.value
  })
})

// 监听路由变化，添加标签
watch(
  () => route.path,
  () => {
    addTags()
  }
)

// 监听一级菜单变化，清除其他专项的标签
watch(
  currentFirstLevel,
  (newValue, oldValue) => {
    if (newValue !== oldValue) {
      // 切换专项时，清除其他专项的标签
      clearOtherFirstLevelTags(newValue)
    }
  }
)

// 初始化
onMounted(() => {
  addTags()
  document.addEventListener('click', closeMenu)
})

onBeforeUnmount(() => {
  document.removeEventListener('click', closeMenu)
})

// 清除其他专项的标签
const clearOtherFirstLevelTags = (currentFirstLevel) => {
  visitedViews.value = visitedViews.value.filter(tag => {
    const tagFirstLevel = tag.path.split('/')[1]
    return tagFirstLevel === currentFirstLevel
  })
}

// 添加标签
const addTags = () => {
  const { name, path, meta } = route
  if (name) {
    // 检查是否已存在该标签
    const isExist = visitedViews.value.some(v => v.path === path)
    if (!isExist) {
      visitedViews.value.push({
        name: name,
        path: path,
        title: meta.title || '未命名',
        affix: meta.affix || false
      })
    }
  }
}

// 判断是否是当前激活的标签
const isActive = (tag) => {
  return tag.path === route.path
}

// 判断是否是固定标签
const isAffix = (tag) => {
  return tag.affix
}

// 点击标签
const handleClick = (tag) => {
  if (tag.path !== route.path) {
    router.push(tag.path)
  }
}

// 关闭选中的标签
const closeSelectedTag = (tag) => {
  const index = visitedViews.value.findIndex(v => v.path === tag.path)
  if (index > -1) {
    visitedViews.value.splice(index, 1)
  }
  if (isActive(tag)) {
    toLastView(currentFirstLevelTags.value, tag)
  }
}

// 关闭其他标签
const closeOthersTags = () => {
  // 只保留固定标签和当前选中的标签，并且必须是当前一级菜单下的
  visitedViews.value = visitedViews.value.filter(tag => {
    const tagFirstLevel = tag.path.split('/')[1]
    return (tag.affix || tag.path === route.path) && tagFirstLevel === currentFirstLevel.value
  })
  closeMenu()
}

// 关闭所有标签
const closeAllTags = () => {
  // 只保留当前一级菜单下的固定标签
  visitedViews.value = visitedViews.value.filter(tag => {
    const tagFirstLevel = tag.path.split('/')[1]
    return tag.affix && tagFirstLevel === currentFirstLevel.value
  })
  
  const affixTags = visitedViews.value.filter(tag => tag.affix)
  if (affixTags.length > 0) {
    router.push(affixTags[0].path)
  } else {
    router.push(`/${currentFirstLevel.value}/home`)
  }
  closeMenu()
}

// 刷新选中的标签
const refreshSelectedTag = (tag) => {
  closeMenu()
  router.replace({ path: '/redirect' + tag.path })
}

// 打开右键菜单
const openMenu = (tag, e) => {
  const menuMinWidth = 105
  const offsetLeft = e.clientX
  const offsetWidth = e.target.offsetWidth
  const maxLeft = window.innerWidth - menuMinWidth
  const menuLeft = offsetLeft + offsetWidth > maxLeft ? maxLeft : offsetLeft

  left.value = menuLeft
  top.value = e.clientY
  visible.value = true
  selectedTag.value = tag
}

// 关闭右键菜单
const closeMenu = () => {
  visible.value = false
}

// 跳转到最后一个标签
const toLastView = (visitedViews, view) => {
  const latestView = visitedViews.slice(-1)[0]
  if (latestView) {
    router.push(latestView.path)
  } else {
    router.push(`/${currentFirstLevel.value}/home`)
  }
}
</script>

<style scoped>
.tags-view-container {
  height: 40px;
  width: calc(100% - 32px);
  background: #FFFFFF;
  border-bottom: 1px solid #d8dce5;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, .12), 0 0 3px 0 rgba(0, 0, 0, .04);
  margin: 0px 16px;
  padding-right: 20px;
  box-sizing: border-box;
}

.tags-view-wrapper {
  height: 100%;
  width: 100%;
}

.tags-view-scrollbar {
  white-space: nowrap;
  width: 100%;
  height: 100%;
}

.tags-view-item-wrapper {
  padding: 2px 4px;
  height: 100%;
  display: flex;
  align-items: center;
}

.tags-view-item {
  display: inline-flex;
  align-items: center;
  position: relative;
  cursor: pointer;
  height: 26px;
  line-height: 26px;
  /* border: 1px solid #d8dce5; */
  color: #495060;
  background: #fff;
  padding: 0 8px;
  font-size: 12px;
  margin-left: 5px;
  margin-top: 4px;
  border-radius: 3px;
}

.tags-view-item:first-of-type {
  margin-left: 5px;
}

.tags-view-item.active {
  background-color: rgba(2, 119, 253, 0.15);
  color: #0277FD;
  /* border-color: rgba(2, 119, 253, 0.3); */
}

.tags-view-item .close-icon {
  width: 16px;
  height: 16px;
  margin-left: 5px;
  border-radius: 50%;
  text-align: center;
  transition: all .3s;
  transform-origin: 100% 50%;
  color: #666;
}

.tags-view-item .close-icon:hover {
  background-color: #b4bccc;
  color: #fff;
}

.contextmenu {
  margin: 0;
  background: #fff;
  z-index: 3000;
  position: absolute;
  list-style-type: none;
  padding: 5px 0;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 400;
  color: #333;
  box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, .3);
}

.contextmenu li {
  margin: 0;
  padding: 7px 16px;
  cursor: pointer;
}

.contextmenu li:hover {
  background: #eee;
}
</style>