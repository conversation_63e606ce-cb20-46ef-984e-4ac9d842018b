/**
 * 配置管理工具类
 */
import { saveAs } from 'file-saver'

// 本地存储键名
const STORAGE_KEY = 'code-generator-configs'

/**
 * 保存配置到本地存储
 * @param {String} name - 配置名称
 * @param {Object} config - 配置数据
 * @returns {Boolean} - 是否保存成功
 */
export function saveConfig(name, config) {
  try {
    // 获取已保存的配置列表
    const configs = getConfigList()
    
    // 检查是否已存在同名配置
    const index = configs.findIndex(item => item.name === name)
    
    // 创建配置对象
    const configItem = {
      name,
      description: config.basic.description || '',
      author: config.basic.author || '',
      createTime: new Date().toLocaleString(),
      updateTime: new Date().toLocaleString(),
      config
    }
    
    // 如果已存在，则更新
    if (index !== -1) {
      configItem.createTime = configs[index].createTime
      configs[index] = configItem
    } else {
      // 否则，添加到列表
      configs.push(configItem)
    }
    
    // 保存到本地存储
    localStorage.setItem(STORAGE_KEY, JSON.stringify(configs))
    
    return true
  } catch (error) {
    console.error('保存配置失败', error)
    return false
  }
}

/**
 * 获取配置列表
 * @returns {Array} - 配置列表
 */
export function getConfigList() {
  try {
    const configsStr = localStorage.getItem(STORAGE_KEY)
    return configsStr ? JSON.parse(configsStr) : []
  } catch (error) {
    console.error('获取配置列表失败', error)
    return []
  }
}

/**
 * 获取配置
 * @param {String} name - 配置名称
 * @returns {Object|null} - 配置数据
 */
export function getConfig(name) {
  try {
    const configs = getConfigList()
    const config = configs.find(item => item.name === name)
    return config ? config.config : null
  } catch (error) {
    console.error('获取配置失败', error)
    return null
  }
}

/**
 * 删除配置
 * @param {String} name - 配置名称
 * @returns {Boolean} - 是否删除成功
 */
export function deleteConfig(name) {
  try {
    const configs = getConfigList()
    const newConfigs = configs.filter(item => item.name !== name)
    localStorage.setItem(STORAGE_KEY, JSON.stringify(newConfigs))
    return true
  } catch (error) {
    console.error('删除配置失败', error)
    return false
  }
}

/**
 * 导出配置
 * @param {Object} config - 配置数据
 * @param {String} filename - 文件名
 */
export function exportConfig(config, filename) {
  try {
    const blob = new Blob([JSON.stringify(config, null, 2)], { type: 'application/json' })
    saveAs(blob, filename || 'config.json')
    return true
  } catch (error) {
    console.error('导出配置失败', error)
    return false
  }
}

/**
 * 验证配置
 * @param {Object} config - 配置数据
 * @returns {Object} - 验证结果，包含 valid 和 errors 字段
 */
export function validateConfig(config) {
  const errors = []
  
  // 验证基础信息
  if (!config.basic) {
    errors.push('缺少基础信息配置')
  } else {
    if (!config.basic.moduleName) {
      errors.push('缺少模块名称')
    }
    if (!config.basic.apiPrefix) {
      errors.push('缺少API前缀')
    }
    if (!config.basic.pageTitle) {
      errors.push('缺少页面标题')
    }
  }
  
  // 验证表格配置
  if (!config.table) {
    errors.push('缺少表格配置')
  } else if (!config.table.columns || config.table.columns.length === 0) {
    errors.push('表格列配置为空')
  }
  
  // 验证API配置
  if (!config.api) {
    errors.push('缺少API配置')
  } else if (!config.api.methods) {
    errors.push('缺少API方法配置')
  }
  
  return {
    valid: errors.length === 0,
    errors
  }
}
