<template>
  <div class="gas-network-point">
    <!-- 搜索区域 -->
    <GasPointSearch @search="handleSearch" @reset="handleReset" />

    <!-- 按钮区域 -->
    <div class="table-header">
      <div class="button-group">
        <el-button type="primary" class="operation-btn" @click="handleAdd">新增</el-button>
        <el-button type="primary" class="operation-btn" @click="handleImport">导入</el-button>
        <el-button type="primary" class="operation-btn" @click="handleExport">导出</el-button>
      </div>
    </div>

    <!-- 管点对话框 -->
    <GasNetworkPointDialog v-model:visible="dialogVisible" :mode="dialogMode" :data="currentPointData"
      @success="handleDialogSuccess" />

    <!-- 表格区域 -->
    <div class="table-container">
      <el-table :data="tableData" style="width: 100%" :header-cell-style="headerCellStyle"
        :row-class-name="tableRowClassName" @row-click="handleRowClick" height="calc(100vh - 380px)">
        <el-table-column label="序号" min-width="60">
          <template #default="{ $index }">
            {{ (currentPage - 1) * pageSize + $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="pointCode" label="管点编码" min-width="100" />
        <el-table-column prop="pointType" label="管点类型" min-width="80" />
        <el-table-column prop="attachment" label="附属物" min-width="100" />
        <el-table-column prop="buriedDepth" label="埋深(m)" min-width="100" />
        <el-table-column prop="elevation" label="高程(m)" min-width="100" />
        <el-table-column prop="relatedPipeline" label="关联管线" min-width="120" />
        <el-table-column prop="manhole" label="所在窨井" min-width="120" />
        <el-table-column prop="location" label="所在道路" min-width="120" />
        <el-table-column prop="managementUnit" label="权属单位" min-width="120" />
        <el-table-column prop="installTime" label="安装时间" min-width="120" />
        <el-table-column prop="position" label="位置" min-width="120" />
        <el-table-column prop="useState" label="使用状态" min-width="80" />
        <el-table-column label="操作" min-width="220" fixed="right" align="center">
          <template #default="scope">
            <div class="operation-btns">
              <div class="operation-btn-row">
                <span class="operation-btn-text" @click.stop="handleEdit(scope.row)">编辑</span>
                <span class="operation-divider">|</span>
                <span class="operation-btn-text" @click.stop="handleDetail(scope.row)">详情</span>
                <span class="operation-divider">|</span>
                <span class="operation-btn-text" @click.stop="handleDelete(scope.row)">删除</span>
                <span class="operation-divider">|</span>
                <span class="operation-btn-text" @click.stop="handleLocation(scope.row)">定位</span>
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[10, 20, 30, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="total" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" :pager-count="5" />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { ElTable, ElTableColumn, ElButton, ElPagination, ElMessage, ElMessageBox } from 'element-plus';
import GasPointSearch from './GasPointSearch.vue';
import GasNetworkPointDialog from './GasNetworkPointDialog.vue';
import { getGasNetworkPointPage, getGasNetworkPointDetail, deleteGasNetworkPoint } from '@/api/gas';
import { POINT_TYPE_MAP, USAGE_STATUS_MAP } from '@/constants/gas';
import { misPosition } from '@/hooks/gishooks' //地图定位

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const tableData = ref([]);

// 查询参数
const queryParams = ref({});

// 处理搜索
const handleSearch = (formData) => {
  queryParams.value = formData;
  fetchPointData();
};

// 处理重置
const handleReset = () => {
  queryParams.value = {};
  fetchPointData();
};

// 获取管点数据
const fetchPointData = async () => {
  try {
    const params = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      ...queryParams.value
    };

    // 如果管点类型是全部，则不传该参数
    if (params.pointType === 'all') {
      delete params.pointType;
    }

    const res = await getGasNetworkPointPage(params);
    if (res && res.data) {
      // 处理返回数据，根据实际API返回结构调整
      tableData.value = res.data.records || [];
      total.value = res.data.total || 0;

      // 处理数据映射，将编码转换为显示文本
      tableData.value = tableData.value.map(item => ({
        ...item,
        pointType: item.pointType ? POINT_TYPE_MAP[item.pointType] : '',
        useState: item.useState ? USAGE_STATUS_MAP[item.useState] : ''
      }));
    }
  } catch (error) {
    console.error('获取管点数据失败:', error);
    ElMessage.error('获取管点数据失败');
    tableData.value = [];
    total.value = 0;
  }
};

// 处理分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val;
  currentPage.value = 1;
  fetchPointData();
};

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val;
  fetchPointData();
};

// 表头样式
const headerCellStyle = {
  background: '#F4F4F4',
  fontFamily: 'PingFangSC, PingFang SC',
  fontWeight: '500',
  fontSize: '14px',
  color: '#282828',
  height: '64px'
};

// 斑马纹和选中行样式
const tableRowClassName = ({ rowIndex }) => {
  if (rowIndex % 2 === 1) {
    return 'zebra-row';
  }
  return '';
};

// 对话框相关状态
const dialogVisible = ref(false);
const dialogMode = ref('add'); // 'add', 'edit', 'view'
const currentPointData = ref({});

// 处理对话框成功提交
const handleDialogSuccess = () => {
  fetchPointData();
};

// 处理行点击
const handleRowClick = (row) => {
  // 可以在这里实现行选中效果
  console.log('行数据:', row);
};

// 操作按钮处理函数
const handleAdd = () => {
  dialogMode.value = 'add';
  currentPointData.value = {};
  dialogVisible.value = true;
};

const handleImport = () => {
  console.log('导入');
  // 导入功能实现
};

const handleExport = () => {
  console.log('导出');
  // 导出功能实现
};

const handleEdit = async (row) => {
  try {
    // 获取详细数据
    const res = await getGasNetworkPointDetail(row.id);
    if (res && res.data) {
      dialogMode.value = 'edit';
      currentPointData.value = res.data;
      dialogVisible.value = true;
    } else {
      ElMessage.error('获取管点详情失败');
    }
  } catch (error) {
    console.error('获取管点详情失败:', error);
    ElMessage.error('获取管点详情失败');
  }
};

const handleDetail = async (row) => {
  try {
    // 获取详细数据
    const res = await getGasNetworkPointDetail(row.id);
    if (res && res.data) {
      dialogMode.value = 'view';
      currentPointData.value = res.data;
      dialogVisible.value = true;
    } else {
      ElMessage.error('获取管点详情失败');
    }
  } catch (error) {
    console.error('获取管点详情失败:', error);
    ElMessage.error('获取管点详情失败');
  }
};

const handleDelete = (row) => {
  ElMessageBox.confirm('确定要删除该管点吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await deleteGasNetworkPoint(row.id);
      if (res && res.code === 200) {
        ElMessage.success('删除成功');
        fetchPointData();
      } else {
        ElMessage.error(res?.msg || '删除失败');
      }
    } catch (error) {
      console.error('删除管点失败:', error);
      ElMessage.error('删除管点失败');
    }
  }).catch(() => {
    // 取消删除
  });
};

const handleLocation = (row) => {
  if (
    row.longitude &&
    row.longitude != '' &&
    row.latitude &&
    row.latitude != ''
  ) {
    misPosition.value = {
      longitude: row.longitude, //经度
      latitude: row.latitude //维度
    }
  } else {
    ElMessage.warning('没有经纬度，无法定位！')
  }
};

onMounted(() => {
  fetchPointData();
});
</script>
<style scoped>
.gas-network-point {
  width: 97%;
  height: 85%;
  display: flex;
  flex-direction: column;
}

/* 表格头部样式 */
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.button-group {
  display: flex;
  gap: 8px;
}

.operation-btn {
  width: 80px;
  height: 32px;
  background: #0277FD;
  border-radius: 2px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 14px;
  color: #FFFFFF;
  padding: 0;
}

/* 表格样式 */
.table-container {
  margin-bottom: 16px;
  flex: 1;
  width: 100%;
  overflow: hidden;
}

:deep(.el-table) {
  overflow-x: hidden !important;
}

:deep(.el-table th) {
  background-color: #F4F4F4;
  height: 64px;
}

:deep(.el-table tr) {
  height: 48px;
}

:deep(.zebra-row) {
  background-color: #F8FAFD;
}

:deep(.el-table__row:hover) {
  background: #EEF5FF !important;
}

:deep(.el-table__row.selected-row) {
  background: #EEF5FF !important;
}

.operation-btns {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.operation-btn-row {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 4px 0;
}

.operation-btn-text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #0086FF;
  cursor: pointer;
}

.operation-divider {
  margin: 0 4px;
  color: #CED3DA;
}

/* 分页样式 */
.pagination-container {
  display: flex;
  justify-content: flex-end;
  padding: 0;
  margin-top: 16px;
  min-height: 32px;
}

:deep(.el-pagination) {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #222222;
  padding-right: 0;
}

:deep(.el-pagination .el-pager li) {
  width: 24px;
  height: 24px;
  background: rgba(255, 255, 255, 0.99);
  border-radius: 2px;
  border: 1px solid #EEEEEE;
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.el-pagination .el-pager li.is-active) {
  width: 24px;
  height: 24px;
  background: #0086FF;
  border-radius: 2px;
  color: #FFFFFF;
  border: none;
}

/* 防止表格过长时遮挡分页组件 */
:deep(.el-table__body-wrapper) {
  overflow-y: auto !important;
  min-height: 200px;
}

/* 确保操作列文字不换行 */
:deep(.cell) {
  white-space: nowrap;
}
</style>