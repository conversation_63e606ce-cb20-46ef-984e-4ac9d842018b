// 桥梁管理系统相关常量定义

// ==================== 区域选项 ====================
// 行政区划选项数据（参考燃气管网点的区域选项结构）
export const AREA_OPTIONS = [
  {
    code: '371728',
    name: '东明县',
    children: [
      { code: '371728109000', name: '沙窝镇' },
      { code: '371728103000', name: '陆圈镇' },
      { code: '371728102000', name: '刘楼镇' },
      { code: '371728204000', name: '长兴集乡' },
      { code: '371728105000', name: '三春集镇' },
      { code: '371728104000', name: '马头镇' },
      { code: '371728106000', name: '大屯镇' },
      { code: '371728101000', name: '东明集镇' },
      { code: '371728110000', name: '小井镇' },
      { code: '371728107000', name: '武胜桥镇' },
      { code: '371728108000', name: '菜园集镇' },
      { code: '371728205000', name: '焦园乡' },
      { code: '371728001000', name: '城关街道办事处' },
      { code: '371728002000', name: '漯洼街道办事处' }
    ]
  }
];

// ==================== 桥段类型选项 ====================
// 桥段类型选项（主桥、引桥等）
export const SEGMENT_TYPE_OPTIONS = [
  { label: '主桥', value: '1' },
  { label: '引桥', value: '2' }
]

// 桥段类型映射
export const SEGMENT_TYPE_MAP = {
  '1': '主桥',
  '2': '引桥'
}

// ==================== 主梁类型选项 ====================
// 主梁类型选项（用于上部结构）
export const MAIN_BEAM_TYPE_OPTIONS = [
  { label: '预应力混凝土梁', value: '1' },
  { label: '钢梁', value: '2' },
  { label: '钢筋混凝土梁', value: '3' }
]

// 主梁类型映射
export const MAIN_BEAM_TYPE_MAP = {
  '1': '预应力混凝土梁',
  '2': '钢梁',
  '3': '钢筋混凝土梁'
}

// ==================== 支座类型选项 ====================
// 支座类型选项（用于上部结构）
export const SUPPORT_TYPE_OPTIONS = [
  { label: '板式橡胶支座', value: '1' },
  { label: '盆式支座', value: '2' },
  { label: '球型支座', value: '3' }
]

// 支座类型映射
export const SUPPORT_TYPE_MAP = {
  '1': '板式橡胶支座',
  '2': '盆式支座',
  '3': '球型支座'
}

// ==================== 伸缩缝类型选项 ====================
// 伸缩缝类型选项（用于上部结构）
export const SHRINKAGE_JOINT_TYPE_OPTIONS = [
  { label: '钢板伸缩缝', value: '1' },
  { label: '橡胶伸缩缝', value: '2' },
  { label: '模数式伸缩缝', value: '3' }
]

// 伸缩缝类型映射
export const SHRINKAGE_JOINT_TYPE_MAP = {
  '1': '钢板伸缩缝',
  '2': '橡胶伸缩缝',
  '3': '模数式伸缩缝'
}

// ==================== 桥墩类型选项 ====================
// 桥墩类型选项（用于下部结构）
export const PIER_TYPE_OPTIONS = [
  { label: '重力式桥墩', value: '1' },
  { label: '柱式桥墩', value: '2' },
  { label: '薄壁桥墩', value: '3' }
]

// 桥墩类型映射
export const PIER_TYPE_MAP = {
  '1': '重力式桥墩',
  '2': '柱式桥墩',
  '3': '薄壁桥墩'
}

// ==================== 桥台类型选项 ====================
// 桥台类型选项（用于下部结构）
export const ABUTMENT_TYPE_OPTIONS = [
  { label: '重力式桥台', value: '1' },
  { label: '轻型桥台', value: '2' },
  { label: '框架桥台', value: '3' }
]

// 桥台类型映射
export const ABUTMENT_TYPE_MAP = {
  '1': '重力式桥台',
  '2': '轻型桥台',
  '3': '框架桥台'
}

// ==================== 护岸类型选项 ====================
// 护岸类型选项（用于附属工程）
export const BANK_PROTECTION_TYPE_OPTIONS = [
  { label: '浆砌石护岸', value: '1' },
  { label: '混凝土护岸', value: '2' },
  { label: '生态护岸', value: '3' }
]

// 护岸类型映射
export const BANK_PROTECTION_TYPE_MAP = {
  '1': '浆砌石护岸',
  '2': '混凝土护岸',
  '3': '生态护岸'
}

// ==================== 引桥扶壁类型选项 ====================
// 引桥扶壁类型选项（用于附属工程）
export const CABLE_ANCHOR_WALL_TYPE_OPTIONS = [
  { label: '重力式扶壁', value: '1' },
  { label: '悬臂式扶壁', value: '2' }
]

// 引桥扶壁类型映射
export const CABLE_ANCHOR_WALL_TYPE_MAP = {
  '1': '重力式扶壁',
  '2': '悬臂式扶壁'
}

// ==================== 附件类型选项 ====================
// 附件类型选项（用于附件资料管理）
export const ATTACHMENT_TYPE_OPTIONS = [
  { label: '设计图纸', value: 1 },
  { label: '施工图纸', value: 2 },
  { label: '改扩建图纸', value: 3 },
  { label: '其它资料', value: 4 }
]

// 附件类型映射
export const ATTACHMENT_TYPE_MAP = {
  1: '设计图纸',
  2: '施工图纸',
  3: '改扩建图纸',
  4: '其它资料'
}

// ==================== 桥梁设备信息相关常量 ====================

// 设备类型 
// 倾角:5000101,挠度:5000102,梁端位移:5000103,加速度:5000104,应变:5000105,索力:5000106,吊杆力:5000107,温度:5000108,湿度:5000109,风速:5000110,风向:5000111,称重:5000112
export const DEVICE_TYPE_OPTIONS = [
  { label: '倾角', value: '5000101' },
  { label: '挠度', value: '5000102' },
  { label: '梁端位移', value: '5000103' },
  { label: '加速度', value: '5000104' },
  { label: '应变', value: '5000105' },
  { label: '索力', value: '5000106' },
  { label: '吊杆力', value: '5000107' },
  { label: '温度', value: '5000108' },
  { label: '湿度', value: '5000109' },
  { label: '风速', value: '5000110' },
  { label: '风向', value: '5000111' },
  { label: '称重', value: '5000112' },
  { label: '工讯静力水准仪', value: 'bridgeGxJl' },
  { label: '工讯结构温度计', value: 'bridgeGxWd' },
  { label: '工讯裂缝计', value: 'bridgeGxLf' },
]

// 监测指标
// 5004001-风速,5004002-风向,5004003-温度,5004004-湿度,5004005-地震,5004006-车重、轴重,5004007-车速,5004008-位移、变形,5004009-XY倾角,5004010-应变,5004011-支座反力,5004012-索力,5004013-车船撞击,5004014-支座偏位,5004015-裂缝,5004016-X向加速度,5004017-Y向加速度,5004018-Z向加速度
export const MONITOR_INDEX_OPTIONS = [
  { label: '风速', value: '5004001' },
  { label: '风向', value: '5004002' },
  { label: '温度', value: '5004003' },
  { label: '湿度', value: '5004004' },
  { label: '地震', value: '5004005' },
  { label: '车重、轴重', value: '5004006' },
  { label: '车速', value: '5004007' },
  { label: '位移/变形', value: '5004008' },
  { label: 'XY倾角', value: '5004009' },
  { label: '应变', value: '5004010' },
  { label: '支座反力', value: '5004011' },
  { label: '索力', value: '5004012' },
  { label: '车船撞击', value: '5004013' },
  { label: '支座偏位', value: '5004014' },
  { label: '裂缝', value: '5004015' },
  { label: 'X向加速度', value: '5004016' },
  { label: 'Y向加速度', value: '5004017' },
  { label: 'Z向加速度', value: '5004018' }
]

// 使用状态
export const DEVICE_STATUS_OPTIONS = [
  { label: '在线', value: 1 },
  { label: '离线', value: 0 }
]

// 监测对象
export const MONITOR_OBJECT_OPTIONS = [
  { label: '桥梁', value: '4001501' }
]

// 设备类型映射
export const DEVICE_TYPE_MAP = {    
  '5000101': '倾角',
  '5000102': '挠度',
  '5000103': '梁端位移',
  '5000104': '加速度',
  '5000105': '应变',
  '5000106': '索力',
  '5000107': '吊杆力',
  '5000108': '温度',
  '5000109': '湿度',
  '5000110': '风速',
  '5000111': '风向',
  '5000112': '称重',
  'bridgeGxJl': '工讯静力水准仪',
  'bridgeGxWd': '工讯结构温度计',
  'bridgeGxLf': '工讯裂缝计'
}

// 监测指标映射
export const MONITOR_INDEX_MAP = {
  '5004001': '风速',
  '5004002': '风向',  
  '5004003': '温度',
  '5004004': '湿度',
  '5004005': '地震',
  '5004006': '车重、轴重',
  '5004007': '车速',
  '5004008': '位移/变形',
  '5004009': 'XY倾角',  
  '5004010': '应变',
  '5004011': '支座反力',
  '5004012': '索力',
  '5004013': '车船撞击',
  '5004014': '支座偏位',
  '5004015': '裂缝',
  '5004016': 'X向加速度',
  '5004017': 'Y向加速度',
  '5004018': 'Z向加速度'
}

// 使用状态映射
export const DEVICE_STATUS_MAP = {
  1: '在线',
  0: '离线'
}

// 监测对象映射
export const MONITOR_OBJECT_MAP = {
  '4001501': '桥梁'
}

// ==================== 桥梁基础信息相关常量 ====================

// 行业属性type（4001101-公路桥梁、4001102-铁路桥梁、4001103-城市桥梁、4001104-人行桥、4001105-特种桥梁）	
export const INDUSTRY_ATTRIBUTE_TYPE_OPTIONS = [
  { label: '公路桥梁', value: '4001101' },
  { label: '铁路桥梁', value: '4001102' },
  { label: '城市桥梁', value: '4001103' },
  { label: '人行桥', value: '4001104' },
  { label: '特种桥梁', value: '4001105' }
]

// 行业属性type映射
export const INDUSTRY_ATTRIBUTE_TYPE_MAP = {
  '4001101': '公路桥梁',
  '4001102': '铁路桥梁',
  '4001103': '城市桥梁',
  '4001104': '人行桥',
  '4001105': '特种桥梁'
}

// 养护等级type（4004101-Ⅰ等养护、4004102-Ⅱ等养护、4004103-Ⅲ等养护、4004104-Ⅳ等养护）
export const MAINTENANCE_LEVEL_TYPE_OPTIONS = [
  { label: 'Ⅰ等养护', value: '4004101' },
  { label: 'Ⅱ等养护', value: '4004102' },
  { label: 'Ⅲ等养护', value: '4004103' },
  { label: 'Ⅳ等养护', value: '4004104' }
]

// 养护等级type映射
export const MAINTENANCE_LEVEL_TYPE_MAP = {
  '4004101': 'Ⅰ等养护',
  '4004102': 'Ⅱ等养护',
  '4004103': 'Ⅲ等养护',
  '4004104': 'Ⅳ等养护'
}

// 养护类别type（4003101-Ⅰ类养护、4003102-Ⅱ类养护、4003103-Ⅲ类养护、4003104-Ⅳ类养护、4003105-Ⅴ类养护）
export const MAINTENANCE_CATEGORY_TYPE_OPTIONS = [
  { label: 'Ⅰ类养护', value: '4003101' },
  { label: 'Ⅱ类养护', value: '4003102' },
  { label: 'Ⅲ类养护', value: '4003103' },
  { label: 'Ⅳ类养护', value: '4003104' },
  { label: 'Ⅴ类养护', value: '4003105' }
]

// 养护类别type映射
export const MAINTENANCE_CATEGORY_TYPE_MAP = {
  '4003101': 'Ⅰ类养护',
  '4003102': 'Ⅱ类养护',
  '4003103': 'Ⅲ类养护',
  '4003104': 'Ⅳ类养护',
  '4003105': 'Ⅴ类养护'
}

// 结构类型type（4000101-斜拉桥、4000102-悬索桥、4000103-梁式桥、4000104-拱式桥、4000105-刚构桥、4000106-组合体系桥）
export const STRUCTURE_TYPE_OPTIONS = [
  { label: '斜拉桥', value: '4000101' },
  { label: '悬索桥', value: '4000102' },
  { label: '梁式桥', value: '4000103' },
  { label: '拱式桥', value: '4000104' },
  { label: '刚构桥', value: '4000105' },
  { label: '组合体系桥', value: '4000106' }
]

// 结构类型type映射
export const STRUCTURE_TYPE_MAP = {
  '4000101': '斜拉桥',
  '4000102': '悬索桥',
  '4000103': '梁式桥',
  '4000104': '拱式桥',
  '4000105': '刚构桥',
  '4000106': '组合体系桥'
}

// 技术状况type（4002101-完好、4002102-良好、4002103-合格、4002104-不合格、4002105-危险）
export const TECHNICAL_CONDITION_TYPE_OPTIONS = [
  { label: '完好', value: '4002101' },
  { label: '良好', value: '4002102' },
  { label: '合格', value: '4002103' },
  { label: '不合格', value: '4002104' },
  { label: '危险', value: '4002105' }
]

// 技术状况type映射
export const TECHNICAL_CONDITION_TYPE_MAP = {
  '4002101': '完好',
  '4002102': '良好',
  '4002103': '合格',
  '4002104': '不合格',
  '4002105': '危险'
}

// 所属部件type（4010101-主梁、4010102-挂梁）
export const SUBJECT_PART_TYPE_OPTIONS = [
  { label: '主梁', value: '4010101' },
  { label: '挂梁', value: '4010102' }
]

// 所属部件type映射
export const SUBJECT_PART_TYPE_MAP = {
  '4010101': '主梁',
  '4010102': '挂梁'
}

// ==================== 桥梁监测报警阈值管理相关常量 ====================

// 生效状态选项
export const ENABLED_STATUS_OPTIONS = [
  { label: '是', value: true },
  { label: '否', value: false }
]

// 生效状态映射
export const ENABLED_STATUS_MAP = {
  true: '是',
  false: '否'
}

// 监管部门选项
export const SUPERVISE_DEPARTMENTS = [
  { label: '市政管理部门', value: '5001001' },
  { label: '交通运输部门', value: '5001002' },
  { label: '住建部门', value: '5001003' },
  { label: '应急管理部门', value: '5001004' },
  { label: '环保部门', value: '5001005' }
]

// ==================== 桥梁报警相关常量 - 开始标记 ====================

// 报警级别选项
export const BRIDGE_ALARM_LEVEL_OPTIONS = [
  { label: '全部', value: '' },
  { label: '一级', value: 4003601 },
  { label: '二级', value: 4003602 },
  { label: '三级', value: 4003603 },
  { label: '四级', value: 4003604 }
];

// 报警级别映射
export const BRIDGE_ALARM_LEVEL_MAP = {
  4003601: '一级',
  4003602: '二级',
  4003603: '三级',
  4003604: '四级'
};

// 报警状态选项
export const BRIDGE_ALARM_STATUS_OPTIONS = [
  { label: '全部', value: '' },
  { label: '待确认', value: 4003701 },
  { label: '误报', value: 4003702 },
  { label: '待处置', value: 4003703 },
  { label: '处置中', value: 4003704 },
  { label: '已处置', value: 4003705 },
  { label: '已归档', value: 4003706 }
];

// 报警状态映射
export const BRIDGE_ALARM_STATUS_MAP = {
  4003701: '待确认',
  4003702: '误报',
  4003703: '待处置',
  4003704: '处置中',
  4003705: '已处置',
  4003706: '已归档'
};

// 报警类型选项
export const BRIDGE_ALARM_TYPE_OPTIONS = [
  { label: '全部', value: '' },
  { label: '桥梁温度监测报警', value: 4003101 },
  { label: '桥梁湿度监测报警', value: 4003102 },
  { label: '桥梁位移/变形监测报警', value: 4003103 },
  { label: '桥梁应变监测报警', value: 4003104 },
  { label: '桥梁裂缝监测报警', value: 4003105 },
  { label: '桥梁挠度监测报警', value: 4003106 }
];

// 报警类型映射
export const BRIDGE_ALARM_TYPE_MAP = {
  4003101: '桥梁温度监测报警',
  4003102: '桥梁湿度监测报警',
  4003103: '桥梁位移/变形监测报警',
  4003104: '桥梁应变监测报警',
  4003105: '桥梁裂缝监测报警',
  4003106: '桥梁挠度监测报警'
};

// 报警来源选项
export const BRIDGE_ALARM_SOURCE_OPTIONS = [
  { label: '全部', value: '' },
  { label: '设备监测报警', value: '设备监测报警' },
  { label: '企业自报报警', value: '企业自报报警' },
  { label: '系统监测', value: '系统监测' }
];

// 报警来源映射
export const BRIDGE_ALARM_SOURCE_MAP = {
  '设备监测报警': '设备监测报警',
  '企业自报报警': '企业自报报警',
  '系统监测': '系统监测'
};

// // 监测指标编码选项
// export const BRIDGE_MONITOR_INDEX_CODE_OPTIONS = [
//   { label: '全部', value: '' },
//   { label: '温度', value: 5002001 },
//   { label: '湿度', value: 5002002 },
//   { label: '位移', value: 5002003 },
//   { label: '应变', value: 5002004 },
//   { label: '裂缝', value: 5002005 },
//   { label: '挠度', value: 5002006 },
//   { label: '振动', value: 5002007 },
//   { label: '倾斜', value: 5002008 }
// ];

// // 监测指标编码映射
// export const BRIDGE_MONITOR_INDEX_CODE_MAP = {
//   5002001: '温度',
//   5002002: '湿度',
//   5002003: '位移',
//   5002004: '应变',
//   5002005: '裂缝',
//   5002006: '挠度',
//   5002007: '振动',
//   5002008: '倾斜'
// };

// 处置状态选项
export const BRIDGE_HANDLE_STATUS_OPTIONS = [
  { label: '处置中', value: 4002101 },
  { label: '已处置', value: 4002102 }
];

// 处置状态映射
export const BRIDGE_HANDLE_STATUS_MAP = {
  4002101: '处置中',
  4002102: '已处置'
};

// 确认结果选项
export const BRIDGE_CONFIRM_RESULT_OPTIONS = [
  { label: '真实报警', value: 4002001 },
  { label: '误报', value: 4002002 }
];

// 确认结果映射
export const BRIDGE_CONFIRM_RESULT_MAP = {
  4002001: '真实报警',
  4002002: '误报'
};

// ==================== 桥梁报警相关常量 - 结束标记 ====================


