<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="1000px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="drainage-manhole-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      :disabled="mode === 'view'"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="窨井编码" prop="wellCode">
            <el-input v-model="formData.wellCode" placeholder="请输入窨井编码" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="窨井类型" prop="wellType">
            <el-select v-model="formData.wellType" placeholder="请选择" class="w-full">
              <el-option v-for="item in wellTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="井深 (m)" prop="wellDepth">
            <el-input-number v-model="formData.wellDepth" :min="0" :precision="2" class="w-full" placeholder="请输入井深" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所在道路" prop="roadName">
            <el-input v-model="formData.roadName" placeholder="请输入所在道路" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="窨井形状" prop="wellShape">
            <el-select v-model="formData.wellShape" placeholder="请选择" class="w-full">
              <el-option v-for="item in wellShapeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="窨井材质" prop="wellMaterial">
            <el-select v-model="formData.wellMaterial" placeholder="请选择" class="w-full">
              <el-option v-for="item in wellMaterialOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="窨井尺寸" prop="wellSize">
            <el-input v-model="formData.wellSize" placeholder="请输入窨井尺寸" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="井室规格" prop="wellRoomStandard">
            <el-input v-model="formData.wellRoomStandard" placeholder="请输入井室规格" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="特征点" prop="feature">
            <el-input v-model="formData.feature" placeholder="请输入特征点" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="附属物" prop="attachedFacilities">
            <el-input v-model="formData.attachedFacilities" placeholder="请输入附属物" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="埋深 (m)" prop="buriedDepth">
            <el-input-number v-model="formData.buriedDepth" :min="0" :precision="2" class="w-full" placeholder="请输入埋深" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="高程 (m)" prop="elevation">
            <el-input-number v-model="formData.elevation" :min="0" :precision="2" class="w-full" placeholder="请输入高程" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="关联管线" prop="pipelineCode">
            <el-select v-model="formData.pipelineCode" placeholder="请选择关联管线" class="w-full" @change="handlePipelineChange">
              <el-option v-for="item in pipelineList" :key="item.id" :label="item.pipelineCode" :value="item.pipelineCode" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="权属单位" prop="managementUnit">
            <el-select v-model="formData.managementUnit" placeholder="请选择" class="w-full">
              <el-option v-for="unit in managementUnits" :key="unit.id" :label="unit.enterpriseName" :value="unit.enterpriseName" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="建设时间" prop="constructionTime">
            <el-date-picker
              v-model="formData.constructionTime"
              type="date"
              placeholder="请选择建设时间"
              class="w-full"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="位置" prop="address">
            <div class="flex items-center">
              <el-cascader
                v-model="formData.town"
                :options="areaOptions"
                :props="{
                  value: 'code',
                  label: 'name',
                  children: 'children'
                }"
                placeholder="请选择所属区域"
                class="w-full"
                @change="handleAreaChange"
              />
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="坐标" prop="coords">
            <div class="flex items-center">
              <el-input v-model="formData.longitude" placeholder="经度" class="mr-2" />
              <el-input v-model="formData.latitude" placeholder="纬度" />
              <el-button type="primary" class="ml-2" icon="Location" circle @click="openMapPicker"></el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="备注" prop="remarks">
            <el-input
              v-model="formData.remarks"
              type="textarea"
              :rows="3"
              placeholder="请输入备注信息"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="mode !== 'view'">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, nextTick, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { saveManholeWell, updateManholeWell, getManholeWellDetail, getEnterpriseList, getPipelineList } from '@/api/drainage';
import { WELL_TYPE_OPTIONS, WELL_SHAPE_OPTIONS, WELL_MATERIAL_OPTIONS } from '@/constants/drainage';
import { AREA_OPTIONS } from '@/constants/gas';
import { collectShow } from '@/hooks/gishooks';
import bus from '@/utils/mitt';
import moment from 'moment';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add', // 'add', 'edit', 'view'
    validator: (value) => ['add', 'edit', 'view'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'success']);

// 表单引用
const formRef = ref(null);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 对话框标题
const dialogTitle = computed(() => {
  const titles = {
    add: '新增窨井',
    edit: '编辑窨井',
    view: '窨井详情'
  };
  return titles[props.mode] || '窨井信息';
});

// 过滤掉"全部"选项
const wellTypeOptions = WELL_TYPE_OPTIONS.filter(item => item.value !== '');
const wellShapeOptions = WELL_SHAPE_OPTIONS.filter(item => item.value !== '');
const wellMaterialOptions = WELL_MATERIAL_OPTIONS.filter(item => item.value !== '');

// 行政区划选项
const areaOptions = ref(AREA_OPTIONS);

// 表单数据
const formData = reactive({
  id: '',
  wellCode: '',
  wellType: '',
  wellTypeName: '',
  wellDepth: 0,
  roadName: '',
  wellShape: '',
  wellShapeName: '',
  wellMaterial: '',
  wellMaterialName: '',
  wellSize: '',
  wellRoomStandard: '',
  feature: '',
  attachedFacilities: '',
  buriedDepth: 0,
  elevation: 0,
  pipelineCode: '',
  pipelineId: '',
  managementUnit: '',
  managementUnitName: '',
  constructionTime: '',
  county: '371728',
  countyName: '东明县',
  town: '',
  townName: '',
  address: '',
  longitude: '',
  latitude: '',
  remarks: '',
  geomText: ''
});

// 表单验证规则
const formRules = {
  wellCode: [{ required: true, message: '请输入窨井编码', trigger: 'blur' }],
  wellType: [{ required: true, message: '请选择窨井类型', trigger: 'change' }],
  wellDepth: [{ required: true, message: '请输入井深', trigger: 'blur' }],
  wellShape: [{ required: true, message: '请选择窨井形状', trigger: 'change' }],
  wellMaterial: [{ required: true, message: '请选择窨井材质', trigger: 'change' }],
  managementUnit: [{ required: true, message: '请选择权属单位', trigger: 'change' }]
};

// 权属单位列表
const managementUnits = ref([]);
// 管线列表
const pipelineList = ref([]);

// 获取权属单位列表
const fetchManagementUnits = async () => {
  try {
    const res = await getEnterpriseList();
    if (res && res.code === 200) {
      managementUnits.value = res.data || [];
    }
  } catch (error) {
    console.error('获取权属单位列表失败', error);
  }
};

// 获取管线列表
const fetchPipelineList = async () => {
  try {
    const res = await getPipelineList({});
    if (res && res.code === 200) {
      pipelineList.value = res.data || [];
    }
  } catch (error) {
    console.error('获取管线列表失败', error);
  }
};

// 监听表单数据变化，自动设置名称字段
watch(() => formData.wellType, (val) => {
  if (val) {
    const selected = wellTypeOptions.find(item => item.value === val);
    formData.wellTypeName = selected ? selected.label : '';
  }
}, { immediate: true });

watch(() => formData.wellShape, (val) => {
  if (val) {
    const selected = wellShapeOptions.find(item => item.value === val);
    formData.wellShapeName = selected ? selected.label : '';
  }
}, { immediate: true });

watch(() => formData.wellMaterial, (val) => {
  if (val) {
    const selected = wellMaterialOptions.find(item => item.value === val);
    formData.wellMaterialName = selected ? selected.label : '';
  }
}, { immediate: true });

watch(() => formData.managementUnit, (val) => {
  formData.managementUnitName = val || '';
}, { immediate: true });

// 地图选点事件处理函数
const handleCollectLocation = (params) => {
  nextTick(() => {
    formData.longitude = params.longitude;
    formData.latitude = params.latitude;
  });
};

// 打开地图选点
const openMapPicker = () => {
  collectShow.value = true; // 激活采集点位窗口
  // 先移除可能存在的旧监听器
  bus.off("getCollectLocation", handleCollectLocation);
  // 添加新的监听器
  bus.on("getCollectLocation", handleCollectLocation);
};

// 处理管线选择变化
const handlePipelineChange = (value) => {
  const selectedPipeline = pipelineList.value.find(item => item.pipelineCode === value);
  if (selectedPipeline) {
    formData.pipelineId = selectedPipeline.id;
  }
};

// 处理区域选择变化
const handleAreaChange = (value) => {
  if (value && value.length > 0) {
    formData.town = value[value.length - 1];
    // 更新区域名称
    const selectedArea = findAreaByCode(areaOptions.value, formData.town);
    if (selectedArea) {
      formData.townName = selectedArea.name;
    }
  }
};

// 根据区域代码查找区域信息
const findAreaByCode = (areas, code) => {
  for (const area of areas) {
    if (area.code === code) {
      return area;
    }
    if (area.children) {
      const found = findAreaByCode(area.children, code);
      if (found) return found;
    }
  }
  return null;
};

// 获取窨井详情
const fetchManholeDetail = async (id) => {
  try {
    const res = await getManholeWellDetail(id);
    if (res && res.code === 200) {
      const data = res.data;
      
      // 将详情数据映射到表单
      Object.keys(formData).forEach(key => {
        if (data[key] !== undefined && data[key] !== null) {
          formData[key] = data[key];
        }
      });

      // 处理日期格式
      if (data.constructionTime) {
        if (typeof data.constructionTime === 'object' && data.constructionTime.time) {
          formData.constructionTime = new Date(data.constructionTime.time);
        } else if (typeof data.constructionTime === 'string') {
          formData.constructionTime = new Date(data.constructionTime);
        }
      }
    } else {
      ElMessage.error(res?.msg || '获取窨井详情失败');
    }
  } catch (error) {
    console.error('获取窨井详情失败:', error);
    ElMessage.error('获取窨井详情失败');
  }
};

// 组件挂载时获取数据
onMounted(() => {
  fetchManagementUnits();
  fetchPipelineList();
});

// 监听props.data变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    if (props.mode === 'edit' || props.mode === 'view') {
      // 编辑模式或查看模式，需要获取详情
      if (newVal.id) {
        fetchManholeDetail(newVal.id);
      }
    } else {
      // 新增模式，直接使用传入的默认数据
      Object.keys(formData).forEach(key => {
        if (newVal[key] !== undefined) {
          formData[key] = newVal[key];
        }
      });
    }
  }
}, { immediate: true, deep: true });

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  // 重置表单
  if (formRef.value) {
    formRef.value.resetFields();
  }
  // 重置表单数据
  Object.keys(formData).forEach(key => {
    if (typeof formData[key] === 'number') {
      formData[key] = 0;
    } else if (key === 'constructionTime') {
      formData[key] = '';
    } else {
      formData[key] = '';
    }
  });
  // 恢复默认值
  formData.county = '371728';
  formData.countyName = '东明县';
  
  // 移除地图监听器
  bus.off("getCollectLocation", handleCollectLocation);
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;
  
  try {
    await formRef.value.validate();
    
    // 准备提交数据
    const submitData = { ...formData };
    
    // 处理日期格式
    if (submitData.constructionTime) {
      submitData.constructionTime = moment(submitData.constructionTime).format('YYYY-MM-DD HH:mm:ss');
    }
    
    // 确保数值类型字段为数值
    if (submitData.wellType) submitData.wellType = Number(submitData.wellType);
    if (submitData.wellMaterial) submitData.wellMaterial = Number(submitData.wellMaterial);
    if (submitData.wellShape) submitData.wellShape = Number(submitData.wellShape);
    if (submitData.longitude) submitData.longitude = Number(submitData.longitude);
    if (submitData.latitude) submitData.latitude = Number(submitData.latitude);
    if (submitData.wellDepth) submitData.wellDepth = Number(submitData.wellDepth);
    if (submitData.buriedDepth) submitData.buriedDepth = Number(submitData.buriedDepth);
    if (submitData.elevation) submitData.elevation = Number(submitData.elevation);
    
    // 提交数据
    let res;
    if (props.mode === 'add') {
      res = await saveManholeWell(submitData);
    } else if (props.mode === 'edit') {
      res = await updateManholeWell(submitData);
    }
    
    if (res && res.code === 200) {
      ElMessage.success(props.mode === 'add' ? '新增成功' : '更新成功');
      emit('success');
      handleClose();
    } else {
      ElMessage.error(res?.msg || (props.mode === 'add' ? '新增失败' : '更新失败'));
    }
  } catch (error) {
    console.error('表单验证失败', error);
  }
};
</script>

<style scoped>
.drainage-manhole-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-form-item__label::before) {
  content: '*';
  color: #ff4d4f;
  margin-right: 4px;
}

:deep(.el-form-item:not([required]) .el-form-item__label::before) {
  content: '';
  margin-right: 0;
}

:deep(.el-input__wrapper),
:deep(.el-select__input) {
  border-radius: 2px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  background-color: #0277FD;
  border-color: #0277FD;
}

:deep(.el-button--primary:hover) {
  background-color: #1890ff;
  border-color: #1890ff;
}

.w-full {
  width: 100%;
}

.w-30 {
  width: 120px;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.mr-2 {
  margin-right: 8px;
}

.ml-2 {
  margin-left: 8px;
}
</style> 