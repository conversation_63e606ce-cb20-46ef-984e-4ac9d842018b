import { ref } from "vue";

const position = {
  id: "",
  type: "",
  label: "",
  longitude: "",
  latitude: "",
};

const location = {
  longitude: "",
  latitude: "",
};

const screenPosition = ref(position); // 大屏定位参数初始化
const cardLayer = ref(""); // 前端选中卡片的设备图层初始化
const misPosition = ref(position); // mis端定位参数初始化
const collectLocation = ref(location); // 从地图采集点位
const collectShow = ref(false); // 是否开启地图采集
const defaultCheckedLayers = ref([]); // 设置默认选中图层编号集合

const mapLoaded = ref(false); // 地图加载状态

export {
  screenPosition,
  misPosition,
  collectShow,
  collectLocation,
  defaultCheckedLayers,
  cardLayer,
  mapLoaded,
};
