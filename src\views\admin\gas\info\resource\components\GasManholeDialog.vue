<template>
  <el-dialog v-model="dialogVisible" :title="dialogTitle" width="1000px" :close-on-click-modal="false"
    :before-close="handleClose" class="gas-manhole-dialog">
    <el-form ref="formRef" :model="formData" :rules="formRules" label-width="120px" :disabled="mode === 'view'">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="窨井编码" prop="wellCode">
            <el-input v-model="formData.wellCode" placeholder="请输入窨井编码" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="井深(m)" prop="wellDepth">
            <el-input v-model="formData.wellDepth" placeholder="请输入井深" type="number" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="所在道路" prop="roadName">
            <el-input v-model="formData.roadName" placeholder="请输入所在道路" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="窨井形状" prop="wellShape">
            <el-select v-model="formData.wellShape" placeholder="请选择" class="w-full">
              <el-option v-for="item in wellShapeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="窨井材质" prop="wellMaterial">
            <el-select v-model="formData.wellMaterial" placeholder="请选择" class="w-full">
              <el-option v-for="item in wellMaterialOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="井盖尺寸" prop="wellSize">
            <el-input v-model="formData.wellSize" placeholder="请输入井盖尺寸" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="井室规格" prop="wellSpec">
            <el-input v-model="formData.wellSpec" placeholder="请输入井室规格" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="特征点" prop="feature">
            <el-input v-model="formData.feature" placeholder="请输入特征点" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="附属物" prop="attachedFacilities">
            <el-input v-model="formData.attachedFacilities" placeholder="请输入附属物" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="埋深(m)" prop="buriedDepth">
            <el-input v-model="formData.buriedDepth" placeholder="请输入埋深" type="number" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="高程(m)" prop="elevation">
            <el-input v-model="formData.elevation" placeholder="请输入高程" type="number" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="关联管线" prop="connectedPipeline">
            <el-select v-model="formData.connectedPipeline" placeholder="请选择" class="w-full">
              <el-option label="待选择" value="" />
              <!-- 这里需要添加管线选项，可以从API获取 -->
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="权属单位" prop="managementUnit">
            <el-select v-model="formData.managementUnit" placeholder="请选择" class="w-full">
              <el-option v-for="unit in managementUnits" :key="unit.id" :label="unit.enterpriseName"
                :value="unit.enterpriseName" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="建设时间" prop="installTime">
            <el-date-picker v-model="formData.installTime" type="date" placeholder="请选择建设时间" format="YYYY-MM-DD"
              value-format="YYYY-MM-DD" class="w-full" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="位置" prop="location">
            <div class="flex items-center">
              <el-select v-model="formData.county" placeholder="区/县" class="location-select mr-2">
                <el-option v-for="area in areaOptions" :key="area.code" :label="area.name" :value="area.code" @change="handleCountyChange" />
              </el-select>
              <el-select v-model="formData.town" placeholder="镇/街道" class="location-select mr-2">
                <el-option v-for="town in townOptions" :key="town.code" :label="town.name" :value="town.code" />
              </el-select>
              <el-input v-model="formData.address" placeholder="详细地址" class="flex-1" />
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="位置坐标" prop="coordinates">
            <div class="flex items-center">
              <el-input v-model="formData.longitude" placeholder="经度" class="mr-2" />
              <el-input v-model="formData.latitude" placeholder="纬度" />
              <el-button type="primary" class="ml-2" icon="Location" circle @click="openMapPicker"
                :disabled="mode === 'view'"></el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="备注" prop="remarks">
            <el-input v-model="formData.remarks" type="textarea" :rows="3" placeholder="请输入备注信息" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="mode !== 'view'">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted, onUnmounted, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import { saveGasWell, updateGasWell, getGasWellDetail, getManagementUnits } from '@/api/gas';
import { WELL_MATERIALS, WELL_SHAPES, WELL_MATERIAL_MAP, WELL_SHAPE_MAP, AREA_OPTIONS } from '@/constants/gas';
import moment from 'moment';
import { collectShow } from "@/hooks/gishooks";
import bus from '@/utils/mitt';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add', // 'add', 'edit', 'view'
    validator: (value) => ['add', 'edit', 'view'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'success']);

// 表单引用
const formRef = ref(null);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 对话框标题
const dialogTitle = computed(() => {
  const titles = {
    add: '新增窨井',
    edit: '编辑窨井',
    view: '窨井详情'
  };
  return titles[props.mode] || '窨井信息';
});

// 表单数据
const formData = reactive({
  id: '',
  wellCode: '',
  wellDepth: '',
  roadName: '',
  wellShape: '',
  wellShapeName: '',
  wellMaterial: '',
  wellMaterialName: '',
  wellSize: '',
  wellSpec: '',
  feature: '',
  attachedFacilities: '',
  buriedDepth: '',
  elevation: '',
  connectedPipeline: '',
  managementUnit: '',
  managementUnitName: '',
  installTime: '',
  county: '',
  countyName: '',
  town: '',
  townName: '',
  address: '',
  longitude: '',
  latitude: '',
  remarks: '',
  usageStatus: '',
  usageStatusName: ''
});

// 表单验证规则
const formRules = {
  wellCode: [{ required: true, message: '请输入窨井编码', trigger: 'blur' }],
  wellDepth: [{ required: true, message: '请输入井深', trigger: 'blur' }],
  roadName: [{ required: true, message: '请输入所在道路', trigger: 'blur' }],
  wellShape: [{ required: true, message: '请选择窨井形状', trigger: 'change' }],
  wellMaterial: [{ required: true, message: '请选择窨井材质', trigger: 'change' }],
  managementUnit: [{ required: true, message: '请选择权属单位', trigger: 'change' }],
  longitude: [{ required: true, message: '请输入经度', trigger: 'blur' }],
  latitude: [{ required: true, message: '请输入纬度', trigger: 'blur' }]
};

// 窨井材质选项
const wellMaterialOptions = WELL_MATERIALS;

// 窨井形状选项
const wellShapeOptions = WELL_SHAPES;

// 权属单位列表
const managementUnits = ref([]);

// 行政区划选项
const areaOptions = ref(AREA_OPTIONS);

// 镇/街道选项
const townOptions = ref([]);

// 获取权属单位列表
const fetchManagementUnits = async () => {
  try {
    const res = await getManagementUnits();
    if (res && res.code === 200) {
      managementUnits.value = res.data || [];
    }
  } catch (error) {
    console.error('获取权属单位列表失败', error);
  }
};

// 处理区县选择变化
const handleCountyChange = () => {
  // 根据选择的区县，过滤出对应的镇/街道选项
  const selectedCounty = areaOptions.value.find(item => item.code === formData.county);
  if (selectedCounty && selectedCounty.children) {
    townOptions.value = selectedCounty.children;
    formData.countyName = selectedCounty.name;
  } else {
    townOptions.value = [];
    formData.countyName = '';
  }
  formData.town = '';
  formData.townName = '';
};

// 监听区县变化
watch(() => formData.county, handleCountyChange);

// 监听镇/街道变化
watch(() => formData.town, (newVal) => {
  if (newVal) {
    const selectedTown = townOptions.value.find(item => item.code === newVal);
    if (selectedTown) {
      formData.townName = selectedTown.name;
    }
  } else {
    formData.townName = '';
  }
});

// 地图选点事件处理函数
const handleCollectLocation = (params) => {
  nextTick(() => {
    formData.longitude = params.longitude;
    formData.latitude = params.latitude;
  });
};

// 打开地图选点
const openMapPicker = () => {
  collectShow.value = true; // 激活采集点位窗口
  // 先移除可能存在的旧监听器
  bus.off("getCollectLocation", handleCollectLocation);
  // 添加新的监听器
  bus.on("getCollectLocation", handleCollectLocation);
};

// 组件卸载时清理事件监听
onUnmounted(() => {
  bus.off("getCollectLocation", handleCollectLocation);
});

// 监听窨井材质变化
watch(() => formData.wellMaterial, (newVal) => {
  if (newVal) {
    formData.wellMaterialName = WELL_MATERIAL_MAP[newVal] || '';
  } else {
    formData.wellMaterialName = '';
  }
}, { immediate: true });

// 监听窨井形状变化
watch(() => formData.wellShape, (newVal) => {
  if (newVal) {
    formData.wellShapeName = WELL_SHAPE_MAP[newVal] || '';
  } else {
    formData.wellShapeName = '';
  }
}, { immediate: true });

// 获取窨井详情
const fetchManholeDetail = async (id) => {
  try {
    const res = await getGasWellDetail(id);
    if (res && (res.code === 200 || res.code === 0)) {
      const data = res.data;
      
      // 将详情数据映射到表单
      Object.keys(formData).forEach(key => {
        if (data[key] !== undefined) {
          formData[key] = data[key];
        }
      });
      
      // 如果有区县信息，加载对应的镇/街道选项
      if (formData.county) {
        handleCountyChange();
      }
    }
  } catch (error) {
    console.error('获取窨井详情失败', error);
    ElMessage.error('获取窨井详情失败');
  }
};

// 监听props.data变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    if (props.mode === 'edit' || props.mode === 'view') {
      // 编辑模式或查看模式，需要先获取详情
      if (newVal.id) {
        fetchManholeDetail(newVal.id);
      }
    } else {
      // 新增模式，直接使用传入的默认数据
      Object.keys(formData).forEach(key => {
        if (newVal[key] !== undefined) {
          formData[key] = newVal[key];
        }
      });
    }
  }
}, { immediate: true, deep: true });

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  // 重置表单
  if (formRef.value) {
    formRef.value.resetFields();
  }
  // 重置表单数据
  Object.keys(formData).forEach(key => {
    if (typeof formData[key] === 'number') {
      formData[key] = '';
    } else if (key === 'installTime') {
      formData[key] = null;
    } else {
      formData[key] = '';
    }
  });
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();

    // 准备提交数据
    const submitData = { ...formData };

    // 确保数值类型字段为数值
    if (submitData.wellDepth) submitData.wellDepth = Number(submitData.wellDepth);
    if (submitData.buriedDepth) submitData.buriedDepth = Number(submitData.buriedDepth);
    if (submitData.elevation) submitData.elevation = Number(submitData.elevation);
    if (submitData.longitude) submitData.longitude = Number(submitData.longitude);
    if (submitData.latitude) submitData.latitude = Number(submitData.latitude);

    // 提交数据
    let res;
    if (props.mode === 'add') {
      res = await saveGasWell(submitData);
    } else if (props.mode === 'edit') {
      res = await updateGasWell(submitData);
    }

    if (res && (res.code === 200 || res.code === 0)) {
      ElMessage.success(props.mode === 'add' ? '新增成功' : '更新成功');
      emit('success');
      handleClose();
    } else {
      ElMessage.error(res?.message || (props.mode === 'add' ? '新增失败' : '更新失败'));
    }
  } catch (error) {
    console.error('表单验证失败', error);
  }
};

// 组件挂载时获取数据
onMounted(() => {
  fetchManagementUnits();
});
</script>

<style scoped>
.gas-manhole-dialog {
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "Helvetica Neue", Arial, sans-serif;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-form-item__label::before) {
  content: '*';
  color: #ff4d4f;
  margin-right: 4px;
}

:deep(.el-form-item:not([required]) .el-form-item__label::before) {
  content: '';
  margin-right: 0;
}

:deep(.el-input__inner),
:deep(.el-select__input) {
  border-radius: 6px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 6px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  background-color: #0277FD;
  border-color: #0277FD;
}

:deep(.el-button--primary:hover) {
  background-color: #1890ff;
  border-color: #1890ff;
}

.w-full {
  width: 100%;
}

.location-select {
  width: 120px;
}

.coordinate-input {
  width: 120px;
}

.flex {
  display: flex;
}

.flex-1 {
  flex: 1;
}

.items-center {
  align-items: center;
}

.ml-2 {
  margin-left: 8px;
}

.mr-2 {
  margin-right: 8px;
}
</style> 