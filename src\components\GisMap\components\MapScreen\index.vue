<template>
  <InitMap />
  <Tools />
  <div id="bubble" class="custom-bubble" v-show="showBubble">
        <div class="bubble-background">
            <div class="bubble-top">
                <a title="关闭模型属性窗体" @click="closeBubble"></a>
                <h3 class="bubble-title">属性信息</h3>
            </div>
            <div class="bubble-container" style="height:21vh;max-height:21vh;overflow:auto;">
                <ul>
                    <li v-for="(value,key,index) in bubbleData.data" :key="index" style="display: flex;">
                        <span style="">{{key}}: </span>
                        <div class="keyValue" style="display: flex;flex: 2;align-items: center;padding-left: 10px;">{{value}}</div>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</template>

<script setup>
import {nextTick, onMounted, onUnmounted, reactive, ref, watch} from "vue";
import {useRoute} from "vue-router";
import InitMap from "@/components/GisMap/InitMap.vue";
import {defaultCheckedLayers, mapLoaded} from "@/hooks/gishooks";
import { mapStates } from "@/components/GisMap/mapStates";
import {defaultShowLayersMap, offsetPopupList, requestDataMap} from "@/components/GisMap/common/gisInfo";
import { gisPopups } from "@/components/GisMap/popup/gisPopup";
import gisDialog from "@/components/GisMap/common/gisDialog";
import Tools from "@/components/GisMap/Tools/index.vue";
import { gisSource } from "@/components/GisMap/common/dataConfig.js";
import bus from "@/utils/mitt";
import { layerQueryInfo } from "@/components/GisMap/common/gisInfo";
import { formatDataByField } from "@/components/GisMap/common/gisUtil";
import {postUsmMonitorDeviceList} from "@/api/layerData.js";

const route = useRoute();

//模型属性弹框
let showBubble = ref(false);
let bubbleData = reactive({
    data:null
});
//点击单体化
let QingxiePolygons=[];
let pipeHighttimer=null,QueryResult={},m_HtmlPopup=null;
//管线高亮
let HightPipetimer=null;
let pipePreviousPickedEntity={
    feature: undefined,
    originalColor: undefined,
};

const dialogs = ref();
let layerAPIRes = {}; //标记调用接口情况
let layerData= {}; //存储地图容器数据

const state = reactive({
  treeIndex: 1, //默认菜单
});

//点击事件：弹窗1信息展示
const handlepickBillboard = async (entity) => {
  //查找entity信息是否存在
  const item = layerData[entity.name].find(
    (im) => im.id === entity.id
  );
  if (item) {
    let position= [];
    if (
      entity.name === "gasPipe" ||
      entity.name === "gasPipeRisk"
    ) {
      position = [
        (parseFloat(item.longitudeStart) + parseFloat(item.longitudeEnd)) / 2,
        (parseFloat(item.latitudeStart) + parseFloat(item.latitudeEnd)) / 2 +
          0.0072,
      ];
    } else {
      position = [
        parseFloat(item.longitude),
        parseFloat(item.latitude)- 0.0072, //
      ];
    }

    mapStates.earth.entity.highlightEntity(entity);

    mapStates.earth.camera.flyTo({
      lon: position[0],
      lat: position[1],
      height: 1200,
      orientation: {
        heading: 0,
        pitch: -45,
        roll: 0,
      },
    });
    const opts = Object.assign({
      viewer: mapStates.viewer,
      position: entity.position._value, //弹框位置--笛卡尔坐标；
      gisPopup: gisPopups[entity.name],
      offset: offsetPopupList.includes(entity.name) ? [0, 0.1] : [0, 0.08], // 使用相对值，表示canvas高度的百分比
      useElement: true, //如果增加了el按钮，放开element渲染
      useEcharts: true, //如果增加了echarts图表，放开echarts渲染
      data: {
          ...item,
          layerId: entity.name,
          // alarmStatus: "1", //报警状态 临时调试
      },
    });
    if (dialogs.value) {
      // 只允许一个弹窗出现
      dialogs.value.windowClose();
    }
    dialogs.value = new gisDialog(opts);
  }
};

/**
 * 清理数据源
 * @param layers
 */
const clearDataSourcesByLayers = (layers) => {
  for (let i = 0; i < layers.length; i++) {
    mapStates.earth.entity.clearDataSourcesEntitiesByLayerId(layers[i]);
  }
};

const handleCheckLayer = (layerId, isChecked) => {
    if (layerId) {
        mapStates.earth.entity.toggleLayerVisibleById(layerId, isChecked);
    }
};

const addPointToMap = (data, layerId) => {
  mapStates.earth.entity.addPointGeometryFromDegrees({
    layerId: layerId,
    data: data,
    width: 26, //屏幕分辨率适配
    height: 49, //屏幕分辨率适配
    show: true,
  });
};

const addPolylineToMap = (data, layerId) => {
  mapStates.earth.entity.addPolylineGeometryFromDegrees({
    layerId: layerId,
    data: data,
    // material: materialRoadH1,
    width: 4,
    show: true,
  });
};

const getGisData = async (typeList) => {
    for (const v of typeList) {
        if (v === "_Pipeline") {
            // todo 管线图层
        } else if (requestDataMap[v]) {
            //某类型上一次是否调用过接口
            if (layerAPIRes[v] === v) {
                handleCheckLayer(v, true);
            } else {
                layerAPIRes[v] = v;
                if(requestDataMap[v]) {
                    requestDataMap[v]['api'](requestDataMap[v]['params']).then((res) => {
                        if (res?.data) {
                            if (requestDataMap[v]['filterList']) {
                                const devices = res.data.filter((item) => requestDataMap[v]['filterList'].includes(item[requestDataMap[v]['filterColumn']]));
                                const gisData = formatDataByField(devices, "gisType", v);
                                layerData[v] = gisData;
                                addPointToMap(gisData, v);
                                bus.emit("searchDataChanged", layerData);
                            } else {
                                const gisData = formatDataByField(res?.data, "gisType", v);
                                layerData[v] = gisData;
                                addPointToMap(gisData, v);
                                bus.emit("searchDataChanged", layerData);
                            }
                        }
                    });
                }
            }
        }
    }

 /* //水源地
  clearDataSourcesByLayers(layerQueryInfo["supplyWater_Source"]);
  const supplyWaterSource = typeList.filter((item) =>
    layerQueryInfo["supplyWater_Source"].includes(item)
  );
  if (supplyWaterSource.length > 0) {
    await getGisMapPoints("source").then((res) => {
      if (res.data && res.data?.waterSourceMapPoint) {
        const supplyWaterData = formatDataByField(
          res.data?.waterSourceMapPoint,
          "gisType",
          "supplyWater_Source"
        );
        layerData["supplyWater_Source"] = supplyWaterData;
        addPointToMap(supplyWaterData, "supplyWater_Source");
      }
    });
  }
  //应急水源地
  clearDataSourcesByLayers(layerQueryInfo["supplyWater_Yj_Source"]);
  const supplyWaterYjSource = typeList.filter((item) =>
    layerQueryInfo["supplyWater_Yj_Source"].includes(item)
  );
  if (supplyWaterYjSource.length > 0) {
    await getGisMapPoints("yj-source").then((res) => {
      if (res.data && res.data?.yjWaterSourceMapPoint) {
        const supplyWaterData = formatDataByField(
          res.data?.yjWaterSourceMapPoint,
          "gisType",
          "supplyWater_Yj_Source"
        );
        layerData["supplyWater_Yj_Source"] = supplyWaterData;
        addPointToMap(supplyWaterData, "supplyWater_Yj_Source");
      }
    });
  }
  //水厂
  clearDataSourcesByLayers(layerQueryInfo["supplyWater_Factory"]);
  const supplyWaterFactory = typeList.filter((item) =>
    layerQueryInfo["supplyWater_Factory"].includes(item)
  );
  if (supplyWaterFactory.length > 0) {
    await getGisMapPoints("factory").then((res) => {
      if (res.data && res.data?.factoryMapPoint) {
        const supplyWaterData = formatDataByField(
          res.data?.factoryMapPoint,
          "gisType",
          "supplyWater_Factory"
        );
        layerData["supplyWater_Factory"] = supplyWaterData;
        addPointToMap(supplyWaterData, "supplyWater_Factory");
      }
    });
  }
  //二次供水泵房
  clearDataSourcesByLayers(layerQueryInfo["supplyWater_Facility"]);
  const supplyWaterFacility = typeList.filter((item) =>
    layerQueryInfo["supplyWater_Facility"].includes(item)
  );
  if (supplyWaterFacility.length > 0) {
    await getGisMapPoints("facility").then((res) => {
      if (res.data && res.data?.facilityMapPoint) {
        const supplyWaterData = formatDataByField(
          res.data?.facilityMapPoint,
          "gisType",
          "supplyWater_Facility"
        );
        layerData["supplyWater_Facility"] = supplyWaterData;
        addPointToMap(supplyWaterData, "supplyWater_Facility");
      }
    });
  }
  //增压泵站
  clearDataSourcesByLayers(layerQueryInfo["supplyWater_Station"]);
  const supplyWaterStation = typeList.filter((item) =>
    layerQueryInfo["supplyWater_Station"].includes(item)
  );
  if (supplyWaterStation.length > 0) {
    await getGisMapPoints("station").then((res) => {
      if (res.data && res.data?.stationMapPoint) {
        const supplyWaterData = formatDataByField(
          res.data?.stationMapPoint,
          "gisType",
          "supplyWater_Station"
        );
        layerData["supplyWater_Station"] = supplyWaterData;
        addPointToMap(supplyWaterData, "supplyWater_Station");
      }
    });
  }*/
};

const optimizeImageryLayer = () => {
  // 获取当前的图层
  let layer = mapStates.viewer.scene.imageryLayers.get(0);
  // 改变当前地图的组织结构
  layer.minificationFilter = Cesium.TextureMinificationFilter.NEAREST;
  layer.magnificationFilter = Cesium.TextureMagnificationFilter.NEAREST;
};

const resetPostion = () => {
    // 116.92085304945306, 33.65247925481872, 1795.3256205835187
  mapStates.earth.camera.flyTo({
    lon: 115.097,
    lat: 35.288,
    // lon: 116.94527734181389,
    // lat: 33.644170804185244,
    height: 8000,
    orientation: {
      heading: 0,
      pitch: -90, //-45
      roll: 0,
    },
  });
};

const modelMap = () => {
  if (import.meta.env.VITE_GIS_MAP_ID === "pro") {
      console.log("生产环境");
      mapStates.earth.basemap.addOsgbModel(gisSource.osgb.dongMing);
      mapStates.earth.basemap.addBridgeModel(gisSource.maxModel.dongMingBridgeModel);
      //加载管道模型
      const ids = [
          'dongMingWSContainer', 'dongMingWSWell','dongMingWSiJoint',
          'dongMingYSContainer', 'dongMingYSWell','dongMingYSiJoint'
      ];
      ids.forEach((id) => {
          mapStates.earth.basemap.addPipeLineModel(id, gisSource.pipeLine[id]);
      })
  } else if (import.meta.env.VITE_GIS_MAP_ID === "dev") {
      console.log("开发环境");
     /* mapStates.earth.basemap.addOsgbModel(gisSource.osgb.dongMing);
      mapStates.earth.basemap.addBridgeModel(gisSource.maxModel.dongMingBridgeModel);
      //加载管道模型
      const ids = [
          'dongMingWSContainer', 'dongMingWSWell','dongMingWSiJoint',
          'dongMingYSContainer', 'dongMingYSWell','dongMingYSiJoint'
      ];
      ids.forEach((id) => {
          mapStates.earth.basemap.addPipeLineModel(id, gisSource.pipeLine[id]);
      })*/
     /* mapStates.earth.basemap.addOsgbModel(gisSource.osgb.suZhou);
      mapStates.earth.basemap.addBridgeModel(gisSource.maxModel.suZhouModelPartA);*/
  }
};

// 监听路由变化，清除地图数据，初始话地图数据
const onPageIndex = (path) => {

    //清除高亮
    mapStates.earth.entity.clearHighlight();
    // 关闭弹窗
    if (dialogs.value) {
        dialogs.value.windowClose();
    }

    const layerIds = [...Object.keys(layerData), ...Object.keys(layerAPIRes)];
    clearDataSourcesByLayers([...new Set(layerIds)]);
    layerData = {};
    layerAPIRes = {};
    if (defaultShowLayersMap[path]) {
        defaultCheckedLayers.value = defaultShowLayersMap[path];
        getGisData(defaultCheckedLayers.value);
    } else {
        defaultCheckedLayers.value = [];
    }
};

//start 模型点击事件
const clickModel11 = ()=>{
    let handler = new Cesium.ScreenSpaceEventHandler(mapStates.viewer.scene.canvas);
    handler.setInputAction(function (click) {
       /* console.log(mapStates.viewer.camera.position);
        console.log(mapStates.viewer.camera.heading)
        console.log(mapStates.viewer.camera.pitch)
        console.log(mapStates.viewer.camera.roll)*/
        CancelQingxiePolyHight();
        const pickingEntity = mapStates.viewer.scene.pick(click.position);
        //判断选择是否为Cesium3DTileFeature
        if (!Cesium.defined(pickingEntity)) {
            return; // 没有拾取
        }
        if (pickingEntity) {
            CancelPipeHight();
            const cartesian = mapStates.viewer.scene.pickPosition(click.position);
            // var cartographic = Cesium.Cartographic.fromCartesian(cartesian);
            // QueryResult.lontable = Cesium.Math.toDegrees(cartographic.longitude); //四舍五入 小数点后保留五位
            // QueryResult.lattable = Cesium.Math.toDegrees(cartographic.latitude); //四舍五入  小数点后保留五位
            // QueryResult.height = cartographic.height;
            let s = mapStates.viewer.scene.pickGlobe(click.position);
            if (s) {
                let a = mapStates.viewer.scene.globe.ellipsoid.cartesianToCartographic(s);
                QueryResult.lontable = (a.longitude*180/Math.PI).toFixed(6);
                QueryResult.lattable = (a.latitude*180/Math.PI).toFixed(6);
                QueryResult.height = a.height.toFixed(3);
            }
            QueryResult.cartesian = cartesian;
            pickingEntity.queryResult = QueryResult;
            if (pickingEntity instanceof Cesium.Cesium3DTileFeature || pickingEntity instanceof Cesium.Cesium3DTilePointFeature) {
                pipePreviousPickedEntity = pickingEntity;
            } else {
                if (pickingEntity && pickingEntity.id)
                    pipePreviousPickedEntity = pickingEntity.id;
            }
            selectSingle(pickingEntity, QueryResult);
        }

    }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
}

const clickModel = async (pickingEntity, degree) => {
        CancelQingxiePolyHight();
        if (pickingEntity) {
            CancelPipeHight();
            const cartesian = mapStates.viewer.scene.pickPosition(degree);
            let s = mapStates.viewer.scene.pickGlobe(degree);
            if (s) {
                let a = mapStates.viewer.scene.globe.ellipsoid.cartesianToCartographic(s);
                QueryResult.lontable = (a.longitude*180/Math.PI).toFixed(6);
                QueryResult.lattable = (a.latitude*180/Math.PI).toFixed(6);
                QueryResult.height = a.height.toFixed(3);
            }
            QueryResult.cartesian = cartesian;
            pickingEntity.queryResult = QueryResult;
            if (pickingEntity instanceof Cesium.Cesium3DTileFeature || pickingEntity instanceof Cesium.Cesium3DTilePointFeature) {
                pipePreviousPickedEntity = pickingEntity;
            } else {
                if (pickingEntity && pickingEntity.id)
                    pipePreviousPickedEntity = pickingEntity.id;
            }
            selectSingle(pickingEntity, QueryResult);
        }
}
const selectSingle=(pickingEntity, QueryResult)=>{
    // let selectPipe = result;
    let queryID = pickingEntity.getProperty("gw_id");
    let queryUUID = pickingEntity.primitive.properties.gw_layer_guid;

    let queryCondition = new CommonService.QueryCondition();
    queryCondition.addPropertyCondition(
        CommonService.SqlOperator.AND,
        "us_id",
        CommonService.SqlOperator.EQUAL,
        queryID
    );

    CommonService.Query.queryProperty(
        gisSource.serverApi.commonService,
        queryUUID,
        queryCondition
    ).then(function (result) {
        if (result._records&&result._records.length>0) {
            //闪烁属性
            HilightPipeline(pickingEntity);
            //弹窗
            // ShowOSGBText(result._records,QueryResult);
            delete result._records[0].geometry;
            // delete result._records[0].gw_layer;
            delete result._records[0].gw_layer_user;
            showBubbleText(result._records[0],QueryResult.cartesian)
        }
    })
    // let queryCondition =  new CommonService.QueryCondition();
    //   queryCondition.setAsCircle(pickingEntity.queryResult.lontable, pickingEntity.queryResult.lattable, pickingEntity.queryResult.height, 1.0);
    //   CommonService.Query.queryProperty(window.queryIP, pickingEntity.primitive.properties.gw_layer_guid, queryCondition)
    //       .then(function (result) {
    //               if (result._records&&result._records.length>0) {
    //                   //闪烁属性
    //                   HilightOsgbFeature(result._records[0].geometry.coordinates);
    //                   //弹窗
    //                   // ShowOSGBText(result._records,QueryResult);
    //                   delete result._records[0].geometry;
    //                   // delete result._records[0].gw_layer;
    //                   delete result._records[0].gw_layer_user;
    //                   showBubbleText(result._records[0],QueryResult.cartesian)
    //               }
    //       })
}
const showBubbleText=(data,cartesian)=>{
    showBubble.value = true;
    bubbleData.data = formatAttributes(data);
    const bubble = document.querySelector("#bubble");
    // bubble.style.display = "block";
    const bubbleBackground = document.querySelector(".bubble-background");
    let posi = cartesian;
    //每帧渲染结束监听
    mapStates.viewer.scene.postRender.addEventListener(function (e) {
        if (posi) {
            const winpos = mapStates.viewer.scene.cartesianToCanvasCoordinates(posi);
            if(winpos){
                bubble.style.left = winpos.x + 80 - bubbleBackground.clientWidth / 2 + "px";
                bubble.style.top = winpos.y - 50 - bubbleBackground.clientHeight + "px";
            }
        }
    });
}
const closeBubble=()=>{
    showBubble.value = false;
    bubbleData.data = null;
    mapStates.viewer.scene.postRender.removeEventListener(showBubbleText)
}
// 取消倾斜摄影高亮
const CancelQingxiePolyHight=()=> {
    if (QingxiePolygons && QingxiePolygons.length > 0) {
        for (let num in QingxiePolygons) {
            mapStates.viewer.entities.remove(QingxiePolygons[num]);
        }
        QingxiePolygons = [];
    }
}
//管线高亮
const HilightPipeline=(pickingEntity)=>{
    if (HightPipetimer){
        clearInterval(HightPipetimer);
    }
    if (pickingEntity != pipePreviousPickedEntity.feature) {
        if (pipePreviousPickedEntity.feature != undefined) {
            //还原前选择要素的本颜色
            pipePreviousPickedEntity.feature.color = pipePreviousPickedEntity.originalColor;
            //将当前选择要素及其颜色添加到previousPickedEntity
            pipePreviousPickedEntity.feature = pickingEntity;
            pipePreviousPickedEntity.originalColor = pickingEntity.color;
        }
        //将当前选择要素及其颜色添加到previousPickedEntity
        pipePreviousPickedEntity.feature = pickingEntity;
        pipePreviousPickedEntity.originalColor = pickingEntity.color;
    }
    let timersun = 0.1;
    HightPipetimer = setInterval(function () {
        if (timersun > 1.0)
            timersun = 0.1;
        timersun += 0.1;
        //pickingEntity.color = pickingEntity.color.withAlpha(timersun);
        pickingEntity.color = Cesium.Color.RED.withAlpha(timersun);
    }, 100);
};
// 取消管线高亮
const CancelPipeHight=()=> {
    if (HightPipetimer){
        clearInterval(HightPipetimer);
    }
    if (pipePreviousPickedEntity.feature != undefined) {
        //还原前选择要素的本颜色
        pipePreviousPickedEntity.feature.color = pipePreviousPickedEntity.originalColor;
    }
    pipePreviousPickedEntity={
        feature:undefined,
        color:undefined
    }
};
// 格式化属性字段
const formatAttributes=(attributes)=>{
    console.log(attributes)
    let obj = {};
    // if(attributes.gw_layer_type){//管线
    //   obj.type='pipeline';
    if(attributes.e_point){

        //   // obj = {'测绘单位':'','管线起点点':'','管线起点埋':'','管线终点点':'','管线终点埋':'','敷设方式':'','管材':'','管径':'','设施位置':''};
        //    if(attributes['管线起点点'].includes('YS')){
        //       obj['管线类型']= '雨水管线';
        //     }else if(attributes['管线起点点'].includes('WS')){
        //       obj['管线类型']= '污水管线';
        //     }else{
        //       obj['管线类型']= '雨污合流管线';
        //     }
        obj['起点点号']= attributes['s_point'];
        obj['终点点号']= attributes['e_point'];
        obj['起点埋深']= attributes['s_deep'];
        obj['终点埋深']= attributes['e_deep'];
        obj['高程']= attributes['e_h'];
        obj['管材']= attributes['material'];
        obj['类型']= '管线';
        obj['所在道路']= attributes['aroadcode'];
        obj['所属单位']= attributes['arrunitc'];
        obj['建设时间']= attributes['builddate'];

    }else{

        obj['管点点号']= attributes['prj_no'];
        obj['井盖材质']= attributes['wellmateri'];
        obj['井盖尺寸']= attributes['wellsize'];
        obj['所属单位']= attributes['arrunitc'];
        obj['特征']= attributes['feature'];
        obj['类型']= '管点';
    }
    return obj;
}
//end 模型点击事件

const InitScreen = () => {
  // 添加默认底图
  mapStates.earth.basemap.add("img");
  mapStates.earth.basemap.add("cia");
  mapStates.earth.basemap.add("tdt_terrain");
  modelMap();
  // 优化影像图层
  optimizeImageryLayer();
  // 重置位置
  resetPostion();

  //模型点击事件注册


  //地图要素点击事件，[点击事件]，[悬停事件]
  mapStates.earth.event.activatePickHandler(
    [
      handlepickBillboard, //点-billboard
      () => {}, //线-polyline
      () => {}, //文本-label
      () => {},
      // handlepickAnything, // handlepickAnything, //所有情况,打印坐标
      () => {}, //点击空白处
      clickModel, // handlepickModel, //模型
      // handlePickPipe // 3dtiles 管道模型
    ],
    [() => {}] //handleHoverPolyline]
  );
  // 最小缩放高度（米）
  // mapStates.viewer.scene.screenSpaceCameraController.minimumZoomDistance = 500;
  // 最大缩放高度（米）
  //   mapStates.viewer.scene.screenSpaceCameraController.maximumZoomDistance = 80000;
};

bus.on("resetGisPopup", () => {
    mapStates.earth.entity.clearHighlight();
    if (dialogs.value) {
        dialogs.value.windowClose();
    }
});

watch(
    () => [mapLoaded.value],
    (val) => {
        if (val) {
            nextTick(() => {
                InitScreen();
            });
        }
    },
    {
        deep: true,
    }
);

watch(
    () => [mapLoaded.value, route],
    (val) => {
        if (val) {
            nextTick(() => {
                resetPostion();
                onPageIndex(route.path);
            });
        }
    },
    {
        deep: true,
    }
);

watch(
    () => defaultCheckedLayers.value,
    (val) => {
        console.log("defaultCheckedLayers.value--22222-->>", val);
        getGisData(val);
    },
    {
        deep: true,
    }
);

onMounted( () => {});
onUnmounted(() => {});
</script>

<style lang="scss" scoped>
.map-screen {
  width: 100%;
  height: 100%;
}

.bubble-container::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 3px;
  /*高宽分别对应横竖滚动条的尺寸*/
  height: 4px;
  scrollbar-arrow-color: red;
}

.bubble-container::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  border-radius: 5px;
  background: rgba(255, 255, 255, 0.5);
}

.bubble-container::-webkit-scrollbar-track {
  border-radius: 0;
  background: rgba(0, 0, 0, 0.1);
}
.bubble-top {
  height: 35px;
  line-height: 35px;
  background: url("@/assets/images/gis/bubble/bubbleTitle.png") no-repeat center 15px;
}

.bubble-top span {
  font-size: 14px;
  font-weight: bold;
  color: #fff;
  float: left;
  text-indent: 20px;
}

.bubble-top a {
  display: block;
  background: url("@/assets/images/gis/bubble/bubbleClose.png") no-repeat;
  width: 22px;
  height: 22px;
  float: right;
  margin-right: 4px;
  margin-top: 5px;
  cursor: pointer;
}

.custom-bubble {
  position: absolute;
  z-index: 4;
}

.bubble-background {
  width: 301px;
  height: 279px;
  background: url("@/assets/images/gis/bubble/bubbleBg.png") no-repeat;
}

.bubble-container {
  margin-top: 10px;
  color: #fff;
}

.bubble-container ul {
  margin-left: 10px;
  margin-right: 10px;
  display: flex;
  flex-wrap: wrap;
  padding-left: 40px;
}

.bubble-container ul > li {
  width: 90%;
  text-align: center;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  /* padding-left: 5px; */
  padding-right: 5px;
  cursor: pointer;
  border-top: 1px solid rgba(34, 189, 142, 0.2);
  padding-bottom: 8px;
  padding-top: 8px;
}

.bubble-container ul > li:hover {
  white-space: normal;
  overflow: auto;
}

.bubble-container ul > li > a {
  display: block;
}

.bubble-container ul > li > span {
  display: block;
  color: rgb(34, 189, 142);
  float: left;
}

.bubble-title {
  text-align: center;
  color: rgb(34, 189, 142);
  margin-left: 27px;
  font-size: 1em;
  padding-top: 5px;
}
</style>
