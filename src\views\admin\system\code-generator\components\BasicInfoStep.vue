<template>
  <div class="basic-info-step">
    <el-form :model="config.basic" label-width="100px">
      <el-form-item label="模块名称" required>
        <el-input v-model="config.basic.moduleName" placeholder="请输入模块名称，如：user, product" />
        <div class="form-item-tip">用于生成文件名和组件名，建议使用小写英文</div>
      </el-form-item>
      
      <el-form-item label="API前缀" required>
        <el-input v-model="config.basic.apiPrefix" placeholder="请输入API前缀，如：/api/users" />
        <div class="form-item-tip">用于生成API请求路径</div>
      </el-form-item>
      
      <el-form-item label="页面标题" required>
        <el-input v-model="config.basic.pageTitle" placeholder="请输入页面标题，如：用户管理" />
        <div class="form-item-tip">显示在页面顶部的标题</div>
      </el-form-item>
      
      <el-form-item label="功能描述">
        <el-input 
          v-model="config.basic.description" 
          type="textarea" 
          placeholder="请输入功能描述"
          :rows="3"
        />
        <div class="form-item-tip">对该功能模块的简要描述，将作为注释添加到代码中</div>
      </el-form-item>
      
      <el-form-item label="作者">
        <el-input v-model="config.basic.author" placeholder="请输入作者姓名" />
        <div class="form-item-tip">代码作者，将作为注释添加到代码中</div>
      </el-form-item>
    </el-form>
    
    <div class="step-actions">
      <el-button type="primary" @click="handleNext">下一步</el-button>
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  config: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update:config', 'next', 'prev'])

// 下一步
const handleNext = () => {
  // 验证必填项
  const { moduleName, apiPrefix, pageTitle } = props.config.basic
  
  if (!moduleName) {
    ElMessage.warning('请输入模块名称')
    return
  }
  
  if (!apiPrefix) {
    ElMessage.warning('请输入API前缀')
    return
  }
  
  if (!pageTitle) {
    ElMessage.warning('请输入页面标题')
    return
  }
  
  emit('next')
}
</script>

<style scoped>
.basic-info-step {
  padding: 0 20px;
}

.form-item-tip {
  font-size: 12px;
  color: #909399;
  line-height: 1.5;
  margin-top: 4px;
}

.step-actions {
  margin-top: 30px;
  display: flex;
  justify-content: flex-end;
}
</style>
