<template>
    <InitMap/>
    <LayerTreeMis
            class="layer-tree"
            ref="layerTreeRef"
            :treeData="layerTreeData"
            @check-layer="handleCheckLayer"
    />
    <ZoomMis v-if="showZoom"/>
  <!-- 图例 -->
    <div class="normal-legend">
        <LegendMis :legendData="legendData"/>
    </div>
    <div class="gas-model-container">
        <GasModelMis v-if="showGasModel"/>
    </div>
</template>

<script setup>
import {nextTick, onMounted, onUnmounted, ref, watch} from "vue";
import InitMap from "@/components/GisMap/InitMap.vue";
import LayerTreeMis from "@/components/GisMap/ToolMis/LayerTreeMis/index.vue";
import ZoomMis from "@/components/GisMap/ToolMis/ZoomMis/index.vue";
import LegendMis from "@/components/GisMap/ToolMis/LegendMis/index.vue";
import GasModelMis from "@/components/GisMap/ToolMis/GasModelMis/index.vue";
import {mapStates} from "@/components/GisMap/mapStates";
import {mapLoaded} from "@/hooks/gishooks";
import {
    layerQueryInfo,
    requestDataMap,
} from "./common/gisInfo.js";
import {treeInfo} from "./common/layerInfo.js";
import {legendInfo} from "./common/legendInfo.js";
import {formatDataByField} from "@/components/GisMap/common/gisUtil";
import {defaultCheckedLayers} from "@/hooks/gishooks";
import {useRoute} from "vue-router";
import {gisSource} from "@/components/GisMap/common/dataConfig.js";
import bus from "@/utils/mitt.js";
import {gisPopups} from "@/components/GisMap/popup/gisPopup.js";
import gisDialog from "@/components/GisMap/common/gisDialog.js";
import {postUsmMonitorDeviceList} from "@/api/layerData.js";
import {offsetPopupList} from "@/components/GisMap/common/gisInfo.js";

const route = useRoute();
//图层start
const layerTreeData = ref([]);
const dialogs = ref();
//图例
const legendData = ref([]);
let layerAPIRes = {}; //标记调用接口情况
let layerData = {}; //存储地图容器数据

const showGasModel = ref(false);
const showZoom = ref(true);

//点击事件：弹窗1信息展示
const handlepickBillboard = async (entity) => {
    //查找entity信息是否存在
    const item = layerData[entity.name].find(
        (im) => im.id === entity.id
    );
    if (item) {
        let position = [];
        if (
            entity.name === "gasPipe" ||
            entity.name === "gasPipeRisk"
        ) {
            position = [
                (parseFloat(item.longitudeStart) + parseFloat(item.longitudeEnd)) / 2,
                (parseFloat(item.latitudeStart) + parseFloat(item.latitudeEnd)) / 2 +
                0.007,
            ];
        } else {
            position = [
                parseFloat(item.longitude),
                parseFloat(item.latitude) - 0.007, //
            ];
        }

        mapStates.earth.entity.highlightEntity(entity);

        mapStates.earth.camera.flyTo({
            lon: position[0],
            lat: position[1],
            height: 1200,
            orientation: {
                heading: 0,
                pitch: -45,
                roll: 0,
            },
        });
        const opts = Object.assign({
            viewer: mapStates.viewer,
            position: entity.position._value, //弹框位置--笛卡尔坐标；
            gisPopup: gisPopups[entity.name],
            offset: offsetPopupList.includes(entity.name) ? [0, 650] : [0, 570], // 弹框偏移量（像素）
            useElement: true, //如果增加了el按钮，放开element渲染
            useEcharts: true, //如果增加了echarts图表，放开echarts渲染
            data: {
                ...item,
                layerId: entity.name,
            },
        });
        if (dialogs.value) {
            // 只允许一个弹窗出现
            dialogs.value.windowClose();
        }
        dialogs.value = new gisDialog(opts);
    }
};

/**
 * 清理数据源
 * @param layers
 */
const clearDataSourcesByLayers = (layers) => {
    for (let i = 0; i < layers.length; i++) {
        mapStates.earth.entity.clearDataSourcesEntitiesByLayerId(layers[i]);
    }
};

const addPointToMap = (data, layerId) => {
    console.log(data, layerId);
    mapStates.earth.entity.addPointGeometryFromDegrees({
        layerId: layerId,
        data: data,
        width: 25, //屏幕分辨率适配
        height: 47, //屏幕分辨率适配
        show: true,
    });
};

const addPolylineToMap = (data, layerId) => {
    mapStates.earth.entity.addPolylineGeometryFromDegrees({
        layerId: layerId,
        data: data,
        // material: materialRoadH1,
        width: 4,
        show: true,
    });
};

// 页面切换：综合预览，监测报警，应急处置……
const onPageIndex = (path) => {
    //清除高亮
    mapStates.earth.entity.clearHighlight();
    // 关闭弹窗
    if (dialogs.value) {
        dialogs.value.windowClose();
    }
    const layerIds = [...Object.keys(layerData), ...Object.keys(layerAPIRes)];
    clearDataSourcesByLayers([...new Set(layerIds)]);
    layerData = {};
    layerAPIRes = {};
    showGasModel.value = false;
    showZoom.value = true;
    switch (path) {
        case "/gas/risk/explosion/heatmap": //燃气管网风险热力图
            layerTreeData.value = treeInfo["mis_gas_pipeline_risk"];
            legendData.value = legendInfo["mis_gas_pipeline_risk"];
            defaultCheckedLayers.value = layerQueryInfo["mis_gas_pipeline_risk"];
            break;
        case "/gas/risk/explosion/station-heatmap": //燃气场站风险热力图
            layerTreeData.value = treeInfo["mis_gas_station_risk"];
            legendData.value = legendInfo["mis_gas_station_risk"];
            defaultCheckedLayers.value = layerQueryInfo["mis_gas_station_risk"];
            break;
        case "/gas/leak/monitor/video": //视频监控
            layerTreeData.value = treeInfo["mis_gas_monitor_video"];
            legendData.value = legendInfo["mis_gas_monitor_video"];
            defaultCheckedLayers.value = layerQueryInfo["mis_gas_monitor_video"];
            break;
        case "/gas/predict/warning/damage": //可燃气爆炸损伤范围分析
            layerTreeData.value = [];
            legendData.value = [];
            defaultCheckedLayers.value = [];
            showGasModel.value = true;
            break;
        case "/gas/home": //燃气首页
            layerTreeData.value = [];
            legendData.value = [];
            defaultCheckedLayers.value = [];
            showZoom.value = false;
            break;
        default:
            layerTreeData.value = [];
            legendData.value = [];
            defaultCheckedLayers.value = [];
            break;

    }
    getGisData(defaultCheckedLayers.value);
    resetPostion();
};

//图层树点击事件
const handleCheckLayer = (layerId, isChecked) => {
    if (layerId) {
        mapStates.earth.entity.toggleLayerVisibleById(layerId, isChecked);
    }
};

const getGisData = async (typeList) => {
    for (const v of typeList) {
        if (v === "_Pipeline") {
            // todo 管线图层
        } else if (requestDataMap[v]) {
            //某类型上一次是否调用过接口
            if (layerAPIRes[v] === v) {
                handleCheckLayer(v, true);
            } else {
                layerAPIRes[v] = v;
                // 可燃气体监测仪
                if (requestDataMap[v] === "gas_combustible") {
                    postUsmMonitorDeviceList().then((res) => {
                        if (res?.data) {
                            const device1 = res.data.filter((item) => ['laserMethane', 'wbFixedCh4GrassDetector', 'aoruideMethane'].includes(item?.deviceType));
                            const gisData = formatDataByField(device1, "gisType", v);
                            layerData[v] = gisData;
                            addPointToMap(gisData, v);
                            bus.emit("searchDataChanged", layerData);
                        }
                    });
                } else if (requestDataMap[v]) {
                    requestDataMap[v]['api'](requestDataMap[v]['params']).then((res) => {
                        if (res?.data) {
                            const gisData = formatDataByField(res?.data, "gisType", v);
                            layerData[v] = gisData;
                            addPointToMap(gisData, v);
                            bus.emit("searchDataChanged", layerData);
                        }
                    });
                }
            }
        }
    }
};

const optimizeImageryLayer = () => {
    // 获取当前的图层
    let layer = mapStates.viewer.scene.imageryLayers.get(0);
    // 改变当前地图的组织结构
    layer.minificationFilter = Cesium.TextureMinificationFilter.NEAREST;
    layer.magnificationFilter = Cesium.TextureMagnificationFilter.NEAREST;
};

const resetPostion = () => {
    mapStates.earth.camera.flyTo({
        lon: 115.097,
        lat: 35.288,
        // lon: 116.94527734181389,
        // lat: 33.644170804185244,
        height: 8000,
        orientation: {
            heading: 0,
            pitch: -90, //-45
            roll: 0,
        },
    });
    // handleDialog1Close(); // 关闭弹窗
};

const modelMap = () => {
    if (import.meta.env.VITE_GIS_MAP_ID === "pro") {
        console.log("生产环境");
        /* mapStates.earth.basemap.addOsgbModel(gisSource.osgb.dongMing);
         mapStates.earth.basemap.addBridgeModel(gisSource.maxModel.dongMingBridgeModel);*/
    } else if (import.meta.env.VITE_GIS_MAP_ID === "dev") {
        console.log("开发环境");
        /* mapStates.earth.basemap.addOsgbModel(gisSource.osgb.suZhou);
         mapStates.earth.basemap.addBridgeModel(gisSource.maxModel.suZhouModelPartA);*/
    }
};

const InitScreen = () => {
    // 添加默认底图
    mapStates.earth.basemap.add("img");
    mapStates.earth.basemap.add("cia");
    mapStates.earth.basemap.add("tdt_terrain");
    modelMap();
    // 优化影像图层
    optimizeImageryLayer();
    // 重置位置
    resetPostion();
    //地图要素点击事件，[点击事件]，[悬停事件]
    mapStates.earth.event.activatePickHandler(
        [
            handlepickBillboard, //点-billboard
            () => {
            }, //线-polyline
            () => {
            }, //文本-label
            // handlepickAnything, // handlepickAnything, //所有情况,打印坐标
            () => {
            }, //点击空白处
            // handlePickPipe // 3dtiles 管道模型
        ],
        [() => {
        }] //handleHoverPolyline]
    );
};

bus.on("resetGisPopup", () => {
    mapStates.earth.entity.clearHighlight();
    if (dialogs.value) {
        dialogs.value.windowClose();
    }
});

watch(
    () => [mapLoaded.value, route],
    ([val]) => {
        if (val) {
            nextTick(() => {
                onPageIndex(route.path);
            });
        }
    },
    {
        deep: true,
    }
);

onMounted(() => {
    // 初始化地图
    InitScreen();
});
onUnmounted(() => {
});
</script>

<style lang="scss" scoped>
.layer-tree,
.gas-model-container {
  position: absolute;
  top: 18px;
  left: 18px;
  z-index: 999;
}

.normal-legend {
  position: absolute;
  top: auto;
  bottom: 18px;
  left: 18px;
  z-index: 999;
}
</style>
