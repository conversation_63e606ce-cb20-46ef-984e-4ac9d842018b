<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="1000px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="gas-disposal-plan-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      :disabled="mode === 'view'"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="方案名称" prop="schemeName">
            <el-input v-model="formData.schemeName" placeholder="请输入方案名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="来源单位" prop="sourceUnit">
            <el-select v-model="formData.sourceUnit" placeholder="请选择" class="w-full" @change="handleSourceUnitChange">
              <el-option v-for="unit in managementUnits" :key="unit.id" :label="unit.enterpriseName"
                :value="unit.enterpriseName"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="匹配报警类型" prop="alarmType">
            <el-select v-model="formData.alarmType" placeholder="请选择" class="w-full" @change="handleAlarmTypeChange">
              <el-option v-for="item in ALARM_TYPES" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="编辑日期" prop="editDate">
            <el-date-picker
              v-model="formData.editDate"
              type="date"
              placeholder="请选择编辑日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              class="w-full"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="formData.remark"
              type="textarea"
              :rows="3"
              placeholder="请输入备注"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="相关附件">
            <template v-if="mode !== 'view'">
              <el-upload
                class="upload-demo"
                :http-request="handleFileUpload"
                :file-list="fileList"
                :on-error="handleUploadError"
              >
                <el-button type="primary">上传附件</el-button>
              </el-upload>
            </template>
            <template v-else>
              <el-link
                v-if="fileList.length > 0"
                :href="fileList[0].url"
                target="_blank"
                type="primary"
              >
                {{ fileList[0].name }}
              </el-link>
            </template>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="mode !== 'view'">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { getManagementUnits } from '@/api/gas';
import { ALARM_TYPES, ALARM_TYPE_MAP } from '@/constants/gas';
import { uploadFile } from '@/api/upload';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add', // 'add', 'edit', 'view'
    validator: (value) => ['add', 'edit', 'view'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'success']);

// 表单引用
const formRef = ref(null);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 对话框标题
const dialogTitle = computed(() => {
  const titles = {
    add: '新增处置方案',
    edit: '编辑处置方案',
    view: '处置方案详情'
  };
  return titles[props.mode] || '处置方案';
});

// 表单数据
const formData = reactive({
  id: '',
  schemeName: '',
  sourceUnit: '',
  sourceUnitName: '',
  alarmType: '',
  alarmTypeName: '',
  editDate: '',
  remark: '',
  fileUrl: ''
});

// 表单验证规则
const formRules = {
  schemeName: [{ required: true, message: '请输入方案名称', trigger: 'blur' }],
  sourceUnit: [{ required: true, message: '请选择来源单位', trigger: 'change' }],
  alarmType: [{ required: true, message: '请选择匹配报警类型', trigger: 'change' }],
  editDate: [{ required: true, message: '请选择编辑日期', trigger: 'change' }]
};

// 权属单位列表
const managementUnits = ref([]);

// 文件列表
const fileList = ref([]);

// 获取权属单位列表
const fetchManagementUnits = async () => {
  try {
    const res = await getManagementUnits();
    if (res && res.code === 200) {
      managementUnits.value = res.data || [];
    }
  } catch (error) {
    console.error('获取权属单位列表失败', error);
  }
};

// 处理来源单位变化
const handleSourceUnitChange = (value) => {
  formData.sourceUnitName = value || '';
};

// 处理报警类型变化
const handleAlarmTypeChange = (value) => {
  formData.alarmTypeName = value ? ALARM_TYPE_MAP[value] || '' : '';
};

// 文件上传处理
const handleFileUpload = async (options) => {
  try {
    const res = await uploadFile(options.file);
    if (res.status === 200) {
      formData.fileUrl = res.data.url;
      fileList.value = [{ name: options.file.name, url: res.data }];
      ElMessage.success('上传成功');
    } else {
      ElMessage.error(res.message || '上传失败');
    }
  } catch (error) {
    console.error('上传相关附件失败:', error);
    ElMessage.error('上传失败');
  }
};

const handleUploadError = () => {
  ElMessage.error('上传失败');
};

// 监听props.data变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    // 复制数据到表单
    Object.keys(formData).forEach(key => {
      if (newVal[key] !== undefined) {
        formData[key] = newVal[key];
      }
    });

    // 设置文件列表
    if (newVal.fileUrl) {
      fileList.value = [{ name: '相关附件', url: newVal.fileUrl }];
    } else {
      fileList.value = [];
    }
  }
}, { immediate: true, deep: true });

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  // 重置表单
  if (formRef.value) {
    formRef.value.resetFields();
  }
  // 重置表单数据
  Object.keys(formData).forEach(key => {
    formData[key] = '';
  });
  // 重置文件列表
  fileList.value = [];
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    emit('success', formData);
    handleClose();
  } catch (error) {
    console.error('表单验证失败', error);
  }
};

// 组件挂载时获取数据
onMounted(() => {
  fetchManagementUnits();
});
</script>

<style scoped>
.gas-disposal-plan-dialog {
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "Helvetica Neue", Arial, sans-serif;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-input__inner),
:deep(.el-select__input) {
  border-radius: 6px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 6px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  background-color: #0277FD;
  border-color: #0277FD;
}

:deep(.el-button--primary:hover) {
  background-color: #1890ff;
  border-color: #1890ff;
}

.w-full {
  width: 100%;
}

.upload-demo {
  :deep(.el-upload-list) {
    width: 100%;
  }
}
</style>