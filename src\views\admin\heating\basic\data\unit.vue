<template>
  <div class="heating-unit-container">
    <!-- 搜索区域 -->
    <div class="unit-search">
      <div class="search-form">
        <div class="form-item">
          <span class="label">权属单位:</span>
          <el-select v-model="formData.ownershipUnit" class="form-input" placeholder="全部">
            <el-option v-for="item in enterpriseOptions" :key="item.id" :label="item.enterpriseName" :value="item.id" />
          </el-select>
        </div>
        <div class="form-item">
          <span class="label">所属换热站:</span>
          <el-select v-model="formData.stationId" class="form-input" placeholder="全部">
            <el-option v-for="item in heatStationOptions" :key="item.id" :label="item.stationName" :value="item.id" />
          </el-select>
        </div>
        <div class="form-item">
          <el-input v-model="formData.unitName" class="form-input" placeholder="输入机组名称" />
        </div>
        <div class="form-item">
          <el-button type="primary" class="search-btn" @click="handleSearch">查询</el-button>
          <el-button class="reset-btn" @click="handleReset">重置</el-button>
        </div>
      </div>
    </div>
    
    <!-- 按钮区域 -->
    <div class="table-header">
      <div class="button-group">
        <el-button type="primary" class="operation-btn" @click="handleAdd">+新增</el-button>
        <el-button type="primary" class="operation-btn" @click="handleExport">导出</el-button>
        <el-button type="primary" class="operation-btn" @click="handleBatchImport">批量导入</el-button>
      </div>
    </div>
    
    <!-- 表格区域 -->
    <div class="table-container">
      <el-table :data="tableData" style="width: 100%" :header-cell-style="headerCellStyle"
        :row-class-name="tableRowClassName" @row-click="handleRowClick" height="calc(100vh - 380px)"
        empty-text="暂无数据">
        <el-table-column label="序号" min-width="60">
          <template #default="{ $index }">
            {{ (currentPage - 1) * pageSize + $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="unitName" label="机组名称" min-width="150" />
        <el-table-column prop="unitCode" label="机组编号" min-width="120" />
        <el-table-column prop="unitType" label="机组类型" min-width="120" />
        <el-table-column prop="stationName" label="所属换热站" min-width="150" />
        <el-table-column prop="ownershipUnitName" label="权属单位" min-width="150" />
        <el-table-column prop="heatBuildingArea" label="供热建筑面积 (m²)" min-width="150" />
        <el-table-column prop="investTime" label="投入使用时间" min-width="150" />
        <el-table-column prop="address" label="位置" min-width="150" />
        <el-table-column label="操作" fixed="right" min-width="180" align="center">
          <template #default="{ row }">
            <div class="operation-btns">
              <div class="operation-btn-row">
                <span class="operation-btn-text" @click.stop="handleDetail(row)">详情</span>
                <span class="operation-divider">|</span>
                <span class="operation-btn-text" @click.stop="handleEdit(row)">编辑</span>
                <span class="operation-divider">|</span>
                <span class="operation-btn-text" @click.stop="handleDelete(row)">删除</span>
                <span class="operation-divider">|</span>
                <span class="operation-btn-text" @click.stop="handleLocation(row)">定位</span>
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    
    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :pager-count="5"
      />
    </div>

    <!-- 机组信息弹窗 -->
    <UnitDialog
      v-model:visible="dialogVisible"
      :mode="dialogMode"
      :data="dialogData"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { getAllEnterpriseList, getAllHeatStationList, getUnitPage, deleteUnit, getUnitDetail } from '@/api/heating';
import { misPosition } from '@/hooks/gishooks';
import UnitDialog from './components/UnitDialog.vue';

// 企业选项
const enterpriseOptions = ref([]);
// 换热站选项
const heatStationOptions = ref([]);

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const tableData = ref([]);

// 表单数据
const formData = ref({
  ownershipUnit: '',
  stationId: '',
  unitName: ''
});

// 弹窗相关
const dialogVisible = ref(false);
const dialogMode = ref('add');
const dialogData = ref({});

// 表格样式
const headerCellStyle = {
  background: '#F4F4F4',
  fontFamily: 'PingFangSC, PingFang SC',
  fontWeight: '500',
  fontSize: '14px',
  color: '#282828',
  height: '64px'
};

// 表格行样式
const tableRowClassName = ({ rowIndex }) => {
  if (rowIndex % 2 === 1) {
    return 'zebra-row';
  }
  return '';
};

// 获取企业列表
const fetchEnterpriseOptions = async () => {
  try {
    const res = await getAllEnterpriseList();
    if (res && res.code === 200) {
      enterpriseOptions.value = res.data || [];
    }
  } catch (error) {
    console.error('获取企业列表失败:', error);
    ElMessage.error('获取企业列表失败');
  }
};

// 获取换热站列表
const fetchHeatStationOptions = async () => {
  try {
    const res = await getAllHeatStationList();
    if (res && res.code === 200) {
      heatStationOptions.value = res.data || [];
    }
  } catch (error) {
    console.error('获取换热站列表失败:', error);
    ElMessage.error('获取换热站列表失败');
  }
};

// 处理查询
const handleSearch = () => {
  currentPage.value = 1;
  fetchUnitData();
};

// 处理重置
const handleReset = () => {
  formData.value = {
    ownershipUnit: '',
    stationId: '',
    unitName: ''
  };
  currentPage.value = 1;
  fetchUnitData();
};

// 获取机组分页数据
const fetchUnitData = async () => {
  try {
    const params = {
      ownershipUnit: formData.value.ownershipUnit,
      stationId: formData.value.stationId,
      unitName: formData.value.unitName
    };
    
    const res = await getUnitPage(currentPage.value, pageSize.value, params);
    
    if (res && res.code === 200) {
      tableData.value = res.data.records;
      total.value = res.data.total;
    }
  } catch (error) {
    console.error('获取机组数据失败:', error);
    ElMessage.error('获取机组数据失败');
    tableData.value = [];
    total.value = 0;
  }
};

// 处理分页大小变化
const handleSizeChange = (size) => {
  pageSize.value = size;
  fetchUnitData();
};

// 处理页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page;
  fetchUnitData();
};

// 处理行点击
const handleRowClick = (row) => {
  console.log('行点击', row);
};

// 处理新增
const handleAdd = () => {
  dialogMode.value = 'add';
  dialogData.value = {};
  dialogVisible.value = true;
};

// 处理编辑
const handleEdit = async (row) => {
  try {
    const res = await getUnitDetail(row.id);
    if (res && res.code === 200) {
      dialogMode.value = 'edit';
      dialogData.value = res.data;
      dialogVisible.value = true;
    } else {
      ElMessage.error(res?.message || '获取机组详情失败');
    }
  } catch (error) {
    console.error('获取机组详情失败:', error);
    ElMessage.error('获取机组详情失败');
  }
};

// 处理详情
const handleDetail = async (row) => {
  try {
    const res = await getUnitDetail(row.id);
    if (res && res.code === 200) {
      dialogMode.value = 'view';
      dialogData.value = res.data;
      dialogVisible.value = true;
    } else {
      ElMessage.error(res?.message || '获取机组详情失败');
    }
  } catch (error) {
    console.error('获取机组详情失败:', error);
    ElMessage.error('获取机组详情失败');
  }
};

// 处理删除
const handleDelete = (row) => {
  ElMessageBox.confirm('确定要删除该机组信息吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await deleteUnit(row.id);
      if (res && res.code === 200) {
        ElMessage.success('删除成功');
        fetchUnitData();
      } else {
        ElMessage.error(res?.message || '删除失败');
      }
    } catch (error) {
      console.error('删除机组信息失败:', error);
      ElMessage.error('删除机组信息失败');
    }
  }).catch(() => {
    // 取消删除
  });
};

// 处理定位
const handleLocation = (row) => {
  if (
    row.latitude &&
    row.latitude !== 0 &&
    row.longitude &&
    row.longitude !== 0
  ) {
    misPosition.value = {
      longitude: row.longitude, //经度
      latitude: row.latitude //纬度
    }
  } else {
    ElMessage.warning('没有经纬度，无法定位！')
  }
};

// 处理弹窗成功
const handleDialogSuccess = () => {
  fetchUnitData();
};

// 处理导出
const handleExport = () => {
  console.log('导出');
  ElMessage.info('导出功能待实现');
};

// 处理批量导入
const handleBatchImport = () => {
  console.log('批量导入');
  ElMessage.info('批量导入功能待实现');
};

// 在组件挂载后获取数据
onMounted(async () => {
  try {
    await fetchEnterpriseOptions();
    await fetchHeatStationOptions();
    await fetchUnitData();
  } catch (error) {
    console.error('初始化数据失败:', error);
    ElMessage.error('初始化数据失败');
  }
});
</script>

<style scoped>
.heating-unit-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  background-color: white;
}

/* 搜索区域样式 */
.unit-search {
  width: 100%;
  margin-bottom: 16px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.form-item {
  display: flex;
  align-items: center;
  margin-right: 16px;
  margin-bottom: 16px;
}

.label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #282828;
  margin-right: 8px;
  white-space: nowrap;
}

.form-input {
  width: 180px;
  height: 32px;
}

:deep(.el-input__wrapper) {
  background: #FFFFFF;
  border-radius: 2px;
  border: 1px solid #CED3DA;
  height: 32px;
  box-shadow: none !important;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #0066FF inset !important;
}

.search-btn {
  width: 80px;
  height: 32px;
  background: #0277FD;
  border-radius: 2px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 14px;
  color: #FFFFFF;
  padding: 0;
  margin-right: 8px;
}

.reset-btn {
  width: 80px;
  height: 32px;
  padding: 0;
  border-radius: 2px;
  border: 1px solid #DCDFE6;
}

/* 表格头部样式 */
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.button-group {
  display: flex;
  gap: 8px;
}

.operation-btn {
  width: 80px;
  height: 32px;
  background: #0277FD;
  border-radius: 2px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 14px;
  color: #FFFFFF;
  padding: 0;
}

/* 表格样式 */
.table-container {
  flex: 1;
  width: 100%;
  overflow: hidden;
  margin-bottom: 16px;
}

:deep(.el-table) {
  overflow-x: hidden !important;
}

:deep(.el-table th) {
  background-color: #F4F4F4;
  height: 64px;
}

:deep(.el-table tr) {
  height: 48px;
}

:deep(.zebra-row) {
  background-color: #F8FAFD;
}

:deep(.el-table__row:hover) {
  background: #EEF5FF !important;
}

:deep(.el-table__row.selected-row) {
  background: #EEF5FF !important;
}

.operation-btns {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.operation-btn-row {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 4px 0;
}

.operation-btn-text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #0086FF;
  cursor: pointer;
}

.operation-divider {
  margin: 0 4px;
  color: #CED3DA;
}

/* 分页样式 */
.pagination-container {
  display: flex;
  justify-content: flex-end;
  padding: 0;
  margin-top: 16px;
  min-height: 32px;
}

:deep(.el-pagination) {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #222222;
  padding-right: 0;
}

:deep(.el-pagination .el-pager li) {
  width: 24px;
  height: 24px;
  background: rgba(255,255,255,0.99);
  border-radius: 2px;
  border: 1px solid #EEEEEE;
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.el-pagination .el-pager li.is-active) {
  width: 24px;
  height: 24px;
  background: #0086FF;
  border-radius: 2px;
  color: #FFFFFF;
  border: none;
}

/* 防止表格过长时遮挡分页组件 */
:deep(.el-table__body-wrapper) {
  overflow-y: auto !important;
  min-height: 200px;
}

/* 确保操作列文字不换行 */
:deep(.cell) {
  white-space: nowrap;
}
</style> 