<template>
  <div class="rtsp-player-container">
    <div class="rtsp-player-controls" v-if="showControls">
      <div class="player-status" :class="{ 'error': statusError }">
        {{ statusMessage }}
      </div>
      <div class="player-buttons">
        <button 
          class="play-btn" 
          @click="handlePlay" 
          :disabled="isPlaying">
          播放
        </button>
        <button 
          class="stop-btn" 
          @click="handleStop" 
          :disabled="!isPlaying">
          停止
        </button>
      </div>
    </div>
    
    <div class="video-wrapper">
      <!-- 全屏按钮和状态提示 -->
      <div class="overlay-controls">
        <div class="status-indicator" :class="{'active': isPlaying, 'error': statusError}">
          {{ isPlaying ? '播放中' : (statusError ? '错误' : '未播放') }}
        </div>
        <button 
          class="fullscreen-btn" 
          @click="toggleFullscreen" 
          title="全屏切换">
          <svg t="1655876293205" viewBox="0 0 1024 1024" width="16" height="16">
            <path fill="currentColor" d="M290 236.4l43.9-43.9a8.01 8.01 0 0 0-4.7-13.6L169 160c-5.1-.6-9.5 3.7-8.9 8.9L179 329.1c.8 6.6 8.9 9.4 13.6 4.7l43.7-43.7L370 423.7c3.1 3.1 8.2 3.1 11.3 0l42.4-42.3c3.1-3.1 3.1-8.2 0-11.3L290 236.4zm352.7 187.3c3.1 3.1 8.2 3.1 11.3 0l133.7-133.6 43.7 43.7a8.01 8.01 0 0 0 13.6-4.7L863.9 169c.6-5.1-3.7-9.5-8.9-8.9L694.8 179c-6.6.8-9.4 8.9-4.7 13.6l43.9 43.9L600.3 370a8.03 8.03 0 0 0 0 11.3l42.4 42.4zM845 694.9c-.8-6.6-8.9-9.4-13.6-4.7l-43.7 43.7L654 600.3a8.03 8.03 0 0 0-11.3 0l-42.4 42.3a8.03 8.03 0 0 0 0 11.3L734 787.6l-43.9 43.9a8.01 8.01 0 0 0 4.7 13.6L855 864c5.1.6 9.5-3.7 8.9-8.9L845 694.9zm-463.7-94.6a8.03 8.03 0 0 0-11.3 0L236.3 733.9l-43.7-43.7a8.01 8.01 0 0 0-13.6 4.7L160.1 855c-.6 5.1 3.7 9.5 8.9 8.9L329.2 845c6.6-.8 9.4-8.9 4.7-13.6L290 787.6 423.7 654c3.1-3.1 3.1-8.2 0-11.3l-42.4-42.4z"/>
          </svg>
        </button>
      </div>
      
      <canvas ref="videoCanvas" :width="width" :height="height"></canvas>
    </div>
  </div>
</template>

<script>
import { onMounted, onBeforeUnmount, ref, watch } from 'vue';
import { RtspPlayer } from './index';

export default {
  name: 'RtspPlayerComponent',
  props: {
    // WebSocket连接管理器实例
    wsManager: {
      type: Object,
      required: true
    },
    // 视频ID，用于区分不同视频流
    videoId: {
      type: String,
      required: true
    },
    // 播放器宽度
    width: {
      type: Number,
      default: 640
    },
    // 播放器高度
    height: {
      type: Number,
      default: 360
    },
    // 是否显示控制按钮
    showControls: {
      type: Boolean,
      default: true
    },
    // 是否自动播放
    autoplay: {
      type: Boolean,
      default: false
    },
    // 是否播放音频
    audio: {
      type: Boolean,
      default: false
    }
  },
  emits: ['status-change', 'error', 'play', 'stop'],
  setup(props, { emit }) {
    const videoCanvas = ref(null);
    const player = ref(null);
    const isPlaying = ref(false);
    const statusMessage = ref('准备就绪');
    const statusError = ref(false);
    const jsMpegLoaded = ref(false);
    const isFullscreen = ref(false);

    // 处理播放器状态变化
    const handleStatusChange = (status) => {
      statusMessage.value = status.message;
      statusError.value = status.isError;
      isPlaying.value = status.status === 'playing';
      
      emit('status-change', status);
      
      if (status.isError) {
        emit('error', new Error(status.message));
      }
    };

    // 全屏切换功能
    const toggleFullscreen = () => {
      const container = videoCanvas.value.parentElement;
      
      if (!document.fullscreenElement) {
        // 进入全屏
        if (container.requestFullscreen) {
          container.requestFullscreen();
        } else if (container.webkitRequestFullscreen) { /* Safari */
          container.webkitRequestFullscreen();
        } else if (container.msRequestFullscreen) { /* IE11 */
          container.msRequestFullscreen();
        }
        isFullscreen.value = true;
      } else {
        // 退出全屏
        if (document.exitFullscreen) {
          document.exitFullscreen();
        } else if (document.webkitExitFullscreen) { /* Safari */
          document.webkitExitFullscreen();
        } else if (document.msExitFullscreen) { /* IE11 */
          document.msExitFullscreen();
        }
        isFullscreen.value = false;
      }
    };
    
    // 监听全屏变化事件
    const handleFullscreenChange = () => {
      isFullscreen.value = !!document.fullscreenElement;
    };

    // 加载JSMpeg脚本
    const loadJSMpeg = () => {
      return new Promise((resolve, reject) => {
        if (window.JSMpeg) {
          jsMpegLoaded.value = true;
          resolve();
          return;
        }

        // 尝试多个CDN源，提高加载成功率
        const jsmpegUrls = [
          'https://cdn.jsdelivr.net/gh/phoboslab/jsmpeg@master/jsmpeg.min.js',
          'https://cdn.jsdelivr.net/npm/jsmpeg@1.0.0/jsmpeg.min.js',
          'https://unpkg.com/jsmpeg@1.0.0/jsmpeg.min.js',
          'https://cdnjs.cloudflare.com/ajax/libs/jsmpeg/0.1/jsmpeg.min.js'
        ];
        
        // 尝试加载第一个URL
        loadScript(jsmpegUrls, 0, resolve, reject);
      });
    };
    
    // 尝试从多个URL加载脚本
    const loadScript = (urls, index, resolve, reject) => {
      if (index >= urls.length) {
        reject(new Error('所有JSMpeg加载源都失败'));
        return;
      }
      
      const script = document.createElement('script');
      script.src = urls[index];
      script.onload = () => {
        console.log('JSMpeg加载成功: ' + urls[index]);
        jsMpegLoaded.value = true;
        
        // 等待一小段时间确保JSMpeg完全初始化
        setTimeout(() => {
          resolve();
        }, 100);
      };
      script.onerror = () => {
        console.error('JSMpeg加载失败: ' + urls[index] + '，尝试下一个源');
        // 尝试下一个URL
        loadScript(urls, index + 1, resolve, reject);
      };
      document.head.appendChild(script);
    };

    // 初始化播放器
    const initPlayer = async () => {
      if (!jsMpegLoaded.value) {
        try {
          await loadJSMpeg();
        } catch (error) {
          statusMessage.value = 'JSMpeg加载失败';
          statusError.value = true;
          // 5秒后自动重试
          setTimeout(() => {
            initPlayer();
          }, 5000);
          return;
        }
      }

      if (videoCanvas.value) {
        // 先显示加载中状态
        statusMessage.value = '初始化播放器...';
        
        // 给JS一点时间来处理
        setTimeout(() => {
          try {
            // 如果已存在播放器实例，销毁它
            if (player.value) {
              player.value.destroy();
              player.value = null;
            }
            
            player.value = new RtspPlayer({
              canvas: videoCanvas.value,
              videoId: props.videoId,
              wsManager: props.wsManager,
              autoplay: false, // 先不自动播放，手动控制
              audio: false,    // 禁用音频
              onStatusChange: handleStatusChange
            });
            
            // 如果设置了自动播放，延迟一段时间后开始播放
            if (props.autoplay) {
              // 添加较长延迟，确保页面和WebSocket都准备好
              setTimeout(() => {
                console.log(`开始播放视频: ${props.videoId}`);
                handlePlay();
              }, 1000 + Math.random() * 1000); // 1-2秒的随机延迟
            }
          } catch (error) {
            console.error('初始化播放器失败', error);
            statusMessage.value = '初始化播放器失败: ' + error.message;
            statusError.value = true;
            
            // 3秒后自动重试
            setTimeout(() => {
              initPlayer();
            }, 3000);
          }
        }, 300 + Math.random() * 300); // 300-600ms的随机延迟
      }
    };

    // 开始播放
    const handlePlay = async () => {
      if (!player.value) {
        await initPlayer();
        return;
      }
      
      statusMessage.value = '正在连接...';
      
      try {
        await player.value.play();
        emit('play', props.videoId);
      } catch (error) {
        console.error('播放失败', error);
        statusMessage.value = '播放失败: ' + error.message;
        statusError.value = true;
        emit('error', error);
        
        // 5秒后自动重试
        setTimeout(() => {
          handlePlay();
        }, 5000);
      }
    };

    // 停止播放
    const handleStop = () => {
      if (!player.value) return;
      
      player.value.stop();
      emit('stop', props.videoId);
    };

    // 监听videoId变化，重新初始化播放器
    watch(() => props.videoId, () => {
      if (player.value) {
        player.value.destroy();
      }
      initPlayer();
    });

    // 组件挂载时初始化
    onMounted(() => {
      // 添加全屏变化事件监听
      document.addEventListener('fullscreenchange', handleFullscreenChange);
      document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
      document.addEventListener('mozfullscreenchange', handleFullscreenChange);
      document.addEventListener('MSFullscreenChange', handleFullscreenChange);
      
      initPlayer();
    });

    // 组件卸载前销毁播放器
    onBeforeUnmount(() => {
      // 移除全屏事件监听
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
      document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
      document.removeEventListener('mozfullscreenchange', handleFullscreenChange);
      document.removeEventListener('MSFullscreenChange', handleFullscreenChange);
      
      if (player.value) {
        player.value.destroy();
        player.value = null;
      }
    });

    return {
      videoCanvas,
      isPlaying,
      statusMessage,
      statusError,
      handlePlay,
      handleStop,
      toggleFullscreen,
      isFullscreen
    };
  }
};
</script>

<style scoped>
.rtsp-player-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: v-bind('width + "px"');
  margin: 0 auto;
}

.rtsp-player-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.player-status {
  color: green;
  font-size: 14px;
}

.player-status.error {
  color: red;
}

.player-buttons button {
  margin-left: 10px;
  padding: 5px 10px;
  cursor: pointer;
}

.player-buttons button:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.video-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
}

/* 覆盖控件样式 */
.overlay-controls {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 20;
  display: flex;
  align-items: center;
  gap: 10px;
  opacity: 0.2;
  transition: opacity 0.3s;
}

.video-wrapper:hover .overlay-controls {
  opacity: 1;
}

/* 全屏按钮样式 */
.fullscreen-btn {
  background: rgba(0, 0, 0, 0.6);
  border: none;
  border-radius: 4px;
  color: white;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
}

.fullscreen-btn:hover {
  background: rgba(0, 0, 0, 0.8);
  transform: scale(1.1);
}

/* 状态指示器样式 */
.status-indicator {
  background: rgba(0, 0, 0, 0.6);
  color: #aaa;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.status-indicator.active {
  background: rgba(0, 128, 0, 0.6);
  color: white;
}

.status-indicator.error {
  background: rgba(255, 0, 0, 0.6);
  color: white;
}

canvas {
  width: 100%;
  background-color: #000;
}

/* 全屏模式样式 */
:fullscreen .video-wrapper {
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

:fullscreen canvas {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}
</style> 