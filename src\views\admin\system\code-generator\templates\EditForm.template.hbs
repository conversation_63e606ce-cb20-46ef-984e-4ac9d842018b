<template>
  <el-dialog
    :title="isEdit ? '编辑{{ pageTitle }}' : '新增{{ pageTitle }}'"
    v-model="dialogVisible"
    width="600px"
    :close-on-click-modal="false"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      style="max-height: 60vh; overflow-y: auto;"
    >
      {{#each formFields}}
      <el-form-item label="{{ label }}" prop="{{ prop }}">
        {{#if_eq type "input"}}
        <el-input 
          v-model="form.{{ prop }}" 
          placeholder="{{ placeholder }}"
          clearable
        />
        {{/if_eq}}
        
        {{#if_eq type "textarea"}}
        <el-input 
          v-model="form.{{ prop }}" 
          type="textarea"
          :rows="3"
          placeholder="{{ placeholder }}"
        />
        {{/if_eq}}
        
        {{#if_eq type "select"}}
        <el-select 
          v-model="form.{{ prop }}" 
          placeholder="{{ placeholder }}"
          style="width: 100%"
          clearable
        >
          {{#each options}}
          <el-option label="{{ label }}" value="{{ value }}" />
          {{/each}}
        </el-select>
        {{/if_eq}}
        
        {{#if_eq type "date"}}
        <el-date-picker
          v-model="form.{{ prop }}"
          type="date"
          placeholder="{{ placeholder }}"
          style="width: 100%"
          value-format="YYYY-MM-DD"
          clearable
        />
        {{/if_eq}}
        
        {{#if_eq type "time"}}
        <el-time-picker
          v-model="form.{{ prop }}"
          placeholder="{{ placeholder }}"
          style="width: 100%"
          value-format="HH:mm:ss"
          clearable
        />
        {{/if_eq}}
        
        {{#if_eq type "number"}}
        <el-input-number 
          v-model="form.{{ prop }}" 
          :min="0"
          :precision="0"
          :step="1"
          style="width: 100%"
          placeholder="{{ placeholder }}"
        />
        {{/if_eq}}
        
        {{#if_eq type "switch"}}
        <el-switch v-model="form.{{ prop }}" />
        {{/if_eq}}
        
        {{#if_eq type "radio"}}
        <el-radio-group v-model="form.{{ prop }}">
          {{#each options}}
          <el-radio label="{{ value }}">{{ label }}</el-radio>
          {{/each}}
        </el-radio-group>
        {{/if_eq}}
        
        {{#if_eq type "checkbox"}}
        <el-checkbox-group v-model="form.{{ prop }}">
          {{#each options}}
          <el-checkbox label="{{ value }}">{{ label }}</el-checkbox>
          {{/each}}
        </el-checkbox-group>
        {{/if_eq}}
        
        {{#if_eq type "upload"}}
        <el-upload
          action="#"
          list-type="picture-card"
          :auto-upload="false"
          :on-change="handleFileChange"
        >
          <el-icon><Plus /></el-icon>
        </el-upload>
        {{/if_eq}}
      </el-form-item>
      {{/each}}
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch } from 'vue'
import { ElMessage } from 'element-plus'
{{#if hasUploadField}}
import { Plus } from '@element-plus/icons-vue'
{{/if}}
import { create, update } from '@/api/{{ moduleName }}'

const props = defineProps({
  visible: {
    type: Boolean,
    required: true
  },
  formData: {
    type: Object,
    default: () => ({})
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:visible', 'success'])

// 表单引用
const formRef = ref(null)

// 对话框可见性
const dialogVisible = ref(false)

// 提交加载状态
const submitLoading = ref(false)

// 表单数据
const form = ref({
  {{#each formFields}}
  {{ prop }}: '',
  {{/each}}
})

// 表单验证规则
const rules = {
  {{#each formFields}}
  {{#if required}}
  {{ prop }}: [
    { required: true, message: '{{ errorMsg }}', trigger: 'blur' },
    {{#if_eq rule "email"}}
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
    {{/if_eq}}
    {{#if_eq rule "phone"}}
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
    {{/if_eq}}
    {{#if_eq rule "url"}}
    { type: 'url', message: '请输入正确的URL地址', trigger: 'blur' }
    {{/if_eq}}
    {{#if_eq rule "number"}}
    { type: 'number', message: '请输入数字', trigger: 'blur' }
    {{/if_eq}}
    {{#if_eq rule "integer"}}
    { pattern: /^-?\d+$/, message: '请输入整数', trigger: 'blur' }
    {{/if_eq}}
    {{#if_eq rule "regexp"}}
    { pattern: {{{ regexp }}}, message: '{{ errorMsg }}', trigger: 'blur' }
    {{/if_eq}}
  ],
  {{/if}}
  {{/each}}
}

// 监听visible属性变化
watch(() => props.visible, (val) => {
  dialogVisible.value = val
})

// 监听dialogVisible变化
watch(() => dialogVisible.value, (val) => {
  emit('update:visible', val)
})

// 监听formData变化
watch(() => props.formData, (val) => {
  if (val) {
    form.value = { ...val }
  }
}, { deep: true, immediate: true })

{{#if hasUploadField}}
// 处理文件变化
const handleFileChange = (file) => {
  console.log('文件变化', file)
  // 这里可以实现文件上传逻辑
}
{{/if}}

// 处理关闭
const handleClose = () => {
  dialogVisible.value = false
  // 重置表单
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 处理提交
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    submitLoading.value = true
    
    if (props.isEdit) {
      // 编辑模式
      await update(form.value.id, form.value)
      ElMessage.success('更新成功')
    } else {
      // 新增模式
      await create(form.value)
      ElMessage.success('创建成功')
    }
    
    // 关闭对话框
    dialogVisible.value = false
    
    // 通知父组件刷新数据
    emit('success')
  } catch (error) {
    console.error('表单验证失败', error)
  } finally {
    submitLoading.value = false
  }
}
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>
