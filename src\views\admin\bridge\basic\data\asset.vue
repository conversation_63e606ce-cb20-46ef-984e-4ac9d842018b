<template>
  <div class="bridge-asset-container">
    <!-- 查询条件 -->
    <div class="search-section">
      <el-form :model="searchForm" :inline="true" label-width="80px">
        <el-form-item label="结构类型">
          <el-select v-model="searchForm.structureType" placeholder="全部" clearable style="width: 150px">
            <el-option
              v-for="item in structureTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="技术状况">
          <el-select v-model="searchForm.techStatus" placeholder="全部" clearable style="width: 150px">
            <el-option
              v-for="item in techStatusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="养护类型">
          <el-select v-model="searchForm.maintainType" placeholder="全部" clearable style="width: 150px">
            <el-option
              v-for="item in maintainTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="养护单位">
          <el-select v-model="searchForm.manageUnitCode" placeholder="全部" clearable style="width: 150px">
            <el-option
              v-for="item in maintenanceUnits"
              :key="item.id"
              :label="item.enterpriseName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-input
            v-model="searchForm.keyword"
            placeholder="请输入桥梁名称或编码"
            style="width: 200px"
            clearable
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 表格区域 -->
    <div class="table-section">
      <!-- 操作按钮 -->
      <div class="action-bar">
        <el-button type="primary" @click="handleAdd">+ 新增</el-button>
      </div>

      <!-- 表格容器 -->
      <div class="table-container">
        <el-table
          ref="dataTable"
          v-loading="loading"
          :data="tableData"
          :header-cell-style="headerCellStyle"
          :row-class-name="tableRowClassName"
          style="width: 100%"
          @row-click="handleRowClick"
        >
          <el-table-column label="序号" width="60" align="center">
            <template #default="{ $index }">
              {{ (pagination.current - 1) * pagination.size + $index + 1 }}
            </template>
          </el-table-column>
          
          <el-table-column prop="bridgeCode" label="桥梁编码" width="140" align="center" />
          
          <el-table-column prop="bridgeName" label="桥梁名称" width="150" align="center" show-overflow-tooltip />
          
          <el-table-column prop="structureTypeName" label="结构类型" width="120" align="center" />
          
          <el-table-column prop="techStatusName" label="技术状况" width="120" align="center">
            <template #default="{ row }">
              <div :class="getTechStatusClass(row.techStatus)">
                {{ row.techStatusName }}
              </div>
            </template>
          </el-table-column>
          
          <el-table-column prop="maintainTypeName" label="养护类型" width="120" align="center" />
          
          <el-table-column prop="manageUnitName" label="养护单位" min-width="150" align="center" show-overflow-tooltip />
          
          <el-table-column prop="endDate" label="竣工日期" width="120" align="center" />
          
          <el-table-column prop="industryTypeName" label="行业属性" width="120" align="center" />
          
          <el-table-column label="位置" min-width="200" align="center" show-overflow-tooltip>
            <template #default="{ row }">
              {{ `${row.startLocation || ''} - ${row.endLocation || ''}` }}
            </template>
          </el-table-column>
          
          <el-table-column prop="updateTime" label="更新时间" width="160" align="center">
            <template #default="{ row }">
              {{ formatDateTime(row.updateTime) }}
            </template>
          </el-table-column>
          
          <el-table-column label="操作" width="200" fixed="right" align="center">
            <template #default="{ row }">
              <div class="operation-btns">
                <div class="operation-btn-row">
                  <span class="operation-btn-text" @click.stop="handleView(row)">详情</span>
                  <span class="operation-divider">|</span>
                  <span class="operation-btn-text" @click.stop="handleEdit(row)">编辑</span>
                </div>
                <div class="operation-btn-row">
                  <span class="operation-btn-text operation-btn-danger" @click.stop="handleDelete(row)">删除</span>
                  <span class="operation-divider">|</span>
                  <span class="operation-btn-text operation-btn-success" @click.stop="handleLocation(row)">定位</span>
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页区域 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 30, 50]"
          layout="total, prev, pager, next, jumper, sizes"
          :total="pagination.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 弹窗组件 -->
    <BridgeFormDialog
      v-model="dialogVisible"
      :mode="dialogMode"
      :bridge-id="currentBridgeId"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import BridgeFormDialog from '@/components/bridge/BridgeFormDialog.vue'
import {
  getBridgeBasicInfoPage,
  deleteBridgeBasicInfo,
  getMaintenanceEnterpriseList
} from '@/api/bridge'
import {
  STRUCTURE_TYPE_OPTIONS,
  TECHNICAL_CONDITION_TYPE_OPTIONS,
  MAINTENANCE_CATEGORY_TYPE_OPTIONS,
  STRUCTURE_TYPE_MAP,
  TECHNICAL_CONDITION_TYPE_MAP,
  MAINTENANCE_CATEGORY_TYPE_MAP,
  INDUSTRY_ATTRIBUTE_TYPE_MAP
} from '@/constants/bridge'

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const dialogVisible = ref(false)
const dialogMode = ref('add')
const currentBridgeId = ref('')

// 搜索表单
const searchForm = reactive({
  structureType: '',
  techStatus: '',
  maintainType: '',
  manageUnitCode: '',
  keyword: ''
})

// 分页信息
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 下拉选项数据
const structureTypeOptions = ref(STRUCTURE_TYPE_OPTIONS)
const techStatusOptions = ref(TECHNICAL_CONDITION_TYPE_OPTIONS)
const maintainTypeOptions = ref(MAINTENANCE_CATEGORY_TYPE_OPTIONS)
const maintenanceUnits = ref([])

// 获取技术状况样式类
const getTechStatusClass = (techStatus) => {
  const map = {
    '4002101': 'tech-status-good',    // 完好
    '4002102': 'tech-status-good',    // 良好
    '4002103': 'tech-status-normal',  // 合格
    '4002104': 'tech-status-bad',     // 不合格
    '4002105': 'tech-status-danger'   // 危险
  }
  return ['tech-status-tag', map[techStatus] || 'tech-status-normal']
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  
  // 如果是时间戳对象格式
  if (typeof dateTime === 'object' && dateTime.time) {
    const date = new Date(dateTime.time)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }
  
  // 如果是标准日期字符串
  if (typeof dateTime === 'string') {
    const date = new Date(dateTime)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }
  
  return ''
}

// 表头样式
const headerCellStyle = {
  background: '#F4F4F4',
  fontFamily: 'PingFangSC, PingFang SC',
  fontWeight: '500',
  fontSize: '14px',
  color: '#282828',
  height: '64px'
}

// 斑马纹和选中行样式
const tableRowClassName = ({ rowIndex }) => {
  return rowIndex % 2 === 0 ? 'even-row' : 'odd-row'
}

// 处理行点击
const handleRowClick = (row) => {
  console.log('行数据:', row)
}

// 加载表格数据
const loadTableData = async () => {
  try {
    loading.value = true
    
    const params = {
      ...searchForm,
      bridgeName: searchForm.keyword,
      bridgeCode: searchForm.keyword
    }
    
    // 移除空值
    Object.keys(params).forEach(key => {
      if (params[key] === '' || params[key] === null || params[key] === undefined) {
        delete params[key]
      }
    })
    
    const response = await getBridgeBasicInfoPage(pagination.current, pagination.size, params)
    
    if (response.code === 200) {
      const data = response.data
      tableData.value = data.records || []
      pagination.total = data.total || 0
      
      // 处理数据，添加名称映射
      tableData.value.forEach(item => {
        item.structureTypeName = item.structureTypeName || STRUCTURE_TYPE_MAP[item.structureType] || ''
        item.techStatusName = item.techStatusName || TECHNICAL_CONDITION_TYPE_MAP[item.techStatus] || ''
        item.maintainTypeName = item.maintainTypeName || MAINTENANCE_CATEGORY_TYPE_MAP[item.maintainType] || ''
        item.industryTypeName = item.industryTypeName || INDUSTRY_ATTRIBUTE_TYPE_MAP[item.industryType] || ''
      })
    } else {
      ElMessage.error(response.message || '获取数据失败')
    }
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 加载养护单位列表
const loadMaintenanceUnits = async () => {
  try {
    const response = await getMaintenanceEnterpriseList({ 'enterpriseType': '5001001' })
    if (response.code === 200) {
      maintenanceUnits.value = response.data || []
    }
  } catch (error) {
    console.error('获取养护单位列表失败:', error)
  }
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  loadTableData()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    structureType: '',
    techStatus: '',
    maintainType: '',
    manageUnitCode: '',
    keyword: ''
  })
  pagination.current = 1
  loadTableData()
}

// 新增
const handleAdd = () => {
  dialogMode.value = 'add'
  currentBridgeId.value = ''
  dialogVisible.value = true
}

// 查看详情
const handleView = (row) => {
  dialogMode.value = 'view'
  currentBridgeId.value = row.id
  dialogVisible.value = true
}

// 编辑
const handleEdit = (row) => {
  dialogMode.value = 'edit'
  currentBridgeId.value = row.id
  dialogVisible.value = true
}

// 删除
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除桥梁"${row.bridgeName}"吗？此操作不可恢复。`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const response = await deleteBridgeBasicInfo(row.id)
    if (response.code === 200) {
      ElMessage.success('删除成功')
      loadTableData()
    } else {
      ElMessage.error(response.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 定位
const handleLocation = (row) => {
  if (row.geomText) {
    // TODO: 实现地图定位功能
    ElMessage.info('定位功能开发中...')
  } else {
    ElMessage.warning('该桥梁暂无位置信息')
  }
}

// 分页大小改变
const handleSizeChange = (size) => {
  pagination.size = size
  pagination.current = 1
  loadTableData()
}

// 当前页改变
const handleCurrentChange = (current) => {
  pagination.current = current
  loadTableData()
}

// 弹窗成功回调
const handleDialogSuccess = () => {
  loadTableData()
}

// 组件挂载时执行
onMounted(() => {
  loadMaintenanceUnits()
  loadTableData()
})
</script>

<style scoped>
.bridge-asset-container {
  width: 100%;
  height: calc(100vh - 180px);
  display: flex;
  flex-direction: column;
  padding: 16px;
  background-color: #F0F2F5;
  gap: 16px;
  overflow: hidden;
}

/* 搜索表单区域 */
.search-section {
  background: #FFFFFF;
  padding: 16px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  margin-bottom: 0;
  flex-shrink: 0;
}

/* 表格区域样式 */
.table-section {
  width: 100%;
  flex: 1;
  background: white;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  padding: 16px;
  min-height: 0;
  overflow: hidden;
}

/* 操作按钮区域 */
.action-bar {
  margin-bottom: 16px;
  flex-shrink: 0;
}

/* 表格样式 */
.table-container {
  flex: 1;
  width: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 200px;
}

:deep(.el-table) {
  --el-table-border-color: #EBEEF5;
  --el-table-header-bg-color: #F5F8FA;
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

:deep(.el-table__inner-wrapper) {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

:deep(.el-table__header-wrapper) {
  flex-shrink: 0;
}

:deep(.el-table__body-wrapper) {
  flex: 1;
  overflow-y: auto !important;
}

:deep(.el-scrollbar) {
  flex: 1;
  display: flex;
  flex-direction: column;
}

:deep(.el-scrollbar__wrap) {
  flex: 1;
  overflow-x: hidden;
}

/* 表格行样式 */
:deep(.el-table .even-row) {
  background-color: #FFFFFF;
}

:deep(.el-table .odd-row) {
  background-color: #F5F8FA;
}

:deep(.el-table th) {
  background-color: #F5F8FA;
  color: #333333;
  font-weight: bold;
  height: 40px;
  padding: 0;
  border-bottom: 1px solid #EBEEF5;
}

:deep(.el-table tr) {
  height: 48px;
}

:deep(.el-table__row:hover) {
  background: #EEF5FF !important;
}

:deep(.el-table__row.selected-row) {
  background: #EEF5FF !important;
}

/* 其他表格样式 */
:deep(.el-table__empty-block) {
  min-height: 60px;
  text-align: center;
  width: 100%;
}

:deep(.el-table__empty-text) {
  line-height: 60px;
  width: 50%;
  color: #909399;
}

/* 技术状况标签样式 */
.tech-status-tag {
  width: 72px;
  height: 32px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  margin: 0 auto;
}

.tech-status-good {
  background: rgba(0, 200, 83, 0.1);
  border: 1px solid #00C853;
  color: #00C853;
}

.tech-status-normal {
  background: rgba(255, 193, 7, 0.1);
  border: 1px solid #FFC107;
  color: #FFC107;
}

.tech-status-bad {
  background: rgba(255, 87, 34, 0.1);
  border: 1px solid #FF5722;
  color: #FF5722;
}

.tech-status-danger {
  background: rgba(244, 67, 54, 0.1);
  border: 1px solid #F44336;
  color: #F44336;
}

/* 操作按钮样式 */
.operation-btns {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.operation-btn-row {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 4px 0;
}

.operation-btn-text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #0086FF;
  cursor: pointer;
}

.operation-btn-text:hover {
  text-decoration: underline;
}

.operation-btn-danger {
  color: #FF5722;
}

.operation-btn-success {
  color: #00C853;
}

.operation-divider {
  margin: 0 4px;
  color: #CED3DA;
}

/* 分页样式 */
.pagination-container {
  display: flex;
  justify-content: flex-end;
  padding: 16px 0 0 0;
  border-top: 1px solid #EBEEF5;
  flex-shrink: 0;
}

:deep(.el-pagination .el-pagination__total) {
  margin-right: 16px;
}

:deep(.el-pagination .el-input__wrapper) {
  box-shadow: 0 0 0 1px #CED3DA inset;
}

:deep(.el-pagination .el-select .el-input) {
  margin: 0 8px;
}

:deep(.el-pagination button:disabled) {
  background-color: #F5F8FA;
}

:deep(.el-pagination .el-pagination__jump) {
  margin-left: 16px;
}

/* 确保操作列文字不换行 */
:deep(.cell) {
  white-space: nowrap;
}

/* 响应式适配 */
@media screen and (max-width: 1920px) {
  .bridge-asset-container {
    width: 100%;
  }
}
</style> 