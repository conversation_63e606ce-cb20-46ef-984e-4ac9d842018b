<template>
  <div 
    class="resizable-panel" 
    :class="{ 'resize-active': isResizing }"
    :style="{ height: `${height}px` }"
  >
    <div class="panel-content">
      <slot></slot>
    </div>
    <div 
      class="resize-handle" 
      @mousedown="startResize"
      @touchstart="startResize"
    ></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue';

const props = defineProps({
  defaultHeight: {
    type: Number,
    default: 280
  },
  minHeight: {
    type: Number,
    default: 200
  },
  maxHeight: {
    type: Number,
    default: 600
  }
});

const height = ref(props.defaultHeight);
const isResizing = ref(false);
let startY = 0;
let startHeight = 0;

// 根据当前窗口高度计算最小高度限制
const actualMinHeight = computed(() => {
  // 如果窗口高度小于940px，进一步减小最小高度限制
  if (window.innerHeight <= 940) {
    return Math.max(props.minHeight * 0.85, 160); // 最小不小于160px
  }
  return props.minHeight;
});

const startResize = (event) => {
  isResizing.value = true;
  startY = event.clientY || (event.touches && event.touches[0].clientY);
  startHeight = height.value;
  
  document.addEventListener('mousemove', resize);
  document.addEventListener('touchmove', resize);
  document.addEventListener('mouseup', stopResize);
  document.addEventListener('touchend', stopResize);
  
  // 阻止默认事件，防止触摸设备上的滚动
  event.preventDefault();
};

const resize = (event) => {
  if (!isResizing.value) return;
  
  const currentY = event.clientY || (event.touches && event.touches[0].clientY);
  const diff = currentY - startY;
  
  // 计算新高度，并限制在最小和最大高度之间
  let newHeight = startHeight + diff;
  newHeight = Math.max(actualMinHeight.value, Math.min(props.maxHeight, newHeight));
  
  height.value = newHeight;
  
  // 阻止默认事件，防止触摸设备上的滚动
  event.preventDefault();
};

const stopResize = () => {
  isResizing.value = false;
  document.removeEventListener('mousemove', resize);
  document.removeEventListener('touchmove', resize);
  document.removeEventListener('mouseup', stopResize);
  document.removeEventListener('touchend', stopResize);
};

// 组件卸载时移除事件监听
onUnmounted(() => {
  document.removeEventListener('mousemove', resize);
  document.removeEventListener('touchmove', resize);
  document.removeEventListener('mouseup', stopResize);
  document.removeEventListener('touchend', stopResize);
});

// 组件挂载时设置初始高度
onMounted(() => {
  height.value = props.defaultHeight;
});
</script>

<style scoped>
.resizable-panel {
  position: relative;
  width: 100%;
  min-height: 200px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  border-radius: 8px;
  background: rgba(3, 24, 55, 0.6);
  transition: box-shadow 0.2s ease;
}

.resize-active {
  box-shadow: 0 0 8px rgba(0, 242, 241, 0.5);
}

.panel-content {
  flex: 1;
  overflow: auto;
}

.resize-handle {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 6px;
  background: transparent;
  cursor: ns-resize;
  z-index: 10;
}

.resize-handle:hover {
  background: rgba(0, 242, 241, 0.3);
}

.resize-handle:after {
  content: '';
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 30px;
  height: 3px;
  background: rgba(0, 242, 241, 0.5);
  border-radius: 2px;
}

/* 低高度屏幕适配 */
@media screen and (max-height: 940px) {
  .resizable-panel {
    min-height: 160px;
  }
  
  .resize-handle {
    height: 4px;
  }
  
  .resize-handle:after {
    width: 25px;
    height: 2px;
  }
}
</style> 