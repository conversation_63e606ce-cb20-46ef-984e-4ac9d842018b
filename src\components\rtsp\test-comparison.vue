<template>
  <div class="comparison-test">
    <h2>🔍 双服务对比测试</h2>
    <div class="player-container">
      <div class="player-section">
        <h3>🟢 jsmpeg服务 (c1002) - 正常工作</h3>
        <SimpleRtspPlayer 
          videoId="c1002"
          wsUrl="ws://172.20.130.240:32021/basic/jsmpeg"
          :width="320"
          :height="240"
          :autoplay="true"
          :showControls="true"
        />
      </div>
      
      <div class="player-section">
        <h3>🔴 jsmpeg2服务 (c1003) - 需要修复</h3>
        <MultiRtspPlayer 
          videoId="c1003"
          wsUrl="ws://172.20.130.240:32021/basic/jsmpeg2"
          :width="320"
          :height="240"
          :autoplay="true"
          :showControls="true"
        />
      </div>
    </div>
    
    <div class="analysis-info">
      <h3>📊 分析说明</h3>
      <ul>
        <li><strong>左侧 jsmpeg服务</strong>：直接返回二进制视频流，应该正常播放</li>
        <li><strong>右侧 jsmpeg2服务</strong>：返回JSON包装的Base64视频流，需要转换处理</li>
        <li><strong>对比要点</strong>：数据格式、播放器创建、写入方式、解码过程</li>
        <li><strong>查看控制台</strong>：详细的日志对比分析，标记为 🔍 [jsmpeg服务] 和 🔍 [jsmpeg2服务]</li>
      </ul>
    </div>
  </div>
</template>

<script setup>
import SimpleRtspPlayer from './SimpleRtspPlayer.vue';
import MultiRtspPlayer from './MultiRtspPlayer.vue';
</script>

<style scoped>
.comparison-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

h2 {
  text-align: center;
  color: #333;
  margin-bottom: 30px;
}

.player-container {
  display: flex;
  gap: 30px;
  justify-content: center;
  margin-bottom: 30px;
}

.player-section {
  flex: 1;
  max-width: 400px;
}

.player-section h3 {
  text-align: center;
  margin-bottom: 15px;
  padding: 10px;
  border-radius: 8px;
  color: white;
}

.player-section:first-child h3 {
  background: #22c55e;
}

.player-section:last-child h3 {
  background: #ef4444;
}

.analysis-info {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border-left: 4px solid #3b82f6;
}

.analysis-info h3 {
  color: #1f2937;
  margin-bottom: 15px;
}

.analysis-info ul {
  list-style-type: disc;
  margin-left: 20px;
}

.analysis-info li {
  margin-bottom: 8px;
  color: #4b5563;
}

.analysis-info strong {
  color: #1f2937;
}

@media (max-width: 768px) {
  .player-container {
    flex-direction: column;
    gap: 20px;
  }
  
  .player-section {
    max-width: 100%;
  }
}
</style> 