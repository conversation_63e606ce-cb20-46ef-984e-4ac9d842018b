# 排水报警信息处置功能实现

## 功能概述

根据原型设计稿要求，实现了完整的排水报警信息处置功能，包含主页面、搜索、确认、处置等完整流程。

## 实现的文件结构

```
src/views/admin/drainage/monitoring/alarm/
├── disposal.vue                                    # 主页面
└── components/
    ├── DrainAlarmDisposalSearch.vue                # 搜索组件
    ├── DrainAlarmConfirmDialog.vue                 # 确认弹窗
    ├── DrainAlarmDisposalDialog.vue                # 处置弹窗
    ├── DrainAlarmHandleFormDialog.vue              # 处置表单弹窗
    └── DrainAlarmDialog.vue                        # 详情弹窗（已存在，直接复用）
```

## 主要功能

### 1. 主页面 (disposal.vue)
- **状态筛选卡片**：全部、待确认、待处置、处置中
- **搜索表单**：支持报警来源、等级、类型、时间范围、编码查询
- **数据表格**：显示报警列表，包含完整的报警信息
- **操作按钮**：确认、处置、详情、定位

### 2. 搜索组件 (DrainAlarmDisposalSearch.vue)
- 报警来源下拉选择
- 报警等级下拉选择 
- 报警类型下拉选择
- 报警时间范围选择
- 报警编码/设备编码输入
- 查询和重置功能

### 3. 确认弹窗 (DrainAlarmConfirmDialog.vue)
- 确认结果选择：真实报警 / 误报
- 确认描述输入（100字限制）
- 表单验证和提交

### 4. 处置弹窗 (DrainAlarmDisposalDialog.vue)
- 新增处置按钮
- 处置记录列表展示
- 照片预览功能
- 记录查看和删除操作

### 5. 处置表单弹窗 (DrainAlarmHandleFormDialog.vue)
- 处置状态选择：处置中 / 已处置
- 处置描述输入
- 处置照片上传（支持多张）
- 处置时间选择
- 处置人员和单位信息
- 备注输入

## API接口

### 新增接口
```javascript
// 获取排水报警处置统计数据
getDrainAlarmDisposalStatistics(params)

// 获取报警处置记录列表
getDrainAlarmHandleList(alarmId)

// 新增报警处置记录
addDrainAlarmHandle(data)

// 删除报警处置记录
deleteDrainAlarmHandle(id)
```

### 复用接口
- `getDrainAlarmList` - 获取报警列表
- `confirmDrainAlarm` - 确认报警
- `handleDrainAlarm` - 处置报警
- `getDrainAlarmDetail` - 获取报警详情

## 常量定义

在 `src/constants/drainage.js` 中新增：

```javascript
// 处置状态相关
DRAIN_HANDLE_STATUS_OPTIONS
DRAIN_HANDLE_STATUS_MAP

// 确认结果相关  
DRAIN_CONFIRM_RESULT_OPTIONS
DRAIN_CONFIRM_RESULT_MAP
```

## 设计特点

1. **与燃气专项保持一致**：完全按照燃气专项的设计模式实现，保证UI和交互的统一性

2. **完整的状态管理**：支持待确认、待处置、处置中等不同状态的筛选和操作

3. **丰富的交互功能**：
   - 状态卡片点击筛选
   - 表格行操作按钮根据状态动态显示
   - 弹窗嵌套（处置弹窗中包含处置表单弹窗）
   - 照片上传和预览

4. **用户体验优化**：
   - 表单验证和错误提示
   - 加载状态提示
   - 操作成功后自动刷新数据
   - 时间格式化显示

5. **地图定位集成**：支持点击定位按钮在地图上显示报警位置

## 使用说明

1. **访问页面**：导航至排水监测 -> 报警管理 -> 报警处置
2. **查询数据**：使用顶部搜索表单筛选报警信息
3. **状态筛选**：点击统计卡片快速筛选不同状态的报警
4. **确认报警**：对待确认状态的报警点击"确认"按钮
5. **处置报警**：对待处置/处置中状态的报警点击"处置"按钮
6. **查看详情**：点击"详情"查看完整的报警信息和监测曲线
7. **地图定位**：点击"定位"在地图上显示报警位置

## 注意事项

1. 确保后端接口路径正确（使用 `/drain/` 前缀）
2. 照片上传功能需要配置实际的上传接口
3. 地图定位功能依赖 gishooks
4. 常量值需要与后端保持一致

## 技术栈

- Vue 3 Composition API
- Element Plus UI 组件库
- ECharts 图表库（用于详情弹窗的监测曲线）
- 响应式设计，支持不同屏幕尺寸 