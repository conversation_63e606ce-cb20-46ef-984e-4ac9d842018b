<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="800px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="gas-expert-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      :disabled="mode === 'view'"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="专家姓名" prop="expertName">
            <el-input v-model="formData.expertName" placeholder="请输入专家姓名" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="专家性别" prop="expertGender">
            <el-select v-model="formData.expertGender" placeholder="请选择" class="w-full">
              <el-option label="男" value="男" />
              <el-option label="女" value="女" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="专家年龄" prop="expertAge">
            <el-input-number v-model="formData.expertAge" :min="1" :max="120" placeholder="请输入年龄" class="w-full" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="专业领域" prop="professionalField">
            <el-input v-model="formData.professionalField" placeholder="请输入专业领域" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="联系电话" prop="contactInfo">
            <el-input v-model="formData.contactInfo" placeholder="请输入联系电话" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="工作单位" prop="belongUnitName">
            <el-input v-model="formData.belongUnitName" placeholder="请输入工作单位" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="mode !== 'view'">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { saveGasExpert, updateGasExpert } from '@/api/gas';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add', // 'add', 'edit', 'view'
    validator: (value) => ['add', 'edit', 'view'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'success']);

// 表单引用
const formRef = ref(null);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 对话框标题
const dialogTitle = computed(() => {
  const titles = {
    add: '新增专家信息',
    edit: '编辑专家信息',
    view: '专家信息详情'
  };
  return titles[props.mode] || '专家信息';
});

// 表单数据
const formData = reactive({
  id: '',
  expertName: '',
  expertGender: '',
  expertAge: '',
  professionalField: '',
  contactInfo: '',
  belongUnitName: ''
});

// 手机号码验证规则
const validatePhone = (rule, value, callback) => {
  if (!value) {
    callback(new Error('请输入联系电话'));
  } else if (!/^1[3-9]\d{9}$/.test(value)) {
    callback(new Error('请输入正确的手机号码'));
  } else {
    callback();
  }
};

// 表单验证规则
const formRules = {
  expertName: [{ required: true, message: '请输入专家姓名', trigger: 'blur' }],
  expertGender: [{ required: true, message: '请选择专家性别', trigger: 'change' }],
  expertAge: [{ required: true, message: '请输入专家年龄', trigger: 'blur' }],
  professionalField: [{ required: true, message: '请输入专业领域', trigger: 'blur' }],
  contactInfo: [{ required: true, validator: validatePhone, trigger: 'blur' }],
  belongUnitName: [{ required: true, message: '请输入工作单位', trigger: 'blur' }]
};

// 监听props.data变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    Object.keys(formData).forEach(key => {
      if (newVal[key] !== undefined) {
        formData[key] = newVal[key];
      }
    });
  }
}, { immediate: true, deep: true });

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  // 重置表单
  if (formRef.value) {
    formRef.value.resetFields();
  }
  // 重置表单数据
  Object.keys(formData).forEach(key => {
    formData[key] = '';
  });
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    const submitData = { ...formData };
    const api = props.mode === 'add' ? saveGasExpert : updateGasExpert;
    const res = await api(submitData);
    if (res.code === 200) {
      ElMessage.success(props.mode === 'add' ? '新增成功' : '更新成功');
      emit('success');
      handleClose();
    } else {
      ElMessage.error(res.message || (props.mode === 'add' ? '新增失败' : '更新失败'));
    }
  } catch (error) {
    console.error('表单验证失败', error);
  }
};
</script>

<style scoped>
.gas-expert-dialog {
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "Helvetica Neue", Arial, sans-serif;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-input__inner),
:deep(.el-select__input) {
  border-radius: 6px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 6px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  background-color: #0277FD;
  border-color: #0277FD;
}

:deep(.el-button--primary:hover) {
  background-color: #1890ff;
  border-color: #1890ff;
}

.w-full {
  width: 100%;
}
</style>