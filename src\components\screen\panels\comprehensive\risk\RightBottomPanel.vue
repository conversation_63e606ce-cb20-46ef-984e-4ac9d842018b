<template>
  <PanelBox title="隐患整改统计">
    <template #extra>
      <div class="extra-container">
        <div class="com-select">
          <CommonSelect v-model="selectedType" :options="typeOptions" @change="handleTypeChange" />
        </div>
        <div class="com-select">
          <CommonSelect v-model="selectedTime" :options="timeOptions" @change="handleTimeChange" />
        </div>
      </div>
    </template>
    <div class="panel-content">
      <!-- 隐患数量统计 -->
      <div class="stats-row">
        <div class="stat-item">
          <span class="stat-dot"><span class="stat-dot-inner"></span></span>
          <span class="stat-label">隐患总数</span>
          <span class="stat-value-red">{{ statsData.totalIssues }} 个</span>
        </div>
        <div class="stat-item">
          <span class="stat-dot"><span class="stat-dot-inner"></span></span>
          <span class="stat-label">已整改</span>
          <span class="stat-value-blue">{{ statsData.fixedIssues }} 个</span>
        </div>
        <div class="stat-item">
          <span class="stat-dot"><span class="stat-dot-inner"></span></span>
          <span class="stat-label">今日整改</span>
          <span class="stat-value-green">{{ statsData.todayFixed }} 个</span>
        </div>
      </div>

      <!-- 图表区域 -->
      <div class="charts-container">
        <div class="chart-left" ref="barChartRef"></div>
        <div class="chart-right" ref="lineChartRef"></div>
      </div>
    </div>
  </PanelBox>
</template>

<script setup>
import { ref, reactive, onMounted, onBeforeUnmount, watch, nextTick } from 'vue'
import * as echarts from 'echarts'
import PanelBox from '@/components/screen/PanelBox.vue'
import CommonSelect from '@/components/screen/CommonSelect.vue'

// 专项类型选择
const selectedType = ref('all')
const typeOptions = [
  { label: '全部', value: 'all' },
  { label: '燃气', value: 'gas' },
  { label: '排水', value: 'water' },
  { label: '桥梁', value: 'bridge' },
  { label: '供热', value: 'heating' }
]

// 时间选择
const selectedTime = ref('year')
const timeOptions = [
  { label: '近一周', value: 'week' },
  { label: '近一月', value: 'month' },
  { label: '近一年', value: 'year' }
]

// 统计数据
const statsData = reactive({
  totalIssues: 511,
  fixedIssues: 304,
  todayFixed: 91
})

// 图表DOM引用
const barChartRef = ref(null)
const lineChartRef = ref(null)

// 图表实例
let barChartInstance = null
let lineChartInstance = null

// 柱状图数据
const barChartData = reactive({
  all: {
    categories: ['燃气', '排水', '供热', '桥梁'],
    series: [
      { name: '已整改', data: [50, 80, 70, 90], color: '#F4DD70' },
      { name: '整改中', data: [30, 40, 35, 30], color: '#FF954C' },
      { name: '未整改', data: [20, 20, 25, 15], color: '#40CDFF' }
    ]
  },
  gas: {
    categories: ['燃气'],
    series: [
      { name: '已整改', data: [120], color: '#F4DD70' },
      { name: '整改中', data: [60], color: '#FF954C' },
      { name: '未整改', data: [40], color: '#40CDFF' }
    ]
  },
  water: {
    categories: ['排水'],
    series: [
      { name: '已整改', data: [90], color: '#F4DD70' },
      { name: '整改中', data: [45], color: '#FF954C' },
      { name: '未整改', data: [30], color: '#40CDFF' }
    ]
  },
  heating: {
    categories: ['供热'],
    series: [
      { name: '已整改', data: [70], color: '#F4DD70' },
      { name: '整改中', data: [35], color: '#FF954C' },
      { name: '未整改', data: [25], color: '#40CDFF' }
    ]
  },
  bridge: {
    categories: ['桥梁'],
    series: [
      { name: '已整改', data: [90], color: '#F4DD70' },
      { name: '整改中', data: [30], color: '#FF954C' },
      { name: '未整改', data: [15], color: '#40CDFF' }
    ]
  }
})

// 折线图数据
const lineChartData = reactive({
  all: {
    xAxis: ['1月', '2月', '3月', '4月', '5月'],
    values: [45, 60, 45, 50, 65]
  },
  gas: {
    xAxis: ['1月', '2月', '3月', '4月', '5月'],
    values: [40, 55, 40, 45, 60]
  },
  water: {
    xAxis: ['1月', '2月', '3月', '4月', '5月'],
    values: [50, 65, 50, 55, 70]
  },
  heating: {
    xAxis: ['1月', '2月', '3月', '4月', '5月'],
    values: [35, 50, 35, 40, 55]
  },
  bridge: {
    xAxis: ['1月', '2月', '3月', '4月', '5月'],
    values: [30, 45, 30, 35, 50]
  }
})

// 处理类型变化
const handleTypeChange = (value) => {
  console.log('专项类型变更为:', value)
  // 更新统计数据和图表
  updateStatsData(value, selectedTime.value)
  updateCharts()
}

// 处理时间范围变化
const handleTimeChange = (value) => {
  console.log('时间范围变更为:', value)
  // 更新统计数据和图表
  updateStatsData(selectedType.value, value)
  updateCharts()
}

// 更新统计数据
const updateStatsData = (type, time) => {
  // 模拟数据变化
  if (type === 'gas') {
    statsData.totalIssues = 220
    statsData.fixedIssues = 120
    statsData.todayFixed = 35
  } else if (type === 'water') {
    statsData.totalIssues = 165
    statsData.fixedIssues = 90
    statsData.todayFixed = 25
  } else if (type === 'heating') {
    statsData.totalIssues = 130
    statsData.fixedIssues = 70
    statsData.todayFixed = 20
  } else if (type === 'bridge') {
    statsData.totalIssues = 135
    statsData.fixedIssues = 90
    statsData.todayFixed = 30
  } else {
    statsData.totalIssues = 511
    statsData.fixedIssues = 304
    statsData.todayFixed = 91
  }

  // 根据时间范围调整数据
  if (time === 'week') {
    statsData.totalIssues = Math.floor(statsData.totalIssues * 0.3)
    statsData.fixedIssues = Math.floor(statsData.fixedIssues * 0.3)
    statsData.todayFixed = Math.floor(statsData.todayFixed * 0.5)
  } else if (time === 'month') {
    statsData.totalIssues = Math.floor(statsData.totalIssues * 0.6)
    statsData.fixedIssues = Math.floor(statsData.fixedIssues * 0.6)
    statsData.todayFixed = Math.floor(statsData.todayFixed * 0.7)
  }
}

// 更新图表
const updateCharts = () => {
  updateBarChart()
  updateLineChart()
}

// 创建柱状图选项
const createBarChartOption = (data) => {
  return {
    backgroundColor: 'transparent',
    grid: {
      top: '25%',
      left: '10%',
      right: '5%',
      bottom: '15%'
    },
    legend: {
      itemWidth: 12,
      itemHeight: 12,
      textStyle: {
        color: '#fff',
        fontSize: 12
      },
      top: '5%',
      icon: 'rect'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      backgroundColor: 'rgba(0, 19, 40, 0.8)',
      borderColor: 'rgba(0, 109, 232, 0.2)',
      textStyle: {
        color: '#fff',
        fontSize: 12
      }
    },
    xAxis: {
      type: 'category',
      data: data.categories,
      axisLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.3)'
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#fff',
        fontSize: 12
      }
    },
    yAxis: {
      type: 'value',
      splitLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)',
          type: 'dashed'
        }
      },
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#fff',
        fontSize: 12
      }
    },
    series: data.series.map((item, index) => {
      let gradientColor
      
      if (item.name === '已整改') {
        gradientColor = new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: '#F4DD70' },
          { offset: 1, color: 'rgba(59, 59, 2, 0.01)' }
        ])
      } else if (item.name === '整改中') {
        gradientColor = new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: '#FF954C' },
          { offset: 1, color: 'rgba(89, 49, 0, 0.01)' }
        ])
      } else {
        gradientColor = new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: '#40CDFF' },
          { offset: 1, color: 'rgba(0, 59, 89, 0.01)' }
        ])
      }
      
      return {
        name: item.name,
        type: 'bar',
        stack: 'total',
        barWidth: 25,
        emphasis: {
          focus: 'series'
        },
        itemStyle: {
          color: gradientColor
        },
        data: item.data
      }
    })
  }
}

// 创建折线图选项
const createLineChartOption = (data) => {
  return {
    backgroundColor: 'transparent',
    grid: {
      top: '25%',
      left: '5%',
      right: '5%',
      bottom: '3%',
      containLabel: true
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0, 19, 40, 0.8)',
      borderColor: 'rgba(0, 109, 232, 0.2)',
      textStyle: {
        color: '#fff',
        fontSize: 12
      }
    },
    xAxis: {
      type: 'category',
      data: data.xAxis,
      axisLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.3)',
          width: 1
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#fff',
        fontSize: 12
      }
    },
    yAxis: {
      type: 'value',
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)',
          type: 'dashed'
        }
      },
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#fff',
        fontSize: 12
      }
    },
    series: [
      {
        type: 'line',
        data: data.values,
        smooth: true,
        symbol: 'circle',
        symbolSize: 8,
        itemStyle: {
          color: '#00C2FF',
          borderColor: 'rgba(215, 236, 255, 0.3)',
          borderWidth: 6
        },
        lineStyle: {
          color: '#246BFD',
          width: 2
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(36, 107, 253, 0.4)' },
              { offset: 1, color: 'rgba(36, 107, 253, 0.01)' }
            ]
          }
        }
      }
    ]
  }
}

// 初始化柱状图
const initBarChart = () => {
  // 检查DOM元素是否存在
  if (!barChartRef.value) return

  // 确保DOM元素有尺寸
  if (barChartRef.value.clientWidth === 0 || barChartRef.value.clientHeight === 0) {
    setTimeout(initBarChart, 100)
    return
  }

  // 初始化柱状图
  barChartInstance = echarts.init(barChartRef.value)
  
  // 获取当前数据
  const data = barChartData[selectedType.value] || barChartData.all
  
  // 设置柱状图配置
  const option = createBarChartOption(data)
  barChartInstance.setOption(option)
}

// 初始化折线图
const initLineChart = () => {
  // 检查DOM元素是否存在
  if (!lineChartRef.value) return

  // 确保DOM元素有尺寸
  if (lineChartRef.value.clientWidth === 0 || lineChartRef.value.clientHeight === 0) {
    setTimeout(initLineChart, 100)
    return
  }

  // 初始化折线图
  lineChartInstance = echarts.init(lineChartRef.value)
  
  // 获取当前数据
  const data = lineChartData[selectedType.value] || lineChartData.all
  
  // 设置折线图配置
  const option = createLineChartOption(data)
  lineChartInstance.setOption(option)
}

// 更新柱状图
const updateBarChart = () => {
  if (!barChartInstance) return
  
  // 获取当前数据
  const data = barChartData[selectedType.value] || barChartData.all
  
  // 更新柱状图配置
  const option = createBarChartOption(data)
  barChartInstance.setOption(option)
}

// 更新折线图
const updateLineChart = () => {
  if (!lineChartInstance) return
  
  // 获取当前数据
  const data = lineChartData[selectedType.value] || lineChartData.all
  
  // 更新折线图配置
  const option = createLineChartOption(data)
  lineChartInstance.setOption(option)
}

// 窗口大小变化时重置图表大小
const handleResize = () => {
  barChartInstance && barChartInstance.resize()
  lineChartInstance && lineChartInstance.resize()
}

// 监听类型变化
watch(selectedType, (newVal) => {
  updateStatsData(newVal, selectedTime.value)
  updateCharts()
})

// 监听时间范围变化
watch(selectedTime, (newVal) => {
  updateStatsData(selectedType.value, newVal)
  updateCharts()
})

// 组件挂载后初始化
onMounted(() => {
  // 更新统计数据
  updateStatsData(selectedType.value, selectedTime.value)

  // 使用nextTick等待DOM更新
  nextTick(() => {
    // 等待一段时间确保DOM完全渲染
    setTimeout(() => {
      initBarChart()
      initLineChart()
      // 监听窗口大小变化
      window.addEventListener('resize', handleResize)
    }, 300)
  })

  // 备份方案：如果图表仍未初始化，重试
  setTimeout(() => {
    if (!barChartInstance && barChartRef.value) {
      initBarChart()
    }
    if (!lineChartInstance && lineChartRef.value) {
      initLineChart()
    }
  }, 1000)
})

// 组件销毁前清理图表实例
onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)
  
  if (barChartInstance) {
    barChartInstance.dispose()
    barChartInstance = null
  }
  
  if (lineChartInstance) {
    lineChartInstance.dispose()
    lineChartInstance = null
  }
})
</script>

<style scoped>
.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.extra-container {
  display: flex;
  gap: 15px;
}

.com-select {
  margin-right: 5px;
}

/* 顶部统计信息样式 */
.stats-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.stat-dot {
  width: 9px;
  height: 9px;
  background: rgba(215, 48, 48, 0.4);
  border-radius: 50%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-dot-inner {
  width: 5px;
  height: 5px;
  background: #D73030;
  border-radius: 50%;
  position: absolute;
}

.stat-label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
}

.stat-value-red {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 18px;
  line-height: 20px;
  text-align: left;
  font-style: normal;
  background: linear-gradient(90deg, #FFFFFF 0%, #DB2D05 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-value-blue {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 18px;
  line-height: 20px;
  text-align: left;
  font-style: normal;
  background: linear-gradient(90deg, #E2FBFF 0%, #23CAFF 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-value-green {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 18px;
  line-height: 20px;
  text-align: left;
  font-style: normal;
  background: linear-gradient(90deg, #FFFFFF 0%, #36F281 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 图表容器样式 */
.charts-container {
  flex: 1;
  display: flex;
  gap: 10px;
}

.chart-left, .chart-right {
  flex: 1;
  height: 100%;
  min-height: 180px;
}

/* 响应式适配 */
@media (min-height: 910px) and (max-height: 1050px) {
  .panel-content {
    padding: 10px;
    gap: 10px;
  }

  .stat-value-red,
  .stat-value-blue,
  .stat-value-green {
    font-size: 16px;
    line-height: 18px;
  }

  .charts-container {
    gap: 8px;
  }
}

@media (min-height: 900px) and (max-height: 940px) {
  .panel-content {
    padding: 8px;
    gap: 5px;
  }

  .stats-row {
    margin-bottom: 5px;
  }

  .stat-dot {
    width: 8px;
    height: 8px;
  }

  .stat-dot-inner {
    width: 4px;
    height: 4px;
  }

  .stat-label {
    font-size: 11px;
  }

  .stat-value-red,
  .stat-value-blue,
  .stat-value-green {
    font-size: 14px;
    line-height: 16px;
  }

  .charts-container {
    gap: 5px;
  }

  .chart-left, .chart-right {
    min-height: 160px;
  }
}
</style> 