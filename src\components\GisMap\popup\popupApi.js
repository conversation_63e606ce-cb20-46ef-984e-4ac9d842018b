import {
  getBridgeUsmMonitorAlarmInfo,
  getBridgeUsmMonitorDeviceInfo,
  getBridgeUsmMonitorIndicatorsInfo,
  getBridgeUsmMonitorRecordMonitorCurve,
  getDrainUsmBasicFloodPointInfo,
  getDrainUsmBasicPointInfo,
  getDrainUsmBasicWellInfo,
  getDrainUsmMonitorAlarmInfo,
  getDrainUsmMonitorDeviceInfo,
  getDrainUsmMonitorIndicatorsInfo,
  getDrainUsmMonitorRecordMonitorCurve,
  getDrainUsmRiskDangerInfo,
  getDrainUsmRiskProtectInfo,
  getGasUsmMonitorAlarmInfo,
  getGasUsmMonitorIndicatorsInfo,
  getGasUsmMonitorRecordMonitorCurve,
  getHeatUsmBasicEnterpriseInfo,
  getHeatUsmBasicHeatFactoryInfo, getHeatUsmBasicHeatStationInfo,
  getHeatUsmBasicPointInfo, getHeatUsmBasicUserInfo,
  getHeatUsmBasicWellInfo,
  getHeatUsmMonitorAlarmInfo,
  getHeatUsmMonitorDeviceInfo,
  getHeatUsmMonitorIndicatorsInfo,
  getHeatUsmMonitorRecordMonitorCurve, getHeatUsmRiskDangerInfo, getHeatUsmRiskProtectInfo,
  getUsmBasicDrainOutletInfo,
  getUsmBasicPumpStationInfo,
  getUsmBasicSewageFactoryInfo,
  getUsmMonitorDeviceInfo,
  getUsmZyGasDangerInfo,
  getUsmZyGasPointInfo,
  getUsmZyGasProtectInfo,
  getUsmZyGasStationInfo,
  getUsmZyGasWellInfo,
  postBridgeAlarmStatusList,
  postDrainAlarmStatusList,
  postGasAlarmStatusList,
  postHeatAlarmStatusList,
} from "@/api/layerData";

// popupApiInfo 存放所有popup组件的api, key为popup组件的name，value为api函数
export const popupApiInfo = {
  gas_pipeline_point: getUsmZyGasPointInfo, // 燃气管点详情
  gas_station: getUsmZyGasStationInfo, // 燃气场站详情
  gas_well: getUsmZyGasWellInfo, // 燃气窨井详情
  gas_dangerous_source: getUsmZyGasDangerInfo, // 燃气危险源详情
  gas_protection_target: getUsmZyGasProtectInfo, // 燃气防护目标详情
  gas_combustible: getUsmMonitorDeviceInfo, // 燃气可燃气体监测仪详情
  gas_manhole_cover: getUsmMonitorDeviceInfo, // 井盖监测详情
  drainage_pump_station: getUsmBasicPumpStationInfo, // 抽水泵站详情
  drainage_sewage_works: getUsmBasicSewageFactoryInfo, // 污水处理厂详情
  drainage_water_outlet: getUsmBasicDrainOutletInfo, // 排水口详情
  drainage_pipeline_point: getDrainUsmBasicPointInfo, // 排水管点详情
  drainage_well: getDrainUsmBasicWellInfo, // 排水窨井详情
  drainage_flooding_point: getDrainUsmBasicFloodPointInfo, // 排水易涝点详情
  drainage_dangerous_source: getDrainUsmRiskDangerInfo, // 排水危险源详情
  drainage_protection_target: getDrainUsmRiskProtectInfo, // 排水防护目标详情
  drainage_combustible: getDrainUsmMonitorDeviceInfo, // 排水可燃气体监测仪详情
  drainage_manhole_cover: getDrainUsmMonitorDeviceInfo, // 排水井盖监测详情
  drainage_level: getDrainUsmMonitorDeviceInfo, // 排水液位计详情
  drainage_water_quality: getDrainUsmMonitorDeviceInfo, // 排水水质监测仪详情
  heating_pipeline_point: getHeatUsmBasicPointInfo, // 供热管点详情
  heating_well: getHeatUsmBasicWellInfo, // 供热窨井详情
  heating_enterprise: getHeatUsmBasicEnterpriseInfo, // 供热企业详情
  heating_source_works: getHeatUsmBasicHeatFactoryInfo, // 供热源厂详情
  heating_station: getHeatUsmBasicHeatStationInfo, // 供热场站详情
  heating_user: getHeatUsmBasicUserInfo, // 供热用户详情
  heating_dangerous_source: getHeatUsmRiskDangerInfo, // 供热危险源详情
  heating_protection_target: getHeatUsmRiskProtectInfo, // 供热防护目标详情
  heating_combustible: getHeatUsmMonitorDeviceInfo, // 供热可燃气体监测仪详情
  heating_manhole_cover: getHeatUsmMonitorDeviceInfo, // 供热井盖监测详情
  heating_temperature: getHeatUsmMonitorDeviceInfo, // 供热温度监测仪详情
  bridge_temperature: getBridgeUsmMonitorDeviceInfo, // 桥梁温度监测仪
  bridge_static_level: getBridgeUsmMonitorDeviceInfo, // 桥梁静力水准仪
  bridge_displacement: getBridgeUsmMonitorDeviceInfo, // 桥梁位移监测仪
  bridge_strain: getBridgeUsmMonitorDeviceInfo, // 桥梁应变监测仪
  bridge_crack_sensor: getBridgeUsmMonitorDeviceInfo, // 桥梁裂缝监测仪
};

//监测设备曲线api
export const popupDeviceCurveApiInfo = {
  gas_combustible: getGasUsmMonitorRecordMonitorCurve, // 燃气可燃气体监测仪详情
  gas_manhole_cover: getGasUsmMonitorRecordMonitorCurve, // 井盖监测详情
  drainage_combustible: getDrainUsmMonitorRecordMonitorCurve, // 排水可燃气体监测仪详情
  drainage_manhole_cover: getDrainUsmMonitorRecordMonitorCurve, // 排水井盖监测详情
  drainage_level: getDrainUsmMonitorRecordMonitorCurve, // 排水液位计详情
  drainage_water_quality: getDrainUsmMonitorRecordMonitorCurve, // 排水水质监测仪详情
  heating_combustible: getHeatUsmMonitorRecordMonitorCurve, // 供热可燃气体监测仪
  heating_manhole_cover: getHeatUsmMonitorRecordMonitorCurve, // 供热井盖监测
  heating_temperature: getHeatUsmMonitorRecordMonitorCurve, // 供热温度监测仪
  bridge_temperature: getBridgeUsmMonitorRecordMonitorCurve, // 桥梁温度监测仪
  bridge_static_level: getBridgeUsmMonitorRecordMonitorCurve, // 桥梁静力水准仪
  bridge_displacement: getBridgeUsmMonitorRecordMonitorCurve, // 桥梁位移监测仪
  bridge_strain: getBridgeUsmMonitorRecordMonitorCurve, // 桥梁应变监测仪
  bridge_crack_sensor: getBridgeUsmMonitorRecordMonitorCurve, // 桥梁裂缝监测仪
}

//监测设备指标api
export const popupMonitorIndicatorsApiInfo = {
  gas_combustible: getGasUsmMonitorIndicatorsInfo, // 燃气可燃气体监测指标详情
  gas_manhole_cover: getGasUsmMonitorIndicatorsInfo, // 井盖监测指标详情
  drainage_combustible: getDrainUsmMonitorIndicatorsInfo, // 排水可燃气体监测仪详情
  drainage_manhole_cover: getDrainUsmMonitorIndicatorsInfo, // 排水井盖监测详情
  drainage_level: getDrainUsmMonitorIndicatorsInfo, // 排水液位计详情
  drainage_water_quality: getDrainUsmMonitorIndicatorsInfo, // 排水水质监测仪详情
  heating_combustible: getHeatUsmMonitorIndicatorsInfo, // 供热可燃气体监测仪
  heating_manhole_cover: getHeatUsmMonitorIndicatorsInfo, // 供热井盖监测
  heating_temperature: getHeatUsmMonitorIndicatorsInfo, // 供热温度监测仪
  bridge_temperature: getBridgeUsmMonitorIndicatorsInfo, // 桥梁温度监测仪
  bridge_static_level: getBridgeUsmMonitorIndicatorsInfo, // 桥梁静力水准仪
  bridge_displacement: getBridgeUsmMonitorIndicatorsInfo, // 桥梁位移监测仪
  bridge_strain: getBridgeUsmMonitorIndicatorsInfo, // 桥梁应变监测仪
  bridge_crack_sensor: getBridgeUsmMonitorIndicatorsInfo, // 桥梁裂缝监测仪
}

//监测设备报警详情api
export const popupMonitorAlarmApiInfo = {
  gas_combustible: getGasUsmMonitorAlarmInfo, // 燃气可燃气体监测指标详情
  gas_manhole_cover: getGasUsmMonitorAlarmInfo, // 井盖监测指标详情
  drainage_combustible: getDrainUsmMonitorAlarmInfo, // 排水可燃气体监测仪详情
  drainage_manhole_cover: getDrainUsmMonitorAlarmInfo, // 排水井盖监测详情
  drainage_level: getDrainUsmMonitorAlarmInfo, // 排水液位计详情
  drainage_water_quality: getDrainUsmMonitorAlarmInfo, // 排水水质监测仪详情
  heating_combustible: getHeatUsmMonitorAlarmInfo, // 供热可燃气体监测仪
  heating_manhole_cover: getHeatUsmMonitorAlarmInfo, // 供热井盖监测
  heating_temperature: getHeatUsmMonitorAlarmInfo, // 供热温度监测仪
  bridge_temperature: getBridgeUsmMonitorAlarmInfo, // 桥梁温度监测仪
  bridge_static_level: getBridgeUsmMonitorAlarmInfo, // 桥梁静力水准仪
  bridge_displacement: getBridgeUsmMonitorAlarmInfo, // 桥梁位移监测仪
  bridge_strain: getBridgeUsmMonitorAlarmInfo, // 桥梁应变监测仪
  bridge_crack_sensor: getBridgeUsmMonitorAlarmInfo, // 桥梁裂缝监测仪
}

export const popupMonitorAlarmStatusListApiInfo = {
  gas_combustible: postGasAlarmStatusList, // 燃气可燃气体监测指标详情
  gas_manhole_cover: postGasAlarmStatusList, // 井盖监测指标详情
  drainage_combustible: postDrainAlarmStatusList, // 排水可燃气体监测仪详情
  drainage_manhole_cover: postDrainAlarmStatusList, // 排水井盖监测详情
  drainage_level: postDrainAlarmStatusList, // 排水液位计详情
  drainage_water_quality: postDrainAlarmStatusList, // 排水水质监测仪详情
  heating_combustible: postHeatAlarmStatusList, // 供热可燃气体监测仪
  heating_manhole_cover: postHeatAlarmStatusList, // 供热井盖监测
  heating_temperature: postHeatAlarmStatusList, // 供热温度监测仪
  bridge_temperature: postBridgeAlarmStatusList, // 桥梁温度监测仪
  bridge_static_level: postBridgeAlarmStatusList, // 桥梁静力水准仪
  bridge_displacement: postBridgeAlarmStatusList, // 桥梁位移监测仪
  bridge_strain: postBridgeAlarmStatusList, // 桥梁应变监测仪
  bridge_crack_sensor: postBridgeAlarmStatusList, // 桥梁裂缝监测仪
}
