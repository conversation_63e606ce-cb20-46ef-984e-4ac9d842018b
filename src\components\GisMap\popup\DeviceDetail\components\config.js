
// 表格配置项
export const columns = [
  {
    prop: "bjsj",
    label: "报警时间",
  },
  {
    prop: "bjjb",
    label: "报警级别",
    // enum: () => getDictionaryByType("alarm_level"),
    fieldNames: { label: "name", value: "code" },
  },
  {
    prop: "bjz",
    label: "报警值",
  },
  {
    prop: "czzt",
    label: "处置状态",
    // enum: () => getDictionaryByType("alarm_state"),
    fieldNames: { label: "name", value: "code" },
  },
  { prop: "operation", label: "操作", fixed: "right", width: 80 },
];

export const alarmInfoConfig = [
  {
    label: "报警编号",
    props: "alarmCode",
    type: "row"
  },
  {
    label: "报警指标",
    props: "monitorIndexName",
    type: "row"
  },
  {
    label: "当前报警级别",
    props: "alarmLevelName",
    type: "row"
  },
  {
    label: "最高报警级别",
    props: "maxAlarmLevel",
    type: "row"
  },
  {
    label: "报警值",
    props: "alarmValue",
    type: "row"
  },
  {
    label: "报警状态",
    props: "alarmStatusName",
    type: "row"
  },
  {
    label: "报警时间",
    props: "alarmTime",
    type: "row"
  }
];

export const dealInfoConfig = {
  报警监测: [
    {
      props: "dealTime",
      label: "报警时间",
    },
    {
      props: "bjjbCn",
      label: "报警级别",
    },
    {
      props: "jczbCn",
      label: "监测指标",
    },
    {
      props: "bjz",
      label: "报警值",
    },
  ],
  报警核实: [
    {
      props: "dealTime",
      label: "核实时间",
    },
    {
      props: "dealCase",
      label: "核实结果",
    },
    {
      props: "dealUser",
      label: "现场排查人员",
      width: "120px",
    },
    {
      props: "dealPhone",
      label: "现场排查人员联系电话",
      width: "180px",
    },
    {
      props: "dealFile",
      label: "现场图片视频文件",
      width: "150px",
    },
  ],
  报警处置: [
    {
      props: "dealTime",
      label: "开始处置时间",
    },
    {
      props: "dealCase",
      label: "处置方案",
    },
    {
      props: "dealUser",
      label: "记录人员",
    },
    {
      props: "updateTime",
      label: "更新时间",
    },
  ],
  解除报警: [
    {
      props: "dealTime",
      label: "完成处置时间",
    },
    {
      props: "dealCase",
      label: "处置结果",
    },
    {
      props: "dealUser",
      label: "记录人员",
    },
    {
      props: "updateTime",
      label: "更新时间",
    },
  ],
};