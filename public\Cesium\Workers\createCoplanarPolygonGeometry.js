/**
 *    ████████                   ██                   ██       ██                  ██      ██
 *   ██░░░░░░██                 ░░                   ░██      ░██                 ░██     ░██
 *  ██      ░░   █████  ███████  ██ ██   ██  ██████  ░██   █  ░██  ██████  ██████ ░██     ░██
 * ░██          ██░░░██░░██░░░██░██░██  ░██ ██░░░░   ░██  ███ ░██ ██░░░░██░░██░░█ ░██  ██████
 * ░██    █████░███████ ░██  ░██░██░██  ░██░░█████   ░██ ██░██░██░██   ░██ ░██ ░  ░██ ██░░░██
 * ░░██  ░░░░██░██░░░░  ░██  ░██░██░██  ░██ ░░░░░██  ░████ ░░████░██   ░██ ░██    ░██░██  ░██
 *  ░░████████ ░░██████ ███  ░██░██░░██████ ██████   ░██░   ░░░██░░██████ ░███    ███░░██████
 *   ░░░░░░░░   ░░░░░░ ░░░   ░░ ░░  ░░░░░░ ░░░░░░    ░░       ░░  ░░░░░░  ░░░    ░░░  ░░░░░░
 *
 * @license
 * GeniusWorld Web SDK - http://zydlxx.cn:1234/
 * Version 4.0.0.B#develop-d3ff2330@202405231107
 *
 *
 * Copyright © 2020 - 2023 正元地理信息集团股份有限公司. All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
define(["./arrayRemoveDuplicates-3cf34348","./BoundingRectangle-aa7bf54e","./Transforms-6a5d79d3","./Matrix2-d550732e","./Matrix3-79d15570","./ComponentDatatype-e95dda25","./CoplanarPolygonGeometryLibrary-30c65fb7","./defaultValue-7b61670d","./GeometryAttribute-d24f9032","./GeometryAttributes-410c425f","./GeometryInstance-b3218e0a","./GeometryPipeline-ab8f5560","./IndexDatatype-7c192505","./Math-6acd1674","./PolygonGeometryLibrary-d2cb98fc","./PolygonPipeline-de89f886","./VertexFormat-6d750b6e","./combine-bc3d0d90","./RuntimeError-7dc4ea5a","./WebGLConstants-68839929","./OrientedBoundingBox-82cae8c7","./EllipsoidTangentPlane-d7ae8406","./AxisAlignedBoundingBox-5054a700","./IntersectionTests-044bd161","./Plane-e4eb0e88","./AttributeCompression-aa106b76","./EncodedCartesian3-e2f2e578","./ArcType-378e21f1","./EllipsoidRhumbLine-997e9b1a"],(function(e,t,n,o,a,r,i,s,l,c,y,p,d,u,m,g,b,C,h,x,f,P,A,L,w,G,F,v,E){"use strict";const _=new a.Cartesian3,T=new t.BoundingRectangle,k=new o.Cartesian2,D=new o.Cartesian2,V=new a.Cartesian3,R=new a.Cartesian3,H=new a.Cartesian3,I=new a.Cartesian3,M=new a.Cartesian3,B=new a.Cartesian3,O=new n.Quaternion,z=new a.Matrix3,S=new a.Matrix3,N=new a.Cartesian3;function Q(e,t,i,y,p,m,b,C,h){const x=e.positions;let f=g.PolygonPipeline.triangulate(e.positions2D,e.holes);f.length<3&&(f=[0,1,2]);const P=d.IndexDatatype.createTypedArray(x.length,f.length);P.set(f);let A=z;if(0!==y){let e=n.Quaternion.fromAxisAngle(b,y,O);if(A=a.Matrix3.fromQuaternion(e,A),t.tangent||t.bitangent){e=n.Quaternion.fromAxisAngle(b,-y,O);const o=a.Matrix3.fromQuaternion(e,S);C=a.Cartesian3.normalize(a.Matrix3.multiplyByVector(o,C,C),C),t.bitangent&&(h=a.Cartesian3.normalize(a.Cartesian3.cross(b,C,h),h))}}else A=a.Matrix3.clone(a.Matrix3.IDENTITY,A);const L=D;t.st&&(L.x=i.x,L.y=i.y);const w=x.length,G=3*w,F=new Float64Array(G),v=t.normal?new Float32Array(G):void 0,E=t.tangent?new Float32Array(G):void 0,T=t.bitangent?new Float32Array(G):void 0,V=t.st?new Float32Array(2*w):void 0;let R=0,H=0,I=0,M=0,B=0;for(let e=0;e<w;e++){const n=x[e];if(F[R++]=n.x,F[R++]=n.y,F[R++]=n.z,t.st)if(s.defined(p)&&p.positions.length===w)V[B++]=p.positions[e].x,V[B++]=p.positions[e].y;else{const e=m(a.Matrix3.multiplyByVector(A,n,_),k);o.Cartesian2.subtract(e,L,e);const t=u.CesiumMath.clamp(e.x/i.width,0,1),r=u.CesiumMath.clamp(e.y/i.height,0,1);V[B++]=t,V[B++]=r}t.normal&&(v[H++]=b.x,v[H++]=b.y,v[H++]=b.z),t.tangent&&(E[M++]=C.x,E[M++]=C.y,E[M++]=C.z),t.bitangent&&(T[I++]=h.x,T[I++]=h.y,T[I++]=h.z)}const N=new c.GeometryAttributes;return t.position&&(N.position=new l.GeometryAttribute({componentDatatype:r.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:F})),t.normal&&(N.normal=new l.GeometryAttribute({componentDatatype:r.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:v})),t.tangent&&(N.tangent=new l.GeometryAttribute({componentDatatype:r.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:E})),t.bitangent&&(N.bitangent=new l.GeometryAttribute({componentDatatype:r.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:T})),t.st&&(N.st=new l.GeometryAttribute({componentDatatype:r.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:V})),new l.Geometry({attributes:N,indices:P,primitiveType:l.PrimitiveType.TRIANGLES})}function j(e){const t=(e=s.defaultValue(e,s.defaultValue.EMPTY_OBJECT)).polygonHierarchy,n=e.textureCoordinates,r=s.defaultValue(e.vertexFormat,b.VertexFormat.DEFAULT);this._vertexFormat=b.VertexFormat.clone(r),this._polygonHierarchy=t,this._stRotation=s.defaultValue(e.stRotation,0),this._ellipsoid=a.Ellipsoid.clone(s.defaultValue(e.ellipsoid,a.Ellipsoid.WGS84)),this._workerName="createCoplanarPolygonGeometry",this._textureCoordinates=n,this.packedLength=m.PolygonGeometryLibrary.computeHierarchyPackedLength(t,a.Cartesian3)+b.VertexFormat.packedLength+a.Ellipsoid.packedLength+(s.defined(n)?m.PolygonGeometryLibrary.computeHierarchyPackedLength(n,o.Cartesian2):1)+2}j.fromPositions=function(e){return new j({polygonHierarchy:{positions:(e=s.defaultValue(e,s.defaultValue.EMPTY_OBJECT)).positions},vertexFormat:e.vertexFormat,stRotation:e.stRotation,ellipsoid:e.ellipsoid,textureCoordinates:e.textureCoordinates})},j.pack=function(e,t,n){return n=s.defaultValue(n,0),n=m.PolygonGeometryLibrary.packPolygonHierarchy(e._polygonHierarchy,t,n,a.Cartesian3),a.Ellipsoid.pack(e._ellipsoid,t,n),n+=a.Ellipsoid.packedLength,b.VertexFormat.pack(e._vertexFormat,t,n),n+=b.VertexFormat.packedLength,t[n++]=e._stRotation,s.defined(e._textureCoordinates)?n=m.PolygonGeometryLibrary.packPolygonHierarchy(e._textureCoordinates,t,n,o.Cartesian2):t[n++]=-1,t[n++]=e.packedLength,t};const U=a.Ellipsoid.clone(a.Ellipsoid.UNIT_SPHERE),Y=new b.VertexFormat,q={polygonHierarchy:{}};return j.unpack=function(e,t,n){t=s.defaultValue(t,0);const r=m.PolygonGeometryLibrary.unpackPolygonHierarchy(e,t,a.Cartesian3);t=r.startingIndex,delete r.startingIndex;const i=a.Ellipsoid.unpack(e,t,U);t+=a.Ellipsoid.packedLength;const l=b.VertexFormat.unpack(e,t,Y);t+=b.VertexFormat.packedLength;const c=e[t++],y=-1===e[t]?void 0:m.PolygonGeometryLibrary.unpackPolygonHierarchy(e,t,o.Cartesian2);s.defined(y)?(t=y.startingIndex,delete y.startingIndex):t++;const p=e[t++];return s.defined(n)||(n=new j(q)),n._polygonHierarchy=r,n._ellipsoid=a.Ellipsoid.clone(i,n._ellipsoid),n._vertexFormat=b.VertexFormat.clone(l,n._vertexFormat),n._stRotation=c,n._textureCoordinates=y,n.packedLength=p,n},j.createGeometry=function(t){const o=t._vertexFormat,r=t._polygonHierarchy,c=t._stRotation,g=t._textureCoordinates,b=s.defined(g);let C=r.positions;if(C=e.arrayRemoveDuplicates(C,a.Cartesian3.equalsEpsilon,!0),C.length<3)return;let h=V,x=R,f=H,P=M;const A=B;if(!i.CoplanarPolygonGeometryLibrary.computeProjectTo2DArguments(C,I,P,A))return;if(h=a.Cartesian3.cross(P,A,h),h=a.Cartesian3.normalize(h,h),!a.Cartesian3.equalsEpsilon(I,a.Cartesian3.ZERO,u.CesiumMath.EPSILON6)){const e=t._ellipsoid.geodeticSurfaceNormal(I,N);a.Cartesian3.dot(h,e)<0&&(h=a.Cartesian3.negate(h,h),P=a.Cartesian3.negate(P,P))}const L=i.CoplanarPolygonGeometryLibrary.createProjectPointsTo2DFunction(I,P,A),w=i.CoplanarPolygonGeometryLibrary.createProjectPointTo2DFunction(I,P,A);o.tangent&&(x=a.Cartesian3.clone(P,x)),o.bitangent&&(f=a.Cartesian3.clone(A,f));const G=m.PolygonGeometryLibrary.polygonsFromHierarchy(r,b,L,!1),F=G.hierarchy,v=G.polygons,E=b?m.PolygonGeometryLibrary.polygonsFromHierarchy(g,!0,(function(e){return e}),!1).polygons:void 0;if(0===F.length)return;C=F[0].outerRing;const _=n.BoundingSphere.fromPoints(C),k=m.PolygonGeometryLibrary.computeBoundingRectangle(h,w,C,c,T),D=[];for(let e=0;e<v.length;e++){const t=new y.GeometryInstance({geometry:Q(v[e],o,k,c,b?E[e]:void 0,w,h,x,f)});D.push(t)}const O=p.GeometryPipeline.combineInstances(D)[0];O.attributes.position.values=new Float64Array(O.attributes.position.values),O.indices=d.IndexDatatype.createTypedArray(O.attributes.position.values.length/3,O.indices);const z=O.attributes;return o.position||delete z.position,new l.Geometry({attributes:z,indices:O.indices,primitiveType:O.primitiveType,boundingSphere:_})},function(e,t){return s.defined(t)&&(e=j.unpack(e,t)),j.createGeometry(e)}}));
