// 风险监测仪表盘配置
export const gaugeChartOptions = {
  backgroundColor: 'transparent',
  series: [{
    type: 'gauge',
    radius: '100%',
    startAngle: 180,
    endAngle: 0,
    min: 0,
    max: 100,
    splitNumber: 10,
    axisLine: {
      lineStyle: {
        width: 20,
        color: [
          [0.3, '#67e0e3'],
          [0.7, '#37a2da'],
          [1, '#fd666d']
        ]
      }
    },
    pointer: {
      itemStyle: {
        color: '#fff'
      }
    },
    axisTick: {
      distance: -25,
      splitNumber: 5,
      lineStyle: {
        color: '#fff',
        width: 2
      }
    },
    splitLine: {
      distance: -30,
      length: 15,
      lineStyle: {
        color: '#fff',
        width: 2
      }
    },
    axisLabel: {
      color: '#fff',
      distance: -40,
      fontSize: 12
    },
    detail: {
      valueAnimation: true,
      formatter: '{value}',
      color: '#fff',
      fontSize: 30,
      offsetCenter: [0, '20%']
    },
    data: [{
      value: 97,
      name: '风险指数',
      title: {
        offsetCenter: [0, '-30%'],
        color: '#fff'
      }
    }]
  }]
}

// 监测预警分类图表配置
export const warningChartOptions = {
  backgroundColor: 'transparent',
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    }
  },
  grid: {
    top: '10%',
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'value',
    axisLine: {
      lineStyle: { color: '#fff' }
    },
    axisLabel: { color: '#fff' },
    splitLine: {
      lineStyle: { color: 'rgba(255,255,255,0.1)' }
    }
  },
  yAxis: {
    type: 'category',
    data: ['生命气', '人口', '交通', '环境', '其他'],
    axisLine: {
      lineStyle: { color: '#fff' }
    },
    axisLabel: { color: '#fff' }
  },
  series: [{
    name: '数量',
    type: 'bar',
    data: [52.1, 66.4, 75.1, 54.2, 21.8],
    itemStyle: {
      color: {
        type: 'linear',
        x: 0, y: 0, x2: 1, y2: 0,
        colorStops: [{
          offset: 0,
          color: '#00f2f1'
        }, {
          offset: 1,
          color: '#0066ff'
        }]
      }
    },
    label: {
      show: true,
      position: 'right',
      color: '#fff'
    }
  }]
}

// 监测趋势图表配置
export const trendChartOptions = {
  backgroundColor: 'transparent',
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'line',
      lineStyle: {
        color: 'rgba(0,242,241,0.3)',
        width: 1,
        type: 'solid'
      }
    }
  },
  legend: {
    data: ['预警数', '处理数'],
    textStyle: {
      color: '#fff'
    },
    top: 10
  },
  grid: {
    top: '20%',
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    boundaryGap: false,
    data: ['00:00', '03:00', '06:00', '09:00', '12:00', '15:00', '18:00', '21:00'],
    axisLine: {
      lineStyle: { color: '#fff' }
    },
    axisLabel: {
      color: '#fff',
      fontSize: 12
    },
    splitLine: {
      show: true,
      lineStyle: {
        color: 'rgba(255,255,255,0.1)',
        type: 'dashed'
      }
    }
  },
  yAxis: {
    type: 'value',
    axisLine: {
      lineStyle: { color: '#fff' }
    },
    axisLabel: {
      color: '#fff',
      fontSize: 12
    },
    splitLine: {
      show: true,
      lineStyle: {
        color: 'rgba(255,255,255,0.1)',
        type: 'dashed'
      }
    }
  },
  series: [
    {
      name: '预警数',
      type: 'line',
      smooth: true,
      symbol: 'circle',
      symbolSize: 8,
      data: [120, 132, 101, 134, 90, 230, 210, 182],
      itemStyle: {
        color: '#00f2f1'
      },
      lineStyle: {
        color: '#00f2f1',
        width: 2
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0, y: 0, x2: 0, y2: 1,
          colorStops: [
            { offset: 0, color: 'rgba(0,242,241,0.3)' },
            { offset: 1, color: 'rgba(0,242,241,0)' }
          ]
        }
      }
    },
    {
      name: '处理数',
      type: 'line',
      smooth: true,
      symbol: 'circle',
      symbolSize: 8,
      data: [220, 182, 191, 234, 290, 330, 310, 123],
      itemStyle: {
        color: '#0066ff'
      },
      lineStyle: {
        color: '#0066ff',
        width: 2
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0, y: 0, x2: 0, y2: 1,
          colorStops: [
            { offset: 0, color: 'rgba(0,102,255,0.3)' },
            { offset: 1, color: 'rgba(0,102,255,0)' }
          ]
        }
      }
    }
  ]
}

// 监测统计数据
export const monitorStats = [
  { icon: 'fa-fire', title: '生命气', value: 34 },
  { icon: 'fa-users', title: '人口', value: 58 },
  { icon: 'fa-car', title: '交通', value: 45 },
  { icon: 'fa-seedling', title: '环境', value: 29 },
  { icon: 'fa-server', title: '其他', value: 16 }
] 