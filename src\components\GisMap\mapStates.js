import { mapLoaded } from "@/hooks/gishooks.js";
import { Earth } from "@/components/GisMap/common/earth";

/**
 * 全局地图相关共享变量
 */
export const mapStates = {
  // 地图对象
  viewer: undefined, // 地图对象
  earth: undefined, // 地球对象
};

window.mapState = mapStates;

// 初始化共享对象
const readyCallback = [];
export function initStates(values) {
  for (const key in mapStates) {
    if (values[key]) {
      // @ts-ignore
      mapStates[key] = values[key];
    }
  }
  if (mapStates.viewer) {
    readyCallback.forEach((callback) => callback());
    readyCallback.splice(0, readyCallback.length);
    mapStates.earth = new Earth(mapStates.viewer);
    window.setTimeout(() => {
      mapLoaded.value = true;
    }, 400);
  }
}

export function resetStates() {
  mapStates.viewer = undefined;
  mapLoaded.value = false;
}

// 地图初始化完成Promise
export function mapReady() {
  return new Promise((resolve, reject) => {
    if (mapStates.viewer) {
      resolve(mapStates.viewer);
    } else {
      readyCallback.push(() => {
        resolve(mapStates.viewer);
      });
    }
  });
}
