<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="90%"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="bridge-form-dialog"
  >
    <el-tabs v-model="activeTab" type="border-card" class="form-tabs">
      <!-- 基本信息 -->
      <el-tab-pane label="基本信息" name="basic">
        <BasicInfoTab
          ref="basicInfoRef"
          v-model="formData.basicInfo"
          :readonly="readonly"
        />
      </el-tab-pane>

      <!-- 资料卡 -->
      <el-tab-pane label="资料卡" name="profile">
        <ProfileCardTab
          ref="profileCardRef"
          v-model="formData.profileCard"
          :readonly="readonly"
        />
      </el-tab-pane>

      <!-- 组成信息 -->
      <el-tab-pane label="组成信息" name="component">
        <ComponentInfoTab
          ref="componentInfoRef"
          v-model="formData.componentInfo"
          :readonly="readonly"
        />
      </el-tab-pane>

      <!-- 附件资料 -->
      <el-tab-pane label="附件资料" name="attachment">
        <AttachmentTab
          ref="attachmentRef"
          v-model="formData.attachment"
          :readonly="readonly"
        />
      </el-tab-pane>
    </el-tabs>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">{{ readonly ? '关闭' : '取消' }}</el-button>
        <el-button 
          v-if="!readonly" 
          type="primary" 
          @click="handleConfirm"
          :loading="loading"
        >
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import BasicInfoTab from './BasicInfoTab.vue'
import ProfileCardTab from './ProfileCardTab.vue'
import ComponentInfoTab from './ComponentInfoTab.vue'
import AttachmentTab from './AttachmentTab.vue'
import {
  saveBridgeBasicInfo,
  updateBridgeBasicInfo,
  getBridgeBasicInfoDetail,
  saveBridgeProfileGeneral,
  updateBridgeProfileGeneral,
  saveBridgeProfileSuperstructure,
  updateBridgeProfileSuperstructure,
  saveBridgeProfileSubstructure,
  updateBridgeProfileSubstructure,
  saveBridgeProfileAccessoryProject,
  updateBridgeProfileAccessoryProject,
  saveBridgeProfilePipeline,
  updateBridgeProfilePipeline,
  saveBridgeComponentInfo,
  updateBridgeComponentInfo,
  saveBridgeAttachmentInfo,
  updateBridgeAttachmentInfo
} from '@/api/bridge'
import moment from 'moment'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add', // add, edit, view
    validator: (value) => ['add', 'edit', 'view'].includes(value)
  },
  bridgeId: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update:modelValue', 'success'])

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const readonly = computed(() => props.mode === 'view')
const dialogTitle = computed(() => {
  const titles = {
    add: '新增',
    edit: '编辑',
    view: '详情'
  }
  return titles[props.mode] || '新增'
})

const activeTab = ref('basic')
const loading = ref(false)

// 各个标签页的ref
const basicInfoRef = ref()
const profileCardRef = ref()
const componentInfoRef = ref()
const attachmentRef = ref()

// 表单数据
const formData = reactive({
  basicInfo: {},
  profileCard: {},
  componentInfo: [],
  attachment: {}
})

// 监听弹窗显示状态
watch(visible, async (newVal) => {
  if (newVal) {
    activeTab.value = 'basic'
    if (props.mode === 'edit' || props.mode === 'view') {
      await loadBridgeData()
    } else {
      resetFormData()
    }
  }
})

// 重置表单数据
const resetFormData = () => {
  Object.assign(formData, {
    basicInfo: {},
    profileCard: {},
    componentInfo: [],
    attachment: {}
  })
}

// 加载桥梁数据
const loadBridgeData = async () => {
  if (!props.bridgeId) return

  try {
    loading.value = true
    
    // 加载基础信息
    const basicResponse = await getBridgeBasicInfoDetail(props.bridgeId)
    if (basicResponse.code === 200) {
      formData.basicInfo = basicResponse.data || {}
    }

    // TODO: 加载其他模块数据
    // 这里可以并行加载各个模块的数据
    
  } catch (error) {
    console.error('加载桥梁数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 格式化时间字段
const formatTimeFields = (data) => {
  if (!data || typeof data !== 'object') return data
  
  const timeFields = [
    'techStatusEvalDate', 'startDate', 'endDate', 'createTime', 'updateTime',
    'uploadTime', 'completionDate', 'constructionDate', 'designDate'
  ]
  
  const formattedData = { ...data }
  
  timeFields.forEach(field => {
    if (formattedData[field]) {
      // 如果是Date对象、时间戳或有效的时间字符串，转换为标准格式
      if (formattedData[field] instanceof Date) {
        formattedData[field] = moment(formattedData[field]).format('YYYY-MM-DD HH:mm:ss')
      } else if (typeof formattedData[field] === 'object' && formattedData[field].time) {
        // 处理时间戳对象格式
        formattedData[field] = moment(formattedData[field].time).format('YYYY-MM-DD HH:mm:ss')
      } else if (typeof formattedData[field] === 'string' && formattedData[field].trim()) {
        // 处理字符串格式的时间
        const momentDate = moment(formattedData[field])
        if (momentDate.isValid()) {
          formattedData[field] = momentDate.format('YYYY-MM-DD HH:mm:ss')
        }
      } else if (typeof formattedData[field] === 'number') {
        // 处理时间戳
        formattedData[field] = moment(formattedData[field]).format('YYYY-MM-DD HH:mm:ss')
      }
    }
  })
  
  return formattedData
}

// 递归格式化对象中的时间字段
const formatNestedTimeFields = (obj) => {
  if (!obj || typeof obj !== 'object') return obj
  
  if (Array.isArray(obj)) {
    return obj.map(item => formatNestedTimeFields(item))
  }
  
  const formatted = {}
  for (const [key, value] of Object.entries(obj)) {
    if (value && typeof value === 'object' && !Array.isArray(value)) {
      formatted[key] = formatNestedTimeFields(value)
    } else if (Array.isArray(value)) {
      formatted[key] = value.map(item => formatNestedTimeFields(item))
    } else {
      formatted[key] = value
    }
  }
  
  return formatTimeFields(formatted)
}

// 保存数据
const saveData = async () => {
  try {
    loading.value = true

    // 1. 先保存基础信息
    let bridgeId = props.bridgeId
    if (props.mode === 'add') {
      const formattedBasicInfo = formatTimeFields(formData.basicInfo)
      const basicResponse = await saveBridgeBasicInfo(formattedBasicInfo)
      if (basicResponse.code === 200) {
        bridgeId = basicResponse.data.id
        ElMessage.success('基础信息保存成功')
      } else {
        throw new Error('基础信息保存失败')
      }
    } else {
      const formattedBasicInfo = formatTimeFields({
        ...formData.basicInfo,
        id: bridgeId
      })
      const basicResponse = await updateBridgeBasicInfo(formattedBasicInfo)
      if (basicResponse.code === 200) {
        ElMessage.success('基础信息更新成功')
      } else {
        throw new Error('基础信息更新失败')
      }
    }

    // 2. 保存资料卡信息
    if (formData.profileCard && Object.keys(formData.profileCard).length > 0) {
      await saveProfileCardData(bridgeId)
    }

    // 3. 保存组成信息
    if (formData.componentInfo && formData.componentInfo.length > 0) {
      await saveComponentData(bridgeId)
    }

    // 4. 保存附件信息
    if (formData.attachment && Object.keys(formData.attachment).length > 0) {
      await saveAttachmentData(bridgeId)
    }

    ElMessage.success('保存成功')
    emit('success')
    visible.value = false

  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error(error.message || '保存失败')
  } finally {
    loading.value = false
  }
}

// 保存资料卡数据
const saveProfileCardData = async (bridgeId) => {
  const { general, superstructure, substructure, accessory, pipeline } = formData.profileCard

  const savePromises = []

  // 一般资料
  if (general && Object.keys(general).length > 0) {
    const data = formatTimeFields({ ...general, bridgeId })
    savePromises.push(
      props.mode === 'add' 
        ? saveBridgeProfileGeneral(data)
        : updateBridgeProfileGeneral(data)
    )
  }

  // 上部结构
  if (superstructure && Object.keys(superstructure).length > 0) {
    const data = formatTimeFields({ ...superstructure, bridgeId })
    savePromises.push(
      props.mode === 'add'
        ? saveBridgeProfileSuperstructure(data)
        : updateBridgeProfileSuperstructure(data)
    )
  }

  // 下部结构
  if (substructure && Object.keys(substructure).length > 0) {
    const data = formatTimeFields({ ...substructure, bridgeId })
    savePromises.push(
      props.mode === 'add'
        ? saveBridgeProfileSubstructure(data)
        : updateBridgeProfileSubstructure(data)
    )
  }

  // 附属工程
  if (accessory && Object.keys(accessory).length > 0) {
    const data = formatTimeFields({ ...accessory, bridgeId })
    savePromises.push(
      props.mode === 'add'
        ? saveBridgeProfileAccessoryProject(data)
        : updateBridgeProfileAccessoryProject(data)
    )
  }

  // 附挂管线
  if (pipeline && Object.keys(pipeline).length > 0) {
    const data = formatTimeFields({ ...pipeline, bridgeId })
    savePromises.push(
      props.mode === 'add'
        ? saveBridgeProfilePipeline(data)
        : updateBridgeProfilePipeline(data)
    )
  }

  if (savePromises.length > 0) {
    await Promise.all(savePromises)
  }
}

// 保存组成信息数据
const saveComponentData = async (bridgeId) => {
  const savePromises = formData.componentInfo.map(item => {
    const data = formatTimeFields({ ...item, bridgeId })
    return props.mode === 'add'
      ? saveBridgeComponentInfo(data)
      : updateBridgeComponentInfo(data)
  })

  if (savePromises.length > 0) {
    await Promise.all(savePromises)
  }
}

// 保存附件数据
const saveAttachmentData = async (bridgeId) => {
  const { design, construction, renovation, other } = formData.attachment
  const allAttachments = [
    ...(design || []),
    ...(construction || []),
    ...(renovation || []),
    ...(other || [])
  ]

  const savePromises = allAttachments.map(item => {
    const data = formatTimeFields({ ...item, bridgeId })
    return props.mode === 'add'
      ? saveBridgeAttachmentInfo(data)
      : updateBridgeAttachmentInfo(data)
  })

  if (savePromises.length > 0) {
    await Promise.all(savePromises)
  }
}

// 表单验证
const validateForm = async () => {
  const validationPromises = []

  // 验证基础信息
  if (basicInfoRef.value) {
    validationPromises.push(basicInfoRef.value.validate())
  }

  // 验证资料卡
  if (profileCardRef.value) {
    validationPromises.push(profileCardRef.value.validate())
  }

  // 验证组成信息
  if (componentInfoRef.value) {
    validationPromises.push(componentInfoRef.value.validate())
  }

  // 验证附件
  if (attachmentRef.value) {
    validationPromises.push(attachmentRef.value.validate())
  }

  try {
    const results = await Promise.allSettled(validationPromises)
    return results.every(result => result.status === 'fulfilled' && result.value !== false)
  } catch (error) {
    return false
  }
}

// 确定按钮点击
const handleConfirm = async () => {
  const isValid = await validateForm()
  if (!isValid) {
    ElMessage.error('请检查表单填写是否正确')
    return
  }

  await saveData()
}

// 取消按钮点击
const handleCancel = () => {
  visible.value = false
}

// 关闭弹窗前确认
const handleClose = async (done) => {
  if (readonly.value) {
    done()
    return
  }

  try {
    await ElMessageBox.confirm('确定要关闭吗？未保存的数据将丢失。', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    done()
  } catch {
    // 用户取消关闭
  }
}
</script>

<style scoped>
.bridge-form-dialog {
  --el-dialog-padding-primary: 0;
}

:deep(.el-dialog__body) {
  padding: 0;
}

.form-tabs {
  border: none;
  box-shadow: none;
}

:deep(.form-tabs .el-tabs__content) {
  padding: 0;
  max-height: 70vh;
  overflow-y: auto;
}

:deep(.form-tabs .el-tabs__header) {
  margin: 0;
  background: #f5f7fa;
}

.dialog-footer {
  padding: 20px;
  text-align: right;
  border-top: 1px solid #ebeef5;
}
</style> 