<template>
  <PanelBox title="视频监控" class="left-middle-panel">
    <template #extra>
      <div class="more-btn" @click="openVideoListModal">
        更多
      </div>
    </template>
    <div class="panel-content flooding-risk-left-middle-panel">
      <div class="video-grid">
        <div class="video-item" v-for="(video, index) in videoList" :key="index">
          <div class="video-title">{{ video.title }}</div>
          <VideoPlayer 
            :src="video.src" 
            :muted="true" 
            :autoplay="true"
            :showControls="true"
            :ref="el => { if (el) playerRefs[index] = el }"
            class="video-player"
          />
        </div>
      </div>
    </div>
  </PanelBox>
  
  <!-- 视频列表弹窗 -->
  <VideoListModal v-model="showVideoListModal" />
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import PanelBox from '@/components/screen/PanelBox.vue'
import VideoPlayer from '@/components/screen/common/VideoPlayer.vue';
import VideoListModal from './VideoListModal.vue';

// 播放器引用集合
const playerRefs = ref([]);

// 视频列表数据
const videoList = ref([
  {
    title: '排水管网1号监控点',
    src: 'https://bitdash-a.akamaihd.net/content/MI201109210084_1/m3u8s/f08e80da-bf1d-4e3d-8899-f0f6155f6efa.m3u8'
  },
  {
    title: '排水管网2号监控点',
    src: 'https://bitdash-a.akamaihd.net/content/MI201109210084_1/m3u8s/f08e80da-bf1d-4e3d-8899-f0f6155f6efa.m3u8'
  },
  {
    title: '排水管网3号监控点',
    src: 'https://test-streams.mux.dev/x36xhzz/x36xhzz.m3u8'
  },
  {
    title: '排水管网4号监控点',
    src: 'https://test-streams.mux.dev/x36xhzz/x36xhzz.m3u8'
  },
  {
    title: '排水管网5号监控点',
    src: 'https://bitdash-a.akamaihd.net/content/MI201109210084_1/m3u8s/f08e80da-bf1d-4e3d-8899-f0f6155f6efa.m3u8'
  },
  {
    title: '排水管网6号监控点',
    src: 'https://bitdash-a.akamaihd.net/content/MI201109210084_1/m3u8s/f08e80da-bf1d-4e3d-8899-f0f6155f6efa.m3u8'
  }
]);

// 视频列表弹窗显示状态
const showVideoListModal = ref(false);

// "更多"按钮点击事件
const openVideoListModal = () => {
  showVideoListModal.value = true;
};

// 组件卸载时处理
onUnmounted(() => {
  // 销毁播放器，释放资源
  playerRefs.value.forEach(player => {
    if (player && player.player) {
      player.player.dispose();
    }
  });
});
</script>

<style scoped>
.left-middle-panel {
  flex: 1;
}
.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

/* 视频网格布局 - 2列4行 */
.video-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(4, 1fr);
  gap: 10px;
  height: 100%;
  width: 100%;
}

.video-item {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 4px;
  overflow: hidden;
  background-color: rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(0, 242, 241, 0.2);
}

.video-title {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 200px;
  height: 32px;
  background: #000000;
  opacity: 0.4;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 10px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #FFFFFF;
}

/* 视频播放器响应式调整 */
.video-player {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 更多按钮样式 */
.more-btn {
  font-family: PingFangSC, 'PingFang SC';
  font-size: 12px;
  color: #3AA1FF;
  cursor: pointer;
  text-decoration: underline;
  margin-right: 30px;
}

.more-btn:hover {
  color: #66B8FF;
}

/* 响应式布局 */
:root {
  --vh-ratio: 1;
}

@media screen and (max-height: 911px) {
  :root {
    --vh-ratio: 0.85;
  }
  
  .video-grid {
    gap: 6px;
  }
  
  .video-title {
    height: 24px;
    font-size: 12px;
    width: 180px;
  }
}

@media screen and (min-height: 1080px) {
  :root {
    --vh-ratio: 1.15;
  }
  
  .video-title {
    height: 36px;
    font-size: 16px;
  }
}

/* 超小屏幕适配 */
@media screen and (max-height: 800px) {
  .video-grid {
    gap: 4px;
  }
  
  .video-title {
    height: 20px;
    width: 160px;
    font-size: 11px;
  }
  
  .panel-content {
    padding: 10px;
    gap: 10px;
  }
}
</style> 