<template>
  <div class="zoom-btns">
    <div class="btn-in" @click="onZoomIn">
      <el-icon><Plus /></el-icon>
    </div>
    <div class="btn-in" @click="onZoomOut">
      <el-icon><Minus /></el-icon>
    </div>
    <div class="btn-in" @click="onReset">
      <el-icon><Aim /></el-icon>
    </div>
  </div>
</template>

<script setup>
import {mapStates} from "@/components/GisMap/mapStates.js";
import {onMounted} from "vue";
import bus from "@/utils/mitt.js";

const resetPostion = () => {
    mapStates.earth.camera.flyTo({
        lon: 115.097,
        lat: 35.288,
        height: 8000,
        orientation: {
            heading: 0,
            pitch: -90, //-45
            roll: 0,
        },
    });
};

const onZoomIn = () => {
    mapStates.earth.camera.onMapZoomIn();
};

const onZoomOut = () => {
    mapStates.earth.camera.onMapZoomOut();
};

const onReset = () => {
    resetPostion();
    bus.emit("resetGisPopup");
};

onMounted(() => {});
</script>

<style lang="scss" scoped>
.zoom-btns {
  position: absolute;
  z-index: 999;
  right: 18px;
  bottom: 18px;
  background: #ffffff;
  box-shadow: 0px 3px 4px 0px rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  padding: 0px 5px;
  cursor: auto;
  .btn-in {
    display: flex;
    align-items: center;
    justify-content: center;

    width: 24px;
    height: 36px;
    cursor: pointer;
    position: relative;
    &:nth-child(1) {
      border-bottom: 1px solid #eee;
    }
    &:nth-child(2) {
      border-bottom: 1px solid #eee;
    }
    :deep(.el-icon) {
      color: #333;
    }
  }
}
</style>
