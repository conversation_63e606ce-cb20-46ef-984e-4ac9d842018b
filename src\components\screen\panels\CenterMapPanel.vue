<template>
  <div class="map-container">
    <div class="map-content">
      <!-- 这里可以放置实际的地图组件 -->
      <div class="map-placeholder">
        <div class="map-info">
          <div>{{ activeTab }} - {{ activeSubTab }} 地图</div>
          <div>dm县生命线安全监管平台</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps, watch } from 'vue';

const props = defineProps({
  activeTab: {
    type: String,
    required: true
  },
  activeSubTab: {
    type: String,
    default: 'overview'
  }
});
// 更新地图视图
const updateMapView = (tab, subTab) => {
  // 这里实现地图更新逻辑
  // 例如：根据不同的一级菜单和二级菜单显示不同的图层、位置等
  console.log(`更新地图视图: ${tab}/${subTab}`);
};
// 监听路由参数变化，可以在这里处理地图更新
watch(
  () => [props.activeTab, props.activeSubTab],
  ([newTab, newSubTab]) => {
    console.log(`地图组件：切换到 ${newTab}/${newSubTab}`);
    // 这里可以添加地图更新逻辑
    updateMapView(newTab, newSubTab);
  },
  { immediate: true }
);


</script>

<style scoped>
.map-container {
  width: 100%;
  height: 100%;
  background: rgba(0, 35, 80, 0.5);
  border: 1px solid rgba(0, 242, 241, 0.2);
  border-radius: 8px;
  box-shadow: 0 0 10px rgba(0, 242, 241, 0.1);
  overflow: hidden;
}

.map-content {
  width: 100%;
  height: 100%;
  position: relative;
}

.map-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at center, rgba(26,59,109,0.6) 0%, rgba(13,35,65,0.6) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.map-info {
  color: #00f2f1;
  font-size: 20px;
  text-align: center;
}
</style> 