<template>
  <el-dialog
    title="配置管理"
    v-model="dialogVisible"
    width="800px"
    :close-on-click-modal="false"
    :before-close="handleClose"
  >
    <div class="config-manager">
      <div class="config-list">
        <div class="list-header">
          <h3>配置列表</h3>
          <div class="header-actions">
            <el-button type="primary" size="small" @click="handleSaveConfig">保存当前配置</el-button>
            <el-button type="success" size="small" @click="handleImport">导入配置</el-button>
          </div>
        </div>
        
        <el-table :data="configList" style="width: 100%" height="400px">
          <el-table-column prop="name" label="配置名称" min-width="150" />
          <el-table-column prop="description" label="描述" min-width="200" />
          <el-table-column prop="author" label="作者" min-width="100" />
          <el-table-column prop="updateTime" label="更新时间" min-width="150" />
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button type="primary" size="small" @click="handleLoadConfig(row)">加载</el-button>
              <el-button type="success" size="small" @click="handleExportConfig(row)">导出</el-button>
              <el-button type="danger" size="small" @click="handleDeleteConfig(row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    
    <!-- 保存配置对话框 -->
    <el-dialog
      v-model="saveDialogVisible"
      title="保存配置"
      width="500px"
      append-to-body
    >
      <el-form :model="saveForm" :rules="saveRules" ref="saveFormRef" label-width="100px">
        <el-form-item label="配置名称" prop="name">
          <el-input v-model="saveForm.name" placeholder="请输入配置名称" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="saveForm.description" type="textarea" placeholder="请输入描述" />
        </el-form-item>
        <el-form-item label="作者" prop="author">
          <el-input v-model="saveForm.author" placeholder="请输入作者" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="saveDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmSaveConfig">确定</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 导入配置对话框 -->
    <el-dialog
      v-model="importDialogVisible"
      title="导入配置"
      width="500px"
      append-to-body
    >
      <el-form :model="importForm" label-width="100px">
        <el-form-item label="配置文件">
          <el-upload
            action="#"
            :auto-upload="false"
            :limit="1"
            accept=".json"
            :on-change="handleFileChange"
          >
            <el-button type="primary">选择文件</el-button>
            <template #tip>
              <div class="el-upload__tip">只能上传 JSON 文件</div>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="importDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmImport">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </el-dialog>
</template>

<script setup>
import { ref, defineProps, defineEmits, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getConfigList, saveConfig, deleteConfig, exportConfig, validateConfig } from '../utils/configManager'

const props = defineProps({
  visible: {
    type: Boolean,
    required: true
  },
  currentConfig: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update:visible', 'load-config'])

// 对话框可见性
const dialogVisible = ref(false)

// 保存对话框可见性
const saveDialogVisible = ref(false)

// 导入对话框可见性
const importDialogVisible = ref(false)

// 配置列表
const configList = ref([])

// 保存表单
const saveForm = ref({
  name: '',
  description: '',
  author: ''
})

// 保存表单验证规则
const saveRules = {
  name: [
    { required: true, message: '请输入配置名称', trigger: 'blur' }
  ]
}

// 保存表单引用
const saveFormRef = ref(null)

// 导入表单
const importForm = ref({
  file: null
})

// 监听visible属性变化
watch(() => props.visible, (val) => {
  dialogVisible.value = val
  if (val) {
    loadConfigList()
  }
})

// 监听dialogVisible变化
watch(() => dialogVisible.value, (val) => {
  emit('update:visible', val)
})

// 加载配置列表
const loadConfigList = () => {
  configList.value = getConfigList()
}

// 处理关闭
const handleClose = () => {
  dialogVisible.value = false
}

// 处理保存配置
const handleSaveConfig = () => {
  // 验证当前配置
  const { valid, errors } = validateConfig(props.currentConfig)
  
  if (!valid) {
    ElMessage.error(`配置验证失败: ${errors.join(', ')}`)
    return
  }
  
  // 初始化表单
  saveForm.value = {
    name: props.currentConfig.basic.moduleName || '',
    description: props.currentConfig.basic.description || '',
    author: props.currentConfig.basic.author || ''
  }
  
  saveDialogVisible.value = true
}

// 确认保存配置
const confirmSaveConfig = async () => {
  if (!saveFormRef.value) return
  
  try {
    await saveFormRef.value.validate()
    
    // 保存配置
    const result = saveConfig(saveForm.value.name, props.currentConfig)
    
    if (result) {
      ElMessage.success('保存成功')
      saveDialogVisible.value = false
      loadConfigList()
    } else {
      ElMessage.error('保存失败')
    }
  } catch (error) {
    console.error('表单验证失败', error)
  }
}

// 处理加载配置
const handleLoadConfig = (row) => {
  ElMessageBox.confirm('加载此配置将覆盖当前配置，是否继续？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    emit('load-config', row.config)
    ElMessage.success('加载配置成功')
    dialogVisible.value = false
  }).catch(() => {
    // 取消操作
  })
}

// 处理导出配置
const handleExportConfig = (row) => {
  const result = exportConfig(row.config, `${row.name}.json`)
  
  if (result) {
    ElMessage.success('导出成功')
  } else {
    ElMessage.error('导出失败')
  }
}

// 处理删除配置
const handleDeleteConfig = (row) => {
  ElMessageBox.confirm('确认删除此配置吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const result = deleteConfig(row.name)
    
    if (result) {
      ElMessage.success('删除成功')
      loadConfigList()
    } else {
      ElMessage.error('删除失败')
    }
  }).catch(() => {
    // 取消操作
  })
}

// 处理导入
const handleImport = () => {
  importDialogVisible.value = true
}

// 处理文件变化
const handleFileChange = (file) => {
  importForm.value.file = file.raw
}

// 确认导入
const confirmImport = () => {
  if (!importForm.value.file) {
    ElMessage.warning('请选择文件')
    return
  }
  
  const reader = new FileReader()
  reader.onload = (e) => {
    try {
      const config = JSON.parse(e.target.result)
      
      // 验证配置格式
      const { valid, errors } = validateConfig(config)
      
      if (!valid) {
        ElMessage.error(`配置验证失败: ${errors.join(', ')}`)
        return
      }
      
      // 加载配置
      emit('load-config', config)
      
      ElMessage.success('导入成功')
      importDialogVisible.value = false
      dialogVisible.value = false
    } catch (error) {
      console.error('导入失败', error)
      ElMessage.error('导入失败，无效的JSON格式')
    }
  }
  
  reader.readAsText(importForm.value.file)
}

// 组件挂载时加载配置列表
onMounted(() => {
  loadConfigList()
})
</script>

<style scoped>
.config-manager {
  width: 100%;
}

.config-list {
  width: 100%;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.header-actions {
  display: flex;
  gap: 8px;
}
</style>
