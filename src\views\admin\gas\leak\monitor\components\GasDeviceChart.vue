<template>
  <div class="chart-wrapper">
    <div class="chart" ref="chartRef"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue';
import * as echarts from 'echarts';

const props = defineProps({
  chartData: {
    type: Object,
    required: true
  }
});

const chartRef = ref(null);
let chart = null;

const initChart = () => {
  if (!chartRef.value) return;
  
  // 初始化图表
  chart = echarts.init(chartRef.value);
  
  // 配置图表选项
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function(params) {
        let result = `${params[0].name}<br/>`;
        params.forEach(param => {
          result += `${param.seriesName}: ${param.value}<br/>`;
        });
        return result;
      }
    },
    legend: {
      data: props.chartData.series.map(item => item.name),
      bottom: 0,
      itemWidth: 12,
      itemHeight: 12,
      textStyle: {
        color: '#333',
        fontSize: 12
      }
    },
    grid: {
      left: '3%',
      right: '3%',
      top: '3%',
      bottom: '13%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: props.chartData.categories,
      axisTick: {
        alignWithLabel: true
      },
      axisLine: {
        lineStyle: {
          color: '#E1E1E1'
        }
      },
      axisLabel: {
        color: '#666',
        fontSize: 12,
        interval: 0,
        rotate: 30
      }
    },
    yAxis: {
      type: 'value',
      name: '数量',
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      splitLine: {
        lineStyle: {
          type: 'dashed',
          color: '#E1E1E1'
        }
      }
    },
    series: props.chartData.series.map(item => ({
      name: item.name,
      type: 'bar',
      barWidth: 16,
      itemStyle: {
        color: item.color
      },
      data: item.data
    }))
  };
  
  // 渲染图表
  chart.setOption(option);
  
  // 添加窗口大小变化时的自适应处理
  window.addEventListener('resize', () => {
    chart && chart.resize();
  });
};

// 监听属性变化重新渲染图表
watch(() => props.chartData, () => {
  if (chart) {
    chart.dispose();
    chart = null;
  }
  initChart();
}, { deep: true });

onMounted(() => {
  initChart();
});

onUnmounted(() => {
  if (chart) {
    chart.dispose();
    chart = null;
  }
  window.removeEventListener('resize', () => {
    chart && chart.resize();
  });
});
</script>

<style scoped>
.chart-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.chart {
  width: 100%;
  height: 100%;
}
</style> 