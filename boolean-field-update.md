# 生效状态字段布尔值修改总结

## 修改概述

将报警阈值管理功能中的"是否生效"字段从数字类型（1/0）修改为布尔类型（true/false），以匹配后端接口的数据格式。

## 修改详情

### 1. 常量定义修改 (src/constants/drainage.js)

```javascript
// 修改前
export const ENABLED_STATUS_OPTIONS = [
  { label: '是', value: 1 },
  { label: '否', value: 0 }
];

export const ENABLED_STATUS_MAP = {
  1: '是',
  0: '否'
};

// 修改后
export const ENABLED_STATUS_OPTIONS = [
  { label: '是', value: true },
  { label: '否', value: false }
];

export const ENABLED_STATUS_MAP = {
  true: '是',
  false: '否'
};
```

### 2. 弹窗组件修改 (ThresholdDialog.vue)

**表单数据默认值：**
```javascript
// 修改前
const formData = reactive({
  isEnabled: 1,
  // ...
});

// 修改后
const formData = reactive({
  isEnabled: true,
  // ...
});
```

**表单重置逻辑：**
```javascript
// 修改前
if (key === 'isEnabled') {
  formData[key] = 1;
}

// 修改后
if (key === 'isEnabled') {
  formData[key] = true;
}
```

**删除了不必要的类型转换：**
- 移除了 `isEnabled` 字段的布尔值到数字的转换逻辑
- 现在直接使用接口返回的布尔值

### 3. 主页面列表修改 (threshold.vue)

**开关组件配置：**
```vue
<!-- 修改前 -->
<el-switch 
  v-model="row.isEnabled" 
  :active-value="1" 
  :inactive-value="0"
  disabled
/>

<!-- 修改后 -->
<el-switch 
  v-model="row.isEnabled" 
  :active-value="true" 
  :inactive-value="false"
  disabled
/>
```

### 4. 搜索组件修改 (ThresholdSearch.vue)

**Placeholder优化：**
```vue
<!-- 修改前 -->
<el-select placeholder="是" ...>

<!-- 修改后 -->
<el-select placeholder="请选择" ...>
```

## 数据流程

### 新增/编辑提交
1. 表单数据：`{ isEnabled: true }` 或 `{ isEnabled: false }`
2. 提交到后端：直接传递布尔值
3. 后端存储：布尔类型

### 列表查询
1. 搜索条件：`{ isEnabled: true }` 或 `{ isEnabled: false }` 或 `''`
2. 后端返回：布尔值
3. 列表显示：开关组件正确显示布尔状态

### 编辑回显
1. 后端返回：`{ isEnabled: true }`
2. 表单回显：直接使用布尔值，无需转换
3. 界面显示：下拉框正确显示"是"或"否"

## 优势

### ✅ 数据一致性
- 前后端数据类型完全一致
- 避免了类型转换的复杂性和潜在错误

### ✅ 代码简洁性
- 移除了不必要的类型转换逻辑
- 减少了代码复杂度

### ✅ 用户体验
- 界面显示更加准确
- 搜索和筛选功能更稳定

## 测试验证

请验证以下功能：

1. **新增功能**：
   - 选择"是"，保存后应该传递 `true`
   - 选择"否"，保存后应该传递 `false`

2. **编辑回显**：
   - 编辑现有记录，"是否生效"应该正确显示"是"或"否"

3. **列表显示**：
   - 列表中的开关应该正确显示生效状态

4. **搜索筛选**：
   - 选择"是"筛选，应该只显示生效的记录
   - 选择"否"筛选，应该只显示未生效的记录

## 兼容性说明

如果现有数据库中存在数字类型的历史数据，可能需要：
1. 数据库迁移脚本将 1 转换为 true，0 转换为 false
2. 或者在后端接口中添加兼容性处理 