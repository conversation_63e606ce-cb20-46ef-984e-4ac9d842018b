/**
 * REM适配核心JS
 * 基于1920*1080设计稿
 */

(function(window, document) {
  // 设计稿尺寸
  const designWidth = 1920;
  const designHeight = 1080;
  
  // 基准字体大小
  const baseFontSize = 16;
  
  // 设置rem基准值的函数
  function setRem() {
    const html = document.documentElement;
    const clientWidth = html.clientWidth;
    const clientHeight = html.clientHeight;
    
    // 计算宽度比例
    const widthScale = clientWidth / designWidth;
    
    // 设置基础字体大小
    let fontSize = baseFontSize * widthScale;
    
    // 设置最小和最大字体大小范围
    fontSize = Math.max(fontSize, 8); // 最小不低于8px
    fontSize = Math.min(fontSize, 36); // 最大不超过36px
    
    // 设置水平缩放比例
    html.style.fontSize = fontSize + 'px';
    
    // 设置垂直缩放比例变量，供CSS使用
    const verticalScale = clientHeight / designHeight;
    html.style.setProperty('--vertical-scale', verticalScale);
    
    // 设置设计宽高比例变量，供CSS使用
    html.style.setProperty('--width-scale', widthScale);
    html.style.setProperty('--client-width', clientWidth + 'px');
    html.style.setProperty('--client-height', clientHeight + 'px');
  }
  
  // 初始化执行
  setRem();
  
  // 监听窗口变化事件
  window.addEventListener('resize', function() {
    setRem();
  });
  
  // 监听屏幕旋转事件
  window.addEventListener('orientationchange', function() {
    setRem();
  });
  
  // 监听页面显示事件(从其他标签页切换回来时)
  document.addEventListener('visibilitychange', function() {
    if (document.visibilityState === 'visible') {
      setRem();
    }
  });
  
  // 向外暴露接口，方便手动刷新
  window.refreshRem = setRem;
  
})(window, document); 