<template>
  <PanelBox title="报警等级统计">
    <template #extra>
      <div class="com-select">
        <CommonSelect v-model="timeRange" :options="timeOptions" @change="handleTimeChange" />
      </div>
    </template>
    <div class="panel-content">
      <div class="alarm-level-stats">
        <div class="alarm-level-item level-one">
          <div class="corner-marks">
            <span class="corner-mark top-left"></span>
            <span class="corner-mark top-right"></span>
            <span class="corner-mark bottom-left"></span>
            <span class="corner-mark bottom-right"></span>
          </div>
          <div class="alarm-level-content">
            <div class="alarm-level-title">一级报警</div>
            <div class="alarm-level-value">
              <span class="value">{{ alarmStats.level1.count }}</span>
              <span class="percent">{{ alarmStats.level1.percent }}</span>
            </div>
          </div>
        </div>
        
        <div class="alarm-level-item level-two">
          <div class="corner-marks">
            <span class="corner-mark top-left"></span>
            <span class="corner-mark top-right"></span>
            <span class="corner-mark bottom-left"></span>
            <span class="corner-mark bottom-right"></span>
          </div>
          <div class="alarm-level-content">
            <div class="alarm-level-title">二级报警</div>
            <div class="alarm-level-value">
              <span class="value">{{ alarmStats.level2.count }}</span>
              <span class="percent">{{ alarmStats.level2.percent }}</span>
            </div>
          </div>
        </div>
        
        <div class="alarm-level-item level-three">
          <div class="corner-marks">
            <span class="corner-mark top-left"></span>
            <span class="corner-mark top-right"></span>
            <span class="corner-mark bottom-left"></span>
            <span class="corner-mark bottom-right"></span>
          </div>
          <div class="alarm-level-content">
            <div class="alarm-level-title">三级报警</div>
            <div class="alarm-level-value">
              <span class="value">{{ alarmStats.level3.count }}</span>
              <span class="percent">{{ alarmStats.level3.percent }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </PanelBox>
</template>

<script setup>
import { ref } from 'vue'
import PanelBox from '@/components/screen/PanelBox.vue'
import CommonSelect from '@/components/screen/CommonSelect.vue'

// 时间选择
const timeRange = ref('week')
const timeOptions = [
  { label: '近一周', value: 'week' },
  { label: '近一月', value: 'month' },
  { label: '近一年', value: 'year' }
]

// 不同时间范围的数据
const timeRangeData = {
  week: {
    level1: { count: 13, percent: '15.63%' },
    level2: { count: 13, percent: '15.63%' },
    level3: { count: 13, percent: '15.63%' }
  },
  month: {
    level1: { count: 25, percent: '18.52%' },
    level2: { count: 30, percent: '22.22%' },
    level3: { count: 20, percent: '14.81%' }
  },
  year: {
    level1: { count: 120, percent: '20.00%' },
    level2: { count: 150, percent: '25.00%' },
    level3: { count: 90, percent: '15.00%' }
  }
}

// 报警等级统计数据
const alarmStats = ref(timeRangeData.week)

// 处理时间范围变化
const handleTimeChange = (value) => {
  console.log('时间范围变更为:', value)
  // 更新统计数据
  alarmStats.value = timeRangeData[value]
}
</script>

<style scoped>
.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
}

.com-select {
  margin-right: 20px;
}

.alarm-level-stats {
  display: flex;
  justify-content: space-between;
  padding: 10px 0;
  gap: 16px;
  margin-top: 5%;
}

.alarm-level-item {
  position: relative;
  width: 138px;
  height: 60px;
  border: 1px solid;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
}

.level-one {
  border-color: #FF2626;
  background: linear-gradient(90deg, rgba(255, 38, 38, 0.2) 0%, rgba(255, 38, 38, 0.05) 100%);
}

.level-two {
  border-color: #FF8127;
  background: linear-gradient(90deg, rgba(255, 129, 39, 0.2) 0%, rgba(255, 129, 39, 0.05) 100%);
}

.level-three {
  border-color: #FFD700;
  background: linear-gradient(90deg, rgba(255, 215, 0, 0.2) 0%, rgba(255, 215, 0, 0.05) 100%);
}

.corner-marks {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  pointer-events: none;
}

.corner-mark {
  position: absolute;
  width: 5px;
  height: 5px;
  background-color: transparent;
}

.level-one .corner-mark {
  border-color: #FF2626;
}

.level-two .corner-mark {
  border-color: #FF8127;
}

.level-three .corner-mark {
  border-color: #FFD700;
}

.corner-mark.top-left {
  top: -1px;
  left: -1px;
  border-top: 2px solid;
  border-left: 2px solid;
}

.corner-mark.top-right {
  top: -1px;
  right: -1px;
  border-top: 2px solid;
  border-right: 2px solid;
}

.corner-mark.bottom-left {
  bottom: -1px;
  left: -1px;
  border-bottom: 2px solid;
  border-left: 2px solid;
}

.corner-mark.bottom-right {
  bottom: -1px;
  right: -1px;
  border-bottom: 2px solid;
  border-right: 2px solid;
}

.alarm-level-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.alarm-level-title {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 16px;
  color: #FFFFFF;
  margin-bottom: 4px;
  text-align: center;
}

.alarm-level-value {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 18px;
  color: #FFFFFF;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 响应式布局适配 */
@media screen and (min-width: 1920px) and (max-width: 2560px) {
  .panel-content {
    padding: 15px;
  }
}

@media screen and (max-width: 1919px) {
  .panel-content {
    padding: 12px;
  }
  
  .alarm-level-item {
    width: 120px;
    height: 55px;
  }
  
  .alarm-level-title {
    font-size: 14px;
  }
  
  .alarm-level-value {
    font-size: 16px;
  }
}

@media screen and (min-width: 2561px) {
  .panel-content {
    padding: 18px;
  }
  
  .alarm-level-item {
    width: 150px;
    height: 70px;
  }
  
  .alarm-level-title {
    font-size: 18px;
  }
  
  .alarm-level-value {
    font-size: 20px;
  }
}

/* 添加全屏模式(1080px高度)的适配 */
@media screen and (min-height: 1056px) and (max-height: 1080px) {
  .panel-content {
    padding: 15px;
  }
}

@media screen and (min-height: 940px) and (max-height: 1055px) {
  .panel-content {
    padding: 10px;
  }
}

@media screen and (min-height: 900px) and (max-height: 940px) {
  .panel-content {
    padding: 8px;
  }
}
</style> 