<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>视频流协议测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-container { border: 1px solid #ccc; padding: 20px; margin-bottom: 20px; }
        .input-group { margin-bottom: 10px; }
        label { min-width: 120px; display: inline-block; }
        input { width: 400px; padding: 5px; }
        button { margin: 5px; padding: 8px 15px; cursor: pointer; }
        textarea { width: 100%; height: 150px; margin: 10px 0; }
        canvas { border: 2px solid #000; margin: 10px 0; display: block; }
        .status { margin: 10px 0; padding: 10px; }
        .error { background: #ffebee; color: #c62828; border: 1px solid #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; border: 1px solid #2e7d32; }
        .info { background: #e3f2fd; color: #1976d2; border: 1px solid #1976d2; }
    </style>
</head>
<body>
    <h1>视频流协议测试</h1>
    <p>测试协议：发送 <code>{type: 110, data: "videoId"}</code>，接收 <code>{type: 110, data: {视频id, 视频流}}</code></p>
    
    <div class="test-container">
        <h2>连接配置</h2>
        <div class="input-group">
            <label for="wsUrl">WebSocket地址：</label>
            <input type="text" id="wsUrl" value="ws://172.20.130.240:32021/basic/jsmpeg">
        </div>
        <div class="input-group">
            <label for="videoId">视频ID：</label>
            <input type="text" id="videoId" value="c1002">
        </div>
        <button onclick="startTest()">开始测试</button>
        <button onclick="stopTest()">停止测试</button>
        <button onclick="clearLog()">清除日志</button>
    </div>
    
    <div class="test-container">
        <h2>视频播放区域</h2>
        <canvas id="videoCanvas" width="640" height="360"></canvas>
        <div id="status" class="status info">准备就绪</div>
    </div>
    
    <div class="test-container">
        <h2>通信日志</h2>
        <textarea id="logArea" readonly placeholder="通信日志将显示在这里..."></textarea>
    </div>

    <script src="https://cdn.jsdelivr.net/gh/phoboslab/jsmpeg@master/jsmpeg.min.js"></script>
    <script>
        let ws = null;
        let player = null;
        let logArea = document.getElementById('logArea');
        let statusDiv = document.getElementById('status');
        let canvas = document.getElementById('videoCanvas');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] ${message}\n`;
            logArea.value += logMessage;
            logArea.scrollTop = logArea.scrollHeight;
            console.log(message);
        }
        
        function updateStatus(message, type = 'info') {
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
            log(`状态: ${message}`, type);
        }
        
        function clearLog() {
            logArea.value = '';
        }
        
        async function startTest() {
            const wsUrl = document.getElementById('wsUrl').value;
            const videoId = document.getElementById('videoId').value;
            
            if (!wsUrl || !videoId) {
                updateStatus('请填写WebSocket地址和视频ID', 'error');
                return;
            }
            
            try {
                // 清理之前的连接
                stopTest();
                
                updateStatus('正在连接WebSocket...', 'info');
                log(`连接到: ${wsUrl}`);
                
                // 建立WebSocket连接
                ws = new WebSocket(wsUrl);
                ws.binaryType = 'blob';
                
                ws.onopen = () => {
                    updateStatus('WebSocket连接成功，发送播放请求', 'success');
                    log('WebSocket连接已建立');
                    
                    // 发送播放请求
                    const request = {
                        type: 110,
                        data: videoId
                    };
                    
                    log(`发送请求: ${JSON.stringify(request)}`);
                    ws.send(JSON.stringify(request));
                };
                
                ws.onmessage = async (event) => {
                    try {
                        log(`收到消息，类型: ${event.data.constructor.name}, 大小: ${event.data.size || event.data.length}`);
                        
                        if (event.data instanceof Blob) {
                            // 处理Blob消息
                            log(`处理Blob消息，大小: ${event.data.size} 字节`);
                            
                            try {
                                // 尝试解析为JSON
                                const text = await event.data.text();
                                const response = JSON.parse(text);
                                
                                log(`解析JSON响应: ${JSON.stringify(response, null, 2)}`);
                                
                                if (response.type === 110 && response.data) {
                                    updateStatus('收到视频流响应', 'success');
                                    
                                    // 根据协议，data应该包含视频ID和视频流
                                    if (response.data.videoId && response.data.stream) {
                                        log(`视频ID: ${response.data.videoId}`);
                                        log(`视频流数据类型: ${typeof response.data.stream}`);
                                        
                                        // 创建播放器处理视频流
                                        await createPlayerAndPlay(response.data.stream);
                                    } else {
                                        log('响应格式不正确，缺少videoId或stream字段');
                                    }
                                } else {
                                    log('响应类型不是110或缺少data字段');
                                }
                                
                            } catch (parseError) {
                                // 如果不是JSON，可能是直接的视频流数据
                                log(`无法解析为JSON，当作二进制视频数据处理: ${parseError.message}`);
                                await handleBinaryVideoData(event.data);
                            }
                            
                        } else if (typeof event.data === 'string') {
                            // 处理字符串消息
                            log(`收到字符串消息: ${event.data}`);
                            
                            try {
                                const response = JSON.parse(event.data);
                                log(`解析字符串JSON: ${JSON.stringify(response, null, 2)}`);
                                
                                if (response.type === 110) {
                                    updateStatus('收到服务器确认消息', 'success');
                                }
                            } catch (e) {
                                log(`普通文本消息: ${event.data}`);
                            }
                        }
                        
                    } catch (error) {
                        log(`处理消息错误: ${error.message}`, 'error');
                        updateStatus('消息处理错误', 'error');
                    }
                };
                
                ws.onerror = (error) => {
                    log(`WebSocket错误: ${error}`, 'error');
                    updateStatus('WebSocket连接错误', 'error');
                };
                
                ws.onclose = (event) => {
                    log(`WebSocket连接关闭，代码: ${event.code}, 原因: ${event.reason}`);
                    updateStatus('连接已断开', 'info');
                };
                
            } catch (error) {
                log(`启动测试失败: ${error.message}`, 'error');
                updateStatus('测试启动失败', 'error');
            }
        }
        
        async function createPlayerAndPlay(streamData) {
            try {
                log('创建JSMpeg播放器...');
                
                // 销毁之前的播放器
                if (player) {
                    player.destroy();
                    player = null;
                }
                
                // 创建虚拟的WebSocket URL
                const dummyUrl = `ws://localhost:0/test`;
                
                player = new JSMpeg.Player(dummyUrl, {
                    canvas: canvas,
                    autoplay: true,
                    audio: false,
                    videoBufferSize: 1024 * 1024,
                    onConnectionCreate: () => {
                        const mockWs = {
                            onopen: null,
                            onclose: null,
                            onmessage: null,
                            send: () => {},
                            close: () => {},
                            readyState: 1
                        };
                        
                        // 添加自定义write方法
                        mockWs.writeData = (data) => {
                            if (mockWs.onmessage) {
                                mockWs.onmessage({ data: data });
                            }
                        };
                        
                        return mockWs;
                    }
                });
                
                // 模拟连接建立并添加write方法
                if (player && player.source) {
                    player.source.established = true;
                    player.source.completed = false;
                    
                    // 为播放器添加write方法
                    player.write = function(data) {
                        try {
                            if (this.source && this.source.socket && this.source.socket.onmessage) {
                                this.source.socket.onmessage({ data: data });
                                log('数据已写入播放器');
                            }
                        } catch (e) {
                            log(`写入数据失败: ${e.message}`, 'error');
                        }
                    };
                    
                    log('播放器创建成功');
                    
                    // 如果有流数据，立即处理
                    if (streamData) {
                        await processStreamData(streamData);
                    }
                }
                
            } catch (error) {
                log(`创建播放器失败: ${error.message}`, 'error');
                updateStatus('播放器创建失败', 'error');
            }
        }
        
        async function processStreamData(streamData) {
            try {
                log(`处理视频流数据，类型: ${typeof streamData}`);
                
                let uint8Array;
                
                if (typeof streamData === 'string') {
                    // 假设是Base64编码
                    log('解码Base64数据...');
                    const binaryString = atob(streamData);
                    uint8Array = new Uint8Array(binaryString.length);
                    for (let i = 0; i < binaryString.length; i++) {
                        uint8Array[i] = binaryString.charCodeAt(i);
                    }
                } else if (streamData instanceof ArrayBuffer) {
                    uint8Array = new Uint8Array(streamData);
                } else if (streamData instanceof Uint8Array) {
                    uint8Array = streamData;
                } else {
                    log(`未知的数据格式: ${typeof streamData}`, 'error');
                    return;
                }
                
                log(`数据大小: ${uint8Array.length} 字节`);
                
                // 写入播放器
                if (player && player.write) {
                    setTimeout(() => {
                        try {
                            player.write(uint8Array);
                            log('视频数据已写入播放器');
                            updateStatus('视频播放中', 'success');
                        } catch (error) {
                            log(`写入数据失败: ${error.message}`, 'error');
                        }
                    }, 200);
                } else {
                    log('播放器write方法不可用', 'error');
                }
                
            } catch (error) {
                log(`处理流数据失败: ${error.message}`, 'error');
            }
        }
        
        async function handleBinaryVideoData(blob) {
            try {
                log('处理二进制视频数据...');
                
                if (!player) {
                    await createPlayerAndPlay(null);
                }
                
                const arrayBuffer = await blob.arrayBuffer();
                const uint8Array = new Uint8Array(arrayBuffer);
                
                if (player && player.write) {
                    player.write(uint8Array);
                    log(`写入二进制数据: ${uint8Array.length} 字节`);
                } else {
                    log('播放器write方法不可用', 'error');
                }
                
            } catch (error) {
                log(`处理二进制数据失败: ${error.message}`, 'error');
            }
        }
        
        function stopTest() {
            if (ws) {
                ws.close();
                ws = null;
                log('WebSocket连接已关闭');
            }
            
            if (player) {
                player.destroy();
                player = null;
                log('播放器已销毁');
            }
            
            updateStatus('测试已停止', 'info');
        }
        
        // 页面卸载时清理资源
        window.onbeforeunload = () => {
            stopTest();
        };
    </script>
</body>
</html> 