<template>
  <div class="code-generator-container">
    <div class="page-header">
      <div class="page-title-container">
        <h2 class="page-title">代码生成器</h2>
        <el-button type="text" @click="openUserGuide">使用说明</el-button>
      </div>
      <div class="page-description">快速生成标准化的CRUD页面代码</div>
    </div>

    <div class="main-content">
      <!-- 左侧配置区域 -->
      <div class="config-area">
        <el-card class="config-card">
          <template #header>
            <div class="card-header">
              <span>配置信息</span>
              <div class="header-actions">
                <el-button type="primary" size="small" @click="generateCode">生成代码</el-button>
                <el-button type="success" size="small" @click="downloadCode">下载代码</el-button>
                <el-button type="warning" size="small" @click="openTemplateManager">模板管理</el-button>
                <el-button type="info" size="small" @click="openConfigManager">配置管理</el-button>
                <el-button size="small" @click="loadExampleConfig">加载示例</el-button>
                <el-button size="small" @click="resetConfig">重置配置</el-button>
              </div>
            </div>
          </template>

          <!-- 步骤条 -->
          <el-steps :active="activeStep" finish-status="success" simple>
            <el-step title="基础信息" />
            <el-step title="表格配置" />
            <el-step title="搜索配置" />
            <el-step title="表单配置" />
            <el-step title="API配置" />
          </el-steps>

          <!-- 步骤内容区域 -->
          <div class="step-content">
            <component
              :is="currentStepComponent"
              v-model:config="configData"
              @next="nextStep"
              @prev="prevStep"
            />
          </div>
        </el-card>
      </div>

      <!-- 右侧预览区域 -->
      <div class="preview-area">
        <el-card class="preview-card">
          <template #header>
            <div class="card-header">
              <span>代码预览</span>
              <div class="header-actions">
                <el-radio-group v-model="previewType" size="small">
                  <el-radio-button label="main">主页面</el-radio-button>
                  <el-radio-button label="search">搜索组件</el-radio-button>
                  <el-radio-button label="form">表单组件</el-radio-button>
                  <el-radio-button label="api">API</el-radio-button>
                </el-radio-group>
                <el-button size="small" @click="copyCode">复制代码</el-button>
              </div>
            </div>
          </template>

          <!-- 代码预览区域 -->
          <div class="code-preview">
            <CodeHighlight :code="previewCode" :language="previewLanguage" />
          </div>
        </el-card>
      </div>
    </div>
  </div>

  <!-- 模板管理对话框 -->
  <TemplateManager
    v-model:visible="templateManagerVisible"
    :current-config="configData"
    @use-template="handleUseTemplate"
  />

  <!-- 配置管理对话框 -->
  <ConfigManager
    v-model:visible="configManagerVisible"
    :current-config="configData"
    @load-config="handleLoadConfig"
  />

  <!-- 使用说明对话框 -->
  <UserGuide
    v-model:visible="userGuideVisible"
  />
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'

// 导入步骤组件
import BasicInfoStep from './components/BasicInfoStep.vue'
import TableConfigStep from './components/TableConfigStep.vue'
import SearchConfigStep from './components/SearchConfigStep.vue'
import FormConfigStep from './components/FormConfigStep.vue'
import ApiConfigStep from './components/ApiConfigStep.vue'
import CodeHighlight from './components/CodeHighlight.vue'
import TemplateManager from './components/TemplateManager.vue'
import ConfigManager from './components/ConfigManager.vue'
import UserGuide from './components/UserGuide.vue'

// 导入配置
import { defaultConfig, exampleConfig } from './config/defaultConfig.js'

// 当前激活的步骤
const activeStep = ref(0)

// 预览类型
const previewType = ref('main')

// 配置数据
const configData = ref(JSON.parse(JSON.stringify(defaultConfig)))

// 当前步骤组件
const currentStepComponent = computed(() => {
  const components = [
    BasicInfoStep,
    TableConfigStep,
    SearchConfigStep,
    FormConfigStep,
    ApiConfigStep
  ]
  return components[activeStep.value]
})

// 预览代码
const previewCode = ref('')

// 预览语言
const previewLanguage = computed(() => {
  switch (previewType.value) {
    case 'main':
    case 'search':
    case 'form':
      return 'xml'
    case 'api':
      return 'javascript'
    default:
      return 'javascript'
  }
})

// 生成表格列代码
const generateTableColumns = () => {
  if (!configData.value.table.columns || configData.value.table.columns.length === 0) {
    return `<el-table-column label="示例列" prop="example" />`
  }

  return configData.value.table.columns
    .map(column => `<el-table-column label="${column.label}" prop="${column.prop}" />`)
    .join('\n        ')
}

// 生成搜索字段代码
const generateSearchFields = () => {
  if (!configData.value.search.fields || configData.value.search.fields.length === 0) {
    return `<el-form-item label="示例字段">
        <el-input v-model="form.example" placeholder="请输入" />
      </el-form-item>`
  }

  return configData.value.search.fields
    .map(field => {
      if (field.type === 'select') {
        return `<el-form-item label="${field.label}">
        <el-select v-model="form.${field.prop}" placeholder="请选择">
          <el-option label="选项1" value="1" />
          <el-option label="选项2" value="2" />
        </el-select>
      </el-form-item>`
      } else {
        return `<el-form-item label="${field.label}">
        <el-input v-model="form.${field.prop}" placeholder="请输入" />
      </el-form-item>`
      }
    })
    .join('\n      ')
}

// 生成表单字段代码
const generateFormFields = () => {
  if (!configData.value.form.fields || configData.value.form.fields.length === 0) {
    return `<el-form-item label="示例字段" prop="example">
        <el-input v-model="form.example" placeholder="请输入" />
      </el-form-item>`
  }

  return configData.value.form.fields
    .map(field => {
      if (field.type === 'select') {
        return `<el-form-item label="${field.label}" prop="${field.prop}">
        <el-select v-model="form.${field.prop}" placeholder="请选择">
          <el-option label="选项1" value="1" />
          <el-option label="选项2" value="2" />
        </el-select>
      </el-form-item>`
      } else if (field.type === 'textarea') {
        return `<el-form-item label="${field.label}" prop="${field.prop}">
        <el-input v-model="form.${field.prop}" type="textarea" placeholder="请输入" />
      </el-form-item>`
      } else {
        return `<el-form-item label="${field.label}" prop="${field.prop}">
        <el-input v-model="form.${field.prop}" placeholder="请输入" />
      </el-form-item>`
      }
    })
    .join('\n      ')
}

// 生成API方法代码
const generateApiMethods = () => {
  const methods = []
  const { api } = configData.value

  if (api.methods.list) {
    methods.push(`// 获取列表
export function getList(params) {
  return request({
    url: \`\${apiPrefix}/list\`,
    method: 'get',
    params
  })
}`)
  }

  if (api.methods.detail) {
    methods.push(`// 获取详情
export function getDetail(id) {
  return request({
    url: \`\${apiPrefix}/\${id}\`,
    method: 'get'
  })
}`)
  }

  if (api.methods.create) {
    methods.push(`// 创建
export function create(data) {
  return request({
    url: \`\${apiPrefix}\`,
    method: 'post',
    data
  })
}`)
  }

  if (api.methods.update) {
    methods.push(`// 更新
export function update(id, data) {
  return request({
    url: \`\${apiPrefix}/\${id}\`,
    method: 'put',
    data
  })
}`)
  }

  if (api.methods.delete) {
    methods.push(`// 删除
export function remove(id) {
  return request({
    url: \`\${apiPrefix}/\${id}\`,
    method: 'delete'
  })
}`)
  }

  // 添加自定义方法
  if (api.customMethods && api.customMethods.length > 0) {
    api.customMethods.forEach(method => {
      methods.push(`// ${method.description || '自定义方法'}
export function ${method.name}(${method.params || ''}) {
  return request({
    url: \`\${apiPrefix}${method.url || ''}\`,
    method: '${method.method || 'get'}',
    ${method.method === 'get' ? 'params' : 'data'}
  })
}`)
    })
  }

  return methods.join('\n\n')
}

// 下一步
const nextStep = () => {
  if (activeStep.value < 4) {
    activeStep.value++
  }
}

// 上一步
const prevStep = () => {
  if (activeStep.value > 0) {
    activeStep.value--
  }
}

// 生成代码
const generateCode = async () => {
  try {
    // 实际生成代码的逻辑
    const { generateCode: generateCodeFromTemplate } = await import('./utils/templateEngine.js')

    // 生成代码
    const code = await generateCodeFromTemplate(configData.value, previewType.value)

    // 更新预览代码
    previewCode.value = code

    // 首次生成不显示成功提示
    if (previewCode.value && previewCode.value.length > 0) {
      ElMessage.success('代码生成成功！')
    }
  } catch (error) {
    console.error('代码生成失败', error)
    ElMessage.error(`代码生成失败: ${error.message}`)
  }
}

// 重置配置
const resetConfig = () => {
  // 重置配置数据
  configData.value = JSON.parse(JSON.stringify(defaultConfig))

  // 重置步骤
  activeStep.value = 0

  ElMessage.success('配置已重置')
}

// 复制代码
const copyCode = () => {
  // 实际复制代码的逻辑
  navigator.clipboard.writeText(previewCode.value)
    .then(() => {
      ElMessage.success('代码已复制到剪贴板')
    })
    .catch(() => {
      ElMessage.error('复制失败，请手动复制')
    })
}

// 下载代码
const downloadCode = async () => {
  try {
    // 验证必填项
    const { moduleName, apiPrefix, pageTitle } = configData.value.basic

    if (!moduleName) {
      ElMessage.warning('请先填写模块名称')
      return
    }

    if (!apiPrefix) {
      ElMessage.warning('请先填写API前缀')
      return
    }

    if (!pageTitle) {
      ElMessage.warning('请先填写页面标题')
      return
    }

    // 导入文件下载工具类
    const { downloadAllFiles } = await import('./utils/fileDownload.js')

    // 生成并下载所有文件
    await downloadAllFiles(configData.value)

    ElMessage.success('代码下载成功！')
  } catch (error) {
    console.error('代码下载失败', error)
    ElMessage.error(`代码下载失败: ${error.message}`)
  }
}

// 模板管理对话框可见性
const templateManagerVisible = ref(false)

// 配置管理对话框可见性
const configManagerVisible = ref(false)

// 使用说明对话框可见性
const userGuideVisible = ref(false)

// 打开模板管理对话框
const openTemplateManager = () => {
  templateManagerVisible.value = true
}

// 打开配置管理对话框
const openConfigManager = () => {
  configManagerVisible.value = true
}

// 打开使用说明对话框
const openUserGuide = () => {
  userGuideVisible.value = true
}

// 处理使用模板
const handleUseTemplate = (template) => {
  configData.value = JSON.parse(JSON.stringify(template))
  ElMessage.success('应用模板成功')
}

// 处理加载配置
const handleLoadConfig = (config) => {
  configData.value = JSON.parse(JSON.stringify(config))
  ElMessage.success('加载配置成功')
}

// 加载示例配置
const loadExampleConfig = () => {
  configData.value = JSON.parse(JSON.stringify(exampleConfig))
  ElMessage.success('示例配置已加载')
}

// 监听预览类型变化
watch(previewType, () => {
  generateCode()
})

// 监听配置数据变化
watch(configData, () => {
  generateCode()
}, { deep: true })

// 组件挂载时的逻辑
onMounted(() => {
  // 生成初始预览代码
  generateCode()
})
</script>

<style scoped>
.code-generator-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-title-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.page-title {
  font-size: 24px;
  font-weight: bold;
  margin: 0;
}

.page-description {
  color: #666;
  font-size: 14px;
}

.main-content {
  display: flex;
  gap: 20px;
}

.config-area {
  flex: 1;
  min-width: 500px;
}

.preview-area {
  flex: 1;
  min-width: 500px;
}

.config-card,
.preview-card {
  height: calc(100vh - 200px);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.step-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px 0;
}

.code-preview {
  flex: 1;
  overflow-y: auto;
  background-color: #f5f7fa;
  border-radius: 4px;
  padding: 16px;
  height: calc(100% - 60px);
}

.code-preview pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.code-preview code {
  font-family: Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.5;
}
</style>
