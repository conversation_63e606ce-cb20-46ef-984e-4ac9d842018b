// 用于导入并注册所有SVG图标
import { defineAsyncComponent } from 'vue'

// 通过Vite的import.meta.glob动态导入所有SVG文件
// 这里使用了懒加载方式，只在需要时才会加载对应的SVG
const svgModules = import.meta.glob('./svg/*.svg', { eager: true })

// 处理导入的SVG，提取文件名作为key
const icons = {}
Object.keys(svgModules).forEach(key => {
  // 提取文件名（不包含扩展名）作为图标名称
  const iconName = key.replace(/(^\.\/svg\/|\.svg$)/g, '')
  icons[iconName] = svgModules[key].default
})

export default icons 