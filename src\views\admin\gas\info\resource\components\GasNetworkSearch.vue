<template>
  <div class="gas-network-search">
    <div class="search-form">
      <div class="form-item">
        <span class="label">压力级别:</span>
        <el-select v-model="formData.pressureLevel" class="form-input" placeholder="请选择">
          <el-option v-for="item in pressureLevelOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
      <div class="form-item">
        <span class="label">埋设类型:</span>
        <el-select v-model="formData.buriedType" class="form-input" placeholder="请选择">
          <el-option v-for="item in buriedTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
      <div class="form-item">
        <span class="label">管材:</span>
        <el-select v-model="formData.material" class="form-input" placeholder="请选择">
          <el-option v-for="item in materialOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
      <div class="form-item">
        <span class="label">管径:</span>
        <el-select v-model="formData.pipeDiameter" class="form-input" placeholder="请选择">
          <el-option v-for="item in pipeDiameterOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
      <div class="form-item">
        <span class="label">权属单位:</span>
        <el-select v-model="formData.managementUnit" class="form-input" placeholder="请选择">
          <el-option v-for="unit in managementUnits" :key="unit.id" :label="unit.enterpriseName" :value="unit.enterpriseName" />
        </el-select>
      </div>
      <div class="form-item">
        <el-input v-model="formData.pipelineCode" class="form-input" placeholder="请输入管网编码" />
      </div>
      <div class="form-item" >
        <el-button type="primary" class="search-btn" @click="handleSearch">查询</el-button>
        <el-button class="reset-btn" @click="handleReset">重置</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { ElSelect, ElOption, ElInput, ElButton } from 'element-plus';
import { PRESSURE_LEVELS, BURIED_TYPES, PIPE_MATERIALS, PIPE_DIAMETERS } from '@/constants/gas';
import { getManagementUnits } from '@/api/gas';

const emit = defineEmits(['search', 'reset']);

// 表单数据
const formData = ref({
  pressureLevel: '',
  buriedType: '',
  material: '',
  pipeDiameter: '',
  managementUnit: '',
  pipelineCode: ''
});

// 添加全部选项
const pressureLevelOptions = [
  { label: '全部', value: '' },
  ...PRESSURE_LEVELS
];

const buriedTypeOptions = [
  { label: '全部', value: '' },
  ...BURIED_TYPES
];

const materialOptions = [
  { label: '全部', value: '' },
  ...PIPE_MATERIALS
];

// 管径选项
const pipeDiameterOptions = [
  { label: '全部', value: '' },
  ...PIPE_DIAMETERS
];

// 权属单位列表
const managementUnits = ref([]);

// 获取权属单位列表
const fetchManagementUnits = async () => {
  try {
    const res = await getManagementUnits();
    if (res && res.code === 200) {
      managementUnits.value = res.data || [];
    }
  } catch (error) {
    console.error('获取权属单位列表失败', error);
  }
};

// 组件挂载时获取数据
onMounted(() => {
  fetchManagementUnits();
});

// 处理查询
const handleSearch = () => {
  emit('search', formData.value);
};

// 处理重置
const handleReset = () => {
  formData.value = {
    pressureLevel: '',
    buriedType: '',
    material: '',
    pipeDiameter: '',
    managementUnit: '',
    pipelineCode: ''
  };
  emit('reset');
};
</script>

<style scoped>
.gas-network-search {
  width: 100%;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.form-item {
  display: flex;
  align-items: center;
  margin-right: 16px;
  margin-bottom: 16px;
}

.label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #282828;
  margin-right: 8px;
  white-space: nowrap;
}

.form-input {
  width: 180px;
  height: 32px;
}

:deep(.el-input__wrapper) {
  background: #FFFFFF;
  border-radius: 2px;
  border: 1px solid #CED3DA;
  height: 32px;
  box-shadow: none !important;
}

:deep(.el-select .el-input__wrapper) {
  background: #FFFFFF;
  border-radius: 2px;
  border: 1px solid #CED3DA;
  height: 32px;
  box-shadow: none !important;
}

:deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px #0277FD inset !important;
}

:deep(.el-select .el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px #0277FD inset !important;
}

.search-btn {
  width: 60px;
  height: 32px;
  background: #0277FD;
  border-radius: 2px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #FFFFFF;
  padding: 0;
  margin-right: 8px;
}

.reset-btn {
  width: 60px;
  height: 32px;
  background: #FFFFFF;
  border-radius: 2px;
  border: 1px solid #CED3DA;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #647688;
  padding: 0;
}
</style>