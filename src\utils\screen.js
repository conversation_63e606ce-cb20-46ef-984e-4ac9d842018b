import { ref, onMounted, onUnmounted } from 'vue'

/**
 * 大屏适配Hook
 * @param {Ref} screenRef - 容器DOM引用
 * @param {Object} options - 配置选项
 * @param {string} options.mode - 适配模式: 'contain'(包含) 或 'cover'(填充)，默认为'cover'
 * @param {number} options.baseWidth - 设计基准宽度，默认为3840
 * @param {number} options.baseHeight - 设计基准高度，默认为1080
 * @returns {Object} 返回包含scale和resize函数的对象
 */
export function useScreen(screenRef, options = {}) {
  const {
    mode = 'cover',
    baseWidth = 3840,
    baseHeight = 1080
  } = options
  
  const scale = ref(1)

  const resize = () => {
    if (!screenRef.value) return
    
    // 获取真实视口尺寸
    const currentWidth = document.documentElement.clientWidth
    const currentHeight = document.documentElement.clientHeight
    
    // 设置容器样式和变换原点
    const container = screenRef.value
    container.style.transformOrigin = 'left top'
    
    // 计算宽高和位置
    let scaleVal, translateX, translateY

    // 根据模式选择缩放策略
    if (mode === 'contain') {
      // 包含模式 - 确保内容完全可见，可能有留白
      scaleVal = Math.min(currentWidth / baseWidth, currentHeight / baseHeight)
    } else { // cover模式
      // 填充模式 - 确保屏幕填满，可能裁剪部分内容
      scaleVal = Math.max(currentWidth / baseWidth, currentHeight / baseHeight)
    }
    
    // 计算中心偏移量，使内容居中
    translateX = (currentWidth - baseWidth * scaleVal) / (2 * scaleVal)
    translateY = (currentHeight - baseHeight * scaleVal) / (2 * scaleVal)
    
    // 更新缩放比例
    scale.value = scaleVal
    
    // 应用变换
    container.style.transform = `scale(${scaleVal}) translate(${translateX}px, ${translateY}px)`
    
    // 添加调试日志
    console.debug('Screen Adaptation:', {
      mode,
      currentRatio: (currentWidth / currentHeight).toFixed(2),
      baseRatio: (baseWidth / baseHeight).toFixed(2),
      scale: scaleVal.toFixed(2),
      translateX: translateX.toFixed(2),
      translateY: translateY.toFixed(2)
    })
  }

  onMounted(() => {
    resize()
    window.addEventListener('resize', resize)
  })

  onUnmounted(() => {
    window.removeEventListener('resize', resize)
  })

  return {
    scale,
    resize
  }
} 