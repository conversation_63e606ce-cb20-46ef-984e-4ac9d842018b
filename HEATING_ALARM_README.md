# 管网运行监测预警管理功能实现说明

## 功能概述

本功能实现了供热管网运行监测预警管理系统，包括报警信息的查询、统计、确认、处置等完整流程。

## 实现的文件结构

```
src/
├── constants/heating.js                    # 新增报警相关常量定义
├── api/heating.js                         # 新增报警相关API接口
├── views/admin/heating/monitoring/warning/
│   ├── network.vue                        # 主页面（已重构）
│   └── components/
│       ├── HeatingAlarmSearch.vue         # 搜索组件
│       ├── HeatingAlarmDialog.vue         # 报警详情弹窗
│       ├── HeatingAlarmConfirmDialog.vue  # 报警确认弹窗
│       └── HeatingAlarmDisposalDialog.vue # 报警处置弹窗
```

## 主要功能

### 1. 报警统计展示
- 全部报警数量统计
- 待确认、待处置、处置中、已处置状态统计
- 报警级别统计（一级、二级、三级、四级）
- 实时百分比显示

### 2. 报警信息查询
- 支持按报警来源、报警等级、报警类型筛选
- 支持按报警时间范围查询
- 支持按报警状态筛选
- 支持按设备编码模糊查询

### 3. 报警信息列表
- 分页展示报警信息
- 显示报警来源、编号、时间、设备编码等详细信息
- 报警级别标签化显示（不同颜色区分）
- 操作列根据报警状态动态显示操作按钮

### 4. 报警确认功能
- 支持确认报警为真实报警或误报
- 可添加确认描述信息
- 确认后更新报警状态

### 5. 报警处置功能
- 支持新增、编辑、删除处置记录
- 处置状态管理（处置中、处置完成）
- 支持上传处置照片
- 处置人员和单位信息记录
- 处置时间记录

### 6. 报警详情查看
- 三个标签页：报警详情、监测曲线、报警记录
- 报警详情：显示完整的报警信息和处置时间线
- 监测曲线：支持查看近24小时、近7天、近30天的监测数据
- 报警记录：显示该设备的历史报警记录

### 7. 地图定位功能
- 点击定位按钮可在地图上定位报警位置
- 集成现有的地图定位系统

## 技术特点

### 1. 响应式设计
- 采用Flexbox布局，适配不同屏幕尺寸
- 表格支持固定表头和滚动
- 分页组件响应式适配

### 2. 组件化开发
- 搜索、弹窗等功能独立封装为组件
- 组件间通过props和events通信
- 便于维护和复用

### 3. 数据可视化
- 使用ECharts绘制监测曲线图
- 支持时间范围切换
- 图表自适应容器大小

### 4. 用户体验优化
- 加载状态提示
- 操作成功/失败消息提示
- 表单验证和错误处理
- 确认删除操作防误操作

## API接口说明

### 统计接口
- `POST /heat/usmAlarmSituationAnalysis/statistics` - 获取报警统计数据
- `POST /heat/usmAlarmSituationAnalysis/level/statistics` - 获取报警等级统计

### 报警信息接口
- `POST /heat/usmMonitorAlarm/search/{page}/{size}` - 分页查询报警信息
- `GET /heat/usmMonitorAlarm/{id}` - 获取报警详情

### 报警处理接口
- `POST /heat/usmMonitorAlarm/alarm/confirm` - 报警确认
- `POST /heat/usmMonitorAlarm/alarm/handle` - 报警处置
- `DELETE /heat/usmMonitorAlarm/alarm/handle/{id}` - 删除处置记录

### 辅助接口
- `GET /heat/usmMonitorAlarm/alarm/handleList/{id}` - 获取处置列表
- `POST /heat/usmMonitorAlarmStatus/list` - 获取报警状态时间线
- `POST /heat/usmMonitorRecord/monitorCurve` - 获取监测曲线数据
- `GET /heat/usmMonitorAlarm/alarmRecord/{deviceId}` - 获取报警记录

## 常量配置

在 `src/constants/heating.js` 中新增了以下常量：

- 报警级别选项和映射
- 报警状态选项和映射  
- 报警类型选项和映射
- 报警来源选项和映射
- 确认结果选项和映射
- 处置状态选项和映射
- 监测指标编码选项和映射

## 样式设计

- 参考排水专项的设计风格
- 统一的色彩搭配和字体规范
- 卡片式布局，清晰的视觉层次
- 表格斑马纹和悬停效果
- 按钮和表单元素的统一样式

## 错误处理

- API调用异常处理
- 表单验证错误提示
- 网络请求失败重试机制
- 数据为空时的友好提示

## 浏览器兼容性

- 支持现代浏览器（Chrome、Firefox、Safari、Edge）
- 使用ES6+语法，需要现代浏览器支持
- 响应式设计，支持移动端访问

## 部署说明

1. 确保项目依赖已安装：`npm install`
2. 启动开发服务器：`npm run dev`
3. 构建生产版本：`npm run build`

## 注意事项

1. 需要确保后端API接口已实现并可访问
2. 地图定位功能依赖现有的GIS系统
3. 文件上传功能需要配置文件服务器
4. 时间格式统一使用 `YYYY-MM-DD HH:mm:ss`
5. 报警状态码需要与后端保持一致 