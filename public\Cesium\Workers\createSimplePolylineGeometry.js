/**
 *    ████████                   ██                   ██       ██                  ██      ██
 *   ██░░░░░░██                 ░░                   ░██      ░██                 ░██     ░██
 *  ██      ░░   █████  ███████  ██ ██   ██  ██████  ░██   █  ░██  ██████  ██████ ░██     ░██
 * ░██          ██░░░██░░██░░░██░██░██  ░██ ██░░░░   ░██  ███ ░██ ██░░░░██░░██░░█ ░██  ██████
 * ░██    █████░███████ ░██  ░██░██░██  ░██░░█████   ░██ ██░██░██░██   ░██ ░██ ░  ░██ ██░░░██
 * ░░██  ░░░░██░██░░░░  ░██  ░██░██░██  ░██ ░░░░░██  ░████ ░░████░██   ░██ ░██    ░██░██  ░██
 *  ░░████████ ░░██████ ███  ░██░██░░██████ ██████   ░██░   ░░░██░░██████ ░███    ███░░██████
 *   ░░░░░░░░   ░░░░░░ ░░░   ░░ ░░  ░░░░░░ ░░░░░░    ░░       ░░  ░░░░░░  ░░░    ░░░  ░░░░░░
 *
 * @license
 * GeniusWorld Web SDK - http://zydlxx.cn:1234/
 * Version 4.0.0.B#develop-d3ff2330@202405231107
 *
 *
 * Copyright © 2020 - 2023 正元地理信息集团股份有限公司. All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
define(["./defaultValue-7b61670d","./Matrix3-79d15570","./ArcType-378e21f1","./Transforms-6a5d79d3","./Color-e280f578","./ComponentDatatype-e95dda25","./GeometryAttribute-d24f9032","./GeometryAttributes-410c425f","./IndexDatatype-7c192505","./Math-6acd1674","./PolylinePipeline-510dd0d2","./Matrix2-d550732e","./RuntimeError-7dc4ea5a","./combine-bc3d0d90","./WebGLConstants-68839929","./EllipsoidGeodesic-c3042500","./EllipsoidRhumbLine-997e9b1a","./IntersectionTests-044bd161","./Plane-e4eb0e88"],(function(e,o,t,l,r,n,i,a,s,c,d,p,y,u,f,h,C,T,g){"use strict";function m(e,o,t,l,n,i,a){const s=d.PolylinePipeline.numberOfPoints(e,o,n);let c;const p=t.red,y=t.green,u=t.blue,f=t.alpha,h=l.red,C=l.green,T=l.blue,g=l.alpha;if(r.Color.equals(t,l)){for(c=0;c<s;c++)i[a++]=r.Color.floatToByte(p),i[a++]=r.Color.floatToByte(y),i[a++]=r.Color.floatToByte(u),i[a++]=r.Color.floatToByte(f);return a}const m=(h-p)/s,P=(C-y)/s,_=(T-u)/s,b=(g-f)/s;let B=a;for(c=0;c<s;c++)i[B++]=r.Color.floatToByte(p+c*m),i[B++]=r.Color.floatToByte(y+c*P),i[B++]=r.Color.floatToByte(u+c*_),i[B++]=r.Color.floatToByte(f+c*b);return B}function P(l){const n=(l=e.defaultValue(l,e.defaultValue.EMPTY_OBJECT)).positions,i=l.colors,a=e.defaultValue(l.colorsPerVertex,!1);this._positions=n,this._colors=i,this._colorsPerVertex=a,this._arcType=e.defaultValue(l.arcType,t.ArcType.GEODESIC),this._granularity=e.defaultValue(l.granularity,c.CesiumMath.RADIANS_PER_DEGREE),this._ellipsoid=e.defaultValue(l.ellipsoid,o.Ellipsoid.WGS84),this._workerName="createSimplePolylineGeometry";let s=1+n.length*o.Cartesian3.packedLength;s+=e.defined(i)?1+i.length*r.Color.packedLength:1,this.packedLength=s+o.Ellipsoid.packedLength+3}P.pack=function(t,l,n){let i;n=e.defaultValue(n,0);const a=t._positions;let s=a.length;for(l[n++]=s,i=0;i<s;++i,n+=o.Cartesian3.packedLength)o.Cartesian3.pack(a[i],l,n);const c=t._colors;for(s=e.defined(c)?c.length:0,l[n++]=s,i=0;i<s;++i,n+=r.Color.packedLength)r.Color.pack(c[i],l,n);return o.Ellipsoid.pack(t._ellipsoid,l,n),n+=o.Ellipsoid.packedLength,l[n++]=t._colorsPerVertex?1:0,l[n++]=t._arcType,l[n]=t._granularity,l},P.unpack=function(t,l,n){let i;l=e.defaultValue(l,0);let a=t[l++];const s=new Array(a);for(i=0;i<a;++i,l+=o.Cartesian3.packedLength)s[i]=o.Cartesian3.unpack(t,l);a=t[l++];const c=a>0?new Array(a):void 0;for(i=0;i<a;++i,l+=r.Color.packedLength)c[i]=r.Color.unpack(t,l);const d=o.Ellipsoid.unpack(t,l);l+=o.Ellipsoid.packedLength;const p=1===t[l++],y=t[l++],u=t[l];return e.defined(n)?(n._positions=s,n._colors=c,n._ellipsoid=d,n._colorsPerVertex=p,n._arcType=y,n._granularity=u,n):new P({positions:s,colors:c,ellipsoid:d,colorsPerVertex:p,arcType:y,granularity:u})};const _=new Array(2),b=new Array(2),B={positions:_,height:b,ellipsoid:void 0,minDistance:void 0,granularity:void 0};return P.createGeometry=function(p){const y=p._positions,u=p._colors,f=p._colorsPerVertex,h=p._arcType,C=p._granularity,T=p._ellipsoid,g=c.CesiumMath.chordLength(C,T.maximumRadius),P=e.defined(u)&&!f;let A;const E=y.length;let k,G,D,L,w=0;if(h===t.ArcType.GEODESIC||h===t.ArcType.RHUMB){let o,l,n;h===t.ArcType.GEODESIC?(o=c.CesiumMath.chordLength(C,T.maximumRadius),l=d.PolylinePipeline.numberOfPoints,n=d.PolylinePipeline.generateArc):(o=C,l=d.PolylinePipeline.numberOfPointsRhumbLine,n=d.PolylinePipeline.generateRhumbArc);const i=d.PolylinePipeline.extractHeights(y,T),a=B;if(h===t.ArcType.GEODESIC?a.minDistance=g:a.granularity=C,a.ellipsoid=T,P){let t=0;for(A=0;A<E-1;A++)t+=l(y[A],y[A+1],o)+1;k=new Float64Array(3*t),D=new Uint8Array(4*t),a.positions=_,a.height=b;let s=0;for(A=0;A<E-1;++A){_[0]=y[A],_[1]=y[A+1],b[0]=i[A],b[1]=i[A+1];const o=n(a);if(e.defined(u)){const e=o.length/3;L=u[A];for(let o=0;o<e;++o)D[s++]=r.Color.floatToByte(L.red),D[s++]=r.Color.floatToByte(L.green),D[s++]=r.Color.floatToByte(L.blue),D[s++]=r.Color.floatToByte(L.alpha)}k.set(o,w),w+=o.length}}else if(a.positions=y,a.height=i,k=new Float64Array(n(a)),e.defined(u)){for(D=new Uint8Array(k.length/3*4),A=0;A<E-1;++A){w=m(y[A],y[A+1],u[A],u[A+1],g,D,w)}const e=u[E-1];D[w++]=r.Color.floatToByte(e.red),D[w++]=r.Color.floatToByte(e.green),D[w++]=r.Color.floatToByte(e.blue),D[w++]=r.Color.floatToByte(e.alpha)}}else{G=P?2*E-2:E,k=new Float64Array(3*G),D=e.defined(u)?new Uint8Array(4*G):void 0;let t=0,l=0;for(A=0;A<E;++A){const n=y[A];if(P&&A>0&&(o.Cartesian3.pack(n,k,t),t+=3,L=u[A-1],D[l++]=r.Color.floatToByte(L.red),D[l++]=r.Color.floatToByte(L.green),D[l++]=r.Color.floatToByte(L.blue),D[l++]=r.Color.floatToByte(L.alpha)),P&&A===E-1)break;o.Cartesian3.pack(n,k,t),t+=3,e.defined(u)&&(L=u[A],D[l++]=r.Color.floatToByte(L.red),D[l++]=r.Color.floatToByte(L.green),D[l++]=r.Color.floatToByte(L.blue),D[l++]=r.Color.floatToByte(L.alpha))}}const V=new a.GeometryAttributes;V.position=new i.GeometryAttribute({componentDatatype:n.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:k}),e.defined(u)&&(V.color=new i.GeometryAttribute({componentDatatype:n.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:4,values:D,normalize:!0})),G=k.length/3;const x=2*(G-1),S=s.IndexDatatype.createTypedArray(G,x);let I=0;for(A=0;A<G-1;++A)S[I++]=A,S[I++]=A+1;return new i.Geometry({attributes:V,indices:S,primitiveType:i.PrimitiveType.LINES,boundingSphere:l.BoundingSphere.fromPoints(y)})},function(t,l){return e.defined(l)&&(t=P.unpack(t,l)),t._ellipsoid=o.Ellipsoid.clone(t._ellipsoid),P.createGeometry(t)}}));
