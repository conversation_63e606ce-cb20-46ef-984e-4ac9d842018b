import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'

// https://vitejs.dev/config/
export default defineConfig(({ command, mode }) => {
  // 根据当前工作目录中的 `mode` 加载 .env 文件
  // 设置第三个参数为 '' 来加载所有环境变量，而不管是否有 `VITE_` 前缀。
  const env = loadEnv(mode, process.cwd(), '')
  
  return {
    plugins: [vue()],
    resolve: {
      alias: {
        '@': path.resolve(__dirname, 'src')
      }
    },
    server: {
       // 允许IP访问
       host: "0.0.0.0",
      port: env.VITE_APP_PORT || 3000,
      // 运行是否自动打开浏览器
      open: true,
      proxy: {
        '/api': {
          target: env.VITE_APP_PROXY_TARGET,
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, ''),
          timeout: 50000,
          followRedirects: true
        }
      }
    },
    build: {
      // 打包文件大小警告限制（KB）
      chunkSizeWarningLimit: 2000,
      // 禁用 CSS 代码拆分
      cssCodeSplit: false,
      // 生产环境移除 console
      minify: 'terser',
      terserOptions: {
        compress: {
          drop_console: mode === 'production',
          drop_debugger: mode === 'production'
        }
      },
      // 确保 Cesium 资源被复制
      assetsInclude: ['**/*.glb', '**/*.wasm'],
      // 复制 Cesium 资源到打包目录
      rollupOptions: {
        input: {
          main: path.resolve(__dirname, 'index.html'),
        },
      }
    },
    // 添加 publicDir 配置
    publicDir: 'public',
  }
})
