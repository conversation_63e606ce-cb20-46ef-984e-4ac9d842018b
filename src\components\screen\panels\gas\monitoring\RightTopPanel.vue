<template>
  <PanelBox title="报警等级" class="gas-monitoring-right-top-panel">
    <div class="panel-content">
      <div class="chart-header">
        <div class="chart-legend">
          <div class="legend-item">
            <span class="legend-icon total"></span>
            <span class="legend-text">总数</span>
          </div>
          <div class="legend-item">
            <span class="legend-icon deployed"></span>
            <span class="legend-text">已处置</span>
          </div>
        </div>
      </div>
      <div class="chart-wrapper" ref="chartRef"></div>
    </div>
  </PanelBox>
</template>

<script setup>
import { ref, onMounted, onUnmounted, reactive, nextTick } from 'vue'
import * as echarts from 'echarts'
import PanelBox from '@/components/screen/PanelBox.vue'
import { getMonitorAnalysisLevelStatistics } from '@/api/gas'

// 页面可见性状态
let isPageVisible = true
// 定时器ID
let refreshTimer = null

// 图表DOM引用
const chartRef = ref(null)
let chartInstance = null

// 图表数据
const chartData = reactive({
  categories: [],
  series: [
    { name: '总数', values: [] },
    { name: '已处置', values: [] }
  ]
})

// 获取报警等级数据
const fetchAlarmLevelData = async () => {
  try {
    const response = await getMonitorAnalysisLevelStatistics()
    if (response?.data?.statistics) {
      const statistics = response.data.statistics
      
      // 更新图表数据
      chartData.categories = statistics.map(item => `${item.alarmLevelName}级报警`)
      chartData.series[0].values = statistics.map(item => item.totalCount)
      chartData.series[1].values = statistics.map(item => item.handledCount)
    }
    
    // 如果图表已初始化，则更新图表
    if (chartInstance) {
      updateChart()
    }
  } catch (error) {
    console.error('获取报警等级数据失败:', error)
  }
}

// 更新图表
const updateChart = () => {
  if (!chartInstance) return
  
  const option = createChartOption()
  chartInstance.setOption(option)
}

// 创建图表配置
const createChartOption = () => {
  return {
    backgroundColor: 'transparent',
    grid: {
      top: '16%',
      left: '3%',
      right: '4%',
      bottom: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: chartData.categories,
      axisLine: {
        lineStyle: {
          color: '#5F5F60',
          width: 1
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#FFFFFF',
        fontSize: 12,
        margin: 16,
        rotate: 0
      }
    },
    yAxis: {
      type: 'value',
      name: '单位(个)',
      nameTextStyle: {
        color: '#FFFFFF',
        fontSize: 12,
        padding: [0, -18, 0, 0]
      },
      max: 100,
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)',
          type: 'dashed'
        }
      },
      axisLabel: {
        color: '#FFFFFF',
        fontSize: 12,
        formatter: '{value}'
      }
    },
    series: [
      {
        name: '总数',
        type: 'bar',
        data: chartData.series[0].values,
        barWidth: 19,
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: '#7AAFFF' },
              { offset: 1, color: '#055ADB' }
            ]
          },
          borderRadius: [2, 2, 0, 0],
          borderColor: '#0A93FF',
          borderWidth: 1,
          shadowColor: 'rgba(5, 122, 255, 0.3)',
          shadowBlur: 10,
          shadowOffsetX: 3,
          shadowOffsetY: 3
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 15,
            shadowColor: 'rgba(5, 90, 219, 0.7)'
          }
        },
        showBackground: true,
        backgroundStyle: {
          color: 'rgba(5, 90, 219, 0.2)',
          borderRadius: [0, 0, 0, 0]
        }
      },
      {
        name: '已处置',
        type: 'bar',
        data: chartData.series[1].values,
        barWidth: 19,
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: '#FFFFFF' },
              { offset: 1, color: '#23CAFF' }
            ]
          },
          borderRadius: [2, 2, 0, 0],
          borderColor: '#34D6FF',
          borderWidth: 1,
          shadowColor: 'rgba(35, 202, 255, 0.3)',
          shadowBlur: 10,
          shadowOffsetX: 3,
          shadowOffsetY: 3
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 15,
            shadowColor: 'rgba(35, 202, 255, 0.7)'
          }
        },
        showBackground: true,
        backgroundStyle: {
          color: 'rgba(35, 202, 255, 0.2)',
          borderRadius: [0, 0, 0, 0]
        }
      }
    ]
  };
}

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return
  
  chartInstance = echarts.init(chartRef.value)
  const option = createChartOption()
  chartInstance.setOption(option)
  
  window.addEventListener('resize', handleResize)
}

// 处理窗口大小调整
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

// 处理页面可见性变化
const handleVisibilityChange = () => {
  if (document.hidden) {
    isPageVisible = false
    // 页面不可见时清除定时器
    if (refreshTimer) {
      clearInterval(refreshTimer)
      refreshTimer = null
    }
  } else {
    isPageVisible = true
    // 页面可见时重新获取数据并启动定时器
    fetchAlarmLevelData()
    startDataRefresh()
  }
}

// 启动数据刷新
const startDataRefresh = () => {
  if (!refreshTimer && isPageVisible) {
    const refreshInterval = 600000 // 10分钟刷新一次
    refreshTimer = setInterval(() => {
      fetchAlarmLevelData()
    }, refreshInterval)
  }
}

onMounted(async () => {
  await nextTick()
  // 初始化图表
  initChart()
  // 获取初始数据
  await fetchAlarmLevelData()
  // 启动数据刷新
  startDataRefresh()
  // 添加页面可见性变化监听
  document.addEventListener('visibilitychange', handleVisibilityChange)
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
  // 清理事件监听和定时器
  window.removeEventListener('resize', handleResize)
  document.removeEventListener('visibilitychange', handleVisibilityChange)
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
})
</script>

<style scoped>
.gas-monitoring-right-top-panel {
  height: 280px; /* 默认高度为280px */
}

.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.chart-header {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.chart-legend {
  display: flex;
  gap: 15px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.legend-icon {
  width: 10px;
  height: 10px;
  border-radius: 2px;
}

.legend-icon.total {
  background: #055ADB;
}

.legend-icon.deployed {
  background: #23CAFF;
}

.legend-text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
}

.chart-wrapper {
  flex: 1;
  width: 100%;
  height: 100%;
  min-height: 180px;
}

/* 媒体查询适配不同分辨率 */
@media screen and (min-width: 1920px) and (max-width: 2560px) {
  .gas-monitoring-right-top-panel {
    height: 280px;
  }
}

@media screen and (max-width: 1919px) {
  .gas-monitoring-right-top-panel {
    height: 240px;
  }
}

@media screen and (min-width: 2561px) {
  .gas-monitoring-right-top-panel {
    height: 340px;
  }
}
</style>