<template>
  <el-dialog
    style="borer-redius: 5px"
    :model-value="visible"
    :title="title"
    :close-on-click-modal="false"
    :before-close="handleClose"
    :width="width"
  >
    <div class="c-box">
      <slot name="content"> </slot>
    </div>
    <template #footer v-if="hasFooter">
      <span class="dialog-footer" v-show="hasFooter">
        <el-button
          style="background: #3385ff; color: #ffff; border-radius: 0px"
          @click="handleOk"
          >确定</el-button
        >
        <el-button
          style="background: #6886b3; color: #ffff; border-radius: 0px"
          @click="handleClose"
          >取消</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { misPosition, collectShow, collectLocation } from "@/hooks/gishooks";
import bus from "@/utils/mitt";

const props = defineProps({
    visible: {
        type: Boolean,
        required: true
    },
    title: {
        type: String,
        required: true
    },
    width: {
        type: String,
        required: true
    },
    hasFooter: {
        type: <PERSON><PERSON><PERSON>,
        required: true
    }
});

const emits = defineEmits(['update:visible']);

const handleClose = () => {
    emits("update:visible", false);
    misPosition.value = {
        id: "",
        type: "",
        longitude: "",
        latitude: "",
    };
    collectShow.value = false;
    collectLocation.value = {
        longitude: "",
        latitude: "",
    };
};

const handleOk = () => {
    emits("update:visible", false);
    bus.emit("getCollectLocation", { ...collectLocation.value });
    misPosition.value = {
        id: "",
        type: "",
        longitude: "",
        latitude: "",
    };
    collectShow.value = false;
};
</script>

<style lang="scss" scoped>
@media (min-width: 3840px) {
  .dialog-footer {
    :deep(.el-button) {
      height: 35px;
      width: 70px;
      font-size: 14px;
    }
  }
}
</style>
