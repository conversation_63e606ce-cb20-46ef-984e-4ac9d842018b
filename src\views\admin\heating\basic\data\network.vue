<template>
  <div class="heating-network-container">
    <el-tabs v-model="activeTab" class="network-tabs">
      <el-tab-pane label="管线信息" name="pipelineInfo">
        <PipelineInfo />
      </el-tab-pane>
      <el-tab-pane label="管点信息" name="pipelineNodeInfo">
        <PipelineNodeInfo />
      </el-tab-pane>
      <el-tab-pane label="管线维修记录" name="pipelineMaintenance">
        <PipelineMaintenance />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import PipelineInfo from './components/PipelineInfo.vue';
import PipelineNodeInfo from './components/PipelineNodeInfo.vue';
import PipelineMaintenance from './components/PipelineMaintenance.vue';

// 当前激活的选项卡
const activeTab = ref('pipelineInfo');
</script>

<style scoped>
.heating-network-container {
  padding: 20px;
  height: 100%;
  background-color: #fff;
}

.network-tabs {
  height: 100%;
}

:deep(.el-tabs__content) {
  height: calc(100% - 55px);
  overflow: auto;
}

:deep(.el-tab-pane) {
  height: 100%;
}
</style> 