<template>
  <PanelBox title="事故处置">
    <template #extra>
      <div class="com-select">
        <CommonSelect
          v-model="timeRange"
          :options="timeOptions"
          @change="handleTimeChange"
        />
      </div>
    </template>
    <div class="panel-content">
      <div class="stats-row">
        <div class="stat-item">
          <span class="stat-dot"><span class="stat-dot-inner"></span></span>
          <span class="stat-label">事故总数</span>
          <span class="stat-value-orange">{{ statsData.totalAccidents }}</span>
          <span class="stat-unit">个</span>
        </div>
        <div class="stat-item">
          <span class="stat-dot"><span class="stat-dot-inner"></span></span>
          <span class="stat-label">已处置</span>
          <span class="stat-value-highlight">{{ statsData.handledAccidents }}</span>
          <span class="stat-unit">个</span>
        </div>
        <div class="stat-item">
          <span class="stat-dot"><span class="stat-dot-inner"></span></span>
          <span class="stat-label">处置完成率</span>
          <span class="stat-value-gradient">{{ statsData.handleRate }}</span>
        </div>
      </div>
      
      <div class="chart-wrapper" ref="chartRef"></div>
    </div>
  </PanelBox>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick, watch } from 'vue'
import * as echarts from 'echarts'
import PanelBox from '@/components/screen/PanelBox.vue'
import CommonSelect from '@/components/screen/CommonSelect.vue'
// 预留导入API方法的地方
// import { getAccidentStatistics } from '@/api/drainage'

// 时间选择
const timeRange = ref('week')
const timeOptions = [
  { label: '近一周', value: 'week' },
  { label: '近一月', value: 'month' },
  { label: '近一年', value: 'year' }
]

// 统计数据
const statsData = reactive({
  totalAccidents: 10,
  handledAccidents: 8,
  handleRate: '80%'
})

// 图表数据
const weekChartData = {
  xAxis: ['03.01', '03', '05', '07', '09', '11', '13', '15', '17', '19', '21', '23', '25', '27', '29', '31'],
  values: [0, 20, 40, 10, 35, 15, 45, 30, 50, 25, 40, 20, 60, 35, 25, 50]
}

const monthChartData = {
  xAxis: ['01', '05', '10', '15', '20', '25', '30'],
  values: [10, 30, 50, 20, 40, 30, 45]
}

const yearChartData = {
  xAxis: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
  values: [20, 35, 30, 45, 25, 50, 40, 30, 45, 35, 25, 40]
}

// 当前展示数据
const currentChartData = ref(weekChartData)

// 图表实例
const chartRef = ref(null)
let chartInstance = null

// 处理时间范围变化
const handleTimeChange = (value) => {
  // 根据选择的时间范围更新图表数据
  if (value === 'week') {
    currentChartData.value = weekChartData
  } else if (value === 'month') {
    currentChartData.value = monthChartData
  } else if (value === 'year') {
    currentChartData.value = yearChartData
  }
  
  // 更新图表
  updateChart()
  
  // 预留后端数据请求
  // fetchData(value)
}

// 从后端获取数据 - 预留接口方法待后续接入
const fetchData = async (timeRange) => {
  try {
    // 预留实际API调用
    // const res = await getAccidentStatistics({ timeRange })
    // if (res.code === 200 && res.data) {
    //   // 处理统计数据
    //   statsData.totalAccidents = res.data.totalAccidents || 0
    //   statsData.handledAccidents = res.data.handledAccidents || 0
    //   statsData.handleRate = (typeof res.data.handleRate === 'number' ? res.data.handleRate + '%' : (res.data.handleRate || '0%'))
    //   
    //   // 处理图表数据
    //   if (res.data.chartData) {
    //     currentChartData.value = {
    //       xAxis: res.data.chartData.xAxis || [],
    //       values: res.data.chartData.values || []
    //     }
    //     updateChart()
    //   }
    // }
  } catch (error) {
    console.error('获取事故处置数据失败:', error)
  }
}

// 更新图表
const updateChart = () => {
  if (!chartInstance) return
  
  const option = createChartOption()
  chartInstance.setOption(option)
}

// 创建图表配置
const createChartOption = () => {
  return {
    backgroundColor: 'transparent',
    grid: {
      top: '15%',
      left: '5%',
      right: '4%',
      bottom: '18%',
      containLabel: true
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0, 19, 40, 0.8)',
      borderColor: 'rgba(0, 109, 232, 0.2)',
      textStyle: {
        color: '#fff',
        fontSize: 12,
        fontFamily: 'PingFangSC, PingFang SC'
      },
      formatter: function(params) {
        const param = params[0]
        return `${param.name}<br/>${param.marker}事故数量：${param.value}`
      }
    },
    xAxis: {
      type: 'category',
      data: currentChartData.value.xAxis,
      axisLine: {
        lineStyle: {
          color: '#5F5F60',
          width: 1
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        fontFamily: 'PingFangSC, PingFang SC',
        fontSize: 12,
        color: '#FFFFFF',
        margin: 12,
        interval: timeRange.value === 'week' ? 1 : 0 // 周数据显示所有标签，其他可能需要间隔
      }
    },
    yAxis: {
      type: 'value',
      name: '单位（个）',
      nameTextStyle: {
        color: "rgba(255, 255, 255, 0.6)",
        fontSize: 12,
        padding: [0, 30, 0, 0]
      },
      splitLine: {
        show: false,
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)',
          type: 'dashed'
        }
      },
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        fontFamily: 'PingFangSC, PingFang SC',
        fontSize: 12,
        color: '#FFFFFF',
        margin: 12
      }
    },
    series: [
      {
        type: 'line',
        data: currentChartData.value.values,
        smooth: true,
        symbol: 'none',
        symbolSize: 6,
        itemStyle: {
          color: '#23CAFF'
        },
        lineStyle: {
          color: '#23CAFF',
          width: 2,
          shadowColor: 'rgba(35, 202, 255, 0.4)',
          shadowBlur: 6
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0.33, color: 'rgba(35, 202, 255, 0.5)' },
              { offset: 1, color: 'rgba(35, 202, 255, 0)' }
            ]
          }
        }
      }
    ]
  }
}

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return
  
  chartInstance = echarts.init(chartRef.value)
  const option = createChartOption()
  chartInstance.setOption(option)
  
  // 响应式调整图表大小
  window.addEventListener('resize', () => {
    chartInstance && chartInstance.resize()
  })
}

// 监听时间范围变化
watch(timeRange, (newVal) => {
  handleTimeChange(newVal)
})

// 初始化
onMounted(async () => {
  await nextTick()
  // 初始化图表
  initChart()
  // 获取初始数据
  // await fetchData(timeRange.value)
})
</script>

<style scoped>
.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.com-select {
  margin-right: 20px;
}

.stats-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.stat-dot {
  width: 9px;
  height: 9px;
  background: rgba(255, 149, 0, 0.4);
  border-radius: 50%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-dot-inner {
  width: 5px;
  height: 5px;
  background: #FF9500;
  border-radius: 50%;
  position: absolute;
}

.stat-label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
}

.stat-value-orange {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 24px;
  line-height: 26px;
  text-align: left;
  font-style: normal;
  background: linear-gradient(90deg, #FFFFFF 0%, #FF9500 98%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-value-highlight {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 24px;
  color: #3CF3FF;
  line-height: 26px;
}

.stat-value-gradient {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 24px;
  line-height: 26px;
  text-align: left;
  font-style: normal;
  background: linear-gradient(90deg, #FFFFFF 0%, #36F281 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-unit {
  color: rgba(255, 255, 255, 0.4);
  font-size: 12px;
}

.chart-wrapper {
  flex: 1;
  width: 100%;
  min-height: 200px;
}

/* 响应式布局适配 */
@media (min-height: 940px) and (max-height: 1055px) {
  .panel-content {
    padding: 10px;
    gap: 10px;
  }
  
  .stat-item {
    gap: 3px;
  }
  
  .stat-dot {
    width: 7px;
    height: 7px;
  }
  
  .stat-dot-inner {
    width: 3px;
    height: 3px;
  }
  
  .stat-label {
    font-size: 11px;
  }
  
  .stat-value-orange,
  .stat-value-highlight,
  .stat-value-gradient {
    font-size: 18px;
    line-height: 22px;
  }
  
  .stat-unit {
    font-size: 10px;
  }
}

@media (max-height: 780px) {
  .chart-wrapper {
    min-height: 170px;
  }
}
/* 适配不同屏幕尺寸 */
@media screen and (min-height: 900px) and (max-height: 940px) {
  .stats-row {
    margin-bottom: 3px;
  }

  .panel-content {
    gap: 0px;
  }
}
</style> 