<template>
  <div class="header" :style="{ backgroundImage: `url(${headerBg})` }">
    <div class="header-container">
      <div class="header-left">
        <div v-if="showUser" class="user-info">
          <img :src="avatarImg" class="user-avatar" alt="用户头像" />
          <span class="user-name">{{ userName }}</span>
        </div>
        <div class="business-system-wrapper">
          <div v-if="showUser" class="business-system" @click="goToBusinessSystem" @mousedown.prevent
            @touchstart.prevent>
            <img :src="sysImg" class="system-icon" alt="业务系统" />
            <span class="system-name">业务系统</span>
          </div>
        </div>
        <div class="portal-wrapper">
          <div v-if="showUser" class="portal-system" @click="goToPortal" @mousedown.prevent @touchstart.prevent>
            <img :src="homeImg" class="portal-icon" alt="返回门户" />
            <span class="portal-name">返回门户</span>
          </div>
        </div>
      </div>
      <div class="header-title">
        东明县城市生命线物联感知平台
      </div>
      <div class="header-right">
        <div class="weather">
          <span>{{ weather.text }}</span>
          <span>{{ weather.temp }}°C</span>
        </div>
        <div class="datetime">
          <div class="date">{{ currentDate }}</div>
          <div class="time">{{ currentTime }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import headerBg from '@/assets/images/screen/screen_header.png'
import avatarImg from '@/assets/images/screen/avtar2.png'
import sysImg from '@/assets/images/screen/sys.png'
import homeImg from '@/assets/images/screen/home.png'
import { useUserStore } from '@/stores/user'

const props = defineProps({
  showUser: {
    type: Boolean,
    default: false
  }
})

const router = useRouter()
const userStore = useUserStore()
const weather = ref({ text: '晴', temp: 20 })
const currentTime = ref('')
const currentDate = ref('')

// 计算用户名称，确保在userInfo为null时有默认值
const userName = computed(() => {
  return userStore.userInfo?.nickName || '系统管理员'
})

// 跳转到业务系统
const goToBusinessSystem = (event) => {
  // 阻止事件冒泡和默认行为
  if (event) {
    event.stopPropagation();
    event.preventDefault();
    event.stopImmediatePropagation(); // 阻止同一元素上的其他事件处理程序
  }

  // 使用setTimeout确保事件处理完全结束后再导航
  setTimeout(() => {
    // router.push('/comprehensive/home');
    // 获取当前路由路径
    const currentPath = router.currentRoute.value.path;
    // 从路径中提取专项名称
    const currentSpecialty = currentPath.split('/')[1] || 'comprehensive'
    // 根据专项跳转到对应路由
    if (currentSpecialty === 'comprehensive') {
      router.push('/comprehensive/industry');
    } else {
      router.push(`/${currentSpecialty}/home`);
    }
  }, 0);
}

// 跳转到门户
const goToPortal = (event) => {
  // 阻止事件冒泡和默认行为
  if (event) {
    event.stopPropagation();
    event.preventDefault();
    event.stopImmediatePropagation(); // 阻止同一元素上的其他事件处理程序
  }

  // 使用setTimeout确保事件处理完全结束后再导航
  setTimeout(() => {
    router.push('/');
  }, 0);
}

// 更新时间
const updateDateTime = () => {
  const now = new Date()
  currentTime.value = now.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
  currentDate.value = now.toLocaleDateString('zh-CN', {
    weekday: 'long',
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
  })
}

// 获取天气信息
const getWeather = async () => {
  try {
    // 使用和风天气API
    const key = 'your_key' // 需要替换成实际的API key
    const location = '371728' // 东明县的地区编码
    const response = await fetch(`https://devapi.qweather.com/v7/weather/now?location=${location}&key=${key}`)
    const data = await response.json()
    if (data.code === '200') {
      weather.value = {
        text: data.now.text,
        temp: data.now.temp
      }
    }
  } catch (error) {
    console.error('获取天气信息失败:', error)
  }
}

let timer = null

onMounted(async () => {
  updateDateTime()
  timer = setInterval(updateDateTime, 1000)
  getWeather()

  // 如果userInfo为空，获取用户信息
  if (!userStore.userInfo) {
    await userStore.getUserInfo()
  }

  // 添加直接的DOM事件监听器
  setTimeout(() => {
    // 业务系统按钮
    const businessSystemBtn = document.querySelector('.business-system')
    if (businessSystemBtn) {
      // 移除现有的事件监听器，防止重复
      businessSystemBtn.replaceWith(businessSystemBtn.cloneNode(true))

      // 重新获取元素并添加事件监听器
      const newBusinessSystemBtn = document.querySelector('.business-system')
      if (newBusinessSystemBtn) {
        newBusinessSystemBtn.addEventListener('click', (e) => {
          e.stopPropagation()
          e.preventDefault()
          e.stopImmediatePropagation()

          // 使用setTimeout确保事件处理完全结束后再导航
          setTimeout(() => {
            // 获取当前路由路径
            const currentPath = router.currentRoute.value.path;
            // 从路径中提取专项名称
            const currentSpecialty = currentPath.split('/')[1] || 'comprehensive'
            // 根据专项跳转到对应路由
            if (currentSpecialty === 'comprehensive') {
              router.push('/comprehensive/industry');
            } else {
              router.push(`/${currentSpecialty}/home`);
            }
          }, 10)

          return false
        }, { capture: true })
      }
    }

    // 返回门户按钮
    const portalBtn = document.querySelector('.portal-system')
    if (portalBtn) {
      // 移除现有的事件监听器，防止重复
      portalBtn.replaceWith(portalBtn.cloneNode(true))

      // 重新获取元素并添加事件监听器
      const newPortalBtn = document.querySelector('.portal-system')
      if (newPortalBtn) {
        newPortalBtn.addEventListener('click', (e) => {
          e.stopPropagation()
          e.preventDefault()
          e.stopImmediatePropagation()

          // 使用setTimeout确保事件处理完全结束后再导航
          setTimeout(() => {
            router.push('/')
          }, 10)

          return false
        }, { capture: true })
      }
    }
  }, 100)
})

onUnmounted(() => {
  if (timer) {
    clearInterval(timer)
  }
})
</script>

<style scoped>
.header {
  width: 100%;
  height: 92px;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  position: relative;
  z-index: 1000;
  /* Ensure header is above map components */
}

.header-container {
  width: 100%;
  height: 100%;
  display: flex;
  padding: 0 20px;
  justify-content: space-between;
  align-items: flex-start;
  padding-top: 14px;
}

.header-left {
  flex: 1;
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  margin-left: 16px;
}

.user-avatar {
  width: 16px;
  height: 16px;
}

.user-name {
  margin-left: 8px;
  font-size: 14px;
  color: #FFFFFF;
  font-weight: bold;
}

.business-system-wrapper,
.portal-wrapper {
  position: relative;
  z-index: 9999;
  /* 非常高的z-index */
  margin-left: 24px;
  pointer-events: auto;
}

.business-system,
.portal-system {
  display: flex;
  align-items: center;
  cursor: pointer;
  position: relative;
  z-index: 2000;
  /* 确保比其他元素更高 */
  pointer-events: auto;
  /* 确保点击事件被捕获 */
}

.system-icon,
.portal-icon {
  width: 16px;
  height: 16px;
}

.system-name,
.portal-name {
  margin-left: 8px;
  font-size: 14px;
  color: #FFFFFF;
  font-weight: bold;
}

.header-title {
  font-family: 'YouSheBiaoTiHei', sans-serif;
  font-size: 42px;
  color: #FFFFFF;
  line-height: 55px;
  letter-spacing: 4px;
  text-align: center;
  font-style: normal;
  background: linear-gradient(90deg, #E8F9FF 0%, #61D7FF 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  white-space: nowrap;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  top: 18px;
}

.header-right {
  display: flex;
  gap: 20px;
  flex: 1;
  justify-content: flex-end;
  align-items: center;
}

.weather,
.datetime {
  font-family: sans-serif;
  font-weight: bold;
  font-size: 16px;
  color: #FFFFFF;
  line-height: 17px;
  letter-spacing: 1px;
  text-align: left;
  font-style: normal;
}

.weather {
  margin-left: auto;
  display: flex;
  gap: 10px;
}

.datetime {
  display: flex;
  gap: 10px;
}
</style>