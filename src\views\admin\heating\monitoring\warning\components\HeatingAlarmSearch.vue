<template>
  <div class="heating-alarm-search">
    <el-form :model="searchForm" :inline="true" label-width="80px">
      <el-form-item label="报警来源:">
        <el-select v-model="searchForm.alarmSource" placeholder="请选择" clearable style="width: 150px">
          <el-option v-for="item in HEATING_ALARM_SOURCE_OPTIONS" :key="item.value" :label="item.label"
            :value="item.value" />
        </el-select>
      </el-form-item>

      <el-form-item label="报警等级:">
        <el-select v-model="searchForm.alarmLevel" placeholder="请选择" clearable style="width: 150px">
          <el-option v-for="item in HEATING_ALARM_LEVEL_OPTIONS" :key="item.value" :label="item.label"
            :value="item.value" />
        </el-select>
      </el-form-item>

      <el-form-item label="报警类型:">
        <el-select v-model="searchForm.alarmType" placeholder="请选择" clearable style="width: 180px">
          <el-option v-for="item in alarmTypes" :key="item.alarmType" :label="item.alarmTypeName" :value="item.alarmType" />
        </el-select>
      </el-form-item>

      <el-form-item label="报警时间:">
        <el-date-picker v-model="searchForm.timeRange" type="datetimerange" range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss" style="width: 350px" />
      </el-form-item>

      <el-form-item label="报警状态:">
        <el-select v-model="searchForm.alarmStatus" placeholder="请选择" clearable style="width: 150px">
          <el-option v-for="item in HEATING_ALARM_STATUS_OPTIONS" :key="item.value" :label="item.label"
            :value="item.value" />
        </el-select>
      </el-form-item>

      <el-form-item label="报警编号:">
        <el-input v-model="searchForm.code" placeholder="输入报警编号" clearable style="width: 200px" />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="handleSearch">查询</el-button>
        <el-button @click="handleReset">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { ElForm, ElFormItem, ElSelect, ElOption, ElDatePicker, ElInput, ElButton } from 'element-plus';
import {
  HEATING_ALARM_SOURCE_OPTIONS,
  HEATING_ALARM_LEVEL_OPTIONS,
  HEATING_ALARM_STATUS_OPTIONS
} from '@/constants/heating';
import { getAlarmType } from '@/api/heating';
// 向父组件发送事件
const emit = defineEmits(['search', 'reset']);

// 搜索表单数据
const searchForm = reactive({
  alarmSource: '',
  alarmLevel: '',
  alarmType: '',
  timeRange: [],
  alarmStatus: '',
  code: ''
});
const alarmTypes = ref([]);
// 获取报警类型 
const getAlarmTypes = async () => {
  const res = await getAlarmType();
  if (res.code === 200 && res.data) {
    alarmTypes.value = res.data;
  }
};
getAlarmTypes()
// 处理搜索
const handleSearch = () => {
  emit('search', { ...searchForm });
};

// 处理重置
const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    if (key === 'timeRange') {
      searchForm[key] = [];
    } else {
      searchForm[key] = '';
    }
  });
  emit('reset');
};
</script>

<style scoped>
.heating-alarm-search {
  width: 100%;
}

:deep(.el-form--inline .el-form-item) {
  margin-right: 16px;
  margin-bottom: 16px;
}

:deep(.el-form-item__label) {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #606266;
}

:deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px #CED3DA inset;
}

:deep(.el-select .el-input__wrapper) {
  box-shadow: 0 0 0 1px #CED3DA inset;
}

:deep(.el-button--primary) {
  background-color: #0086FF;
  border-color: #0086FF;
}

:deep(.el-button--primary:hover) {
  background-color: #40A9FF;
  border-color: #40A9FF;
}
</style>