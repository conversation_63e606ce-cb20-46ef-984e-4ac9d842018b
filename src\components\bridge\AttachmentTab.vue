<template>
  <div class="attachment-tab">
    <div class="upload-tip">
      每类附件最多上传6个文件，单文件大小30M以内
    </div>

    <!-- 设计图纸 -->
    <div class="attachment-section">
      <div class="section-title">设计图纸：</div>
      <el-upload
        class="upload-demo"
        :auto-upload="false"
        :on-change="(file, fileList) => handleFileChange(file, fileList, 'design')"
        :file-list="designFiles"
        :limit="6"
        :disabled="readonly"
        multiple
      >
        <el-button type="primary" :disabled="readonly">选择文件</el-button>
        <template #tip>
          <div class="el-upload__tip">
            最多上传6个文件，单文件大小30M以内
          </div>
        </template>
      </el-upload>

      <!-- 已上传文件列表 -->
      <div v-if="designAttachments.length > 0" class="file-list">
        <div class="file-list-header">
          <span class="file-name">文件名</span>
          <span class="file-size">大小</span>
          <span class="file-actions">操作</span>
        </div>
        <div 
          v-for="(file, index) in designAttachments" 
          :key="index" 
          class="file-item"
        >
          <span class="file-name">{{ file.fileName }}</span>
          <span class="file-size">{{ formatFileSize(file.fileSize) }}</span>
          <div class="file-actions">
            <el-button type="primary" link @click="downloadFile(file)">下载</el-button>
            <el-button 
              type="danger" 
              link 
              @click="removeFile('design', index)"
              :disabled="readonly"
            >
              删除
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 施工图纸 -->
    <div class="attachment-section">
      <div class="section-title">施工图纸：</div>
      <el-upload
        class="upload-demo"
        :auto-upload="false"
        :on-change="(file, fileList) => handleFileChange(file, fileList, 'construction')"
        :file-list="constructionFiles"
        :limit="6"
        :disabled="readonly"
        multiple
      >
        <el-button type="primary" :disabled="readonly">选择文件</el-button>
        <template #tip>
          <div class="el-upload__tip">
            最多上传6个文件，单文件大小30M以内
          </div>
        </template>
      </el-upload>

      <!-- 已上传文件列表 -->
      <div v-if="constructionAttachments.length > 0" class="file-list">
        <div class="file-list-header">
          <span class="file-name">文件名</span>
          <span class="file-size">大小</span>
          <span class="file-actions">操作</span>
        </div>
        <div 
          v-for="(file, index) in constructionAttachments" 
          :key="index" 
          class="file-item"
        >
          <span class="file-name">{{ file.fileName }}</span>
          <span class="file-size">{{ formatFileSize(file.fileSize) }}</span>
          <div class="file-actions">
            <el-button type="primary" link @click="downloadFile(file)">下载</el-button>
            <el-button 
              type="danger" 
              link 
              @click="removeFile('construction', index)"
              :disabled="readonly"
            >
              删除
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 改扩建图纸 -->
    <div class="attachment-section">
      <div class="section-title">改扩建图纸：</div>
      <el-upload
        class="upload-demo"
        :auto-upload="false"
        :on-change="(file, fileList) => handleFileChange(file, fileList, 'renovation')"
        :file-list="renovationFiles"
        :limit="6"
        :disabled="readonly"
        multiple
      >
        <el-button type="primary" :disabled="readonly">选择文件</el-button>
      </el-upload>

      <!-- 已上传文件列表 -->
      <div v-if="renovationAttachments.length > 0" class="file-list">
        <div class="file-list-header">
          <span class="file-name">文件名</span>
          <span class="file-size">大小</span>
          <span class="file-actions">操作</span>
        </div>
        <div 
          v-for="(file, index) in renovationAttachments" 
          :key="index" 
          class="file-item"
        >
          <span class="file-name">{{ file.fileName }}</span>
          <span class="file-size">{{ formatFileSize(file.fileSize) }}</span>
          <div class="file-actions">
            <el-button type="primary" link @click="downloadFile(file)">下载</el-button>
            <el-button 
              type="danger" 
              link 
              @click="removeFile('renovation', index)"
              :disabled="readonly"
            >
              删除
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 其它资料 -->
    <div class="attachment-section">
      <div class="section-title">其它资料：</div>
      <el-upload
        class="upload-demo"
        :auto-upload="false"
        :on-change="(file, fileList) => handleFileChange(file, fileList, 'other')"
        :file-list="otherFiles"
        :limit="6"
        :disabled="readonly"
        multiple
      >
        <el-button type="primary" :disabled="readonly">选择文件</el-button>
      </el-upload>

      <!-- 已上传文件列表 -->
      <div v-if="otherAttachments.length > 0" class="file-list">
        <div class="file-list-header">
          <span class="file-name">文件名</span>
          <span class="file-size">大小</span>
          <span class="file-actions">操作</span>
        </div>
        <div 
          v-for="(file, index) in otherAttachments" 
          :key="index" 
          class="file-item"
        >
          <span class="file-name">{{ file.fileName }}</span>
          <span class="file-size">{{ formatFileSize(file.fileSize) }}</span>
          <div class="file-actions">
            <el-button type="primary" link @click="downloadFile(file)">下载</el-button>
            <el-button 
              type="danger" 
              link 
              @click="removeFile('other', index)"
              :disabled="readonly"
            >
              删除
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { uploadFile } from '@/api/upload'
import { ATTACHMENT_TYPE_MAP } from '@/constants/bridge'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  },
  readonly: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue'])

// 各类型附件数据
const designAttachments = ref([])
const constructionAttachments = ref([])
const renovationAttachments = ref([])
const otherAttachments = ref([])

// 上传组件的文件列表（用于显示上传进度）
const designFiles = ref([])
const constructionFiles = ref([])
const renovationFiles = ref([])
const otherFiles = ref([])

// 附件类型映射
const attachmentTypeMap = {
  design: { type: 1, name: '设计图纸' },
  construction: { type: 2, name: '施工图纸' },
  renovation: { type: 3, name: '改扩建图纸' },
  other: { type: 4, name: '其它资料' }
}

// 监听props变化
watch(() => props.modelValue, (newVal) => {
  if (newVal && typeof newVal === 'object') {
    designAttachments.value = newVal.design || []
    constructionAttachments.value = newVal.construction || []
    renovationAttachments.value = newVal.renovation || []
    otherAttachments.value = newVal.other || []
  }
}, { immediate: true, deep: true })

// 监听附件数据变化
watch([designAttachments, constructionAttachments, renovationAttachments, otherAttachments], () => {
  emit('update:modelValue', {
    design: [...designAttachments.value],
    construction: [...constructionAttachments.value],
    renovation: [...renovationAttachments.value],
    other: [...otherAttachments.value]
  })
}, { deep: true })

// 文件选择变化处理
const handleFileChange = async (file, fileList, type) => {
  // 检查文件大小
  const isLt30M = file.size / 1024 / 1024 < 30
  if (!isLt30M) {
    ElMessage.error('上传文件大小不能超过 30MB!')
    return
  }

  try {
    // 上传文件
    const response = await uploadFile(file.raw)
    if (response.status === 200) {
      const attachmentData = {
        fileName: file.name,
        fileSize: file.size,
        fileUrl: response.data.url,
        attachmentType: attachmentTypeMap[type].type,
        attachmentTypeName: attachmentTypeMap[type].name,
        uploadTime: new Date()
      }

      switch (type) {
        case 'design':
          designAttachments.value.push(attachmentData)
          break
        case 'construction':
          constructionAttachments.value.push(attachmentData)
          break
        case 'renovation':
          renovationAttachments.value.push(attachmentData)
          break
        case 'other':
          otherAttachments.value.push(attachmentData)
          break
      }

      ElMessage.success('上传成功')
    } else {
      ElMessage.error('上传失败')
    }
  } catch (error) {
    console.error('上传失败:', error)
    ElMessage.error('上传失败')
  }
}

// 删除文件
const removeFile = async (type, index) => {
  try {
    await ElMessageBox.confirm('确定要删除这个文件吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    switch (type) {
      case 'design':
        designAttachments.value.splice(index, 1)
        break
      case 'construction':
        constructionAttachments.value.splice(index, 1)
        break
      case 'renovation':
        renovationAttachments.value.splice(index, 1)
        break
      case 'other':
        otherAttachments.value.splice(index, 1)
        break
    }

    ElMessage.success('删除成功')
  } catch {
    // 用户取消删除
  }
}

// 下载文件
const downloadFile = (file) => {
  if (file.fileUrl) {
    const link = document.createElement('a')
    link.href = file.fileUrl
    link.download = file.fileName
    link.click()
  } else {
    ElMessage.error('文件链接不存在')
  }
}

// 格式化文件大小
const formatFileSize = (size) => {
  if (!size) return '0 B'
  
  const units = ['B', 'KB', 'MB', 'GB']
  let index = 0
  let fileSize = size

  while (fileSize >= 1024 && index < units.length - 1) {
    fileSize /= 1024
    index++
  }

  return `${fileSize.toFixed(1)} ${units[index]}`
}

// 表单验证方法
const validate = () => {
  return Promise.resolve(true)
}

// 重置组件
const resetComponent = () => {
  designAttachments.value = []
  constructionAttachments.value = []
  renovationAttachments.value = []
  otherAttachments.value = []
  designFiles.value = []
  constructionFiles.value = []
  renovationFiles.value = []
  otherFiles.value = []
}

// 暴露方法给父组件
defineExpose({
  validate,
  resetForm: resetComponent
})
</script>

<style scoped>
.attachment-tab {
  padding: 20px;
}

.upload-tip {
  background: #f5f7fa;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 20px;
  color: #606266;
  font-size: 14px;
}

.attachment-section {
  margin-bottom: 30px;
}

.section-title {
  font-weight: bold;
  margin-bottom: 10px;
  color: #303133;
}

.file-list {
  margin-top: 15px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

.file-list-header {
  display: flex;
  background: #f5f7fa;
  padding: 10px 15px;
  border-bottom: 1px solid #dcdfe6;
  font-weight: bold;
  color: #606266;
}

.file-item {
  display: flex;
  padding: 10px 15px;
  border-bottom: 1px solid #ebeef5;
  align-items: center;
}

.file-item:last-child {
  border-bottom: none;
}

.file-name {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-size {
  width: 100px;
  text-align: center;
  color: #909399;
}

.file-actions {
  width: 120px;
  text-align: right;
}

.upload-demo {
  margin-bottom: 10px;
}

:deep(.el-upload__tip) {
  margin-top: 7px;
  color: #999;
  font-size: 12px;
}
</style> 