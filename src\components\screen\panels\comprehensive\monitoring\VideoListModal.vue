<template>
  <teleport to="body">
    <transition name="fade">
      <div v-if="modelValue" class="modal-overlay" @click.self="closeModal">
        <div class="modal-container">
          <div class="modal-header">
            <div class="modal-title">视频监控列表</div>
            <div class="close-icon" @click="closeModal">×</div>
          </div>
          <div class="modal-content">
            <!-- 查询条件 -->
            <div class="search-container">
              <div class="search-row">
                <div class="search-item">
                  <span class="search-label">设备编码:</span>
                  <input type="text" v-model="searchParams.code" placeholder="请输入设备编码" class="search-input" />
                </div>
                <div class="search-item">
                  <span class="search-label">所属区域:</span>
                  <select v-model="searchParams.district" class="search-select">
                    <option value="">请选择区</option>
                    <option v-for="district in districtOptions" :key="district.value" :value="district.value">
                      {{ district.label }}
                    </option>
                  </select>
                </div>
              </div>
              <div class="search-row">
                <div class="search-item">
                  <span class="search-label">运行状态:</span>
                  <select v-model="searchParams.status" class="search-select">
                    <option value="">全部</option>
                    <option value="1">在线</option>
                    <option value="0">离线</option>
                  </select>
                </div>
                <div class="search-btn-group">
                  <button class="search-btn" @click="searchVideos">查 询</button>
                  <button class="reset-btn" @click="resetSearch">重 置</button>
                </div>
              </div>
            </div>
            
            <!-- 表格列表 -->
            <div class="table-container">
              <table class="video-table">
                <thead>
                  <tr>
                    <th class="checkbox-column">
                      <label class="checkbox-wrap">
                        <input type="checkbox" v-model="selectAll" @change="handleSelectAll" />
                        <span class="checkmark"></span>
                      </label>
                    </th>
                    <th>序号</th>
                    <th>摄像头名称</th>
                    <th>设备编码</th>
                    <th>位置</th>
                    <th>运行状态</th>
                    <th>定位</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="(item, index) in currentPageData" :key="index" @click="handleRowClick(item)">
                    <td>
                      <label class="checkbox-wrap">
                        <input type="checkbox" v-model="item.selected" />
                        <span class="checkmark"></span>
                      </label>
                    </td>
                    <td>{{ index + 1 }}</td>
                    <td>{{ item.name }}</td>
                    <td>{{ item.code }}</td>
                    <td>{{ item.location }}</td>
                    <td :class="item.status === '在线' ? 'status-online' : 'status-offline'">
                      {{ item.status }}
                    </td>
                    <td>
                      <div class="location-icon">
                        <SvgIcon :raw="locationIconSvg" color="#FF6D28" size="16px" />
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            
            <!-- 分页 -->
            <div class="pagination">
              <div class="page-info">共 {{ totalItems }} 条记录，每页 {{ pageSize }} 条</div>
              <div class="page-controls">
                <span class="page-btn" :class="{ disabled: currentPage === 1 }" @click="changePage(currentPage - 1)">上一页</span>
                <span class="page-number" v-for="page in pageNumbers" :key="page" :class="{ active: currentPage === page }" @click="changePage(page)">{{ page }}</span>
                <span class="page-btn" :class="{ disabled: currentPage === totalPages }" @click="changePage(currentPage + 1)">下一页</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </transition>
  </teleport>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import SvgIcon from '@/components/SvgIcon.vue'

const props = defineProps({
  modelValue: Boolean
})

const emit = defineEmits(['update:model-value'])

// 关闭弹窗
const closeModal = () => {
  emit('update:model-value', false)
}

// 搜索参数
const searchParams = ref({
  code: '',
  district: '',
  status: ''
})

// 区域选项
const districtOptions = [
  { label: '区域一', value: 'MGS' },
  { label: '区域二', value: 'DQ' },
  { label: '区域三', value: 'AJ' },
  { label: '区域四', value: 'NX' }
]

// 位置图标SVG
const locationIconSvg = `<svg viewBox="0 0 13 16" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path d="M6.5 0C2.91 0 0 2.91 0 6.5C0 11.375 6.5 16 6.5 16C6.5 16 13 11.375 13 6.5C13 2.91 10.09 0 6.5 0ZM6.5 8.8C5.235 8.8 4.2 7.765 4.2 6.5C4.2 5.235 5.235 4.2 6.5 4.2C7.765 4.2 8.8 5.235 8.8 6.5C8.8 7.765 7.765 8.8 6.5 8.8Z" fill="currentColor"/>
</svg>`

// 模拟视频数据
const videoList = ref([
  { id: 1, name: '莫怒湖东路', code: 'TB1', location: '*****郭中街道', status: '在线', selected: false },
  { id: 2, name: '莫怒湖东路', code: 'TB1', location: '*****郭中街道', status: '在线', selected: false },
  { id: 3, name: '莫怒湖东路', code: 'TB1', location: '*****郭中街道', status: '在线', selected: false },
  { id: 4, name: '排水管网4号', code: 'PS004', location: '*****新区街道', status: '离线', selected: false },
  { id: 5, name: '排水管网5号', code: 'PS005', location: '*****高新区', status: '在线', selected: false },
  { id: 6, name: '排水管网6号', code: 'PS006', location: '*****湖畔街道', status: '在线', selected: false },
  { id: 7, name: '排水管网7号', code: 'PS007', location: '*****科创路', status: '离线', selected: false },
  { id: 8, name: '排水管网8号', code: 'PS008', location: '*****望江路', status: '在线', selected: false },
  { id: 9, name: '排水管网9号', code: 'PS009', location: '*****城东路', status: '在线', selected: false },
  { id: 10, name: '排水管网10号', code: 'PS010', location: '*****钱塘江路', status: '离线', selected: false },
  { id: 11, name: '排水管网11号', code: 'PS011', location: '*****河西区', status: '在线', selected: false },
  { id: 12, name: '排水管网12号', code: 'PS012', location: '*****临平路', status: '在线', selected: false }
])

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const totalItems = computed(() => filteredData.value.length)
const totalPages = computed(() => Math.ceil(totalItems.value / pageSize.value))

// 计算页码显示
const pageNumbers = computed(() => {
  const pages = []
  let startPage = Math.max(1, currentPage.value - 2)
  let endPage = Math.min(totalPages.value, startPage + 4)
  
  // 调整startPage，确保始终有5个页码（如果总页数足够）
  if (endPage - startPage + 1 < 5 && totalPages.value >= 5) {
    startPage = Math.max(1, endPage - 4)
  }
  
  for (let i = startPage; i <= endPage; i++) {
    pages.push(i)
  }
  return pages
})

// 筛选数据
const filteredData = computed(() => {
  return videoList.value.filter(item => {
    const codeMatch = !searchParams.value.code || item.code.toLowerCase().includes(searchParams.value.code.toLowerCase())
    const statusMatch = !searchParams.value.status || 
      (searchParams.value.status === '1' && item.status === '在线') || 
      (searchParams.value.status === '0' && item.status === '离线')
    const districtMatch = !searchParams.value.district || item.location.includes(searchParams.value.district)
    
    return codeMatch && statusMatch && districtMatch
  })
})

// 当前页数据
const currentPageData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredData.value.slice(start, end)
})

// 全选状态
const selectAll = ref(false)

// 处理全选/取消全选
const handleSelectAll = () => {
  currentPageData.value.forEach(item => {
    item.selected = selectAll.value
  })
}

// 处理行点击
const handleRowClick = (item) => {
  console.log('查看视频详情:', item)
  // 这里可以添加查看详情的逻辑
}

// 切换页码
const changePage = (page) => {
  if (page < 1 || page > totalPages.value || page === currentPage.value) return
  currentPage.value = page
}

// 搜索视频
const searchVideos = () => {
  currentPage.value = 1
  // 实际项目中这里可能需要调用API
}

// 重置搜索
const resetSearch = () => {
  searchParams.value = {
    code: '',
    district: '',
    status: ''
  }
  currentPage.value = 1
}

onMounted(() => {
  // 如果有实际API，可以在这里调用获取数据
})
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.modal-container {
  width: 900px;
  height: 650px;
  background: linear-gradient(180deg, rgba(0, 22, 72, 0.9) 0%, rgba(0, 35, 91, 0.9) 100%);
  border: 1px solid rgba(59, 141, 242, 0.5);
  box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.3);
  border-radius: 4px;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid rgba(59, 141, 242, 0.3);
}

.modal-title {
  font-family: PingFangSC, 'PingFang SC';
  font-weight: 500;
  font-size: 16px;
  color: #FFFFFF;
}

.close-icon {
  font-size: 20px;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
}

.close-icon:hover {
  color: #FFFFFF;
}

.modal-content {
  padding: 15px 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 15px;
  overflow: hidden;
}

/* 查询条件区域 */
.search-container {
  background: rgba(3, 24, 55, 0.5);
  border-radius: 4px;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.search-row {
  display: flex;
  align-items: center;
  gap: 15px;
}

.search-item {
  flex: 1;
  display: flex;
  align-items: center;
}

.search-label {
  font-family: PingFangSC, 'PingFang SC';
  font-weight: 400;
  font-size: 14px;
  color: #D3E5FF;
  margin-right: 10px;
  white-space: nowrap;
}

.search-input,
.search-select {
  flex: 1;
  height: 32px;
  background: rgba(0, 19, 47, 0.35);
  border: 1px solid rgba(59, 141, 242, 0.5);
  border-radius: 2px;
  padding: 0 10px;
  color: #FFFFFF;
  font-size: 14px;
}

.search-input::placeholder {
  color: rgba(255, 255, 255, 0.3);
}

.search-input:focus,
.search-select:focus {
  outline: none;
  border-color: rgba(59, 141, 242, 0.8);
}

.search-btn-group {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-left: auto;
}

.search-btn,
.reset-btn {
  height: 32px;
  border-radius: 2px;
  border: none;
  padding: 0 15px;
  cursor: pointer;
  font-family: PingFangSC, 'PingFang SC';
  font-weight: 500;
  font-size: 14px;
}

.search-btn {
  background: #1890FF;
  color: #FFFFFF;
}

.reset-btn {
  background: rgba(24, 144, 255, 0.1);
  border: 1px solid #1890FF;
  color: #1890FF;
}

.search-btn:hover {
  background: #40A9FF;
}

.reset-btn:hover {
  background: rgba(24, 144, 255, 0.2);
}

/* 表格容器 */
.table-container {
  flex: 1;
  overflow: auto;
}

.video-table {
  width: 100%;
  border-collapse: collapse;
  color: #FFFFFF;
}

.video-table th {
  background: rgba(0, 35, 91, 0.8);
  padding: 10px;
  text-align: center;
  font-family: PingFangSC, 'PingFang SC';
  font-weight: 500;
  font-size: 14px;
  height: 50px;
}

.video-table td {
  padding: 0 10px;
  text-align: center;
  font-family: PingFangSC, 'PingFang SC';
  font-weight: 400;
  font-size: 14px;
  border-bottom: 1px solid rgba(59, 141, 242, 0.2);
  height: 40px; /* 设置行高为40px */
}

.video-table tr:hover td {
  background-color: rgba(0, 163, 255, 0.2);
  cursor: pointer;
}

.checkbox-column {
  width: 50px;
}

/* 复选框样式 */
.checkbox-wrap {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.checkbox-wrap input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.checkmark {
  position: relative;
  height: 16px;
  width: 16px;
  background-color: rgba(0, 19, 47, 0.5);
  border: 1px solid rgba(59, 141, 242, 0.6);
  border-radius: 2px;
}

.checkbox-wrap input:checked ~ .checkmark:after {
  content: "";
  position: absolute;
  left: 5px;
  top: 2px;
  width: 4px;
  height: 8px;
  border: solid #1890FF;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.location-icon {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 状态样式 */
.status-online {
  color: #3FD87C;
}

.status-offline {
  color: #FB3737;
}

/* 分页 */
.pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
}

.page-info {
  font-family: PingFangSC, 'PingFang SC';
  font-weight: 400;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
}

.page-controls {
  display: flex;
  align-items: center;
  gap: 5px;
}

.page-btn,
.page-number {
  min-width: 30px;
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  border-radius: 2px;
  font-family: PingFangSC, 'PingFang SC';
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
}

.page-number:hover {
  background: rgba(59, 141, 242, 0.2);
}

.page-number.active {
  background: #1890FF;
  color: #FFFFFF;
}

.page-btn.disabled {
  cursor: not-allowed;
  color: rgba(255, 255, 255, 0.3);
}

/* 动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style> 