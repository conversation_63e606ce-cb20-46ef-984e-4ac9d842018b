# HeatingAlarmDisposalDialog 组件修改说明

## 修改内容

### 1. 新增查看按钮
- 在处置列表的操作列添加了"查看"按钮
- 操作列宽度从180px调整为200px以容纳三个按钮
- 查看按钮点击后以只读模式显示处置详情

### 2. 修复数据回显问题
- 新增 `currentMode` 状态管理（add/edit/view）
- 重构表单数据填充逻辑，新增 `fillFormData` 函数
- 修复编辑和查看模式下的数据回显问题
- 正确处理时间字段映射

### 3. 照片上传功能优化
- 参考排水模块的实现方式
- 引入 `uploadFile` API进行真实的文件上传
- 新增照片上传、删除、错误处理逻辑
- 区分编辑和查看模式的照片显示方式：
  - 编辑模式：使用上传组件，支持增删改
  - 查看模式：使用图片预览组件，只读显示

### 4. 表单状态管理
- 表单在查看模式下设置为禁用状态
- 动态显示弹窗标题（新增/编辑/查看处置）
- 查看模式下隐藏确定按钮，只显示取消按钮
- 编辑模式下按钮文本显示为"更新"

## 主要新增功能

### 查看功能
```javascript
// 处理查看
const handleView = (row) => {
  currentMode.value = 'view';
  fillFormData(row);
  formDialogVisible.value = true;
};
```

### 照片上传处理
```javascript
// 照片上传处理
const handlePhotoUpload = async (options) => {
  try {
    const res = await uploadFile(options.file);
    if (res.status === 200) {
      // 添加到照片列表
      const currentUrls = disposalForm.picUrls ? disposalForm.picUrls.split(',').filter(Boolean) : [];
      currentUrls.push(res.data.url);
      disposalForm.picUrls = currentUrls.join(',');
      
      // 更新文件列表显示
      photoFileList.value.push({ 
        name: options.file.name, 
        url: res.data.url,
        status: 'success'
      });
      
      ElMessage.success('上传成功');
    } else {
      ElMessage.error(res.message || '上传失败');
    }
  } catch (error) {
    console.error('上传照片失败:', error);
    ElMessage.error('上传失败');
  }
};
```

### 数据回显逻辑
```javascript
// 填充表单数据
const fillFormData = (row) => {
  Object.keys(disposalForm).forEach(key => {
    if (row[key] !== undefined) {
      disposalForm[key] = row[key];
    }
  });
  
  // 处理时间字段映射
  if (row.createTime && !disposalForm.handleTime) {
    disposalForm.handleTime = row.createTime;
  }
  
  // 处理照片显示
  if (row.picUrls) {
    const urls = row.picUrls.split(',').filter(Boolean);
    photoPreviewList.value = urls;
    
    // 编辑模式下需要设置文件列表用于上传组件显示
    if (currentMode.value === 'edit') {
      photoFileList.value = urls.map((url, index) => ({
        name: `照片${index + 1}`,
        url: url,
        status: 'success'
      }));
    } else {
      // 查看模式下清空文件列表
      photoFileList.value = [];
    }
  } else {
    photoFileList.value = [];
    photoPreviewList.value = [];
  }
};
```

## 模板变化

### 操作列
```html
<el-table-column label="操作" width="200" align="center">
  <template #default="scope">
    <el-button type="text" @click="handleView(scope.row)">查看</el-button>
    <el-button type="text" @click="handleEdit(scope.row)">编辑</el-button>
    <el-button type="text" @click="handleDelete(scope.row)">删除</el-button>
  </template>
</el-table-column>
```

### 表单禁用状态
```html
<el-form 
  :model="disposalForm" 
  :rules="rules" 
  ref="formRef" 
  label-width="100px"
  :disabled="currentMode === 'view'"
>
```

### 照片处理
```html
<el-form-item label="处置照片:" prop="picUrls">
  <template v-if="currentMode !== 'view'">
    <!-- 编辑模式：上传组件 -->
    <el-upload
      class="upload-demo"
      :http-request="handlePhotoUpload"
      :file-list="photoFileList"
      :on-error="handleUploadError"
      :on-remove="handlePhotoRemove"
      :limit="3"
      list-type="picture-card"
      :auto-upload="true"
    >
      <el-icon><Plus /></el-icon>
      <template #tip>
        <div class="el-upload__tip">最多上传3张照片，支持jpg、png格式，单张不超过5MB</div>
      </template>
    </el-upload>
  </template>
  <template v-else>
    <!-- 查看模式：图片预览 -->
    <div class="photo-preview">
      <el-image
        v-for="(photo, index) in photoPreviewList"
        :key="index"
        :src="photo"
        :preview-src-list="photoPreviewList"
        style="width: 100px; height: 100px; margin-right: 10px"
        fit="cover"
      />
    </div>
  </template>
</el-form-item>
```

## 依赖变化

新增导入：
- `ElImage` 组件用于图片预览
- `Plus` 图标用于上传按钮
- `uploadFile` API用于文件上传

## 测试要点

1. **查看功能**：点击查看按钮，确认表单为只读状态，照片正确显示
2. **编辑功能**：点击编辑按钮，确认数据正确回显，照片可以增删改
3. **新增功能**：点击新增按钮，确认表单为空白状态，可以上传照片
4. **照片上传**：测试照片上传、删除功能是否正常
5. **数据保存**：测试编辑和新增的数据保存是否正常

## 注意事项

1. 需要确保 `uploadFile` API 可用
2. 照片URL格式需要与后端保持一致（逗号分隔）
3. 时间格式处理需要与后端接口匹配
4. 文件上传限制（3张照片，支持格式等）可根据实际需求调整 