<template>
  <div class="screen-footer">
    <div class="nav-container">
      <router-link
        v-for="(item, index) in navItems" 
        :key="index"
        :to="getRouteByItem(item)"
        custom
        v-slot="{ navigate, href }"
      >
        <div 
          :class="['nav-item', isActiveRoute(item.value) ? 'active' : '']"
          @click="navigate"
        >
          {{ item.label }}
        </div>
      </router-link>
    </div>
  </div>
</template>

<script setup>
import { defineProps } from 'vue';
import { useRoute } from 'vue-router';
import footerBg from '@/assets/images/screen/screen_foot.png';
import menuActionBg from '@/assets/images/screen/menu_action.png';

const route = useRoute();

const props = defineProps({
  activeTab: {
    type: String,
    required: true
  }
});

// 导航项数据
const navItems = [
  { label: '综合', value: 'comprehensive' },
  { label: '燃气', value: 'gas' },
  { label: '排水', value: 'drainage' },
  { label: '供热', value: 'heating' },
  { label: '桥梁', value: 'bridge' }
];

// 判断路由是否激活
const isActiveRoute = (value) => {
  // For bridge, check both the direct route and when primaryTab param equals 'bridge'
  if (value === 'bridge') {
    return route.path === '/bridge' || props.activeTab === 'bridge';
  }
  // For other tabs, check if primaryTab param matches or activeTab prop matches
  return route.params.primaryTab === value || props.activeTab === value;
};

// 根据导航项获取路由
const getRouteByItem = (item) => {
  // 桥梁专项直接跳转到 /bridge
  if (item.value === 'bridge') {
    return '/bridge';
  }
  // 其他专项跳转到对应的 overview 页面
  return `/${item.value}/overview`;
};
</script>

<style scoped>
.screen-footer {
  width: 100%;
  height: 59px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-image: url('@/assets/images/screen/screen_foot.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
}

.nav-container {
  display: flex;
  gap: 1px;
}

.nav-item {
  padding: 8px 26px;
  font-family: PingFangSC, 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 16px;
  color: rgba(255, 255, 255, 0.6);
  line-height: 22px;
  text-align: left;
  font-style: normal;
  cursor: pointer;
  transition: all 0.3s;
  border-radius: 4px;
  position: relative;
}

.nav-item:hover {
  color: rgba(255, 255, 255, 0.8);
}

.nav-item.active {
  height: 46px;
  line-height: 15px;
  background-image: url('@/assets/images/screen/menu_action.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  color: rgba(255, 255, 255, 1);
}
</style> 
