<template>
  <div class="radar-chart-container">
    <div ref="chartRef" class="chart-content"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  // 雷达图指示器，格式 [{name: '指标1', max: 100}, {name: '指标2', max: 100}]
  indicators: {
    type: Array,
    required: true
  },
  // 数据系列，格式 [{name: '系列1', value: [50, 80, 70]}, {name: '系列2', value: [80, 60, 90]}]
  series: {
    type: Array,
    required: true
  },
  // 颜色列表
  colorList: {
    type: Array,
    default: () => [
      ['#00F2F1', '#0066FF'], // 蓝色渐变
      ['#FFD24D', '#FF8F35'], // 橙色渐变
      ['#35FF6B', '#00B36B'], // 绿色渐变
      ['#FF59C8', '#FF0066'], // 粉色渐变
      ['#B36CFF', '#7700FF'], // 紫色渐变
    ]
  },
  // 形状：circle-圆形，polygon-多边形
  shape: {
    type: String,
    default: 'polygon',
    validator: (value) => ['circle', 'polygon'].includes(value)
  },
  // 是否显示图例
  showLegend: {
    type: Boolean,
    default: true
  },
  // 图例位置：top, bottom, left, right
  legendPosition: {
    type: String,
    default: 'top',
    validator: (value) => ['top', 'bottom', 'left', 'right'].includes(value)
  },
  // 是否显示区域
  showArea: {
    type: Boolean,
    default: true
  },
  // 线条宽度
  lineWidth: {
    type: Number,
    default: 2
  },
  // 区域透明度
  areaOpacity: {
    type: Number,
    default: 0.2
  },
  // 是否显示数值
  showLabel: {
    type: Boolean,
    default: false
  },
  // 单位
  unit: {
    type: String,
    default: ''
  }
})

const chartRef = ref(null)
let chartInstance = null

onMounted(async () => {
  await nextTick()
  if (chartRef.value) {
    initChart()
  }
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
  window.removeEventListener('resize', handleResize)
})

watch(() => props.indicators, () => {
  if (chartInstance) {
    updateChart()
  }
}, { deep: true })

watch(() => props.series, () => {
  if (chartInstance) {
    updateChart()
  }
}, { deep: true })

watch(() => [props.shape, props.showArea, props.showLegend, props.legendPosition], () => {
  if (chartInstance) {
    updateChart()
  }
})

const initChart = () => {
  try {
    if (chartInstance) {
      chartInstance.dispose()
    }
    
    chartInstance = echarts.init(chartRef.value)
    
    window.addEventListener('resize', handleResize)
    
    updateChart()
    chartInstance.resize()
  } catch (error) {
    console.error('初始化雷达图失败:', error)
  }
}

const handleResize = () => {
  if (chartInstance) {
    try {
      chartInstance.resize()
    } catch (error) {
      console.error('调整雷达图大小失败:', error)
    }
  }
}

const updateChart = () => {
  if (!chartInstance) return
  
  try {
    // 创建本地副本
    const localIndicators = JSON.parse(JSON.stringify(props.indicators))
    const localSeries = JSON.parse(JSON.stringify(props.series))
    const localTitle = props.title
    
    // 配置雷达图选项
    const option = {
      backgroundColor: 'transparent',
      title: localTitle ? {
        text: localTitle,
        left: 'center',
        top: 10,
        textStyle: {
          color: '#FFFFFF',
          fontSize: 14,
          fontWeight: 'normal',
          fontFamily: 'PingFangSC, PingFang SC'
        }
      } : null,
      tooltip: {
        trigger: 'item',
        formatter: (params) => {
          let res = `${params.name}<br/>`
          params.value.forEach((val, index) => {
            if (index < localIndicators.length) {
              res += `${localIndicators[index].name}: ${val}${props.unit}<br/>`
            }
          })
          return res
        }
      },
      legend: props.showLegend ? {
        type: 'scroll',
        orient: ['left', 'right'].includes(props.legendPosition) ? 'vertical' : 'horizontal',
        top: props.legendPosition === 'top' ? (localTitle ? '30px' : '5%') : (props.legendPosition === 'bottom' ? 'bottom' : (localTitle ? '30px' : '5%')),
        left: props.legendPosition === 'left' ? '5%' : (props.legendPosition === 'right' ? 'right' : 'center'),
        icon: 'rect',
        itemWidth: 10,
        itemHeight: 10,
        pageIconSize: 10,
        pageTextStyle: {
          color: 'rgba(255,255,255,0.7)'
        },
        textStyle: {
          color: 'rgba(255,255,255,0.7)',
          fontSize: 10
        },
        data: localSeries.map(item => item.name)
      } : { show: false },
      radar: {
        indicator: localIndicators,
        shape: props.shape,
        center: ['50%', '55%'],
        radius: '60%',
        splitNumber: 5,
        nameGap: 10,
        name: {
          color: 'rgba(255,255,255,0.8)',
          fontSize: 12
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(255,255,255,0.2)',
            width: 1
          }
        },
        splitArea: {
          show: true,
          areaStyle: {
            color: ['rgba(255,255,255,0.04)', 'rgba(255,255,255,0.06)']
          }
        },
        axisLine: {
          lineStyle: {
            color: 'rgba(255,255,255,0.3)',
            width: 1
          }
        }
      },
      series: [{
        type: 'radar',
        data: localSeries.map((item, index) => {
          const colorPair = props.colorList[index % props.colorList.length]
          
          return {
            name: item.name,
            value: item.value,
            symbol: 'circle',
            symbolSize: 4,
            itemStyle: {
              color: colorPair[0]
            },
            areaStyle: props.showArea ? {
              color: {
                type: 'linear',
                x: 0, y: 0, x2: 0, y2: 1,
                colorStops: [
                  { offset: 0, color: colorPair[0].replace(')', `, ${props.areaOpacity})`).replace('rgb', 'rgba') },
                  { offset: 1, color: colorPair[1].replace(')', `, ${props.areaOpacity / 2})`).replace('rgb', 'rgba') }
                ]
              }
            } : null,
            lineStyle: {
              width: props.lineWidth,
              color: colorPair[0]
            },
            label: props.showLabel ? {
              show: true,
              formatter: (params) => {
                return params.value + props.unit
              },
              color: 'rgba(255,255,255,0.8)',
              fontSize: 10
            } : {
              show: false
            }
          }
        })
      }]
    }
    
    // 根据图例位置调整图表位置
    if (props.showLegend) {
      const hasTitle = !!localTitle
      
      if (props.legendPosition === 'left') {
        option.grid = {
          left: '25%',
          top: hasTitle ? '15%' : '5%',
          right: '5%',
          bottom: '5%'
        }
        option.radar.center = ['60%', '55%']
      } else if (props.legendPosition === 'right') {
        option.grid = {
          left: '5%',
          top: hasTitle ? '15%' : '5%',
          right: '25%',
          bottom: '5%'
        }
        option.radar.center = ['40%', '55%']
      } else if (props.legendPosition === 'top') {
        option.grid = {
          left: '5%',
          top: hasTitle ? '20%' : '15%',
          right: '5%',
          bottom: '5%'
        }
        option.radar.center = ['50%', '60%']
      } else if (props.legendPosition === 'bottom') {
        option.grid = {
          left: '5%',
          top: hasTitle ? '15%' : '5%',
          right: '5%',
          bottom: '15%'
        }
        option.radar.center = ['50%', '45%']
      }
    }
    
    chartInstance.setOption(option, true)
  } catch (error) {
    console.error('更新雷达图失败:', error)
  }
}
</script>

<style scoped>
.radar-chart-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 160px;
}

.chart-content {
  width: 100%;
  height: 100%;
  min-height: 160px;
}
</style> 