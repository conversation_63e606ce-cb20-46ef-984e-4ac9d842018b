<!-- 报警处置 -->
<template>
  <div class="device-process">
    <el-timeline class="timeline screen-table-style custom-table-card">
      <el-timeline-item
              v-for="(activity, index) in activities"
              :key="index"
              :type="activity.type"
              :size="activity.size"
              :hollow="activity.hollow"
      >
          <div class="activities-content">
              <h4>{{ activity.title }}</h4>
              <p v-if="activity.content">{{ activity.content }}</p>
              <p v-if="activity.extra.confirmResult">确认结果：{{ activity.extra.confirmResult }}</p>
              <p v-if="activity.extra.handleStatus">处置状态：{{ activity.extra.handleStatus }}</p>
              <p v-if="activity.extra.handleUser">处置人：{{ activity.extra.handleUser }}</p>
              <p v-if="activity.extra.unit">处置单位：{{ activity.extra.unit }}</p>
              <p>{{ activity.timestamp }}</p>
          </div>
      </el-timeline-item>
    </el-timeline>
  </div>
</template>

<script setup>
import {computed, reactive, ref, watch} from "vue";
import {popupMonitorAlarmStatusListApiInfo} from "@/components/GisMap/popup/popupApi.js";

const props = defineProps({
    //弹框标题
    data: {
        type: Object,
        default: () => ({}),
    },
});

// 时间轴数据
const activities = ref([]);

// 获取状态类型
const getStatusType = (status) => {
    const typeMap = {
        '92001': 'danger',    // 发生报警
        '92002': 'warning',   // 确认报警
        '92003': 'success',   // 处置报警
        '92004': 'info'       // 关闭报警
    };
    return typeMap[status] || 'info';
};

// 报警处理信息
const getAlarmDeal = async () => {
    if (popupMonitorAlarmStatusListApiInfo[props.data?.layerId]) {
        popupMonitorAlarmStatusListApiInfo[props.data?.layerId]({ alarmId: props.data?.alarmId }).then(res => {
            activities.value = [];
            if (res?.data) {
                res?.data.map((item) => {
                    activities.value.push({
                        title: item?.alarmStatusName,
                        content: item?.description || '',
                        timestamp: item?.createTime,
                        type: getStatusType(item.alarmStatus),
                        size: 'normal',
                        hollow: false,
                        extra: {
                            confirmResult: item?.confirmResultName,
                            handleStatus: item?.handleStatusName,
                            handleUser: item?.handleUser,
                            unit: item?.unit
                        }
                    })
                })
            }
        })
    }
};
watch(
    () => props.data,
    () => {
        getAlarmDeal();
    },
    {
        deep: true,
        immediate: true,
    }
);
</script>
<style lang="scss" scoped>
.device-process {
  height: 360px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;

  .timeline {
    flex: 1;
    overflow: auto;
    padding: 0 15px;
  }
  :deep(.el-timeline-item__timestamp) {
    color: #fff;
    font-size: 14px;
    letter-spacing: 2px;
  }
  :deep(.el-timeline-item__tail) {
    border-color: #19385c;
  }
}
.file-icon {
  width: 32px;
  height: 26px;
  position: relative;
  top: 7px;
  left: 10px;
}
.file-list {
  .file-item {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    cursor: pointer;
    font-size: 15px;
    margin-bottom: 5px;
    &:hover {
      color: var(--el-color-primary);
    }
  }
}
.dot {
  background: #fff;
  width: 20px;
  height: 20px;
  border-radius: 10px;
  margin-left: -5px;
  background: #19385c;
  position: relative;
  &::after {
    content: "";
    position: absolute;
    width: 7px;
    height: 7px;
    background: #ffffff;
    border-radius: 20px;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
  }
}
:deep(.isCurrent.el-timeline-item) {
  .dot::after {
    background: var(--el-color-primary);
  }
  .el-timeline-item__timestamp {
    font-weight: bold;
  }
}
:deep(.isAfter.el-timeline-item) {
  .el-timeline-item__timestamp {
    color: rgba(255, 255, 255, 0.8);
  }
}

.btn-group {
  padding: 10px 10px 0;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.activities-content {
  color: rgba(255, 255, 255, 0.8);;
}

</style>

<style>
.file-popover.el-popover.el-popper {
  .el-popover__title {
    color: #000;
    font-size: 17px;
  }
}
.el-table__cell.dealFile {
  padding: 0;
}
</style>
