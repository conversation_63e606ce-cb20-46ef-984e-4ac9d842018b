<template>
  <PanelBox title="基础设施">
    <div class="panel-content">
      <div class="basic-bg">
        <img src="@/assets/images/screen/comprehensive/basic_bg.png" alt="基础设施背景" />
      </div>

      <div class="modules-container">
        <!-- 燃气模块 -->
        <div class="module left-module">
          <div class="module-header">
            <div class="module-title-bg">
              <img src="@/assets/images/screen/comprehensive/left_bg.png" alt="燃气背景" />
            </div>
            <div class="module-title left-title">
              <div class="icon">
                <img src="@/assets/images/screen/comprehensive/ranqi.png" alt="燃气" />
              </div>
              <div class="title-text">燃气</div>
            </div>
          </div>
          <div class="module-data">
            <div class="data-item">
              <span class="data-label">管网</span>
              <span>
                <span class="data-value left-value">498.69</span>
                <span class="data-unit">公里</span>
              </span>
            </div>
            <div class="data-item">
              <span class="data-label">场站</span>
              <span>
                <span class="data-value left-value">4</span>
                <span class="data-unit">座</span>
              </span>
            </div>
            <div class="data-item">
              <span class="data-label">燃气井</span>
              <span>
                <span class="data-value left-value">13211</span>
                <span class="data-unit">个</span>
              </span>
            </div>
          </div>
        </div>

        <!-- 排水模块 -->
        <div class="module right-module">
          <div class="module-header">
            <div class="module-title-bg">
              <img src="@/assets/images/screen/comprehensive/right_bg.png" alt="排水背景" />
            </div>
            <div class="module-title right-title">
              <div class="title-text">排水</div>
              <div class="icon">
                <img src="@/assets/images/screen/comprehensive/paishui.png" alt="排水" />
              </div>
            </div>
          </div>
          <div class="module-data">
            <div class="data-item">
              <span class="data-label">雨水管网</span>
              <span>
                <span class="data-value right-value">396</span>
                <span class="data-unit">公里</span>
              </span>
            </div>
            <div class="data-item">
              <span class="data-label">污水管网</span>
              <span>
                <span class="data-value right-value">396</span>
                <span class="data-unit">公里</span>
              </span>
            </div>
            <div class="data-item">
              <span class="data-label">易涝点</span>
              <span>
                <span class="data-value right-value">4</span>
                <span class="data-unit">个</span>
              </span>
            </div>
          </div>
        </div>

        <!-- 桥梁模块 -->
        <div class="module left-module">
          <div class="module-header">
            <div class="module-title-bg">
              <img src="@/assets/images/screen/comprehensive/left_bg.png" alt="桥梁背景" />
            </div>
            <div class="module-title left-title">
              <div class="icon">
                <img src="@/assets/images/screen/comprehensive/qiaoliang.png" alt="桥梁" />
              </div>
              <div class="title-text">桥梁</div>
            </div>
          </div>
          <div class="module-data">
            <div class="data-item">
              <span class="data-label">总数</span>
              <span>
                <span class="data-value left-value">24</span>
                <span class="data-unit">座</span>
              </span>
            </div>
            <div class="data-item">
              <span class="data-label">养护单位</span>
              <span>
                <span class="data-value left-value">2</span>
                <span class="data-unit">家</span>
              </span>
            </div>
          </div>
        </div>

        <!-- 供热模块 -->
        <div class="module right-module">
          <div class="module-header">
            <div class="module-title-bg">
              <img src="@/assets/images/screen/comprehensive/right_bg.png" alt="供热背景" />
            </div>
            <div class="module-title right-title">
              <div class="title-text">供热</div>
              <div class="icon">
                <img src="@/assets/images/screen/comprehensive/gongre.png" alt="供热" />
              </div>
            </div>
          </div>
          <div class="module-data">
            <div class="data-item">
              <span class="data-label">热源</span>
              <span>
                <span class="data-value right-value">125</span>
                <span class="data-unit">座</span>
              </span>
            </div>
            <div class="data-item">
              <span class="data-label">管网</span>
              <span>
                <span class="data-value right-value">498.69</span>
                <span class="data-unit">公里</span>
              </span>
            </div>
            <div class="data-item">
              <span class="data-label">供热站</span>
              <span>
                <span class="data-value right-value">2</span>
                <span class="data-unit">座</span>
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </PanelBox>
</template>

<script setup>
import PanelBox from '@/components/screen/PanelBox.vue'
// 综合态势总览左上面板组件
</script>

<style scoped>
.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
  position: relative;
}

.basic-bg {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.basic-bg img {
  max-width: 100%;
  max-height: 100%;
}

.modules-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 15px;
  height: 100%;
  position: relative;
  z-index: 2;
}

.module {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.module-header {
  position: relative;
  height: 30px;
}

.module-title-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.module-title-bg img {
  width: 221px;
  height: 30px;
  object-fit: cover;
}

.module-title {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  padding: 0 10px;
  padding-right: 1rem;
}

.left-title {
  justify-content: flex-start;
}

.right-title {
  justify-content: flex-end;
}

.icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon img {
  width: 20px;
  height: 20px;
}

.title-text {
  margin: 0 5px;
  font-family: YouSheBiaoTiHei;
  font-size: 18px;
  line-height: 23px;
  letter-spacing: 1px;
  font-style: normal;
}

.left-title .title-text {
  color: #FFFFFF;
  text-align: left;
  background: linear-gradient(90deg, #CDF7FF 20%, #30DCFF 81%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.right-title .title-text {
  color: #FFFFFF;
  text-align: right;
  background: linear-gradient(90deg, #FFF4CD 20%, #FFB930 81%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.module-data {
  display: flex;
  flex-direction: column;
  gap: 0.2rem;
  padding-left: 1.8rem;
  padding-right: 2rem;
}

.data-item {
  display: flex;
  align-items: center;
}

.left-module .data-item {
  justify-content: space-between;
}

.right-module .data-item {
  justify-content: space-between;
}

.data-label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
}

.left-module .data-label {
  color: #BFEAFF;
}

.right-module .data-label {
  color: #FFEABF;
  margin-left: 5px;
}

.data-value {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 18px;
  line-height: 20px;
  font-style: normal;
}

.left-value {
  text-align: left;
  background: linear-gradient(90deg, #CDF7FF 20%, #30DCFF 81%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.right-value {
  text-align: right;
  background: linear-gradient(90deg, #FFF4CD 20%, #FFB930 81%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.data-unit {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
}

.left-module .data-unit {
  color: #BFEAFF;
  margin-left: 5px;
}

.right-module .data-unit {
  color: #FFEABF;
  margin-left: 5px;
}

/* 响应式布局适配 */
@media screen and (max-height: 940px) {
  .panel-content {
    padding: 10px;
  }

  .modules-container {
    gap: 1px;
  }

  .module {
    gap: 1px;
  }

  .module-header {
    height: 25px;
  }

  .module-title-bg img {
    height: 25px;
  }

  .title-text {
    font-size: 16px;
    line-height: 20px;
  }

  .data-label {
    font-size: 12px;
  }

  .data-value {
    font-size: 16px;
    line-height: 18px;
  }

  .data-unit {
    font-size: 12px;
  }
}
</style>