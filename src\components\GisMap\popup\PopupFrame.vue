<template>
  <div
    class="popup-frame"
    :class="{ hasArrow, hasPosition }"
    :style="
      height && width
        ? {
            height: height + 'px',
            width: width + 'px',
          }
        : {}
    "
  >
    <div class="frame-title">
      {{ title }}
      <div class="title-close" title="关闭" @click="emits('close')">
        <el-icon><Close /></el-icon>
      </div>
    </div>
    <div class="frame-content">
      <div class="tab-list flex items-center" v-if="tabList.length">
        <div
          :key="item.id"
          :class="{ active: item.id === tabIndex }"
          @click="changeTab(item.id)"
          class="tab-item"
          v-for="item in tabList"
        >
          {{ item.name }}
        </div>
      </div>
      <div style="flex: 1; overflow: auto">
        <slot></slot>
      </div>
    </div>
  </div>
</template>

<script setup>
import {ref, watch} from "vue";

const emits = defineEmits(["close", "changeTab"]);

const props = defineProps({
    title: "查看详情",
    hasArrow: true, // 是否有箭头
    isAlarm: false, // 是否是报警弹窗
    hasPosition: false, // 是否需要定位
    data: {
        type: Object,
        default: () => ({}),
    },
    tabList: {
        type: Array,
        default: () => [],
    },
    height: {
        type: Number,
        default: 0,
    },
    width: {
        type: Number,
        default: 0,
    },
});

const tabIndex = ref(1);

watch(
    () => props.isAlarm,
    () => {
        if (props.isAlarm) {
            tabIndex.value = 4;
        }
    },
    {
        immediate: true,
        deep: true,
    }
);

const changeTab = (id) => {
    tabIndex.value = id;
    emits("changeTab", id);
};
</script>

<style lang="scss" scoped>
.popup-frame {
  width: 640px;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  border-radius: 8px;
  position: relative;
  z-index: 999;
  pointer-events: all;
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    backdrop-filter: blur(10px);
    background: rgba(11, 30, 65, 0.8);
    border-radius: 8px;
    border: 1px solid #19385c;
    z-index: -1;
  }
  .frame-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-top-left-radius: 12px;
    padding: 15px 24px;
    font-size: 16px;
    font-weight: bold;
    color: #ffffff;
    border-bottom: 1px solid #19385c;
    cursor: pointer;
    .title-close {
      font-size: 22px;
    }
  }
  .frame-content {
    flex: 1;
    overflow: hidden;
    padding: 18px 22px;
    display: flex;
    flex-direction: column;
  }
  .tab-list {
    margin: 0px 0 16px;
    border-bottom: 1px solid #19385c;
    padding: 0px 0 15px;
    .tab-item {
      color: rgba(255, 255, 255, 0.8);
      font-size: 14px;
      letter-spacing: 2px;
      position: relative;
      margin-right: 40px;
      &::after {
        content: "";
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        bottom: -15px;
        width: 0;
        height: 4px;
        background: #fff;
        opacity: 0;
        border-radius: 2px;
        transition: all 0.2s ease;
      }
      cursor: pointer;
      &.active {
        position: relative;
        color: #fff;
        &:after {
          opacity: 1;
          width: 56px;
        }
      }
    }
  }
  &.hasPosition {
    position: absolute;
    z-index: 999;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    .frame-content {
      padding: 0 24px;
    }
    :deep(.el-button + .el-button) {
      margin-left: 8px;
    }
  }
  &.hasArrow {
    position: relative;
    box-shadow: 0px 4px 6px 0px rgba(0, 0, 0, 0.06);
    &::after {
      content: "";
      width: 0;
      height: 0;
      border-left: 10px solid transparent;
      border-right: 10px solid transparent;
      border-top: 15px solid rgba(11, 30, 65, 0.8);
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      bottom: -15px;
    }
  }
}
</style>
