# REM适配使用指南

本项目基于1920*1080分辨率设计稿实现了全局REM适配方案，使页面在不同屏幕尺寸下能够等比例缩放，保持一致的视觉效果。

## 基准值说明

- 设计稿宽度：1920px
- 设计稿高度：1080px
- 基准字体大小：16px（1rem = 16px）

## 使用方式

### 在CSS/SCSS中使用

在普通CSS中，可以直接使用rem单位：

```css
.example {
  width: 6.25rem;  /* 相当于100px */
  height: 12.5rem; /* 相当于200px */
  font-size: 1rem; /* 相当于16px */
  padding: 0.625rem 1.25rem; /* 相当于10px 20px */
}
```

### 在SCSS中使用rem函数

项目提供了rem函数和多个混合宏，可以在SCSS文件中直接使用：

```scss
@import '@/styles/mixins/rem.scss';

.example {
  width: rem(100px);
  height: rem(200px);
  font-size: rem(16px);
  padding: rem(10px) rem(20px);
  margin-bottom: rem(30px);
  
  // 使用混合宏
  @include position(absolute, 10px, 20px, 30px, 40px);
  @include font-size(14px);
  @include border-radius(4px);
}
```

### 在JS中使用

项目提供了px转rem的JS工具函数：

```javascript
import { pxToRem, objectPxToRem } from '@/utils/pxToRem';

// 单个值转换
const width = pxToRem(100); // "6.25rem"

// 对象批量转换
const styles = objectPxToRem({
  width: 100,
  height: 200,
  fontSize: 14
});
// 结果: { width: '6.25rem', height: '12.5rem', fontSize: '0.875rem' }
```

## Element Plus组件适配

项目已对Element Plus组件进行了rem适配，无需额外处理，组件会自动适配不同屏幕尺寸。

## 可用的混合宏

```scss
@include font-size(14px);      // 字体大小
@include width(100px);         // 宽度
@include height(200px);        // 高度
@include padding(10px, 20px);  // 内边距
@include margin(10px, 20px);   // 外边距
@include position(absolute, 10px, 20px, 30px, 40px);  // 定位
@include max-width(1200px);    // 最大宽度
@include min-width(300px);     // 最小宽度
@include border-radius(4px);   // 边框圆角
```

## 常见问题

### 1. 小数点精度问题

由于计算过程中可能出现小数点，可能导致1px边框在高分辨率屏幕下显示不清晰。对于1px边框，可以使用以下方案：

```scss
.border-bottom {
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background-color: #ddd;
    transform: scaleY(0.5);
    transform-origin: bottom;
  }
}
```

### 2. 背景图片适配

对于大型背景图片，可以使用以下方式适配：

```css
.bg-container {
  background-size: 100% 100%;
  /* 或者 */
  background-size: cover;
}
```

### 3. 字体模糊问题

如果在某些缩放情况下文字显示模糊，可以尝试以下CSS属性：

```css
.text {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
```

## 手动更新REM基准值

在特殊情况下需要手动更新REM基准值（如动态改变窗口大小后），可以调用：

```javascript
window.refreshRem();
``` 