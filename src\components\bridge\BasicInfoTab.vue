<template>
  <div class="basic-info-tab">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="120px"
      :disabled="readonly"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="桥梁名称" prop="bridgeName" required>
            <el-input v-model="formData.bridgeName" placeholder="请输入桥梁名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="桥梁编码" prop="bridgeCode" required>
            <el-input v-model="formData.bridgeCode" placeholder="请输入桥梁编码" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="所属区域" prop="town" required>
            <el-cascader 
              v-model="formData.town" 
              :options="areaOptions" 
              :props="{
                value: 'code',
                label: 'name',
                children: 'children'
              }" 
              placeholder="请选择所属区域" 
              style="width: 100%" 
              @change="handleAreaChange" 
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="所属道路" prop="roadName">
            <el-input v-model="formData.roadName" placeholder="请输入所属道路" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="起始位置" prop="startLocation" required>
            <el-input v-model="formData.startLocation" placeholder="请输入起始位置" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="终止位置" prop="endLocation" required>
            <el-input v-model="formData.endLocation" placeholder="请输入终止位置" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="结构类型" prop="structureType" required>
            <el-select v-model="formData.structureType" placeholder="请选择" style="width: 100%" @change="handleStructureTypeChange">
              <el-option
                v-for="item in structureTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="技术状况" prop="techStatus" required>
            <el-select v-model="formData.techStatus" placeholder="请选择" style="width: 100%" @change="handleTechStatusChange">
              <el-option
                v-for="item in techStatusOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="养护类型" prop="maintainType" required>
            <el-select v-model="formData.maintainType" placeholder="请选择" style="width: 100%" @change="handleMaintainTypeChange">
              <el-option
                v-for="item in maintainTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="技术状况评定日期" prop="techStatusEvalDate">
            <el-date-picker
              v-model="formData.techStatusEvalDate"
              type="date"
              placeholder="年/月/日"
              style="width: 100%"
              format="YYYY/MM/DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="监管单位" prop="superviseUnitCode" required>
            <el-select v-model="formData.superviseUnitCode" placeholder="请选择" style="width: 100%" @change="handleSuperviseUnitChange">
              <el-option
                v-for="item in superviseUnits"
                :key="item.id"
                :label="item.enterpriseName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="养护单位" prop="manageUnitCode" required>
            <el-select v-model="formData.manageUnitCode" placeholder="请选择" style="width: 100%" @change="handleManageUnitChange">
              <el-option
                v-for="item in maintenanceUnits"
                :key="item.id"
                :label="item.enterpriseName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="养护等级" prop="maintainLevel" required>
            <el-select v-model="formData.maintainLevel" placeholder="请选择" style="width: 100%" @change="handleMaintainLevelChange">
              <el-option
                v-for="item in maintainLevelOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="行业属性" prop="industryType" required>
            <el-select v-model="formData.industryType" placeholder="请选择" style="width: 100%" @change="handleIndustryTypeChange">
              <el-option
                v-for="item in industryTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="开工日期" prop="startDate">
            <el-date-picker
              v-model="formData.startDate"
              type="date"
              placeholder="年/月/日"
              style="width: 100%"
              format="YYYY/MM/DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="竣工日期" prop="endDate" required>
            <el-date-picker
              v-model="formData.endDate"
              type="date"
              placeholder="年/月/日"
              style="width: 100%"
              format="YYYY/MM/DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <el-form-item label="桥梁简介" prop="description">
            <el-input
              v-model="formData.description"
              type="textarea"
              :rows="4"
              placeholder="请输入桥梁简介"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <el-form-item label="桥梁图片" prop="imageUrl">
            <el-upload
              class="upload-demo"
              :auto-upload="false"
              :on-change="handleFileChange"
              :file-list="fileList"
              list-type="picture-card"
              :limit="9"
              :disabled="readonly"
              multiple
            >
              <el-icon><Plus /></el-icon>
              <template #tip>
                <div class="el-upload__tip">
                  大小20M以内，建议尺寸16：9
                </div>
              </template>
            </el-upload>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive, watch, onMounted } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { getMaintenanceEnterpriseList } from '@/api/bridge'
import { uploadFile } from '@/api/upload'
import {
  AREA_OPTIONS,
  STRUCTURE_TYPE_OPTIONS,
  TECHNICAL_CONDITION_TYPE_OPTIONS,
  MAINTENANCE_CATEGORY_TYPE_OPTIONS,
  MAINTENANCE_LEVEL_TYPE_OPTIONS,
  INDUSTRY_ATTRIBUTE_TYPE_OPTIONS
} from '@/constants/bridge'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  },
  readonly: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'validate'])

const formRef = ref()
const fileList = ref([])

// 表单数据
const formData = reactive({
  bridgeName: '',
  bridgeCode: '',
  city: '',
  county: '',
  town: '',
  townName: '',
  roadName: '',
  startLocation: '',
  endLocation: '',
  structureType: '',
  structureTypeName: '',
  techStatus: '',
  techStatusName: '',
  maintainType: '',
  maintainTypeName: '',
  techStatusEvalDate: '',
  superviseUnitCode: '',
  superviseUnitName: '',
  manageUnitCode: '',
  manageUnitName: '',
  maintainLevel: '',
  maintainLevelName: '',
  industryType: '',
  industryTypeName: '',
  startDate: '',
  endDate: '',
  description: '',
  imageUrl: ''
})

// 下拉选项数据
const areaOptions = ref(AREA_OPTIONS)
const structureTypeOptions = ref(STRUCTURE_TYPE_OPTIONS)
const techStatusOptions = ref(TECHNICAL_CONDITION_TYPE_OPTIONS)
const maintainTypeOptions = ref(MAINTENANCE_CATEGORY_TYPE_OPTIONS)
const maintainLevelOptions = ref(MAINTENANCE_LEVEL_TYPE_OPTIONS)
const industryTypeOptions = ref(INDUSTRY_ATTRIBUTE_TYPE_OPTIONS)
const maintenanceUnits = ref([])
const superviseUnits = ref([])

// 表单验证规则
const rules = {
  bridgeName: [
    { required: true, message: '请输入桥梁名称', trigger: 'blur' }
  ],
  bridgeCode: [
    { required: true, message: '请输入桥梁编码', trigger: 'blur' }
  ],
  town: [
    { required: true, message: '请选择所属区域', trigger: 'change' }
  ],
  startLocation: [
    { required: true, message: '请输入起始位置', trigger: 'blur' }
  ],
  endLocation: [
    { required: true, message: '请输入终止位置', trigger: 'blur' }
  ],
  structureType: [
    { required: true, message: '请选择结构类型', trigger: 'change' }
  ],
  techStatus: [
    { required: true, message: '请选择技术状况', trigger: 'change' }
  ],
  maintainType: [
    { required: true, message: '请选择养护类型', trigger: 'change' }
  ],
  superviseUnitCode: [
    { required: true, message: '请选择监管单位', trigger: 'change' }
  ],
  manageUnitCode: [
    { required: true, message: '请选择养护单位', trigger: 'change' }
  ],
  maintainLevel: [
    { required: true, message: '请选择养护等级', trigger: 'change' }
  ],
  industryType: [
    { required: true, message: '请选择行业属性', trigger: 'change' }
  ],
  endDate: [
    { required: true, message: '请选择竣工日期', trigger: 'change' }
  ]
}
// 处理结构类型选择变化
const handleStructureTypeChange = (value) => {
  formData.structureTypeName = structureTypeOptions.value.find(item => item.value === value)?.label || ''
}
// 处理技术状况选择变化
const handleTechStatusChange = (value) => {
  formData.techStatusName = techStatusOptions.value.find(item => item.value === value)?.label || ''
}
// 处理养护类型选择变化
const handleMaintainTypeChange = (value) => {
  formData.maintainTypeName = maintainTypeOptions.value.find(item => item.value === value)?.label || '' 
}
// 处理养护等级选择变化
const handleMaintainLevelChange = (value) => {
  formData.maintainLevelName = maintainLevelOptions.value.find(item => item.value === value)?.label || ''
}
// 处理行业属性选择变化
const handleIndustryTypeChange = (value) => {
  formData.industryTypeName = industryTypeOptions.value.find(item => item.value === value)?.label || ''
}
// 处理监管单位选择变化
const handleSuperviseUnitChange = (value) => {
  console.log(value,superviseUnits.value)
  console.log(value,superviseUnits.value.find(item => item.id === value)?.enterpriseName)
  formData.superviseUnitName = superviseUnits.value.find(item => item.id === value)?.enterpriseName || ''
}
// 处理养护单位选择变化
const handleManageUnitChange = (value) => {
  console.log(value,maintenanceUnits.value.find(item => item.id === value)?.enterpriseName,maintenanceUnits.value)
  formData.manageUnitName =  maintenanceUnits.value.find(item => item.id === value)?.enterpriseName || ''
}

// 处理区域选择变化
const handleAreaChange = (value) => {
  if (value && value.length > 0) {
    formData.town = value[value.length - 1]
    // 更新区域名称
    const selectedArea = findAreaByCode(areaOptions.value, formData.town)
    if (selectedArea) {
      formData.townName = selectedArea.name
    }
  }
}

// 根据区域代码查找区域信息
const findAreaByCode = (areas, code) => {
  for (const area of areas) {
    if (area.code === code) {
      return area
    }
    if (area.children) {
      const found = findAreaByCode(area.children, code)
      if (found) return found
    }
  }
  return null
}

// 监听props变化
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    Object.assign(formData, newVal)
    // 处理图片显示
    if (newVal.imageUrl) {
      fileList.value = newVal.imageUrl.split(',').map((url, index) => ({
        name: `image_${index}`,
        url: url,
        uid: Date.now() + index
      }))
    }
  }
}, { immediate: true, deep: true })

// 监听表单数据变化
watch(formData, (newVal) => {
  emit('update:modelValue', { ...newVal })
}, { deep: true })

// 文件选择变化处理
const handleFileChange = async (file, fileList) => {
  // 检查文件大小
  const isLt20M = file.size / 1024 / 1024 < 20
  if (!isLt20M) {
    ElMessage.error('上传图片大小不能超过 20MB!')
    return
  }

  // 检查文件类型
  const isImage = file.raw.type.startsWith('image/')
  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return
  }

  try {
    // 上传文件
    const response = await uploadFile(file.raw)
    if (response.status === 200) {
      const urls = formData.imageUrl ? formData.imageUrl.split(',') : []
      urls.push(response.data.url)
      formData.imageUrl = urls.join(',')
      ElMessage.success('上传成功')
    } else {
      ElMessage.error('上传失败')
    }
  } catch (error) {
    console.error('上传失败:', error)
    ElMessage.error('上传失败')
  }
}

// 获取养护单位列表
const loadMaintenanceUnits = async () => {
  try {
    const response = await getMaintenanceEnterpriseList({ 'enterpriseType': '5001002' })
    if (response.code === 200) {
      maintenanceUnits.value = response.data || []
    }
  } catch (error) {
    console.error('获取养护单位列表失败:', error)
  }
}
// 获取监管单位列表
const loadSuperviseUnits = async () => {
  try {
    const response = await getMaintenanceEnterpriseList({ 'enterpriseType': '5001001' })
    if (response.code === 200) {
      superviseUnits.value = response.data || []
    }
  } catch (error) {
    console.error('获取监测单位列表失败:', error)
  }
}

// 表单验证方法
const validate = () => {
  return formRef.value?.validate()
}

// 重置表单
const resetForm = () => {
  formRef.value?.resetFields()
  fileList.value = []
}

onMounted(() => {
  loadMaintenanceUnits()
  loadSuperviseUnits()
})

// 暴露方法给父组件
defineExpose({
  validate,
  resetForm
})
</script>

<style scoped>
.basic-info-tab {
  padding: 20px;
}

.upload-demo .el-upload__tip {
  margin-top: 7px;
  color: #999;
  font-size: 12px;
}
</style> 