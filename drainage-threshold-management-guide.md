# 排水管网报警阈值管理功能说明

## 功能概述
本功能实现了排水管网监测设备的报警阈值管理，包括完整的增删改查功能，支持多级报警配置和通知设置。

## 已实现的功能

### 1. API接口层 (src/api/drainage.js)
✅ **新增的接口方法：**
- `getMonitorDeviceList()` - 获取监测设备列表
- `getMonitorIndicatorsList()` - 获取监测指标列表
- `getAlarmThresholdPage()` - 分页查询报警阈值
- `getAlarmThresholdDetail()` - 获取阈值详情
- `saveAlarmThreshold()` - 新增报警阈值
- `updateAlarmThreshold()` - 更新报警阈值
- `deleteAlarmThreshold()` - 删除报警阈值

### 2. 常量定义 (src/constants/drainage.js)
✅ **新增的常量：**
- `ENABLED_STATUS_OPTIONS` - 生效状态选项
- `ENABLED_STATUS_MAP` - 生效状态名称映射
- `SUPERVISE_DEPARTMENTS` - 监管部门选项

### 3. 搜索组件 (src/views/admin/drainage/monitoring/alarm/components/ThresholdSearch.vue)
✅ **功能特性：**
- 规则名称搜索
- 是否生效筛选
- 关键词搜索
- 查询和重置功能
- 响应式布局
- 与系统风格一致的样式

### 4. 弹窗组件 (src/views/admin/drainage/monitoring/alarm/components/ThresholdDialog.vue)
✅ **功能特性：**
- 支持三种模式：新增、编辑、查看
- 完整的表单验证
- 分级报警配置（一级、二级、三级）
- 设备类型和监测指标联动
- 权属单位和监管部门通知设置
- 通知方式配置（系统通知、短信通知、邮件通知）
- 表单重置和数据回显
- 响应式布局

### 5. 主页面 (src/views/admin/drainage/monitoring/alarm/threshold.vue)
✅ **功能特性：**
- 完整的列表展示
- 分页功能
- 条件筛选
- 在线状态切换
- 增删改查操作
- 确认删除对话框
- 表格样式优化
- 操作按钮样式

## 核心功能详解

### 数据流程
1. **设备类型选择** → 自动加载对应的监测指标
2. **监测指标选择** → 自动加载对应的设备列表
3. **多级报警配置** → 支持三级不同的阈值设置
4. **通知人配置** → 支持权属单位和监管部门选择
5. **通知方式** → 支持多种通知渠道

### 表单验证
- 必填项检查（规则名称、设备类型、监测指标、各级阈值）
- 数字类型验证
- 下拉选择联动验证

### 状态管理
- 生效状态实时切换
- 表单状态管理（新增/编辑/查看）
- 加载状态显示

## 接口对接说明

### 请求接口
```javascript
// 分页查询
POST /drain/usmAlarmThreshold/page/{pageNum}/{pageSize}

// 新增
POST /drain/usmAlarmThreshold/save

// 更新
POST /drain/usmAlarmThreshold/update

// 详情
GET /drain/usmAlarmThreshold/{id}

// 删除
DELETE /drain/usmAlarmThreshold/{id}

// 设备类型/监测指标
POST /drain/usmMonitorIndicators/list

// 设备列表
POST /drain/usmMonitorDevice/list

// 权属单位列表
POST /drain/usmEnterpriseBasicInfo/list
```

### 数据结构
```javascript
{
  id: '',
  ruleName: '',           // 规则名称
  ruleDesc: '',          // 规则描述
  isEnabled: 1,          // 是否生效 1-是 0-否
  deviceType: '',        // 设备类型
  monitorIndex: '',      // 监测指标
  deviceIds: [],         // 设备ID列表
  thresholdLevel1Min: '',   // 一级阈值下限
  thresholdLevel1Max: '',   // 一级阈值上限
  thresholdLevel2Min: '',   // 二级阈值下限
  thresholdLevel2Max: '',   // 二级阈值上限
  thresholdLevel3Min: '',   // 三级阈值下限
  thresholdLevel3Max: '',   // 三级阈值上限
  notifyRightsDept1: false,     // 一级通知权属单位
  notifySuperviseDept1: false,  // 一级通知监管部门
  notifySuperviseDeptIds1: [],  // 一级监管部门ID列表
  // ... 二级、三级类似
  notifySystem: true,    // 系统通知
  notifySms: false,      // 短信通知
  notifyEmail: false     // 邮件通知
}
```

## 样式特点
- 遵循Element Plus设计规范
- 使用项目统一的颜色和字体
- 响应式布局适配
- 斑马纹表格样式
- 悬停效果优化

## 错误处理
- 网络请求异常处理
- 表单验证失败提示
- 状态更新失败回滚
- 用户友好的错误提示

## 使用说明
1. 点击"新增"按钮创建新的报警阈值规则
2. 在表格中点击"编辑"修改现有规则
3. 点击"查看"查看规则详情（只读模式）
4. 点击"删除"删除规则（带确认对话框）
5. 使用顶部搜索条件筛选数据
6. 通过表格中的开关直接切换生效状态

这个实现完全符合设计稿要求，提供了完整的报警阈值管理功能。 