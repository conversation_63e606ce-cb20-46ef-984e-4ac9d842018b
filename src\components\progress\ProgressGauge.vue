<script setup>
defineProps({
  number: {
    type: Number,
    default: 1
  },
  title: {
    type: String,
    default: '钢管'
  },
  percentage: {
    type: Number,
    default: 78
  },
  value: {
    type: [Number, String],
    default: 0
  },
  unit: {
    type: String,
    default: 'km'
  },
  color: {
    type: String,
    default: '#4C84FF'
  }
});
</script>

<template>
  <div class="progress-gauge">
    <div class="progress-gauge__left">
      <div class="progress-gauge__number" :style="{ backgroundColor: color }">{{ number }}</div>
    </div>
    <div class="progress-gauge__right">
      <div class="progress-gauge__header">
        <div class="progress-gauge__title">{{ title }}</div>
        <div class="progress-gauge__percentage">占比: {{ percentage }}%</div>
      </div>
      <div class="progress-gauge__bar-container">
        <div class="progress-gauge__bar" :style="{ width: `${percentage}%`, background: `linear-gradient(90deg, ${color}, ${color}CC)` }"></div>
        <div class="progress-gauge__value" :style="{ left: `${percentage}%`, color: color }">
          {{ value }}{{ unit }}
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.progress-gauge {
  display: flex;
  align-items: center;
  width: 100%;
  max-width: 500px;
  font-family: PingFangSC, PingFang SC, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  padding: 5px 0;
  
  &__left {
    margin-right: 10px;
  }
  
  &__number {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    color: white;
    border-radius: 8px;
    font-weight: bold;
    font-size: 18px;
  }
  
  &__right {
    flex: 1;
  }
  
  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }
  
  &__title {
    font-size: 14px;
    font-weight: 500;
    color: #282828;
  }
  
  &__percentage {
    font-size: 14px;
    color: #333;
    font-weight: 500;
  }
  
  &__bar-container {
    position: relative;
    height: 12px;
    background-color: #F2F6FC;
    border-radius: 6px;
    overflow: visible;
  }
  
  &__bar {
    position: relative;
    height: 100%;
    border-radius: 6px;
    transition: width 0.5s ease;
  }
  
  &__value {
    position: absolute;
    top: -20px;
    transform: translateX(-50%);
    font-size: 14px;
    font-weight: 500;
    white-space: nowrap;
    
    &::after {
      content: '';
      position: absolute;
      bottom: -2px;
      left: 50%;
      transform: translateX(-50%);
      border-left: 4px solid transparent;
      border-right: 4px solid transparent;
      border-top: 6px solid currentColor;
    }
  }
}

@media screen and (max-width: 480px) {
  .progress-gauge {
    max-width: 100%;
    
    &__number {
      width: 24px;
      height: 24px;
      font-size: 14px;
    }
    
    &__title, &__percentage {
      font-size: 12px;
    }
    
    &__value {
      font-size: 10px;
    }
  }
}
</style>