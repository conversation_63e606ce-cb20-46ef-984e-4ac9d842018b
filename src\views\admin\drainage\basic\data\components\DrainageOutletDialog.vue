<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="1000px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="drainage-outlet-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      :disabled="mode === 'view'"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="排口名称" prop="outletName">
            <el-input v-model="formData.outletName" placeholder="请输入排口名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="排口类型" prop="outletType">
            <el-select v-model="formData.outletType" placeholder="请选择" class="w-full">
              <el-option v-for="item in outletTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="地表高程 (m)" prop="floorDistance">
            <el-input-number v-model="formData.floorDistance" :min="0" :precision="2" class="w-full" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="顶部高程 (m)" prop="topDistance">
            <el-input-number v-model="formData.topDistance" :min="0" :precision="2" class="w-full" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="排放尺寸 (mm)" prop="outletSize">
            <el-input v-model="formData.outletSize" placeholder="请输入排放尺寸" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="出流形式" prop="flowType">
            <el-input v-model="formData.flowType" placeholder="请输入出流形式" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="是否有闸门" prop="isFlapGate">
            <el-select v-model="formData.isFlapGate" placeholder="请选择" class="w-full">
              <el-option :key="0" label="否" :value="0" />
              <el-option :key="1" label="是" :value="1" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="经纬度">
            <div class="flex items-center">
              <el-input v-model="formData.longitude" placeholder="经度" class="mr-2 w-32" />
              <el-input v-model="formData.latitude" placeholder="纬度" class="w-32" />
              <el-button type="primary" class="ml-2" icon="Location" circle @click="openMapPicker"></el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="所属区域">
            <div class="flex items-center">
              <el-cascader
                v-model="formData.town"
                :options="areaOptions"
                :props="{
                  value: 'code',
                  label: 'name',
                  children: 'children'
                }"
                placeholder="请选择所属区域"
                class="w-full"
                @change="handleAreaChange"
              />
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="备注">
            <el-input
              v-model="formData.remarks"
              type="textarea"
              :rows="4"
              placeholder="请输入备注"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="mode !== 'view'">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted, onUnmounted, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import { saveOutlet, updateOutlet } from '@/api/drainage';
import { OUTLET_TYPE_OPTIONS } from '@/constants/drainage';
import { AREA_OPTIONS } from '@/constants/gas';
import { collectShow } from "@/hooks/gishooks";
import bus from '@/utils/mitt';

// 过滤掉选项中的"全部"选项
const outletTypeOptions = OUTLET_TYPE_OPTIONS.filter(item => item.value !== '');

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add', // 'add', 'edit', 'view'
    validator: (value) => ['add', 'edit', 'view'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'success']);

// 表单引用
const formRef = ref(null);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 对话框标题
const dialogTitle = computed(() => {
  const titles = {
    add: '新增排水口',
    edit: '编辑排水口',
    view: '排水口详情'
  };
  return titles[props.mode] || '排水口信息';
});

// 行政区划选项
const areaOptions = ref(AREA_OPTIONS);

// 表单数据
const formData = reactive({
  id: '',
  outletName: '',
  outletType: '',
  outletTypeName: '',
  floorDistance: 0, // 地表高程
  topDistance: 0, // 顶部高程
  outletSize: '',
  flowType: '', // 出流形式
  isFlapGate: 0, // 是否有闸门 0-否，1-是
  longitude: '', // 经度
  latitude: '', // 纬度
  address: '',
  city: '',
  county: '371728',
  countyName: '东明县',
  town: '',
  townName: '',
  remarks: ''
});

// 表单验证规则
const formRules = {
  outletName: [{ required: true, message: '请输入排口名称', trigger: 'blur' }],
  outletType: [{ required: true, message: '请选择排口类型', trigger: 'change' }],
  floorDistance: [{ required: true, message: '请输入地表高程', trigger: 'blur' }],
  topDistance: [{ required: true, message: '请输入顶部高程', trigger: 'blur' }],
  outletSize: [{ required: true, message: '请输入排放尺寸', trigger: 'blur' }],
  flowType: [{ required: true, message: '请输入出流形式', trigger: 'blur' }],
  isFlapGate: [{ required: true, message: '请选择是否有闸门', trigger: 'change' }]
};

// 重置表单
const resetForm = () => {
  // 重置表单数据
  Object.keys(formData).forEach(key => {
    if (key === 'floorDistance' || key === 'topDistance') {
      formData[key] = 0;
    } else if (key === 'isFlapGate') {
      formData[key] = 0;
    } else {
      formData[key] = '';
    }
  });
  
  // 保留默认值
  formData.county = '371728';
  formData.countyName = '东明县';
};

// 更新各字段的名称，基于选中的值
const updateNamesByValues = () => {
  // 排口类型
  const selectedOutletType = outletTypeOptions.find(item => item.value === formData.outletType);
  if (selectedOutletType) {
    formData.outletTypeName = selectedOutletType.label;
  }
};

// 监听props.data变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    // 复制数据到表单
    Object.keys(formData).forEach(key => {
      if (newVal[key] !== undefined) {
        formData[key] = newVal[key];
      }
    });
    
    // 确保选项值和名称一致，处理回显问题
    if (newVal.outletType) {
      const selectedOutletType = outletTypeOptions.find(item => item.value === newVal.outletType);
      if (selectedOutletType) {
        formData.outletType = selectedOutletType.value;
        formData.outletTypeName = selectedOutletType.label;
      }
    }
  } else if (props.mode === 'add') {
    // 新增模式清空表单，保留默认值
    resetForm();
  }
}, { immediate: true, deep: true });

// 监听下拉框值变化，自动更新对应的名称
watch(() => formData.outletType, (val) => {
  if (val) {
    const selected = outletTypeOptions.find(item => item.value === val);
    if (selected) {
      formData.outletTypeName = selected.label;
    }
  }
});

// 处理区域选择变化
const handleAreaChange = (value) => {
  if (value && value.length > 0) {
    formData.town = value[value.length - 1];
    // 更新区域名称
    const selectedArea = findAreaByCode(areaOptions.value, formData.town);
    if (selectedArea) {
      formData.townName = selectedArea.name;
    }
  }
};

// 根据区域代码查找区域信息
const findAreaByCode = (areas, code) => {
  for (const area of areas) {
    if (area.code === code) {
      return area;
    }
    if (area.children) {
      const found = findAreaByCode(area.children, code);
      if (found) return found;
    }
  }
  return null;
};

// 地图选点事件处理函数
const handleCollectLocation = (params) => {
  nextTick(() => {
    formData.longitude = params.longitude;
    formData.latitude = params.latitude;
  });
};

// 打开地图选点
const openMapPicker = () => {
  collectShow.value = true; // 激活采集点位窗口
  // 先移除可能存在的旧监听器
  bus.off("getCollectLocation", handleCollectLocation);
  // 添加新的监听器
  bus.on("getCollectLocation", handleCollectLocation);
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  // 重置表单
  if (formRef.value) {
    formRef.value.resetFields();
  }
  resetForm();
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;
  
  try {
    await formRef.value.validate();
    
    // 准备提交数据
    const submitData = { ...formData };
    
    // 设置枚举值对应的名称
    submitData.outletTypeName = outletTypeOptions.find(item => item.value === formData.outletType)?.label || '';
    
    // 提交数据
    let res;
    if (props.mode === 'add') {
      res = await saveOutlet(submitData);
    } else if (props.mode === 'edit') {
      res = await updateOutlet(submitData);
    }
    
    if (res && res.code === 200) {
      ElMessage.success(props.mode === 'add' ? '新增成功' : '更新成功');
      emit('success');
      handleClose();
    } else {
      ElMessage.error(res?.msg || (props.mode === 'add' ? '新增失败' : '更新失败'));
    }
  } catch (error) {
    console.error('表单验证失败', error);
  }
};

// 组件卸载时清理事件监听
onUnmounted(() => {
  bus.off("getCollectLocation", handleCollectLocation);
});

// 组件挂载时初始化
onMounted(() => {
  // 可以在这里添加其他初始化操作
});
</script>

<style scoped>
.drainage-outlet-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-form-item__label::before) {
  content: '*';
  color: #ff4d4f;
  margin-right: 4px;
}

:deep(.el-form-item:not([required]) .el-form-item__label::before) {
  content: '';
  margin-right: 0;
}

:deep(.el-input__wrapper),
:deep(.el-select__input) {
  border-radius: 2px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  background-color: #0277FD;
  border-color: #0277FD;
}

:deep(.el-button--primary:hover) {
  background-color: #1890ff;
  border-color: #1890ff;
}

.w-full {
  width: 100%;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.ml-2 {
  margin-left: 8px;
}

.mr-2 {
  margin-right: 8px;
}

.w-32 {
  width: 140px;
}
</style> 