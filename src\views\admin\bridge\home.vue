<template>
  <div class="bridge-home">
    <!-- 顶部卡片统计 -->
    <div class="top-section">
      <!-- 左侧管网信息 -->
      <div class="network-cards">
        <!-- 桥梁总数 -->
        <div class="network-card" style="background: #F1F8FF;">
          <div class="icon-box">
            <img src="@/assets/images/mis/bridge/zongshu.png" alt="桥梁总数">
          </div>
          <div class="content">
            <div class="title">桥梁总数</div>
            <div class="data">
              <span class="value">456</span>
              <span class="unit">座</span>
            </div>
          </div>
          <div class="chart-img">
            <img src="@/assets/images/mis/gas/rani.png" alt="桥梁总数图表">
          </div>
        </div>

        <!-- 特大桥 -->
        <div class="network-card" style="background: #FFF3F1;">
          <div class="icon-box">
            <img src="@/assets/images/mis/bridge/teda.png" alt="特大桥">
          </div>
          <div class="content">
            <div class="title">特大桥</div>
            <div class="data">
              <span class="value">20</span>
              <span class="unit">座</span>
            </div>
          </div>
          <div class="chart-img">
            <img src="@/assets/images/mis/gas/gaoya.png" alt="特大桥图表">
          </div>
        </div>

        <!-- 大桥 -->
        <div class="network-card" style="background: #FFF8F0;">
          <div class="icon-box">
            <img src="@/assets/images/mis/bridge/daqiao.png" alt="大桥">
          </div>
          <div class="content">
            <div class="title">大桥</div>
            <div class="data">
              <span class="value">20</span>
              <span class="unit">座</span>
            </div>
          </div>
          <div class="chart-img">
            <img src="@/assets/images/mis/gas/zhongya.png" alt="大桥图表">
          </div>
        </div>

        <!-- 中小桥 -->
        <div class="network-card" style="background: #F1F5FF;">
          <div class="icon-box">
            <img src="@/assets/images/mis/bridge/zhongxiao.png" alt="中小桥">
          </div>
          <div class="content">
            <div class="title">中小桥</div>
            <div class="data">
              <span class="value">23</span>
              <span class="unit">座</span>
            </div>
          </div>
          <div class="chart-img">
            <img src="@/assets/images/mis/gas/diya.png" alt="中小桥图表">
          </div>
        </div>
      </div>

      <!-- 右侧报警信息 -->
      <div class="alarm-info">
        <div class="alarm-row">
          <div class="alarm-title">今日报警</div>
          <div class="alarm-value alarm-today">15</div>
        </div>
        <div class="alarm-row">
          <div class="alarm-title">本月报警</div>
          <div class="alarm-value alarm-month">25</div>
        </div>
      </div>
    </div>

    <!-- 第二行：待处理报警和桥梁报警排名 -->
    <div class="second-row">
      <!-- 左侧：待处理报警区域 -->
      <div class="pending-alarm-section">
        <div class="section-header">
          <div class="section-title">待处理报警</div>
        </div>

        <!-- 报警分级统计 -->
        <div class="alarm-levels">
          <div class="level-card level-one">
            <div class="level-name">一级报警</div>
            <div class="level-value">100</div>
          </div>
          <div class="level-card level-two">
            <div class="level-name">二级报警</div>
            <div class="level-value">80</div>
          </div>
          <div class="level-card level-three">
            <div class="level-name">三级报警</div>
            <div class="level-value">80</div>
          </div>
        </div>

        <!-- 报警列表 -->
        <div class="alarm-list">
          <div class="alarm-item">
            <div class="alarm-info-detail">
              <div class="alarm-title">**********出现报警信息 (2m)</div>
              <div class="alarm-location-time">
                <div class="alarm-location">
                  <el-icon>
                    <Location />
                  </el-icon>
                  <span>**********位置</span>
                </div>
                <div class="alarm-time">
                  <el-icon>
                    <Clock />
                  </el-icon>
                  <span>12月5日 18:01</span>
                </div>
              </div>
            </div>
            <div class="alarm-level-tag level-1-tag">一级报警</div>
          </div>

          <div class="alarm-item">
            <div class="alarm-info-detail">
              <div class="alarm-title">**********出现报警信息 (2m)</div>
              <div class="alarm-location-time">
                <div class="alarm-location">
                  <el-icon>
                    <Location />
                  </el-icon>
                  <span>**********位置</span>
                </div>
                <div class="alarm-time">
                  <el-icon>
                    <Clock />
                  </el-icon>
                  <span>12月5日 18:01</span>
                </div>
              </div>
            </div>
            <div class="alarm-level-tag level-2-tag">二级报警</div>
          </div>

          <div class="alarm-item">
            <div class="alarm-info-detail">
              <div class="alarm-title">**********出现报警信息 (2m)</div>
              <div class="alarm-location-time">
                <div class="alarm-location">
                  <el-icon>
                    <Location />
                  </el-icon>
                  <span>**********位置</span>
                </div>
                <div class="alarm-time">
                  <el-icon>
                    <Clock />
                  </el-icon>
                  <span>12月5日 18:01</span>
                </div>
              </div>
            </div>
            <div class="alarm-level-tag level-3-tag">三级报警</div>
          </div>
        </div>
      </div>

      <!-- 右侧：近30日桥梁报警排名 -->
      <div class="bridge-alarm-ranking-section">
        <div class="section-header">
          <div class="section-title">近30日桥梁报警排名</div>
        </div>

        <div class="bridge-alarm-table-container">
          <el-table :data="bridgeAlarmRankingData" style="width: 100%"
            :header-cell-style="{ background: '#EEF5FF', color: '#0E1D33', fontWeight: '600' }"
            :row-class-name="tableRowClassName" highlight-current-row height="300px">
            <el-table-column prop="index" label="排名" width="70" align="center">
              <template #default="scope">
                <div class="rank-tag" :class="'rank-' + scope.row.index">
                  {{ scope.row.index }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="bridgeName" label="桥梁名称" min-width="180"></el-table-column>
            <el-table-column prop="alarmType" label="结构类型" min-width="120"></el-table-column>
            <el-table-column prop="count" label="报警数" width="100" align="center"></el-table-column>
          </el-table>
        </div>
      </div>
    </div>
    <!-- 底部统计和安全评分 -->
    <div class="bottom-section">
      <!-- 左侧统计图表 -->
      <div class="statistics-charts">
        <div class="section-header">
          <div class="section-title">报警统计</div>
          <div class="action">
            <el-radio-group v-model="timeRange" size="small">
              <el-radio-button label="7">近7日</el-radio-button>
              <el-radio-button label="30">近30日</el-radio-button>
            </el-radio-group>
          </div>
        </div>

        <div class="statistics-data">
          <div class="stat-item">
            <div class="stat-value">{{ alarmStatistics.alarmCount }}</div>
            <div class="stat-label">全部报警</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ alarmStatistics.handleCount }}</div>
            <div class="stat-label">已处理</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ alarmStatistics.handleRate }}%</div>
            <div class="stat-label">处理完成率</div>
          </div>
        </div>

        <div ref="trendChartRef" class="trend-chart-container"></div>
      </div>

      <!-- 右侧桥梁安全评分 -->
      <div class="bridge-safety-section">
        <div class="section-header">
          <div class="section-title">桥梁安全评分</div>
        </div>
        <div class="bridge-safety-content">
          <div class="bridge-safety-table-container">
            <el-table :data="bridgeSafetyData" style="width: 100%"
              :header-cell-style="{ background: '#EEF5FF', color: '#0E1D33', fontWeight: '600' }"
              :row-class-name="tableRowClassName" highlight-current-row>
              <el-table-column prop="index" label="排名" width="70" align="center">
                <template #default="scope">
                  <div class="rank-tag" :class="'rank-' + scope.row.index">
                    {{ scope.row.index }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="bridgeName" label="桥梁名称" min-width="180"></el-table-column>
              <el-table-column prop="score" label="安全评分" width="100" align="center">
                <template #default="scope">
                  <span :style="{ color: getSafetyScoreColor(scope.row.score) }">{{ scope.row.score }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="level" label="安全等级" width="100" align="center">
                <template #default="scope">
                  <el-tag :type="getSafetyLevelType(scope.row.level)">{{ scope.row.level }}</el-tag>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive, onBeforeUnmount, computed, watch } from 'vue'
import { Location, Clock } from '@element-plus/icons-vue'
import * as echarts from 'echarts'

// 窗口尺寸状态
const windowSize = ref({
  width: window.innerWidth,
  height: window.innerHeight
})

// 根据窗口大小计算是否应该截断长文本
const shouldTruncate = computed(() => {
  return windowSize.value.width < 1440
})

// 处理窗口大小变化
const handleResize = () => {
  windowSize.value = {
    width: window.innerWidth,
    height: window.innerHeight
  }

  if (trendChart) {
    trendChart.resize()
  }
}

// 桥梁报警排名数据
const bridgeAlarmRankingData = ref([
  { index: 1, bridgeName: '*****桥梁', alarmType: '特大桥', count: 20 },
  { index: 2, bridgeName: '*****桥梁', alarmType: '特大桥', count: 18 },
  { index: 3, bridgeName: '*****桥梁', alarmType: '特大桥', count: 16 },
  { index: 4, bridgeName: '*****桥梁', alarmType: '大桥', count: 15 },
  { index: 5, bridgeName: '*****桥梁', alarmType: '大桥', count: 12 },
  { index: 6, bridgeName: '*****桥梁', alarmType: '大桥', count: 10 },
  { index: 7, bridgeName: '*****桥梁', alarmType: '中桥', count: 8 },
  { index: 8, bridgeName: '*****桥梁', alarmType: '中桥', count: 6 }
])

// 桥梁安全评分数据
const bridgeSafetyData = ref([
  { index: 1, bridgeName: '*****桥梁', score: 98, level: 'A级' },
  { index: 2, bridgeName: '*****桥梁', score: 95, level: 'A级' },
  { index: 3, bridgeName: '*****桥梁', score: 89, level: 'B级' },
  { index: 4, bridgeName: '*****桥梁', score: 80, level: 'B级' },
  { index: 5, bridgeName: '*****桥梁', score: 75, level: 'C级' }
])

// 时间范围选择
const timeRange = ref('7')

// 报警统计数据
const alarmStatistics = ref({
  alarmCount: 156,
  handleCount: 142,
  handleRate: 91,
  alarmTrendStatistics: [
    { date: '12-01', count: 5 },
    { date: '12-02', count: 8 },
    { date: '12-03', count: 3 },
    { date: '12-04', count: 10 },
    { date: '12-05', count: 7 },
    { date: '12-06', count: 12 },
    { date: '12-07', count: 6 }
  ]
})

// 图表引用
const trendChartRef = ref(null)
let trendChart = null

// 表格行样式
const tableRowClassName = ({ rowIndex }) => {
  return rowIndex % 2 === 1 ? 'striped-row' : ''
}

// 获取安全评分颜色
const getSafetyScoreColor = (score) => {
  if (score >= 90) return '#67C23A' // 绿色
  if (score >= 80) return '#E6A23C' // 黄色
  if (score >= 70) return '#F56C6C' // 红色
  return '#F56C6C' // 默认红色
}

// 获取安全等级类型
const getSafetyLevelType = (level) => {
  if (level === 'A级') return 'success'
  if (level === 'B级') return 'warning'
  if (level === 'C级') return 'danger'
  return 'info'
}

// 初始化趋势图表
const initTrendChart = () => {
  if (trendChartRef.value) {
    if (trendChart) {
      trendChart.dispose()
    }

    trendChart = echarts.init(trendChartRef.value)

    // 根据时间范围获取不同的数据
    let dates = [];
    let counts = [];

    if (timeRange.value === '7') {
      dates = ['12-01', '12-02', '12-03', '12-04', '12-05', '12-06', '12-07'];
      counts = alarmStatistics.value.alarmTrendStatistics.map(item => item.count);
    } else {
      // 模拟30天数据
      dates = Array.from({ length: 30 }, (_, i) => `${Math.floor((i + 1) / 10)}-${(i + 1) % 10 || 10}`);
      counts = Array.from({ length: 30 }, () => Math.floor(Math.random() * 20) + 1);
    }

    const option = {
      tooltip: {
        trigger: 'axis'
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: dates,
        axisLine: {
          lineStyle: {
            color: '#E5E5E5'
          }
        },
        axisLabel: {
          color: '#666',
          interval: timeRange.value === '30' ? 4 : 0 // 当显示30天数据时，每5个标签显示一个
        }
      },
      yAxis: {
        type: 'value',
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        splitLine: {
          lineStyle: {
            color: '#E5E5E5',
            type: 'dashed'
          }
        },
        axisLabel: {
          color: '#666',
          formatter: '{value}'
        }
      },
      series: [
        {
          name: '报警数量',
          type: 'line',
          smooth: true,
          data: counts,
          markPoint: {
            data: [
              { type: 'max', name: '最大值' },
              { type: 'min', name: '最小值' }
            ]
          },
          itemStyle: {
            color: '#409EFF'
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: 'rgba(64, 158, 255, 0.8)'
                },
                {
                  offset: 1,
                  color: 'rgba(64, 158, 255, 0.1)'
                }
              ]
            }
          }
        }
      ]
    }

    trendChart.setOption(option)
  }
}

// 监听时间范围变化
watch(timeRange, (newVal) => {
  console.log('时间范围变化:', newVal)
  // 重新初始化图表以更新数据
  initTrendChart()
})

onMounted(() => {
  console.log('桥梁首页组件已挂载')
  window.addEventListener('resize', handleResize)

  // 初始化图表
  setTimeout(() => {
    initTrendChart()
  }, 100)
})

onBeforeUnmount(() => {
  // 移除监听器
  window.removeEventListener('resize', handleResize)

  // 释放图表实例
  if (trendChart) {
    trendChart.dispose()
    trendChart = null
  }
})
</script>

<style scoped>
.bridge-home {
  padding: 1px;
  height: 99%;
  overflow: auto;
}

/* 顶部卡片统计样式 */
.top-section {
  display: flex;
  gap: 16px;
  margin-bottom: 20px;
}

.network-cards {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  width: 1556px;
  height: 140px;
  background: #FFFFFF;
  padding: 15px;
  border-radius: 4px;
}

.network-card {
  display: flex;
  width: 292px;
  height: 110px;
  border-radius: 4px;
  padding: 12px;
  box-sizing: border-box;
}

.icon-box {
  width: 56px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.icon-box img {
  width: 56px;
  height: 56px;
}

.content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.title {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 18px;
  color: #000000;
  margin-bottom: 8px;
}

.data {
  display: flex;
  align-items: baseline;
}

.value {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 24px;
  color: #000000;
}

.unit {
  font-size: 14px;
  color: #909399;
  margin-left: 4px;
}

.chart-img {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.chart-img img {
  max-width: 84px;
  max-height: 64px;
}

/* 为不同的图表设置不同的大小 */
.network-card:nth-child(1) .chart-img img {
  width: 84px;
  height: 64px;
}

.network-card:nth-child(2) .chart-img img,
.network-card:nth-child(4) .chart-img img {
  width: 78px;
  height: 51px;
}

.network-card:nth-child(3) .chart-img img {
  width: 64px;
  height: 64px;
}

/* 右侧报警信息样式 */
.alarm-info {
  width: 300px;
  height: 140px;
  background: linear-gradient(180deg, #FFE9E9 0%, #FFF7F7 100%);
  border-radius: 4px;
  border: 1px solid #EFF0F2;
  display: flex;
  gap: 78px;
  padding: 39px 47px;
}

.alarm-row {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 12px;
}

.alarm-row:last-child {
  margin-bottom: 0;
}

.alarm-title {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 16px;
  color: #0E1D33;
  margin-bottom: 4px;
  white-space: nowrap;
}

.alarm-value {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 32px;
}

.alarm-today {
  color: #FF1414;
}

.alarm-month {
  color: #333333;
}

/* 第二行样式 */
.second-row {
  display: flex;
  gap: 16px;
  margin-bottom: 20px;
}

.pending-alarm-section,
.bridge-alarm-ranking-section {
  background: #FFFFFF;
  border-radius: 4px;
  padding: 16px;
  border: 1px solid #EFF0F2;
  height: 378px;
}

/* 调整左右两侧宽度比例 */
.pending-alarm-section {
  flex: 1;
}

.bridge-alarm-ranking-section {
  flex: 1;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-title {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 18px;
  color: #222222;
}

.alarm-levels {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.level-card {
  width: 278px;
  height: 75px;
  border-radius: 4px;
  border: 1px solid #F3F3F3;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 12px;
  box-sizing: border-box;
}

.level-one {
  background: linear-gradient(315deg, #FFFAFA 0%, #FF6565 100%);
}

.level-two {
  background: linear-gradient(135deg, #FFA149 0%, #FFFDFB 100%);
}

.level-three {
  background: linear-gradient(135deg, #8FBAFF 0%, #F3F8FF 100%);
}

.level-name {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 16px;
  color: #0E1D33;
  margin-bottom: 8px;
}

.level-value {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 24px;
  color: #303133;
}

.alarm-list {
  max-height: 180px;
  overflow-y: auto;
  padding-right: 8px;
}

.alarm-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 16px;
  background-color: #FFFFFF;
  margin-bottom: 12px;
  border-radius: 4px;
  border-left: 4px solid rgba(0, 0, 0, 0.04);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.04);
}

.alarm-info-detail {
  display: flex;
  flex-direction: column;
}

.alarm-title {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 16px;
  color: #0E1D33;
  margin-bottom: 8px;
}

.alarm-location-time {
  display: flex;
  align-items: center;
  gap: 24px;
  margin-top: 8px;
}

.alarm-location,
.alarm-time {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #647688;
  display: flex;
  align-items: center;
}

.alarm-location :deep(svg),
.alarm-time :deep(svg) {
  margin-right: 4px;
  font-size: 16px;
  color: #909399;
}

.alarm-level-tag {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
}

.level-1-tag {
  background-color: #FFEFEF;
  color: #FF1414;
}

.level-2-tag {
  background-color: #FFF6EC;
  color: #FF7C00;
}

.level-3-tag {
  background-color: #EDF5FF;
  color: #2D7EFF;
}

/* 排名标签样式 */
.rank-tag {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: bold;
  color: #FFFFFF;
}

.rank-1 {
  background: linear-gradient(135deg, #FF4D4F 0%, #FF7875 100%);
}

.rank-2 {
  background: linear-gradient(135deg, #FF7A45 0%, #FF9C6E 100%);
}

.rank-3 {
  background: linear-gradient(135deg, #FFC53D 0%, #FFD666 100%);
}

.rank-4,
.rank-5,
.rank-6,
.rank-7,
.rank-8,
.rank-9,
.rank-10 {
  background: linear-gradient(135deg, #4096FF 0%, #69B1FF 100%);
}

/* 底部统计和安全评分样式 */
.bottom-section {
  display: flex;
  gap: 16px;
}

.statistics-charts,
.bridge-safety-section {
  flex: 1;
  height: 378px;
  background: #FFFFFF;
  border: 1px solid #EFF0F2;
  padding: 20px;
  box-sizing: border-box;
  border-radius: 4px;
}

.action {
  display: flex;
  align-items: center;
}

.statistics-data {
  display: flex;
  justify-content: space-around;
  margin-bottom: 16px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-value {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 24px;
  color: #0E1D33;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.trend-chart-container {
  height: 240px;
  width: 100%;
}

.bridge-safety-content {
  height: calc(100% - 40px);
  overflow-y: auto;
}

.bridge-safety-table-container {
  width: 100%;
}

/* 表格样式 */
:deep(.el-table .striped-row) {
  background-color: #F5F7FA;
}

:deep(.el-table th) {
  background-color: #EEF5FF !important;
}
@media (min-height: 900px) and (max-height: 940px) {
  .bridge-home {
    height: 76%;
    overflow: auto;
  }
}
</style>