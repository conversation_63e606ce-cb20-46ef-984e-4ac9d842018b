<template>
  <div class="search-form">
    <el-form ref="searchFormRef" :model="searchForm" :inline="true" label-width="auto">
      <el-form-item label="报警来源">
        <el-select v-model="searchForm.alarmSource" placeholder="请选择报警来源" clearable style="width: 180px">
          <el-option v-for="item in BRIDGE_ALARM_SOURCE_OPTIONS" :key="item.value" :label="item.label"
            :value="item.value" />
        </el-select>
      </el-form-item>

      <el-form-item label="报警级别">
        <el-select v-model="searchForm.alarmLevel" placeholder="请选择报警级别" clearable style="width: 180px">
          <el-option v-for="item in BRIDGE_ALARM_LEVEL_OPTIONS" :key="item.value" :label="item.label"
            :value="item.value" />
        </el-select>
      </el-form-item>

      <el-form-item label="报警类型">
        <el-select v-model="searchForm.alarmType" placeholder="请选择报警类型" clearable style="width: 200px">
          <el-option v-for="item in BRIDGE_ALARM_TYPE_OPTIONS" :key="item.value" :label="item.label"
            :value="item.value" />
        </el-select>
      </el-form-item>

      <el-form-item label="报警时间">
        <el-date-picker v-model="searchForm.timeRange" type="datetimerange" range-separator="至"
          start-placeholder="开始时间" end-placeholder="结束时间" format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss" style="width: 360px" />
      </el-form-item>

      <el-form-item label="报警状态">
        <el-select v-model="searchForm.alarmStatus" placeholder="请选择报警状态" clearable style="width: 180px">
          <el-option v-for="item in BRIDGE_ALARM_STATUS_OPTIONS" :key="item.value" :label="item.label"
            :value="item.value" />
        </el-select>
      </el-form-item>

      <el-form-item label="报警编号">
        <el-input v-model="searchForm.code" placeholder="请输入报警编号" clearable style="width: 180px" />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="handleSearch">
          <el-icon>
            <Search />
          </el-icon>
          查询
        </el-button>
        <el-button @click="handleReset">
          <el-icon>
            <Refresh />
          </el-icon>
          重置
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { ElForm, ElFormItem, ElSelect, ElOption, ElDatePicker, ElInput, ElButton, ElIcon } from 'element-plus';
import { Search, Refresh } from '@element-plus/icons-vue';
import {
  BRIDGE_ALARM_LEVEL_OPTIONS,
  BRIDGE_ALARM_STATUS_OPTIONS,
  BRIDGE_ALARM_SOURCE_OPTIONS,
  BRIDGE_ALARM_TYPE_OPTIONS
} from '@/constants/bridge';

// 定义事件
const emit = defineEmits(['search', 'reset']);

// 表单引用
const searchFormRef = ref();

// 搜索表单数据
const searchForm = reactive({
  alarmSource: '',
  alarmLevel: '',
  alarmType: '',
  timeRange: [],
  alarmStatus: '',
  code: ''
});

// 处理搜索
const handleSearch = () => {
  emit('search', { ...searchForm });
};

// 处理重置
const handleReset = () => {
  searchFormRef.value?.resetFields();
  Object.keys(searchForm).forEach(key => {
    if (key === 'timeRange') {
      searchForm[key] = [];
    } else {
      searchForm[key] = '';
    }
  });
  emit('reset');
};
</script>

<style scoped>
.search-form {
  width: 100%;
}

:deep(.el-form--inline .el-form-item) {
  margin-right: 16px;
  margin-bottom: 16px;
}

:deep(.el-form-item__label) {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #333333;
}

:deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px #CED3DA inset;
}

:deep(.el-select .el-input__wrapper) {
  box-shadow: 0 0 0 1px #CED3DA inset;
}

:deep(.el-button) {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
}

:deep(.el-button--primary) {
  background: #0086FF;
  border-color: #0086FF;
}
</style> 