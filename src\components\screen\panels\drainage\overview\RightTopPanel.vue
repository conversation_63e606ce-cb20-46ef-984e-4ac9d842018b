<template>
  <PanelBox title="隐患整改">
    <div class="panel-content">
      <div class="stats-row">
        <div class="stat-item">
          <span class="stat-dot"><span class="stat-dot-inner"></span></span>
          <span class="stat-label">隐患总数</span>
          <span class="stat-value-red">{{ statsData.totalIssues }}</span>
          <span class="stat-unit">个</span>
        </div>
        <div class="stat-item">
          <span class="stat-dot"><span class="stat-dot-inner"></span></span>
          <span class="stat-label">已整改</span>
          <span class="stat-value-highlight">{{ statsData.fixedIssues }}</span>
          <span class="stat-unit">个</span>
        </div>
        <div class="stat-item">
          <span class="stat-dot"><span class="stat-dot-inner"></span></span>
          <span class="stat-label">整改完成率</span>
          <span class="stat-value-gradient">{{ statsData.fixRate }}</span>
        </div>
      </div>

      <div class="issue-cards">
        <div class="issue-card">
          <div class="issue-left serious">
            <div class="card-title">重大隐患</div>
          </div>
          <div class="issue-middle">
            <div class="progress-container">
              <div class="progress-bar">
                <div class="progress-bg"></div>
                <div class="progress-total serious-total" :style="{ width: getTotalWidth('serious') }"></div>
              </div>
              <div class="progress-bar">
                <div class="progress-bg"></div>
                <div class="progress-fixed serious-fixed" :style="{ width: getFixedWidth('serious') }"></div>
              </div>
            </div>
          </div>
          <div class="issue-right">
            <div class="count-container">
              <div class="count-item">
                <div class="count-label">总数</div>
                <div class="count-value serious-value">{{ statsData.seriousIssues.total }}</div>
              </div>
              <div class="count-item">
                <div class="count-label">已整改</div>
                <div class="count-value fixed-value">{{ statsData.seriousIssues.fixed }}</div>
              </div>
            </div>
          </div>
        </div>

        <div class="issue-card">
          <div class="issue-left major">
            <div class="card-title">较大隐患</div>
          </div>
          <div class="issue-middle">
            <div class="progress-container">
              <div class="progress-bar">
                <div class="progress-bg"></div>
                <div class="progress-total major-total" :style="{ width: getTotalWidth('major') }"></div>
              </div>
              <div class="progress-bar">
                <div class="progress-bg"></div>
                <div class="progress-fixed major-fixed" :style="{ width: getFixedWidth('major') }"></div>
              </div>
            </div>
          </div>
          <div class="issue-right">
            <div class="count-container">
              <div class="count-item">
                <div class="count-label">总数</div>
                <div class="count-value major-value">{{ statsData.majorIssues.total }}</div>
              </div>
              <div class="count-item">
                <div class="count-label">已整改</div>
                <div class="count-value fixed-value">{{ statsData.majorIssues.fixed }}</div>
              </div>
            </div>
          </div>
        </div>

        <div class="issue-card">
          <div class="issue-left normal">
            <div class="card-title">一般隐患</div>
          </div>
          <div class="issue-middle">
            <div class="progress-container">
              <div class="progress-bar">
                <div class="progress-bg"></div>
                <div class="progress-total normal-total" :style="{ width: getTotalWidth('normal') }"></div>
              </div>
              <div class="progress-bar">
                <div class="progress-bg"></div>
                <div class="progress-fixed normal-fixed" :style="{ width: getFixedWidth('normal') }"></div>
              </div>
            </div>
          </div>
          <div class="issue-right">
            <div class="count-container">
              <div class="count-item">
                <div class="count-label">总数</div>
                <div class="count-value normal-value">{{ statsData.normalIssues.total }}</div>
              </div>
              <div class="count-item">
                <div class="count-label">已整改</div>
                <div class="count-value fixed-value">{{ statsData.normalIssues.fixed }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </PanelBox>
</template>

<script setup>
import { reactive, computed } from 'vue'
import PanelBox from '@/components/screen/PanelBox.vue'
// 预留导入API方法的地方
// import { getIssueStatistics } from '@/api/drainage'

// 统计数据
const statsData = reactive({
  totalIssues: 0,
  fixedIssues: 0,
  fixRate: '0%',
  seriousIssues: {
    total: 0,
    fixed: 0,
    totalRatio: 0 // 在总隐患中的占比
  },
  majorIssues: {
    total: 0,
    fixed: 0,
    totalRatio: 0
  },
  normalIssues: {
    total: 0,
    fixed: 0,
    totalRatio: 0
  }
})

// 计算总数进度条宽度（按占比）
const getTotalWidth = (type) => {
  const data = {
    serious: statsData.seriousIssues,
    major: statsData.majorIssues,
    normal: statsData.normalIssues
  }
  
  const item = data[type]
  return `${item.totalRatio * 100}%`
}

// 计算已整改进度条宽度
const getFixedWidth = (type) => {
  const data = {
    serious: statsData.seriousIssues,
    major: statsData.majorIssues,
    normal: statsData.normalIssues
  }
  
  const item = data[type]
  if (!item || item.total === 0) return '0%'
  
  const percentageOfTotal = item.totalRatio
  const percentageFixed = item.fixed / item.total
  
  return `${percentageOfTotal * percentageFixed * 100}%`
}

// 从后端获取数据 - 预留接口方法待后续接入
const fetchData = async () => {
  try {
    // 预留实际API调用
    // const res = await getIssueStatistics()
    // if (res.code === 200 && res.data) {
    //   // 处理统计数据
    //   statsData.totalIssues = res.data.totalIssues || 0
    //   statsData.fixedIssues = res.data.fixedIssues || 0
    //   statsData.fixRate = (typeof res.data.fixRate === 'number' ? res.data.fixRate + '%' : (res.data.fixRate || '0%'))
    //   
    //   // 计算占比
    //   const totalIssues = res.data.totalIssues || 1 // 防止除以0
    //   
    //   // 处理各类隐患数据
    //   if (res.data.seriousIssues) {
    //     statsData.seriousIssues = res.data.seriousIssues
    //     statsData.seriousIssues.totalRatio = statsData.seriousIssues.total / totalIssues
    //   }
    //   
    //   if (res.data.majorIssues) {
    //     statsData.majorIssues = res.data.majorIssues
    //     statsData.majorIssues.totalRatio = statsData.majorIssues.total / totalIssues
    //   }
    //   
    //   if (res.data.normalIssues) {
    //     statsData.normalIssues = res.data.normalIssues
    //     statsData.normalIssues.totalRatio = statsData.normalIssues.total / totalIssues
    //   }
    // }
  } catch (error) {
    console.error('获取隐患整改数据失败:', error)
  }
}

// 初始化数据
fetchData()
</script>

<style scoped>
.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.stats-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.stat-dot {
  width: 9px;
  height: 9px;
  background: rgba(215, 48, 48, 0.4);
  border-radius: 50%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-dot-inner {
  width: 5px;
  height: 5px;
  background: #D73030;
  border-radius: 50%;
  position: absolute;
}

.stat-label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
}

.stat-value-red {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 24px;
  line-height: 26px;
  text-align: left;
  font-style: normal;
  background: linear-gradient(90deg, #FFFFFF 0%, #DB2D05 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-value-highlight {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 24px;
  color: #3CF3FF;
  line-height: 26px;
}

.stat-value-gradient {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 24px;
  line-height: 26px;
  text-align: left;
  font-style: normal;
  background: linear-gradient(90deg, #FFFFFF 0%, #36F281 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-unit {
  color: rgba(255, 255, 255, 0.4);
  font-size: 12px;
}

.issue-cards {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.issue-card {
  display: flex;
  height: 52px;
  width: 100%;
}

.issue-left {
  position: relative;
  width: 93px;
  height: 52px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.issue-left.serious {
  background: rgba(215, 48, 48, 0.3);
}

.issue-left.major {
  background: rgba(255, 204, 53, 0.3);
}

.issue-left.normal {
  background: rgba(36, 179, 255, 0.3);
}

.card-title {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 16px;
  color: #FFFFFF;
}

.issue-middle {
  flex: 1;
  position: relative;
  border-top: 1px dashed rgba(255, 255, 255, 0.2);
  border-bottom: 1px dashed rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.05);
}

.progress-container {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
  padding: 8px 0;
}

.progress-bar {
  position: relative;
  height: 6px;
  width: 265px;
  margin-bottom: 4px;
}

.progress-bg {
  position: absolute;
  width: 100%;
  height: 6px;
  background: rgba(255, 255, 255, 0.1);
  top: 0;
  left: 0;
}

.progress-total, .progress-fixed {
  position: absolute;
  height: 6px;
  max-width: 265px;
  transition: width 0.3s ease;
  top: 0;
  left: 0;
  z-index: 2;
}

.serious-total {
  background: #D73030;
}

.serious-fixed {
  background: #19D18C;
}

.major-total {
  background: #FFCC35;
}

.major-fixed {
  background: #19D18C;
}

.normal-total {
  background: #24B3FF;
}

.normal-fixed {
  background: #19D18C;
}

.issue-right {
  position: relative;
  width: 84px;
  height: 52px;
  background: rgba(138, 171, 255, 0.1);
  display: flex;
  justify-content: center;
}

.count-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%;
  gap: 4px;
}

.count-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 0 8px;
}

.count-label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

.count-value {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 16px;
}

.serious-value {
  color: #D73030;
}

.major-value {
  color: #FFCC35;
}

.normal-value {
  color: #24B3FF;
}

.fixed-value {
  color: #19D18C;
}

/* 940px左右高度的屏幕特别优化 */
@media (min-height: 940px) and (max-height: 1055px) {
  .panel-content {
    padding: 10px;
    gap: 15px;
  }
  
  .stats-row {
    margin-bottom: 5px;
  }
  
  .stat-item {
    gap: 3px;
  }
  
  .issue-cards {
    gap: 8px;
  }
  
  .card-title {
    font-size: 14px;
  }
  
  .count-value {
    font-size: 14px;
  }
}

/* 适配不同屏幕尺寸 */
@media screen and (min-height: 900px) and (max-height: 940px) {
  .stats-row {
    margin-bottom: 3px;
  }

  .panel-content {
    gap: 0px;
  }
}
</style> 