// import * as Cesium from "cesium";
import proj4 from "proj4";
import {
  checkCoordinate,
  cartesian3ToDegree,
} from "@/components/GisMap/common/gisUtil.js";
import { imageInfo } from "@/components/GisMap/images/imageInfo.js";

export const position0 = [115.097, 35.288, 8000];

// 定义4526坐标系，可以在epsg.io上查
proj4.defs("EPSG:4490", "+proj=+proj=longlat +ellps=GRS80 +no_defs");

export const resize = (res) => {
  const width = window.screen.width;
  const ratio = width / 1920;
  return Math.round(res * ratio);
};

//定义参数start
const tiandituToken = "40a623cbe2c72e883a0c0066ef20e8cd"; //<EMAIL> 天地图许可
const customTileColor = "#0E3674"
const lineFlow = false; //流动线材质开关

// 创建自定义着色器

export class Earth {
  //****本构造类可按照项目需求进行配置
  constructor(viewer) {
    //在Earth中创建的entity图层，感觉是异步的。_dataSources 是 array(0)
    this.basemap = new BaseMap(viewer);
    this.camera = new Camera(viewer);
    this.entity = new Entity(viewer);
    this.primitive = new Primitive(viewer);
    this.event = new Event(viewer);
    this.listen = new Listen(viewer);
    this.sceneMode = new SceneMode(viewer);
    this.time = new Time(viewer);
    this.roam = new Roam(viewer);
    this.tiles = new Tiles(viewer);
  }
}

class BaseMap {
  constructor(viewer) {
      this.img = undefined;
      this.vec = undefined;
      this.cia = undefined;
      this.osm = undefined;
      this.google = undefined;
      this.mapbox = undefined;
      this.tdt_vec = undefined;
      this.tdt_cva = undefined;
      this.tdt_img = undefined;
      this.tdt_cva = undefined;
      this.tdt_terrain = undefined;
      this.osgbLayer = undefined;
      this.bridgeLayer = undefined;

    this.add = (id) => {
      if (id === "mapbox") {
        this.mapbox = viewer.scene.imageryLayers.addImageryProvider(
            new Cesium.MapboxStyleImageryProvider({
              url: "https://api.mapbox.com/styles/v1",
              username: "rexlong",
              styleId: "clft4m7ik000n01mucjtabuzb",
              accessToken:
                  "pk.eyJ1IjoicmV4bG9uZyIsImEiOiJjbGZ0MnRubDcwYzNsM2ZvMWhteDhrOHlzIn0.XnPgfzDOq_97EHRCxnH5nQ",
              tilesize: 256,
              scaleFactor: true,
            })
        );
      } else if (id === "vec") {
        this.vec = viewer.scene.imageryLayers.addImageryProvider(
            new Cesium.WebMapTileServiceImageryProvider({
              url: "http://t0.tianditu.gov.cn/vec_w/wmts?tk=" + tiandituToken,
              layer: "vec",
              style: "default",
              tileMatrixSetID: "w",
              format: "tiles",
              maximumLevel: 18,
            })
        );
      } else if (id === "cva") {
        this.cva = viewer.scene.imageryLayers.addImageryProvider(
            new Cesium.WebMapTileServiceImageryProvider({
              url: "http://t1.tianditu.gov.cn/cva_w/wmts?tk=" + tiandituToken,
              layer: "cva",
              style: "default",
              tileMatrixSetID: "w",
              format: "tiles",
              maximumLevel: 18,
            })
        );
      } else if (id === "img") {
        this.img = viewer.scene.imageryLayers.addImageryProvider(
            new Cesium.WebMapTileServiceImageryProvider({
              url: "http://t0.tianditu.gov.cn/img_w/wmts?tk=" + tiandituToken,
              layer: "img",
              style: "default",
              tileMatrixSetID: "w",
              format: "tiles",
              maximumLevel: 18,
            })
        );
      } else if (id === "cia") {
        this.cia = viewer.scene.imageryLayers.addImageryProvider(
            new Cesium.WebMapTileServiceImageryProvider({
              url: "http://t0.tianditu.gov.cn/cia_w/wmts?tk=" + tiandituToken,
              layer: "cia",
              style: "default",
              tileMatrixSetID: "w",
              format: "tiles",
              maximumLevel: 18,
            })
        );
      } else if (id === "google") {
        const geoserverUrl =
            "http://139.196.203.134:9090/geoserver/hefei/gwc/service/wmts?layer=hefei:baohe16&style=&tilematrixset=EPSG:4326&Service=WMTS&Request=GetTile&Version=1.0.0&Format=image%2Fpng&TileMatrix=EPSG:4326:{TileMatrix}&TileCol={TileCol}&TileRow={TileRow}";
        this.google = viewer.scene.imageryLayers.addImageryProvider(
            new Cesium.WebMapTileServiceImageryProvider({
              url: geoserverUrl,
              layer: "hefei:baohe16",
              style: "raster",
              tileMatrixSetID: "EPSG:4326",
              format: "image/png",
              maximumLevel: 16,
              tilingScheme: new Cesium.GeographicTilingScheme(),
            })
        );
      } else if (id === "osm") {
        const geoserverUrl2 =
            "http://139.196.203.134:9090/geoserver/hefeiOsm/gwc/service/wmts?layer=hefeiOsm:hefeiOsm&style=&tilematrixset=WebMercatorQuad&Service=WMTS&Request=GetTile&Version=1.0.0&Format=image%2Fpng&TileMatrix={TileMatrix}&TileCol={TileCol}&TileRow={TileRow}";
        this.osm = viewer.scene.imageryLayers.addImageryProvider(
            new Cesium.WebMapTileServiceImageryProvider({
              url: geoserverUrl2,
              layer: "hefeiOsm:hefeiOsm",
              style: "raster",
              tileMatrixSetID: "WebMercatorQuad",
              format: "image/png",
              maximumLevel: 16,
            })
        );
      } else if (id === "tdt_vec") {
          this.tdt_vec = viewer.imageryLayers.addImageryProvider(
              Cesium.TianDiTuImageryProvider(
                  Cesium.TiandituMapsStyle.TDT_VEC_C,
                  tiandituToken,
                  18
              )
          );
      } else if (id === "tdt_cva") {
          this.tdt_cva = viewer.imageryLayers.addImageryProvider(
              Cesium.TianDiTuImageryProvider(
                  Cesium.TiandituMapsStyle.TDT_CVA_C,
                  tiandituToken,
                  18
              )
          );
      } else if (id === "tdt_img") {
          this.tdt_img = viewer.imageryLayers.addImageryProvider(
              Cesium.TianDiTuImageryProvider(
                  Cesium.TiandituMapsStyle.TDT_IMG_C,
                  tiandituToken,
                  18
              )
          );
      } else if (id === "tdt_cia") {
          this.tdt_cia = viewer.imageryLayers.addImageryProvider(
              Cesium.TianDiTuImageryProvider(
                  Cesium.TiandituMapsStyle.TDT_CIA_C,
                  tiandituToken,
                  18
              )
          );
      } else if (id === "tdt_terrain") {
          const randomTokens = [tiandituToken]
          this.tdt_terrain = viewer.terrainProvider = new Cesium.TiandituTerrainProvider({
              token: tiandituToken,
              customTags: {
                  token: function (terrainProvider, x, y, level) {
                      return randomTokens[(x + y + level) % randomTokens.length];
                  },
              },
          });
      }
    };

    this.addOsgbModel = (url) => {
        //添加倾斜摄影模型
        this.osgbLayer = new Cesium.Layer({
            url: url,
            name:"倾斜摄影",
            // lightColor: new Cesium.Cartesian3(200.0, 200.0, 200.0),
            skipLevelOfDetail: true, //添加此句话可以提高加载倾斜摄影数据的速度
        });
        viewer.scene.layers.add(this.osgbLayer);
    }

    this.addBridgeModel = (url) => {
        this.bridgeLayer = new Cesium.Layer({
            url: url,
            name:"桥梁",
            geomErrorScale: 0.5,
        });
        viewer.scene.layers.add(this.bridgeLayer);
    }

    this.removeOsgbModel = () => {
        viewer.scene.layers.remove(this.osgbLayer);
        this.osgbLayer = undefined;
    }

    this.removeBridgelModel = () => {
        viewer.scene.layers.remove(this.bridgeLayer);
        this.bridgeLayer = undefined;
    }

    this.addPipeLineModel = (id, url) => {
        //添加管道模型
        const pipeLayer = new Cesium.Layer({
            id: id,
            url: url,
            show: true,
        });
        viewer.scene.layers.add(pipeLayer);
        return pipeLayer;
    }

    this.removePipeLineModels = (ids) => {
        if (!ids) return;
        for (let i = 0; i < ids.length; i++) {
            const id = ids[i];
            const pipeLayer = viewer.scene.layers.find(id);
            if (pipeLayer) {
                viewer.scene.layers.remove(pipeLayer);
            }
        }
    }

    this.addSuperMapWMTS = (options) => {
      viewer.scene.imageryLayers.addImageryProvider(
          new Cesium.WebMapTileServiceImageryProvider({
            url: options.url, //'https://iserver.supermap.io/iserver/services/map-china400/wmts-china', //geoserver服务地址，如：'http://localhost:8080/geoserver/gwc/service/wmts'
            layer: options.layer, //图层名称，如：'China'
            style: "default",
            format: "image/png",
            tileMatrixSetID: options.tileMatrixSetID, //'GoogleMapsCompatible_China',
            tilingScheme: new Cesium.WebMercatorTilingScheme(), //墨卡托投影坐标系 Cesium.WebMercatorTilingScheme
            maximumLevel: 19,
          })
      );
    };

    // @ts-ignore
    this.getUrlParam = (url) => {
      // str为？之后的参数部分字符串
      const str = url.substr(url.indexOf("?") + 1);
      // arr每个元素都是完整的参数键值
      const arr = str.split("&");
      // result为存储参数键值的集合
      const result = {};
      for (let i = 0; i < arr.length; i++) {
        // item的两个元素分别为参数名和参数值
        const item = arr[i].split("=");
        result[item[0]] = item[1];
      }
      return result;
    };

    //传参示例：超图发布的wms服务：https://iserver.supermap.io/iserver/services/map-china400/wms111/China?layers=China&srs=EPSG:4326
    //传参示例：geoserver发布的wms服务：http://localhost:8080/geoserver/tiger/wms?layers=tiger:poly_landmarks&srs=EPSG:4326
    this.addWMSLayer = (options) => {
      // 参数处理
      // 参数处理
      const mapUrl = options.url.substr(0, options.url.indexOf("?"));
      const mapParameters = {
        url: mapUrl,
        parameters: {
          transparent: true,
          format: "image/png",
        },
      };
      const params = this.getUrlParam(options.url);
      Object.keys(params).forEach((key) => {
        if (params[key]) {
          mapParameters.parameters[key] = params[key];
        }
      });
      mapParameters.layers = mapParameters.parameters.layers;
      // 初始化图层
      viewer.scene.imageryLayers.addImageryProvider(
          new Cesium.WebMapServiceImageryProvider(mapParameters)
      );
    };

    //传参示例---超图发布的wmts服务：https://iserver.supermap.io/iserver/services/map-china400/wmts-china?layer=China&tileMatrixSetID=ChinaPublicServices_China&style=default
    //传参示例---geoserver发布的wmts服务：http://localhost:8080/geoserver/gwc/service/wmts?layer=sf:restricted&tileMatrixSetID=EPSG:4326&style=default
    this.addWMTSLayer = (options) => {
      // 参数处理
      const mapUrl = options.url.substr(0, options.url.indexOf("?"));
      const mapParameters = null;
      const params = this.getUrlParam(options.url);
      Object.keys(params).forEach((key) => {
        if (params[key]) {
          mapParameters[key] = params[key];
        }
      });
      //切片方案选择：3857使用WebMercatorTilingScheme，地理坐标系使用GeographicTilingScheme
      if (options.id.includes("3857")) {
        mapParameters.tilingScheme = new Cesium.WebMercatorTilingScheme();
      } else if (options.id.includes("4490")) {
        /* mapParameters.tileMatrixLabels = [
          "1",
          "2",
          "3",
          "4",
          "5",
          "6",
          "7",
          "8",
          "9",
          "10",
          "11",
          "12",
          "13",
          "14",
          "15",
          "16",
          "17",
          "18",
          "19",
        ];*/
        mapParameters.tilingScheme = new Cesium.GeographicTilingScheme({
          /*   rectangle: new Cesium.Rectangle(
            Cesium.Math.toRadians(119.45046424900006),
            Cesium.Math.toRadians(32.575532913000075),
            Cesium.Math.toRadians(120.96887779200006),
            Cesium.Math.toRadians(34.46629524200017)
          ),*/
          numberOfLevelZeroTilesX: 2,
          numberOfLevelZeroTilesY: 1,
        });
        /* mapParameters.tilingScheme = new GISTilingScheme();
        mapParameters.maximumLevel = 20;
        mapParameters.rectangle = new Cesium.Rectangle(
          Cesium.Math.toRadians(119.45046424900006),
          Cesium.Math.toRadians(32.575532913000075),
          Cesium.Math.toRadians(120.96887779200006),
          Cesium.Math.toRadians(34.46629524200017)
        );*/
      } else {
        mapParameters.tilingScheme = new Cesium.GeographicTilingScheme();
      }
      // 初始化图层
      viewer.scene.imageryLayers.addImageryProvider(
          new Cesium.WebMapTileServiceImageryProvider(mapParameters)
      );
    };

    this.raise = (map) => {
      viewer.scene.imageryLayers.raiseToTop(map);
    };
  }
}

class Camera {
  constructor(viewer) {
    this.flyTo = ({
               lon,
               lat,
               height = 1000,
               orientation = { heading: 0, pitch: -45, roll: 0 },
               duration = 1,
             })=> {
      // const offsetY = (height * 0.015) / 1800;
      viewer.camera.flyTo({
        // destination: Cesium.Cartesian3.fromDegrees(lon, lat - offsetY, height),
        destination: Cesium.Cartesian3.fromDegrees(lon, lat, height),
        orientation: {
          heading: Cesium.Math.toRadians(orientation.heading),
          pitch: Cesium.Math.toRadians(orientation.pitch),
          roll: Cesium.Math.toRadians(orientation.roll),
        },
        duration: duration,
      });
    };

    this.flyToBoundingSphere = ({
                             position,
                             distanceMode = "current",
                             distanceFixed = 500,
                             distanceScale = 1.7,
                           })=> {
      const camera = viewer.scene.camera;
      const cartesian3 = camera.position;
      const Scene3D = viewer.scene.mode === Cesium.SceneMode.SCENE3D;
      const height = Cesium.Cartographic.fromCartesian(cartesian3).height;
      const height2D = viewer.scene.camera.getMagnitude();
      const distance = Math.abs(height / Math.sin(camera.pitch));
      let rangeDistance, deltX, deltY;

      if (distanceMode === "current") {
        rangeDistance = Scene3D ? distance : height2D;
        if (Scene3D) {
          deltX = Math.sin(camera.heading) * height * 0.000001;
          deltY = Math.cos(camera.heading) * height * 0.000001;
        } else {
          deltX = Math.sin(camera.heading) * height2D * 0.000001;
          deltY = Math.cos(camera.heading) * height2D * 0.000001;
        }
      } else if (distanceMode === "fixed") {
        rangeDistance = distanceFixed;
        deltX = 0;
        deltY = 0;
      } else {
        rangeDistance = (Scene3D ? distance : height) / distanceScale;
        deltX = 0;
        deltY = 0;
      }

      const bs = new Cesium.BoundingSphere(
          Cesium.Cartesian3.fromDegrees(
              position[0] + deltX,
              position[1] + deltY,
              0
          ),
          100
      );

      const offset = new Cesium.HeadingPitchRange(
          Scene3D ? camera.heading : Cesium.Math.toRadians(0),
          Scene3D ? camera.pitch : Cesium.Math.toRadians(-90),
          rangeDistance
      );
      camera.flyToBoundingSphere(bs, { offset, duration: 1 });
    };

    this.setView = ({
                 lon,
                 lat,
                 height,
                 orientation = { heading: 0, pitch: -90, roll: 0 },
               }) => {
      if (checkCoordinate(lon, lat)) {
        viewer.camera.setView({
          destination: Cesium.Cartesian3.fromDegrees(lon, lat, height),
          orientation: {
            heading: Cesium.Math.toRadians(orientation.heading),
            pitch: Cesium.Math.toRadians(orientation.pitch),
            roll: Cesium.Math.toRadians(orientation.roll),
          },
        });
      } else {
        console.log("错误提示:camera.setView经纬度错误");
      }
    };

    this.getCameraInfo = (print = false)=> {
      const cartesian3 = viewer.camera.position;
      const cartographic = Cesium.Cartographic.fromCartesian(cartesian3);
      const lng = Cesium.Math.toDegrees(cartographic.longitude);
      const lat = Cesium.Math.toDegrees(cartographic.latitude);
      const position = [lng, lat];
      const direction = viewer.camera.direction;
      const transform = viewer.camera.transform;
      const cameraFlytoParam = {
        lon: lng,
        lat: lat,
        height: cartographic.height,
        orientation: {
          heading: Cesium.Math.toDegrees(viewer.camera.heading),
          pitch: Cesium.Math.toDegrees(viewer.camera.pitch),
          roll: Cesium.Math.toDegrees(viewer.camera.roll),
        },
        duration: 1,
      };
      if (print) {
        console.log("错误提示:getCameraInfo", cameraFlytoParam);
      }
      return cameraFlytoParam;
    };

    this.getCameraHeight = () => {
      const cartesian3 = viewer.camera.position;
      const cartographic = Cesium.Cartographic.fromCartesian(cartesian3);
      return cartographic.height;
    };

    this.onMapZoomIn = () => {
      const position = viewer.camera.positionCartographic;
      const height = position.height;
      viewer.camera.zoomIn(height / 10);
    };

    this.onMapZoomOut = () => {
      const position = viewer.camera.positionCartographic;
      const height = position.height;
      viewer.camera.zoomOut(height / 10);
    };
  }
}

class Entity {
  constructor(viewer) {
    this.activeEntity = undefined;
    this.measure = undefined;
    this.polylineArray = [];
    this.labelArray = [];
    this.parentArray = [];
    this.poiArray = [];
    this.highlightDataSource = new Cesium.CustomDataSource('highlightLayer');
    viewer.dataSources.add(this.highlightDataSource);
    this.lastHighlightedEntity = undefined;

    // @ts-ignore
    this.addPointGeometryFromDegrees = ({
                                     layerId,
                                     data,
                                     width = 53,
                                     height = 36,
                                     show = true,
                                   }) => {
      const findDataSource = viewer.dataSources.getByName(layerId);
      if (findDataSource.length <= 0) {
        findDataSource[0] = new Cesium.CustomDataSource(layerId);
        viewer.dataSources.add(findDataSource[0]);
      }
      findDataSource[0].show = show;
      data.map((item) => {
        findDataSource[0].entities.add({
          id: item.id,
          name: layerId,
          show: true,
          position: Cesium.Cartesian3.fromDegrees(
              parseFloat(item.longitude),
              parseFloat(item.latitude)
          ),
          billboard: {
            image: imageInfo[item.gisType],
            width: resize(width),
            height: resize(height),
            verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
            scaleByDistance: new Cesium.NearFarScalar(1.5e2, 1.5, 1.5e4, 0.5),
            disableDepthTestDistance: Number.POSITIVE_INFINITY,
            heightReference: Cesium.HeightReference.RELATIVE_TO_GROUND,
            translucencyByDistance: new Cesium.NearFarScalar(
                1.5e3,
                1.0,
                1.5e7,
                0.2
            ),
          },
        });
      });
    };

    this.addCircleGeometryFromDegrees = ({
                                      layerId,
                                      position,
                                      radius = 1000,
                                      colorStr = "#D9001B",
                                      show = true,
                                    }) => {
      const findDataSource = viewer.dataSources.getByName(layerId);
      if (findDataSource.length <= 0) {
        findDataSource[0] = new Cesium.CustomDataSource(layerId);
        viewer.dataSources.add(findDataSource[0]);
      }
      findDataSource[0].show = show;
      findDataSource[0].entities.add({
        name: layerId,
        show: true,
        position: Cesium.Cartesian3.fromDegrees(position[0], position[1]),
        ellipse: {
          semiMinorAxis: radius,
          semiMajorAxis: radius,
          material: Cesium.Color.fromCssColorString(colorStr).withAlpha(0.5),
          outline: true,
          outlineColor: Cesium.Color.fromCssColorString(colorStr).withAlpha(1),
        },
      });
    };

    this.addPolylineGeometryFromDegrees = ({
                                        layerId,
                                        data,
                                        material,
                                        width = 4,
                                        show = true,
                                      }) => {
      const findDataSource = viewer.dataSources.getByName(layerId);
      if (findDataSource.length <= 0) {
        findDataSource[0] = new Cesium.CustomDataSource(layerId);
        viewer.dataSources.add(findDataSource[0]);
      }
      findDataSource[0].show = show;
      data.map((item) => {
/*        findDataSource[0].entities.add({
          id: item.id + "_style",
          name: layerId,
          show: show,
          polyline: {
            stroke: Cesium.Color.fromCssColorString(item.colorStr).withAlpha(1),
            strokeWidth: width,
            fill: Cesium.Color.fromCssColorString(item.colorStr).withAlpha(1),
            positions: Cesium.Cartesian3.fromDegreesArray(item.degrees),
            // @ts-ignore
            material: material,
            distanceDisplayCondition: new Cesium.DistanceDisplayCondition(
                0.0,
                1.5e4
            ),
            clampToGround: true,
          },
        });*/
        findDataSource[0].entities.add({
          id: item.id,
          name: layerId,
          show: true,
          polyline: {
            positions: Cesium.Cartesian3.fromDegreesArray(item.degrees),
            material: Cesium.Color.fromCssColorString(item.colorStr).withAlpha(
                0.6
            ),
            distanceDisplayCondition: new Cesium.DistanceDisplayCondition(
                0.0,
                1.5e4
            ),
            width: width + 1,
          },
        });
      });
    };

    this.toggleLayerVisibleById = (layerId, show = true) => {
        const findDataSource = viewer.dataSources.getByName(layerId);
        if (findDataSource && findDataSource.length > 0) {
          findDataSource.forEach((dataSource) => {
            dataSource.show = show;
          });
            console.log("提示:toggleLayerVisibleById-图层未找到:", layerId);
        } else {
            console.log("提示:toggleLayerVisibleById-图层未找到:", layerId);
        }
    }

    this.clearDataSourcesEntitiesByLayerId = (layerId) => {
      const findDataSource = viewer.dataSources.getByName(layerId);
      if (findDataSource && findDataSource.length > 0) {
        findDataSource.forEach((dataSource) => {
          dataSource.show = false;
          dataSource.entities.removeAll();
        });
      }
    };

    this.clearAllDataSourcesEntities = () => {
      viewer.dataSources.removeAll();
    };

    this.highlightEntity = (entity) => {
      if (this.lastHighlightedEntity) {
        this.lastHighlightedEntity.show = true;
      }

      entity.show = false;

      const highlightEntity = new Cesium.Entity({
        id: entity.id + '_highlight',
        name: entity.name,
        show: true
      });

      if (entity.position) {
        highlightEntity.position = entity.position;
      }

      if (entity.billboard) {
        highlightEntity.billboard = {
          image: entity.billboard.image,
          width: entity.billboard.width * 1.5,
          height: entity.billboard.height * 1.5,
          verticalOrigin: entity.billboard.verticalOrigin,
          scaleByDistance: entity.billboard.scaleByDistance,
          disableDepthTestDistance: entity.billboard.disableDepthTestDistance,
          heightReference: entity.billboard.heightReference,
          color: Cesium.Color.AQUA.withAlpha(0.8)
        };
      }

      if (entity.polyline) {
        highlightEntity.polyline = {
          positions: entity.polyline.positions,
          material: Cesium.Color.AQUA.withAlpha(0.8),
          width: entity.polyline.width * 1.5,
          distanceDisplayCondition: entity.polyline.distanceDisplayCondition,
          clampToGround: entity.polyline.clampToGround
        };
      }

      if (entity.polygon) {
        highlightEntity.polygon = {
          hierarchy: entity.polygon.hierarchy,
          material: Cesium.Color.AQUA.withAlpha(0.3),
          outline: true,
          outlineColor: Cesium.Color.AQUA.withAlpha(0.8),
          outlineWidth: entity.polygon.outlineWidth ? entity.polygon.outlineWidth * 1.5 : 2,
          heightReference: entity.polygon.heightReference,
          extrudedHeight: entity.polygon.extrudedHeight,
          perPositionHeight: entity.polygon.perPositionHeight
        };
      }

      this.highlightDataSource.entities.removeAll();
      this.highlightDataSource.entities.add(highlightEntity);

      this.lastHighlightedEntity = entity;
    };

    this.clearHighlight = () => {
      if (this.lastHighlightedEntity) {
        this.lastHighlightedEntity.show = true;
        this.lastHighlightedEntity = undefined;
      }

      this.highlightDataSource.entities.removeAll();
    };

    this.addBillboard = ({
                      position,
                      type,
                      image,
                      id,
                      width = 53,
                      height = 36,
                      show = true,
                      parentShow = true,
                      status = "0",
                      labelText = "",
                      propertiesType = "",
                      risk = "",
                      hiddenDanger = "",
                    }) => {
      const datasource = viewer.dataSources.getByName("entity")[0];
      const parent = datasource.entities.getOrCreateEntity(type);
      parent.show = parentShow;
      if (!this.parentArray.includes(type)) {
        this.parentArray.push(type);
      }
      datasource.entities.add({
        name: type,
        parent,
        id,
        show,
        position: Cesium.Cartesian3.fromDegrees(position[0], position[1]),
        billboard: {
          image,
          width,
          height,
          verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          scaleByDistance: new Cesium.NearFarScalar(1.0e2, 1.5, 1.5e3, 1),
          disableDepthTestDistance: Number.POSITIVE_INFINITY,
          heightReference: Cesium.HeightReference.RELATIVE_TO_GROUND,
          color:
              status === "1"
                  ? new Cesium.CallbackProperty((time) => {
                    const alpha =
                        time.secondsOfDay % 1 < 0.5
                            ? 1.5 * (time.secondsOfDay % 1) + 0.25
                            : -1.5 * (time.secondsOfDay % 1) + 1.75;

                    return Cesium.Color.WHITE.withAlpha(alpha);
                  }, false)
                  : Cesium.Color.WHITE,
        },
        label: {
          text: labelText,
          font: `bold ${height / 2.5}px MicroSoft YaHei`,
          disableDepthTestDistance: Number.POSITIVE_INFINITY,
          eyeOffset: new Cesium.Cartesian3(0, 0, -1),
          fillColor: Cesium.Color.WHITE,
          showBackground: true,
          backgroundColor: Cesium.Color.DODGERBLUE,
          backgroundPadding: new Cesium.Cartesian2(1, 1),
          distanceDisplayCondition: new Cesium.DistanceDisplayCondition(0, 6000),
        },
        properties: {
          type: propertiesType,
          status,
          risk,
          hiddenDanger,
          active: false,
        },
      });
    };

    this.removeEntitiesByType = (type) => {
      const index = this.parentArray.indexOf(type);
      if (index > -1) {
        this.parentArray.splice(index, 1);
      }

      const dataSources = viewer.dataSources.getByName(type);
      if (dataSources.length !== 0) {
        const datasource = dataSources[0];
        const parent = datasource.entities.getById(type);
        if (parent !== undefined) {
          const entities = datasource.entities.values;
          for (let i = 0; i < entities.length; i++) {
            const entity = entities[i];
            if (entity.parent === parent) {
              datasource.entities.removeById(entity.id);
              i--;
            }
          }
          datasource.entities.remove(parent);
        }
      }
    };

    this.addClusterBillboard = ({
                             position,
                             type,
                             image,
                             id,
                             width = 53,
                             height = 36,
                             show = true,
                             status = "normal",
                             labelText = "",
                             propertiesType = "",
                             alarm = "",
                             risk = "",
                             hiddenDanger = "",
                           }) => {
      const datasource = viewer.dataSources.getByName(type)[0];
      const parent = datasource.entities.getOrCreateEntity(type);
      if (!this.parentArray.includes(type)) {
        this.parentArray.push(type);
      }

      datasource.clustering.enabled = true;
      datasource.clustering.pixelRange = 10;
      datasource.clustering.minimumClusterSize = 2;
      datasource.clustering.clusterEvent.addEventListener(
          (clusteredEntities, cluster) => {
            cluster.billboard.show = true;
            cluster.billboard.id = cluster.label.id;
            cluster.billboard.verticalOrigin = Cesium.VerticalOrigin.BOTTOM;
            cluster.billboard.image = image;
            cluster.billboard.width = width;
            cluster.billboard.height = height;
            cluster.billboard.disableDepthTestDistance = Number.POSITIVE_INFINITY;

            cluster.label.show = true;
            cluster.label.text = clusteredEntities.length + "";
            cluster.label.font = `${height / 4}pt monospace`;
            cluster.label.style = Cesium.LabelStyle.FILL;
            cluster.label.fillColor = Cesium.Color.YELLOW;
            cluster.label.outlineWidth = 2;
            cluster.label.horizontalOrigin = Cesium.HorizontalOrigin.CENTER;
            cluster.label.eyeOffset = new Cesium.Cartesian3(0, 0, -1);
            cluster.label.disableDepthTestDistance = Number.POSITIVE_INFINITY;

            cluster.label.pixelOffset = new Cesium.Cartesian2(
                0,
                -cluster.billboard.height * 1.1
            );
          }
      );

      datasource.entities.add({
        name: type,
        parent,
        id,
        show,
        position: Cesium.Cartesian3.fromDegrees(position[0], position[1]),
        billboard: {
          image,
          width,
          height,
          verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          scaleByDistance: new Cesium.NearFarScalar(1.0e2, 1.5, 1.5e3, 1),
          disableDepthTestDistance: Number.POSITIVE_INFINITY,
          heightReference: Cesium.HeightReference.RELATIVE_TO_GROUND,
          pixelOffset: new Cesium.Cartesian2(0, height / 4),
        },
        label: {
          text: labelText,
          font: `${height / 4}pt monospace`,
          disableDepthTestDistance: Number.POSITIVE_INFINITY,
          eyeOffset: new Cesium.Cartesian3(0, 0, -1),
          fillColor: Cesium.Color.YELLOW,
          pixelOffset: new Cesium.Cartesian2(0, -height),
        },
        properties: {
          type: propertiesType,
          alarm,
          risk,
          hiddenDanger,
          active: false,
        },
      });
    };

    this.showEntitiesByType = (datasourceType, layerId, show= true) => {
      if (["polyline", "polygon", "label", "poi"].includes(datasourceType)) {
      const dataSources = viewer.dataSources.getByName(layerId);
      if (dataSources.length !== 0) {
        dataSources[0].show = show;
      }
    } else if (datasourceType === "point") {
      const dataSources = viewer.dataSources.getByName("entity");
      if (dataSources.length !== 0) {
        const datasource = dataSources[0];
        const parent = datasource.entities.getById(layerId);
        if (parent !== undefined) {
          parent.show = show;
        } else {
          console.log("错误提示:showEntitiesByType-图层未找到:", layerId);
        }
      } else {
        console.log("错误提示:showEntitiesByType-datasource未创建");
      }
    } else {
      const dataSources = viewer.dataSources.getByName(layerId);
      if (dataSources.length !== 0) {
        dataSources[0].show = show;
      }
    }
  }

    this.openEntitiesByType = (datasourceType, layerId, show= true) => {
      if (datasourceType === "polyline") {
      // No implementation provided in the original code
    } else if (datasourceType === "point") {
      const dataSources = viewer.dataSources.getByName("entity");
      if (dataSources.length !== 0) {
        const datasource = dataSources[0];
        const parent = datasource.entities.getById(layerId);
        if (parent !== undefined) {
          const entities = datasource.entities.values;
          entities.forEach((entity) => {
            if (entity.parent === parent) {
              entity.show = show;
            }
          });
        }
      }
    }
  }

    this.showAllEntities = (show= true) => {
      this.polylineArray.forEach((datasourcesname) => {
        const dataSources = viewer.dataSources.getByName(datasourcesname);
        if (dataSources.length !== 0) {
          dataSources[0].show = show;
        }
      });

      const dataSources = viewer.dataSources.getByName("entity");
      if (dataSources.length !== 0) {
      const datasource = dataSources[0];
      this.parentArray.forEach((parentId) => {
        const parent = datasource.entities.getById(parentId);
        if (parent !== undefined) {
          parent.show = show;
        }
      });
    }
  }

    this.showAllPoint = (show = true) => {
      const dataSources = viewer.dataSources.getByName("entity");
      if (dataSources.length !== 0) {
      const datasource = dataSources[0];
      this.parentArray.forEach((parentId) => {
        const parent = datasource.entities.getById(parentId);
        if (parent !== undefined) {
          parent.show = show;
        }
      });
    }
  }

    this.showPointByProperty = (
        layerId,
        property,
        propertyValue,
        show = true
  ) => {
      const dataSources = viewer.dataSources.getByName("entity");
      const entities = dataSources[0].entities.values;
      entities.forEach((entity) => {
        if (
            entity.name === layerId &&
            // @ts-ignore
            entity.properties[property] &&
            // @ts-ignore
            entity.properties[property]._value === propertyValue
        ) {
          entity.show = show;
        }
      });
    }
    this.switchPointByProperty = (
        layerId,
        property,
        propertyValue
  ) => {
      const dataSources = viewer.dataSources.getByName("entity");
      const entities = dataSources[0].entities.values;
      entities.forEach((entity) => {
        if (
            entity.name === layerId &&
            // @ts-ignore
            entity.properties[property] &&
            // @ts-ignore
            entity.properties[property]._value === propertyValue
        ) {
          entity.show = !entity.show;
        }
      });
    }

    this.showPolylineByProperty = (
        layerId,
        property,
        propertyValue,
        show = true
  ) => {
      const dataSources = viewer.dataSources.getByName(layerId);
      const entities = dataSources[0].entities.values;
      entities.forEach((entity) => {
        if (
            // @ts-ignore
            entity.properties[property] &&
            // @ts-ignore
            entity.properties[property]._value === propertyValue
        ) {
          entity.show = show;
        }
      });
    }

    const updateEntity = (entity) => {
      if (entity.billboard) {
      entity.billboard.scale = entity.properties.getValue(0).active === true ? 1.2 : 1;
    }
    if (entity.polyline) {
      entity.polyline.width = entity.properties.getValue(0).active === true ? 6 : 4;
    }
  }

    this.activateEntity = (entity) => {
      this.activeEntity = entity;
      entity.properties.active._value = true;
      updateEntity(entity);
    }

    this.inactivateEntity = (entity) => {
      if (entity instanceof Cesium.Entity) {
      entity.properties.active._value = false;
      updateEntity(entity);
    }
  }

    this.loadEntitiesPolyline = ({
      type,
      url,
      color = Cesium.Color.RED,
      outlineWidth = 4,
      show = true,
      callback = (dataSources) => {},
    }) => {
      if (!this.polylineArray.includes(type)) {
      this.polylineArray.push(type);
    }

    Cesium.GeoJsonDataSource.load(url, {
      stroke: color,
      strokeWidth: outlineWidth,
      fill: Cesium.Color.BLUE,
      clampToGround: true,
    }).then((dataSource) => {
      dataSource.name = type;
      viewer.dataSources.add(dataSource);
      dataSource.show = show;
      callback(dataSource);
    });
  }

    this.loadEntitiesPolygon = ({
      type,
      url,
      outlineColor = Cesium.Color.BLACK,
      fillColor = Cesium.Color.RED,
      outlineWidth = 4,
      show = true,
      callback = (dataSources) => {},
    }) =>{
      if (!this.polylineArray.includes(type)) {
      this.polylineArray.push(type);
    }

    Cesium.GeoJsonDataSource.load(url, {
      clampToGround: true,
    }).then((dataSource) => {
      dataSource.name = type;
      viewer.dataSources.add(dataSource);
      dataSource.show = show;
      callback(dataSource);
    });
  }

    this.loadEntitiesLabel = ({
      type,
      url,
      outlineWidth = 4,
      show = true,
      callback = (dataSources) => {},
    }) =>{
      if (!this.labelArray.includes(type)) {
      this.labelArray.push(type);
    }

    Cesium.GeoJsonDataSource.load(url, {
      stroke: Cesium.Color.fromCssColorString("#FF0000"),
      strokeWidth: outlineWidth,
    }).then((dataSource) => {
      dataSource.name = type;
      viewer.dataSources.add(dataSource);
      dataSource.show = show;
      callback(dataSource);
    });
  }

    this.loadEntitiesPoi = ({
      type,
      url,
      outlineWidth = 4,
      show = true,
      callback = (dataSources) => {},
    }) =>{
      if (!this.poiArray.includes(type)) {
      this.poiArray.push(type);
    }

    Cesium.GeoJsonDataSource.load(url, {
      stroke: Cesium.Color.fromCssColorString("#FF0000"),
      strokeWidth: outlineWidth,
    }).then((dataSource) => {
      dataSource.name = type;
      viewer.dataSources.add(dataSource);
      dataSource.show = show;
      callback(dataSource);
    });
  }

    this.getEntityById = (entityID)=> {
      for (let i = 0; i < viewer.dataSources.length; i++) {
        const entities = viewer.dataSources.get(i).entities.values;
        const finded = entities.find((item) => item.id === entityID);
        if (finded) {
          return finded;
        }
      }
      return false;
    }

    this.addPoint = (degree) => {
      viewer.entities.add({
        position: Cesium.Cartesian3.fromDegrees(degree[0], degree[1]),
        billboard: {
          image: imageInfo["point"],
          width: 36,
          height: 36,
          verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          scaleByDistance: new Cesium.NearFarScalar(1.5e2, 1.5, 8.0e6, 0.0),
          disableDepthTestDistance: Number.POSITIVE_INFINITY,
          heightReference: Cesium.HeightReference.RELATIVE_TO_GROUND,
        },
      });
    }

  }
}

class Primitive {
  constructor(viewer) {
    this.polylines = {}
    this.loadGroundPolylines = ({
                             type,
                             jsonData,
                             show = true,
                             outlineColor = Cesium.Color.fromCssColorString("#39A74A"),
                             outlineWidth = 5,
                             outlineType = "Solid",
                             distanceDisplayCondition = new Cesium.DistanceDisplayConditionGeometryInstanceAttribute(
                                 0,
                                 Number.MAX_VALUE
                             ),
                           }) => {
      const instances = [];
      const features = jsonData.features;

      features.forEach((item) => {
        const type = item.geometry.type;
        const coordinates = item.geometry.coordinates;

        const polygons = [];
        if (type === "MultiPolygon") {
          coordinates.forEach((item2) => {
            polygons.push(item2);
          });
        } else if (type === "Polygon") {
          polygons.push(coordinates);
        } else if (type === "MultiLineString") {
          polygons.push(coordinates);
        } else if (type === "LineString") {
          polygons.push([coordinates]);
        }

        polygons.forEach((polygon, indexPolygon) => {
          polygon.forEach((polyline, indexPolyline) => {
            const geometryId =
                item.properties.name + indexPolygon + indexPolyline;

            const degrees = polyline.flat(2);
            const positions = Cesium.Cartesian3.fromDegreesArray(degrees);

            instances.push(
                new Cesium.GeometryInstance({
                  geometry: new Cesium.GroundPolylineGeometry({
                    positions,
                    width: outlineWidth,
                  }),
                  attributes: {
                    // @ts-ignore
                    color: new Cesium.ColorGeometryInstanceAttribute.fromColor(
                        outlineColor
                    ),
                    distanceDisplayCondition,
                  },
                  id: geometryId,
                })
            );
          });
        });
      });

      let appearance = new Cesium.PolylineColorAppearance({
        translucent: false,
      });

      switch (outlineType) {
        case "Solid":
          appearance = new Cesium.PolylineColorAppearance({
            translucent: false,
          });
          break;
        case "Dash":
          appearance = new Cesium.PolylineMaterialAppearance({
            material: Cesium.Material.fromType(Cesium.Material.PolylineDashType, {
              color: outlineColor,
              gapColor: Cesium.Color.BLACK,
              dashLength: 200,
            }),
          });
          break;
        case "arrow":
          appearance = new Cesium.PolylineMaterialAppearance({
            material: new Cesium.Material({
              fabric: {
                type: "my-polyline-arrow",
              },
            }),
          });
          break;
      }

      this.polylines[type] = new Cesium.GroundPolylinePrimitive({
        geometryInstances: instances,
        appearance,
      });

      viewer.scene.primitives.add(this.polylines[type]);
      this.polylines[type].show = show;
    };

    this.loadPipeline = async ({
                            type,
                            url,
                            outlineColor = Cesium.Color.fromCssColorString("#39A74A"),
                            outlineWidth = 5,
                          }) => {
      // @ts-ignore
      const jsonData = await Cesium.Resource.fetchJson(url);
      this.loadGroundPolylines({
        type,
        jsonData,
        outlineColor,
        outlineWidth,
        outlineType: "Solid",
      });
    };
}

}

class Event {
  constructor(viewer) {
    this.pickPosition = [];
    this.handler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);
    this.callbackArrayOfPick = []
    //callbackArray：点击事件，hoverArray: 悬停事件

    this.activatePickHandler = (
        callbackArray,
        callbackHoverArray
    ) => {
      let callbackArrayOfPick = [];
      const pick = (movement) => {
        const cartesian3 = viewer.scene.globe.pick(
            viewer.camera.getPickRay(movement.position),
            viewer.scene
        );
        // @ts-ignore
        const degree = cartesian3ToDegree(cartesian3);
        this.pickPosition = degree;
        const pickedObject = viewer.scene.pick(movement.position);
        callbackArray[3](degree);
        if (
            pickedObject &&
            pickedObject.id instanceof Cesium.Entity &&
            pickedObject.id.billboard
        ) {
          callbackArray[0](pickedObject.id);
        } else if (
            pickedObject &&
            pickedObject.id instanceof Cesium.Entity &&
            pickedObject.id.polyline
        ) {
          callbackArray[0](pickedObject.id);
        } else if (
            pickedObject &&
            pickedObject.id instanceof Cesium.Entity &&
            pickedObject.id.label
        ) {
          callbackArray[2](pickedObject.id);
        } else if (
            pickedObject &&
            pickedObject instanceof Cesium.Cesium3DTileFeature
        ) {
          callbackArray[5](pickedObject, degree);
        } else if (typeof pickedObject === "undefined") {
          callbackArray[4]();
        }
      };
      callbackArrayOfPick = callbackArray;
      this.handler.setInputAction(pick, Cesium.ScreenSpaceEventType.LEFT_CLICK);

      const move = (movement) => {
        const hoverPosition = { x: 400, y: 200 };
        const pickedObject = viewer.scene.pick(movement.endPosition);

        if (
            pickedObject &&
            pickedObject.id instanceof Cesium.Entity &&
            pickedObject.id.polyline
        ) {
          const cartesian3 = viewer.scene.globe.pick(
              viewer.camera.getPickRay(movement.endPosition),
              viewer.scene
          );
          Cesium.SceneTransforms.wgs84ToWindowCoordinates(
              viewer.scene,
              cartesian3,
              hoverPosition
          );
          const pipename = pickedObject.id.properties.name._value;
          callbackHoverArray[0](
              pipename,
              true,
              hoverPosition.x + 20 + "px",
              hoverPosition.y + 20 + "px"
          );
        } else {
          callbackHoverArray[0]("pipename", false, "800px", "600px");
        }
      };
      // handler.setInputAction(move, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
    };

    this.inactivatePickHandler = () => {
      this.handler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);
    };

    this.keyboard = () => {
      const canvas = viewer.canvas;
      canvas.setAttribute("tabindex", "0");
      canvas.onclick = function () {
        canvas.focus();
      };
      const ellipsoid = viewer.scene.globe.ellipsoid;
      const flags = {
        moveForward: false,
        moveBackward: false,
        moveUp: false,
        moveDown: false,
        moveLeft: false,
        moveRight: false,
        lookUp: false,
        lookDown: false,
        twistRight: false,
        twistLeft: false,
        lookLeft: false,
        lookRight: false,
      };

      const getFlagForKeyCode = (keyCode) => {
        switch (keyCode) {
          case "w":
            return "moveForward";
          case "s":
            return "moveBackward";
          case "q":
            return "moveUp";
          case "e":
            return "moveDown";
          case "d":
            return "moveRight";
          case "a":
            return "moveLeft";
          case "W":
            return "lookUp";
          case "S":
            return "lookDown";
          case "Q":
            return "twistRight";
          case "E":
            return "twistLeft";
          case "D":
            return "lookRight";
          case "A":
            return "lookLeft";
          default:
            return undefined;
        }
      };

      document.addEventListener(
          "keydown",
          function (e) {
            const flagName = getFlagForKeyCode(e.key);
            if (typeof flagName !== "undefined") {
              flags[flagName] = true;
            }
          },
          false
      );

      document.addEventListener(
          "keyup",
          function (e) {
            const flagName = getFlagForKeyCode(e.key);
            if (typeof flagName !== "undefined") {
              flags[flagName] = false;
            }
          },
          false
      );

      viewer.clock.onTick.addEventListener(function () {
        const camera = viewer.camera;
        const cameraHeight = ellipsoid.cartesianToCartographic(
            camera.position
        ).height;
        const moveRate = cameraHeight / 200.0;
        const rotateRate = 0.01;

        if (flags.moveForward) {
          camera.moveForward(moveRate);
        }
        if (flags.moveBackward) {
          camera.moveBackward(moveRate);
        }
        if (flags.moveUp) {
          camera.moveUp(moveRate);
        }
        if (flags.moveDown) {
          camera.moveDown(moveRate);
        }
        if (flags.moveLeft) {
          camera.moveLeft(moveRate);
        }
        if (flags.moveRight) {
          camera.moveRight(moveRate);
        }
        if (flags.lookRight) {
          camera.lookRight(rotateRate);
        }
        if (flags.lookLeft) {
          camera.lookLeft(rotateRate);
        }
        if (flags.twistRight) {
          camera.twistRight(rotateRate);
        }
        if (flags.twistLeft) {
          camera.twistLeft(rotateRate);
        }
        if (flags.lookUp) {
          camera.lookUp(rotateRate);
        }
        if (flags.lookDown) {
          camera.lookDown(rotateRate);
        }
      });
    };

  }

}

class Listen {
  constructor(viewer) {

    this.listenPosition = (
        position,
        callback
    ) => {
      const cartesian3 = Cesium.Cartesian3.fromDegrees(position[0], position[1]);
      const listen = viewer.scene.postRender.addEventListener(() =>
          callback(cartesian3)
      );
      return listen;
    };

    this.removeListener = (listener) => {
      listener && listener();
    };

    this.listenModelEntity = (modelEntity) => {
      if (viewer.clock.shouldAnimate === true) {
        const ori = modelEntity.orientation.getValue(
            viewer.clock.currentTime
        );
        const center = modelEntity.position.getValue(
            viewer.clock.currentTime
        );
        let transform = Cesium.Transforms.eastNorthUpToFixedFrame(center);
        transform = Cesium.Matrix4.fromRotationTranslation(
            Cesium.Matrix3.fromQuaternion(ori),
            center
        );
        viewer.camera.lookAtTransform(
            transform,
            new Cesium.Cartesian3(-2, 0, 0)
        );
      }
    };

  }
}

class SceneMode {
  constructor(viewer) {
    this.camera = new Camera(viewer);
    this.Scene2D = () => {
      viewer.scene.morphTo2D(0.2);
      setTimeout(() => {
        this.camera.flyTo({
          lon: position0[0],
          lat: position0[1],
          height: position0[2],
          duration: 0.5,
        });
      }, 600);
    };
    this.Scene3D = () => {
      viewer.scene.morphTo3D(0.2);
      setTimeout(() => {
        this.camera.flyTo({
          lon: position0[0],
          lat: position0[1],
          height: position0[2],
          duration: 0.6,
        });
      }, 800);
    };
    this.ColumbusView = () => {
      viewer.scene.morphToColumbusView(0.2);
      setTimeout(() => {
        this.camera.flyTo({
          lon: position0[0],
          lat: position0[1],
          height: position0[2],
          duration: 0.6,
        });
      }, 800);
    };

  }


}

class Time {
  constructor(viewer) {

    this.createTimeline = ({
                        duration = 4, // 4 days
                        clockRange = Cesium.ClockRange.UNBOUNDED, // always
                        multiplier = 1, // speed = 1
                      }) => {
      const timeStart = Cesium.JulianDate.fromDate(new Date());
      const timeEnd = Cesium.JulianDate.addDays(
          timeStart,
          duration,
          new Cesium.JulianDate()
      );
      viewer.clock.startTime = timeStart.clone();
      viewer.clock.stopTime = timeEnd.clone();
      viewer.clock.currentTime = timeStart.clone();
      viewer.clock.clockRange = clockRange; // UNBOUNDED(:always), CLAMPED(:stop), LOOP_STOP(:loop)
      viewer.clock.multiplier = multiplier; // time speed
      viewer.clock.shouldAnimate = true;
    };

    this.activateTimeline = () => {
      viewer.clock.shouldAnimate = true;
    };

    this.inactivateTimeline = () => {
      viewer.clock.shouldAnimate = false;
    };

    this.pause = () => {
      viewer.clock.shouldAnimate = !viewer.clock.shouldAnimate;
      // if (viewer.clock.shouldAnimate) {
      //     viewer.scene.preUpdate.addEventListener(listenerFirstPerspective);
      // } else {
      //     viewer.scene.preUpdate.removeEventListener(listenerFirstPerspective);
      //     viewer.camera.lookAtTransform(Cesium.Matrix4.IDENTITY); // switch mouse control model
      // }
    };

  }
}

class Roam {
  constructor(viewer) {
    this.modelEntity = undefined;

    this.cameraLookAtEntity = (entity) => {
      if (viewer.clock.shouldAnimate === true) {
        const ori = entity.orientation.getValue(
            viewer.clock.currentTime
        );
        const center = entity.position.getValue(
            viewer.clock.currentTime
        );
        let transform = Cesium.Transforms.eastNorthUpToFixedFrame(center);
        transform = Cesium.Matrix4.fromRotationTranslation(
            Cesium.Matrix3.fromQuaternion(ori),
            center
        );
        viewer.camera.lookAtTransform(
            transform,
            new Cesium.Cartesian3(-2, 0, 0)
        );
      }
    };

    this.Roaming = ({ velocity = 15 }) => {
      viewer.entities && viewer.entities.removeAll();

      const coordinates = [
        [119.78628475809859, 25.50347264740604, 5.613364833908882],
        [119.78650124187148, 25.503752812993547, 4.7107064190196315],
        [119.78654547138368, 25.503812188050603, 4.993588853426436],
      ];

      const c3Array = coordinates.map((coordinate) => {
        return Cesium.Cartesian3.fromDegrees(
            coordinate[0],
            coordinate[1],
            coordinate[2]
        );
      });

      this.modelEntity = viewer.entities.add({
        position: c3Array[0],
      });

      const property = new Cesium.SampledPositionProperty();

      const start1 = Cesium.JulianDate.fromDate(new Date());
      let totalDistance = 0;
      let totalTime;
      const velocityKmh = velocity;
      const velocityMs = (velocityKmh * 1000) / 3600;

      for (let i = 0; i < c3Array.length; i++) {
        if (i !== 0) {
          const deltDistance = Cesium.Cartesian3.distance(
              c3Array[i - 1],
              c3Array[i]
          );
          totalDistance += deltDistance;
        }
        totalTime = totalDistance / velocityMs;

        const time = Cesium.JulianDate.addSeconds(
            start1,
            totalTime,
            new Cesium.JulianDate()
        );

        property.addSample(time, c3Array[i]);
      }

      const loopProperty = new Cesium.CallbackProperty(
          (time) => {
            const mod = time.secondsOfDay % totalTime;
            const relativeTime = Cesium.JulianDate.addSeconds(
                start1,
                mod,
                new Cesium.JulianDate()
            );
            return property.getValue(relativeTime);
          },
          false
      );
      // @ts-ignore
      this.modelEntity.position = loopProperty;
      this.modelEntity.orientation = new Cesium.VelocityOrientationProperty(
          // @ts-ignore
          loopProperty
      );

      viewer.scene.preUpdate.addEventListener(() =>
          this.cameraLookAtEntity(this.modelEntity)
    );
    };
  }
}

export class Tiles {
  constructor(viewer) {
    this.dbIdToFeatures = {};
    this.BIM = {};

    const update3dtilesMaxtrix = ({
      type,
      scale = 1.0,
      longitude = 115.57,
      latitude = 36.26,
      height = 800,
      rx = 0,
      ry = 0,
      rz = 0,
      alpha = 1,
    }) => {
      const mx = Cesium.Matrix3.fromRotationX(Cesium.Math.toRadians(rx));
      const my = Cesium.Matrix3.fromRotationY(Cesium.Math.toRadians(ry));
      const mz = Cesium.Matrix3.fromRotationZ(Cesium.Math.toRadians(rz));
      const rotationX = Cesium.Matrix4.fromRotationTranslation(mx);
      const rotationY = Cesium.Matrix4.fromRotationTranslation(my);
      const rotationZ = Cesium.Matrix4.fromRotationTranslation(mz);
      const position = Cesium.Cartesian3.fromDegrees(longitude, latitude, height);
      const m = Cesium.Transforms.eastNorthUpToFixedFrame(position);

      Cesium.Matrix4.multiply(m, rotationX, m);
      Cesium.Matrix4.multiply(m, rotationY, m);
      Cesium.Matrix4.multiply(m, rotationZ, m);
      const scaleMatrix = Cesium.Matrix4.fromUniformScale(scale);
      Cesium.Matrix4.multiply(m, scaleMatrix, m);

      this.BIM[type]._root.transform = m;
    }

    this.load3Dtiles = ({
      type,
      url,
      position = [117, 31],
      height = 0,
      rotation = [0, 0, 0],
      scale = 1,
      alpha = 1,
    }) => {
      const tileset = new Cesium.Cesium3DTileset({ url });
      tileset.enableModelExperimental = false;
      tileset.show = true;
      viewer.scene.primitives.add(tileset);
      this.BIM[type] = tileset;
      update3dtilesMaxtrix({
        type,
        scale,
        longitude: position[0],
        latitude: position[1],
        height,
        rx: rotation[0],
        ry: rotation[1],
        rz: rotation[2],
        alpha,
      });
    }

    this.load3DtilesPark = ({
      type,
      url,
      glsl,
      show = true,
    }) => {
      if (this.BIM[type] === undefined) {
      const tileset = new Cesium.Cesium3DTileset({ url });
      tileset.enableModelExperimental = false;
      viewer.scene.primitives.add(tileset);
      this.BIM[type] = tileset;
    } else {
      this.BIM[type].show = true;
    }
  }

    this.load3DtilesPipe = ({
      type,
      url,
      show = true,
    }) => {
      if (this.BIM[type] === undefined) {
      this.BIM[type] = viewer.scene.primitives.add(
          // @ts-ignore
          new Cesium.Cesium3DTileset({ url })
      );

      this.BIM[type].readyPromise.then((tileset) => {
        attachTileset(tileset);
      });
      this.BIM[type].show = show;
    }
  }

    this.load3DtilesCity = ({
      type,
      url,
      glsl,
      show = true,
    }) => {
      // @ts-ignore
      const tileset = new Cesium.Cesium3DTileset({ url });
      tileset.style = new Cesium.Cesium3DTileStyle({
        color: {
          conditions: [["true", "color('rgb(51, 153, 255)',1)"]],
        },
      });
      tileset.show = show;
      viewer.scene.primitives.add(tileset);
      this.BIM[type] = tileset;
    }

    const attachTileset = (tileset) => {
      const getFeatureDbId = (feature) => {
        if (Cesium.defined(feature) && Cesium.defined(feature.getProperty)) {
          return parseInt(feature.getProperty("DbId"), 10);
        }
        return -1;
      };

      const loadFeature = (feature) => {
        const dbId = getFeatureDbId(feature);
        let features = this.dbIdToFeatures[dbId];
        if (!Cesium.defined(features)) {
          this.dbIdToFeatures[dbId] = features = [];
        }
        features.push(feature);
      };

      const processContentFeatures = (
          content,
          callback
      ) => {
        const featuresLength = content.featuresLength;
        for (let i = 0; i < featuresLength; ++i) {
          const feature = content.getFeature(i);
          callback(feature);
        }
      };

      const processTileFeatures = (
          tile,
          callback
      ) => {
        const content = tile.content;
        const innerContents = content.innerContents;
        if (Cesium.defined(innerContents)) {
          const length = innerContents.length;
          for (let i = 0; i < length; ++i) {
            processContentFeatures(innerContents[i], callback);
          }
        } else {
          processContentFeatures(content, callback);
        }
      };

      tileset.tileLoad.addEventListener((tile) => {
        processTileFeatures(tile, loadFeature);
      });
    }

    this.show3Dtiles = (type, show = true) => {
      if (this.BIM[type] !== undefined) {
      this.BIM[type].show = show;
    }
  }

    this.destroy = (type) => {
      if (this.BIM[type] !== undefined) {
      this.BIM[type].destroy();
      // @ts-ignore
      this.BIM[type] = undefined;
    }
  }

  }
}

export class PolylineFlowMaterialProperty extends Cesium.PolylineOutlineMaterialProperty {
  constructor({ options = {}, type, image, time, imageW, source, imageColor }) {
    super(options)
    this.createMaterial({ type, image, time, imageW, source, imageColor })
    this.type = type
  }

  createMaterial ({ type, image, time, imageW = 15, source, imageColor = new Cesium.Cartesian3(1.0, 1.0, 1.0) }) {
    Cesium.Material[type] = type
    Cesium.Material._materialCache.addMaterial(type, {
      // strict: true,
      fabric: {
        type,
        uniforms: {
          image,
          time,
          imageW, //贴图比例，每种线条的贴图宽度不同，贴图拉伸不同，需要给出参数
          imageColor
        },
        source
      }
      // translucent: true
    })
  }

  getType () {
    return this.type
  }
}
