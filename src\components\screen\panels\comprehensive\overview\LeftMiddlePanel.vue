<template>
  <PanelBox title="风险监测">
    <div class="panel-content">
      <div class="stats-row">
        <div class="stat-item">
          <span class="stat-dot"><span class="stat-dot-inner"></span></span>
          <span class="stat-label">设备总数</span>
          <span class="stat-value-blue">{{ statsData.totalDevices }}</span>
          <span class="stat-unit">台</span>
        </div>
        <div class="stat-item">
          <span class="stat-dot"><span class="stat-dot-inner"></span></span>
          <span class="stat-label">在线/离线</span>
          <span class="stat-value-highlight">{{ statsData.onlineDevices }}</span>
          <span class="stat-value-warning">/{{ statsData.offlineDevices }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-dot"><span class="stat-dot-inner"></span></span>
          <span class="stat-label">设备在线率</span>
          <span class="stat-value-gradient">{{ statsData.onlineRate }}</span>
        </div>
      </div>

      <div class="chart-container">
        <div class="chart-header">
          <div class="unit-label">单位（台）</div>
          <div class="chart-legend">
            <div class="legend-item">
              <span class="legend-icon access"></span>
              <span class="legend-text">接入总数</span>
            </div>
            <div class="legend-item">
              <span class="legend-icon online"></span>
              <span class="legend-text">在线数</span>
            </div>
            <div class="legend-item">
              <span class="legend-icon rate"></span>
              <span class="legend-text">在线率</span>
            </div>
          </div>
        </div>

        <div class="chart-wrapper" ref="chartRef"></div>
      </div>
    </div>
  </PanelBox>
</template>

<script setup>
import { ref, onMounted, nextTick, reactive } from 'vue'
import * as echarts from 'echarts'
import PanelBox from '@/components/screen/PanelBox.vue'
// 这里预留导入API方法的地方
// import { getRiskMonitorStatistics } from '@/api/drainage'

const chartRef = ref(null)
let chartInstance = null

// 统计数据
const statsData = reactive({
  totalDevices: 8766,
  onlineDevices: 8505,
  offlineDevices: 261,
  onlineRate: '97%'
})

// 图表数据
const chartData = ref([
  { category: '燃气', access: 280, online: 260, rate: 92.8 },
  { category: '排水', access: 7014, online: 6960, rate: 99.2 },
  { category: '供热', access: 342, online: 320, rate: 93.6 },
  { category: '桥梁', access: 1130, online: 1115, rate: 98.6 }
])

// 从后端获取数据 - 预留接口方法待后续接入
const fetchData = async () => {
  try {
    // 预留实际API调用
    // const res = await getRiskMonitorStatistics()
    // if (res.code === 200 && res.data) {
    //   // 处理统计数据
    //   statsData.totalDevices = res.data.totalDevices || 0
    //   statsData.onlineDevices = res.data.onlineDevices || 0
    //   statsData.offlineDevices = (res.data.totalDevices || 0) - (res.data.onlineDevices || 0)
    //   statsData.onlineRate = (typeof res.data.onlineRate === 'number' ? res.data.onlineRate + '%' : (res.data.onlineRate || '0%'))
    //   // 处理图表数据
    //   chartData.value = (res.data.deviceTypeStatistics || []).map(item => ({
    //     category: item.deviceType,
    //     access: item.count,
    //     online: item.onlineCount,
    //     rate: typeof item.onlineRate === 'number' ? item.onlineRate : 0
    //   }))
    //   if (chartInstance) {
    //     updateChart()
    //   }
    // }

    // 暂时使用静态数据
    if (chartInstance) {
      updateChart()
    }
  } catch (error) {
    console.error('获取风险监测数据失败:', error)
  }
}

// 更新图表
const updateChart = () => {
  if (!chartInstance) return

  const option = createChartOption()
  chartInstance.setOption(option)
}

// 创建图表配置
const createChartOption = () => {
  return {
    backgroundColor: 'transparent',
    grid: {
      top: '12%',
      left: '3%',
      right: '4%',
      bottom: '22%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: chartData.value.map(item => item.category),
      axisLine: {
        lineStyle: {
          color: '#5F5F60',
          width: 1
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#FFFFFF',
        fontSize: 12,
        margin: 16,
        rotate: 0,
        formatter: function (value) {
          return value;
        }
      }
    },
    yAxis: {
      type: 'value',
      max: 8000,
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)',
          type: 'dashed'
        }
      },
      axisLabel: {
        color: '#FFFFFF',
        fontSize: 12,
        formatter: '{value}'
      }
    },
    series: [
      {
        name: '接入总数',
        type: 'bar',
        data: chartData.value.map(item => item.access),
        barWidth: 12,
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 1,
            y2: 0,
            colorStops: [
              { offset: 0, color: '#0576FF' },
              { offset: 0.5, color: '#0567E8' },
              { offset: 1, color: '#055ADB' }
            ]
          },
          borderRadius: [2, 2, 0, 0],
          borderColor: '#0A93FF',
          borderWidth: 1,
          shadowColor: 'rgba(5, 122, 255, 0.3)',
          shadowBlur: 10,
          shadowOffsetX: 3,
          shadowOffsetY: 3
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 15,
            shadowColor: 'rgba(5, 90, 219, 0.7)'
          }
        },
        showBackground: true,
        backgroundStyle: {
          color: 'rgba(5, 90, 219, 0.2)',
          borderRadius: [0, 0, 0, 0]
        }
      },
      {
        name: '在线数',
        type: 'bar',
        data: chartData.value.map(item => item.online),
        barWidth: 12,
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 1,
            y2: 0,
            colorStops: [
              { offset: 0, color: '#23CAFF' },
              { offset: 0.5, color: '#1FB4E9' },
              { offset: 1, color: '#1A9AD6' }
            ]
          },
          borderRadius: [2, 2, 0, 0],
          borderColor: '#34D6FF',
          borderWidth: 1,
          shadowColor: 'rgba(35, 202, 255, 0.3)',
          shadowBlur: 10,
          shadowOffsetX: 3,
          shadowOffsetY: 3
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 15,
            shadowColor: 'rgba(35, 202, 255, 0.7)'
          }
        },
        showBackground: true,
        backgroundStyle: {
          color: 'rgba(35, 202, 255, 0.2)',
          borderRadius: [0, 0, 0, 0]
        }
      },
      {
        name: '在线率',
        type: 'line',
        data: chartData.value.map(item => item.rate),
        symbolSize: 6,
        symbol: 'circle',
        smooth: true,
        lineStyle: {
          width: 3,
          color: '#3FD87C'
        },
        itemStyle: {
          color: '#3FD87C'
        }
      }
    ]
  };
}

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return

  chartInstance = echarts.init(chartRef.value)
  const option = createChartOption()
  chartInstance.setOption(option)

  window.addEventListener('resize', () => {
    chartInstance && chartInstance.resize()
  })
}

onMounted(async () => {
  await nextTick()
  // 获取初始数据
  await fetchData()
  // 初始化图表
  initChart()
})
</script>

<style scoped>
.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.stats-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.stat-dot {
  width: 9px;
  height: 9px;
  background: rgba(5, 90, 219, 0.4);
  border-radius: 50%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-dot-inner {
  width: 5px;
  height: 5px;
  background: #055ADB;
  border-radius: 50%;
  position: absolute;
}

.stat-label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
}

.stat-value-blue {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 24px;
  line-height: 26px;
  text-align: left;
  font-style: normal;
  background: linear-gradient(90deg, #FFFFFF 0%, #055ADB 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-value-highlight {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 24px;
  color: #3CF3FF;
  line-height: 26px;
}

.stat-value-warning {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 24px;
  color: #FFD11C;
  line-height: 26px;
}

.stat-value-gradient {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 24px;
  line-height: 26px;
  text-align: left;
  font-style: normal;
  background: linear-gradient(90deg, #FFFFFF 0%, #36F281 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-unit {
  color: rgba(255, 255, 255, 0.4);
  font-size: 12px;
}

.chart-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  border-radius: 4px;
  overflow: hidden;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 30px;
  padding: 0 10px;
}

.unit-label {
  color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
}

.chart-legend {
  display: flex;
  gap: 15px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.legend-icon {
  width: 8px;
  height: 8px;
  display: block;
}

.legend-icon.access {
  background-color: #0576FF;
}

.legend-icon.online {
  background-color: #23CAFF;
}

.legend-icon.rate {
  background-color: #3FD87C;
}

.legend-text {
  color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
}

.chart-wrapper {
  flex: 1;
  min-height: 180px;
}

/* 940px左右高度的屏幕特别优化 */
@media (min-height: 940px) and (max-height: 1055px) {
  .panel-content {
    padding: 10px;
    gap: 10px;
  }

  .stats-row {
    margin-bottom: 5px;
  }

  .stat-item {
    gap: 3px;
  }

  .stat-dot {
    width: 7px;
    height: 7px;
  }

  .stat-dot-inner {
    width: 3px;
    height: 3px;
  }

  .stat-label {
    font-size: 12px;
  }

  .stat-value-blue,
  .stat-value-highlight,
  .stat-value-warning,
  .stat-value-gradient {
    font-size: 18px;
  }

  .stat-unit {
    font-size: 10px;
  }

  .chart-header {
    height: 24px;
    padding: 0 8px;
  }

  .unit-label {
    font-size: 10px;
  }

  .chart-legend {
    gap: 10px;
  }

  .legend-icon {
    width: 6px;
    height: 6px;
  }

  .legend-text {
    font-size: 10px;
  }

  .chart-wrapper {
    min-height: 160px;
  }
}

/* 适配不同屏幕尺寸 */
@media screen and (min-height: 900px) and (max-height: 940px) {
  .stats-row {
    margin-bottom: 4px;
  }

  .panel-content {
    gap: 0px;
  }
}
</style>