<template>
  <div class="gas-network-statistics">
    <!-- 主内容区域 -->
    <div class="content-wrapper">
      <!-- 上部分区域 -->
      <div class="top-section">
        <!-- 左侧卡片区域 -->
        <div class="left-cards">
          <!-- 总长度卡片 -->
          <div class="stat-card blue-card">
            <div class="card-content">
              <div class="stat-value">{{ lengthStatistics.totalLength }}</div>
              <div class="stat-label">总长度 / km</div>
            </div>
            <div class="card-icon">
              <img src="@/assets/images/mis/gas/pipeline-icon.png" alt="管线图标" class="pipeline-icon" />
            </div>
          </div>

          <!-- 新增长度卡片 -->
          <div class="stat-card green-card">
            <div class="card-content">
              <div class="stat-value">{{ lengthStatistics.increaseLength }}</div>
              <div class="stat-label">本年增管线长度 / km</div>
            </div>
            <div class="card-icon">
              <img src="@/assets/images/mis/gas/pipeline-add-icon.png" alt="新增管线图标" class="pipeline-icon" />
            </div>
          </div>
        </div>

        <!-- 右侧管线趋势图 -->
        <div class="right-trend">
          <div class="trend-chart chart-box">
            <div class="chart-header">
              <h4 class="chart-title">管线趋势</h4>
              <div class="chart-subtitle">单位/km</div>
            </div>
            <div class="chart-container" ref="trendChartRef"></div>
          </div>
        </div>
      </div>

      <!-- 下部分三个统计图表 -->
      <div class="bottom-charts">
        <!-- 压力级别 -->
        <div class="pressure-chart chart-box">
          <h4 class="chart-title">压力级别</h4>
          <div class="chart-container" ref="pressureChartRef"></div>
        </div>

        <!-- 管线材质 -->
        <div class="material-chart chart-box">
          <h4 class="chart-title">管线材质</h4>
          <div class="chart-container" ref="materialChartRef">
            <div class="material-list">
              <div v-for="item in materialData" :key="item.rank" class="material-item">
                <ProgressGauge
                  :number="item.rank"
                  :title="item.name"
                  :percentage="item.percentage"
                  :value="item.value"
                  unit="km"
                  :color="item.color"
                />
              </div>
            </div>
          </div>
        </div>

        <!-- 管线年龄 -->
        <div class="age-chart chart-box">
          <h4 class="chart-title">管线年龄</h4>
          <div class="chart-subtitle">单位/km</div>
          <div class="chart-container" ref="ageChartRef"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, nextTick } from 'vue';
import * as echarts from 'echarts';
import {
  getPipelineLengthStatistics,
  getPipelineLengthTrend,
  getPipelinePressureLevelStatistics,
  getPipelineMaterialStatistics,
  getPipelineAgeStatistics
} from '@/api/gas';

// 图表引用
const trendChartRef = ref(null);
const pressureChartRef = ref(null);
const materialChartRef = ref(null);
const ageChartRef = ref(null);

// 统计数据
const lengthStatistics = ref({
  totalLength: 0,
  increaseLength: 0
});

// 图表实例
let trendChart = null;
let pressureChart = null;
let materialChart = null;
let ageChart = null;

// 初始化管线趋势图
const initTrendChart = async () => {
  if (!trendChartRef.value) return;

  try {
    const response = await getPipelineLengthTrend();
    const trendData = response.data || [];

    // 准备月份数据
    const months = Array.from({ length: 12 }, (_, i) => `${i + 1}月`);
    const values = Array(12).fill(0);

    // 填充实际数据
    trendData.forEach(item => {
      const monthIndex = parseInt(item.month) - 1;
      if (monthIndex >= 0 && monthIndex < 12) {
        values[monthIndex] = item.pipeLength || 0;
      }
    });

    // 找出最大值的月份和值
    let maxIndex = 0;
    let maxValue = 0;
    values.forEach((value, index) => {
      if (value > maxValue) {
        maxValue = value;
        maxIndex = index;
      }
    });

    trendChart = echarts.init(trendChartRef.value);

    const option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            backgroundColor: '#6a7985'
          }
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '10%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: months,
        axisLine: {
          lineStyle: {
            color: '#E0E0E0'
          }
        }
      },
      yAxis: {
        type: 'value',
        min: 0,
        max: Math.max(1000, Math.ceil(maxValue * 1.2)),
        interval: Math.ceil(Math.max(1000, maxValue * 1.2) / 5),
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        splitLine: {
          lineStyle: {
            color: '#E0E0E0',
            type: 'dashed'
          }
        }
      },
      series: [
        {
          name: '管线长度',
          type: 'line',
          data: values,
          smooth: true,
          symbol: 'circle',
          symbolSize: 6,
          lineStyle: {
            width: 2,
            color: '#0086FF'
          },
          itemStyle: {
            color: '#0086FF'
          },
          markPoint: maxValue > 0 ? {
            data: [
              { coord: [maxIndex, maxValue], name: `${maxIndex + 1}月`, value: `${maxValue} km`, itemStyle: { color: '#0086FF' } }
            ],
            symbol: 'pin',
            symbolSize: 60,
            label: {
              formatter: '{b}\n{c}',
              position: 'inside',
              fontSize: 12
            }
          } : null,
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [{
                offset: 0, color: 'rgba(64, 158, 255, 0.3)'
              }, {
                offset: 1, color: 'rgba(64, 158, 255, 0.1)'
              }]
            }
          }
        }
      ]
    };

    trendChart.setOption(option);
  } catch (error) {
    console.error('获取管线趋势数据失败:', error);
    // 如果接口失败，仍然初始化图表但使用空数据
    trendChart = echarts.init(trendChartRef.value);
    trendChart.setOption({
      tooltip: { trigger: 'axis' },
      xAxis: { type: 'category', data: Array.from({ length: 12 }, (_, i) => `${i + 1}月`) },
      yAxis: { type: 'value' },
      series: [{ type: 'line', data: Array(12).fill(0) }]
    });
  }
};

// 初始化压力级别图 - 玫瑰图
const initPressureChart = async () => {
  if (!pressureChartRef.value) return;

  try {
    const response = await getPipelinePressureLevelStatistics();
    const pressureData = response.data || [];

    // 处理数据格式
    const chartData = pressureData.map((item, index) => {
      // 定义不同的颜色
      const colors = ['#4C84FF', '#5ED3FB', '#26DFA9', '#FFC542', '#FF6B72'];
      // 处理百分比格式
      const percent = item.percent ? parseFloat(item.percent.replace('%', '')) : 0;

      return {
        value: item.pipeLength || 0,
        name: item.pressureLevelName || `级别${index + 1}`,
        percentage: percent,
        itemStyle: { color: colors[index % colors.length] }
      };
    });

    // 计算总长度
    const totalLength = chartData.reduce((sum, item) => sum + item.value, 0).toFixed(1);

    pressureChart = echarts.init(pressureChartRef.value);

    const option = {
      tooltip: {
        trigger: 'item',
        formatter: '{b}: {c}km ({d}%)'
      },
      legend: {
        orient: 'horizontal',
        left: 0,
        bottom: '20%',
        itemWidth: 10,
        itemHeight: 10,
        itemGap: 15,
        icon: 'circle',
        data: chartData.map(item => ({ name: item.name, icon: 'circle' })),
        formatter: name => name,
        textStyle: {
          fontSize: 12,
          color: '#666'
        }
      },
      series: [
        {
          name: '压力级别',
          type: 'pie',
          radius: ['20%', '60%'],
          roseType: 'area',
          avoidLabelOverlap: false,
          center: ['50%', '30%'],
          label: {
            show: true,
            position: 'outside',
            formatter: params => {
              return `${params.name}\n${params.value}km ${params.percent}%`;
            },
            color: '#333',
            fontSize: 12,
            alignTo: 'edge',
            edgeDistance: 10
          },
          labelLine: {
            length: 15,
            length2: 10,
            smooth: true
          },
          itemStyle: {
            borderWidth: 2,
            borderColor: '#fff'
          },
          emphasis: {
            label: {
              fontSize: 14,
              fontWeight: 'bold'
            }
          },
          data: chartData
        }
      ],
      graphic: {
        type: 'text',
        left: 'center',
        top: '24%',
        style: {
          text: `${totalLength}km\n总长度`,
          textAlign: 'center',
          font: 'bold 12px Arial',
          fill: '#333'
        }
      }
    };

    pressureChart.setOption(option);
  } catch (error) {
    console.error('获取管线压力级别数据失败:', error);
    // 如果接口失败，仍然初始化图表但使用默认数据
    const defaultData = [
      { value: 67, name: '低压管线', percentage: 75, itemStyle: { color: '#4C84FF' } },
      { value: 35.1, name: '中压管线', percentage: 10, itemStyle: { color: '#5ED3FB' } },
      { value: 8, name: '高压管线', percentage: 15, itemStyle: { color: '#26DFA9' } }
    ];

    const totalLength = defaultData.reduce((sum, item) => sum + item.value, 0).toFixed(1);

    pressureChart = echarts.init(pressureChartRef.value);
    pressureChart.setOption({
      tooltip: { trigger: 'item' },
      series: [{ type: 'pie', radius: ['20%', '60%'], data: defaultData }],
      graphic: {
        type: 'text',
        left: 'center',
        top: '24%',
        style: {
          text: `${totalLength}km\n总长度`,
          textAlign: 'center',
          font: 'bold 12px Arial',
          fill: '#333'
        }
      }
    });
  }
};

import ProgressGauge from '@/components/progress/ProgressGauge.vue';

// 材质数据
const materialData = ref([
  { rank: 1, name: '钢管', value: 78, percentage: 78, color: '#4C84FF' },
  { rank: 2, name: 'PE管', value: 39, percentage: 25, color: '#5ED3FB' },
  { rank: 3, name: '铸铁管', value: 91, percentage: 86, color: '#26DFA9' },
  { rank: 4, name: '金属软管', value: 42, percentage: 33, color: '#FFC542' }
]);

// 初始化管线材质图
const initMaterialChart = async () => {
  if (!materialChartRef.value) return;

  try {
    const response = await getPipelineMaterialStatistics();
    const materialDataFromApi = response.data || [];

    // 定义颜色数组
    const colors = ['#4C84FF', '#5ED3FB', '#26DFA9', '#FFC542', '#FF6B72'];

    // 处理数据格式
    const formattedData = materialDataFromApi.map((item, index) => {
      // 处理百分比格式
      const percent = item.percent ? parseFloat(item.percent.replace('%', '')) : 0;

      return {
        rank: index + 1,
        name: item.materialName || `材质${index + 1}`,
        value: item.pipeLength || 0,
        percentage: percent,
        color: colors[index % colors.length]
      };
    });

    // 更新材质数据
    materialData.value = formattedData.length > 0 ? formattedData : materialData.value;
  } catch (error) {
    console.error('获取管线材质数据失败:', error);
    // 如果接口失败，保留默认数据
  }

  // 使用Vue3的组件式API，不需要手动创建DOM
  // materialChartRef.value会被template中的内容自动填充
};

// 初始化管线年龄图
const initAgeChart = async () => {
  if (!ageChartRef.value) return;

  try {
    const response = await getPipelineAgeStatistics();
    const ageData = response.data || [];

    // 定义年龄区间数组
    const ageRanges = ['5年以下', '5-10年', '10-15年', '15-20年', '20年以上'];

    // 处理数据格式
    const chartData = [];
    let maxValue = 0;
    let maxIndex = 0;

    // 将API数据映射到图表数据
    ageData.forEach((item, index) => {
      const value = item.pipeLength || 0;

      // 记录最大值及其索引
      if (value > maxValue) {
        maxValue = value;
        maxIndex = index;
      }

      // 创建数据项
      const dataItem = {
        value,
        itemStyle: {color: '#4C84FF'}
      };

      // 如果是最大值，添加标签
      if (value > 0 && index === maxIndex) {
        dataItem.label = {
          show: true,
          position: 'top',
          distance: 10,
          formatter: `${value} km`,
          padding: [4, 8],
          borderRadius: 4,
          backgroundColor: '#F2F6FC',
          color: '#333'
        };
      }

      chartData.push(dataItem);
    });

    // 如果数据不足，填充空数据
    while (chartData.length < ageRanges.length) {
      chartData.push({value: 0, itemStyle: {color: '#4C84FF'}});
    }

    ageChart = echarts.init(ageChartRef.value);

    const option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '20%',
        top: '5%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: ageRanges,
        axisLine: {
          lineStyle: {
            color: '#E0E0E0'
          }
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          interval: 0,
          rotate: 0,
          fontSize: 12,
          color: '#666'
        }
      },
      yAxis: {
        type: 'value',
        max: Math.max(100, Math.ceil(maxValue * 1.2)),
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        splitLine: {
          lineStyle: {
            color: '#E0E0E0',
            type: 'dashed'
          }
        }
      },
      series: [
        {
          name: '管线长度',
          type: 'bar',
          barWidth: '40%',
          data: chartData,
          itemStyle: {
            borderRadius: [4, 4, 0, 0]
          }
        }
      ]
    };

    ageChart.setOption(option);
  } catch (error) {
    console.error('获取管线年龄数据失败:', error);
    // 如果接口失败，仍然初始化图表但使用默认数据
    const defaultData = [
      {value: 52, itemStyle: {color: '#4C84FF'}},
      {
        value: 38,
        itemStyle: {color: '#4C84FF'},
        label: {
          show: true,
          position: 'top',
          distance: 10,
          formatter: '38 km',
          padding: [4, 8],
          borderRadius: 4,
          backgroundColor: '#F2F6FC',
          color: '#333'
        }
      },
      {value: 32, itemStyle: {color: '#4C84FF'}},
      {value: 20, itemStyle: {color: '#4C84FF'}},
      {value: 75, itemStyle: {color: '#4C84FF'}}
    ];

    ageChart = echarts.init(ageChartRef.value);
    ageChart.setOption({
      tooltip: { trigger: 'axis' },
      xAxis: {
        type: 'category',
        data: ['5年以下', '5-10年', '10-15年', '15-20年', '20年以上']
      },
      yAxis: { type: 'value' },
      series: [{
        type: 'bar',
        barWidth: '40%',
        data: defaultData,
        itemStyle: {
          borderRadius: [4, 4, 0, 0]
        }
      }]
    });
  }
};

// 初始化所有图表
const initCharts = async () => {
  await nextTick();

  // 并行初始化所有图表，提高加载速度
  await Promise.all([
    initTrendChart(),
    initPressureChart(),
    initMaterialChart(),
    initAgeChart()
  ]);

  // 添加窗口大小变化监听
  window.addEventListener('resize', handleResize);
};

// 窗口大小调整处理函数
const handleResize = () => {
  trendChart && trendChart.resize();
  pressureChart && pressureChart.resize();
  materialChart && materialChart.resize();
  ageChart && ageChart.resize();
};

// 获取管线长度统计数据
const fetchLengthStatistics = async () => {
  try {
    const response = await getPipelineLengthStatistics();
    if (response.data) {
      lengthStatistics.value = {
        totalLength: response.data.totalLength || 0,
        increaseLength: response.data.increaseLength || 0
      };
    }
  } catch (error) {
    console.error('获取管线长度统计数据失败:', error);
  }
};

// 组件挂载后初始化
onMounted(() => {
  fetchLengthStatistics();
  initCharts();
});

// 组件销毁前清理事件
onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize);

  // 销毁图表实例
  trendChart && trendChart.dispose();
  pressureChart && pressureChart.dispose();
  materialChart && materialChart.dispose();
  ageChart && ageChart.dispose();
});
</script>

<style scoped>
.gas-network-statistics {
  width: 100%;
  height: auto;
  min-height: calc(100vh - 80px);
  padding: 16px;
  box-sizing: border-box;
  overflow: auto;
}

.component-title {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 18px;
  color: #282828;
  margin-bottom: 24px;
}

.content-wrapper {
  display: flex;
  flex-direction: column;
  gap: 16px;
  height: calc(100vh - 340px);
  overflow: auto;
}

/* 上部分区域 */
.top-section {
  display: flex;
  gap: 16px;
  height: 350px;
}

/* 左侧卡片区域 */
.left-cards {
  width: 320px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.stat-card {
  border-radius: 4px;
  padding: 24px;
  height: 162px;
  display: flex;
  position: relative;
  overflow: hidden;
}

.blue-card {
  background-color: #2878FF;
  color: white;
}

.green-card {
  background-color: #1BD0A4;
  color: white;
}

.card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  z-index: 2;
}

.stat-value {
  font-size: 36px;
  font-weight: bold;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  opacity: 0.85;
}

.card-icon {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1;
}

.pipeline-icon {
  width: 80px;
  height: 80px;
  object-fit: contain;
  opacity: 0.8;
}

/* 右侧趋势图区域 */
.right-trend {
  flex: 1;
}

.chart-box {
  background-color: #FFFFFF;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  padding: 16px;
  position: relative;
  height: 100%;
}

.chart-title {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 16px;
  color: #282828;
  margin: 0 0 8px 0;
}

.chart-subtitle {
  font-size: 12px;
  color: #909399;
  margin-bottom: 8px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.trend-chart {
  height: 100%;
}

.chart-container {
  width: 100%;
  height: 100%;
  flex: 1;
}

.trend-chart .chart-container {
  height: calc(100% - 40px);
}

/* 下部分三个图表区域 */
.bottom-charts {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 16px;
  flex: 1;
  min-height: 350px;
  margin-bottom: 20px;
}

/* 材质列表样式 */
.material-list {
  margin-top: 16px;
  height: calc(100% - 30px);
  display: flex;
  flex-direction: column;
  gap: 20px;
  overflow-y: auto;
}

.material-item {
  padding: 0 10px;
}

/* 压力级别图表样式 */
.pressure-chart .chart-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 管线年龄图表样式 */
.age-chart .chart-container {
  padding-top: 10px;
}

/* 确保所有图表容器有足够的高度 */
.pressure-chart, .material-chart, .age-chart {
  height: 100%;
}

/* 响应式处理 */
@media (max-width: 1366px) {
  .top-section {
    flex-direction: column;
    height: auto;
  }

  .left-cards {
    width: 100%;
    flex-direction: row;
  }

  .stat-card {
    flex: 1;
  }

  .right-trend {
    height: 300px;
  }

  .trend-chart .chart-container {
    height: calc(100% - 40px);
  }
}

@media (max-width: 992px) {
  .left-cards {
    flex-direction: column;
  }

  .bottom-charts {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .pressure-chart, .material-chart, .age-chart {
    height: 350px;
    margin-bottom: 16px;
  }

  .gas-network-statistics {
    height: auto;
    min-height: calc(100vh - 80px);
  }

  .content-wrapper {
    height: auto;
    padding-bottom: 20px;
  }
}

/* 美化滚动条 */
.gas-network-statistics::-webkit-scrollbar {
  width: 8px;
}

.gas-network-statistics::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.gas-network-statistics::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.gas-network-statistics::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 针对940px高度的特殊适配 */
@media screen and (min-height: 930px) and (max-height: 950px) {
  /* 调整整体布局 */
  .gas-network-statistics {
    padding: 12px 12px 0 12px;
    min-height: 920px;
  }

  /* 调整上部分区域高度 */
  .top-section {
    height: 310px;
    min-height: 310px;
  }

  /* 调整下部分图表区域 */
  .bottom-charts {
    min-height: 310px;
    margin-bottom: 0;
    gap: 12px;
  }

  /* 调整各图表容器高度 */
  .pressure-chart, .material-chart, .age-chart {
    min-height: 270px;
    padding: 12px;
  }

  /* 调整内容间距 */
  .content-wrapper {
    gap: 10px;
    padding-bottom: 0;
  }

  /* 调整图表内部布局 */
  .chart-container {
    height: calc(100% - 25px);
  }

  /* 调整材质列表 */
  .material-list {
    gap: 6px;
    margin-top: 5px;
    height: calc(100% - 20px);
    padding-bottom: 0;
  }

  /* 调整材质列表项 */
  .material-item {
    padding: 0 5px;
    transform: scale(0.92);
    transform-origin: left center;
    margin-bottom: -5px;
  }

  /* 调整压力图表 */
  .pressure-chart .chart-container {
    transform: scale(0.92);
    transform-origin: center center;
  }

  /* 调整年龄图表 */
  .age-chart .chart-container {
    transform: scale(0.92);
    transform-origin: center center;
    padding-top: 0;
  }

  /* 调整图表标题 */
  .chart-title {
    margin: 0 0 5px 0;
  }

  /* 调整图表副标题 */
  .chart-subtitle {
    margin-bottom: 5px;
  }

  /* 调整卡片高度 */
  .stat-card {
    height: 150px;
    padding: 20px;
  }
}
</style>