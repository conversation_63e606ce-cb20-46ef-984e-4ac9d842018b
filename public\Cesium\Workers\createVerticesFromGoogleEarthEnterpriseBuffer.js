/**
 *    ████████                   ██                   ██       ██                  ██      ██
 *   ██░░░░░░██                 ░░                   ░██      ░██                 ░██     ░██
 *  ██      ░░   █████  ███████  ██ ██   ██  ██████  ░██   █  ░██  ██████  ██████ ░██     ░██
 * ░██          ██░░░██░░██░░░██░██░██  ░██ ██░░░░   ░██  ███ ░██ ██░░░░██░░██░░█ ░██  ██████
 * ░██    █████░███████ ░██  ░██░██░██  ░██░░█████   ░██ ██░██░██░██   ░██ ░██ ░  ░██ ██░░░██
 * ░░██  ░░░░██░██░░░░  ░██  ░██░██░██  ░██ ░░░░░██  ░████ ░░████░██   ░██ ░██    ░██░██  ░██
 *  ░░████████ ░░██████ ███  ░██░██░░██████ ██████   ░██░   ░░░██░░██████ ░███    ███░░██████
 *   ░░░░░░░░   ░░░░░░ ░░░   ░░ ░░  ░░░░░░ ░░░░░░    ░░       ░░  ░░░░░░  ░░░    ░░░  ░░░░░░
 *
 * @license
 * GeniusWorld Web SDK - http://zydlxx.cn:1234/
 * Version 4.0.0.B#develop-d3ff2330@202405231107
 *
 *
 * Copyright © 2020 - 2023 正元地理信息集团股份有限公司. All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
define(["./AxisAlignedBoundingBox-5054a700","./Transforms-6a5d79d3","./Matrix2-d550732e","./Matrix3-79d15570","./defaultValue-7b61670d","./TerrainEncoding-70ced8fe","./Math-6acd1674","./OrientedBoundingBox-82cae8c7","./RuntimeError-7dc4ea5a","./WebMercatorProjection-c5288372","./createTaskProcessorWorker","./combine-bc3d0d90","./AttributeCompression-aa106b76","./ComponentDatatype-e95dda25","./WebGLConstants-68839929","./EllipsoidTangentPlane-d7ae8406","./IntersectionTests-044bd161","./Plane-e4eb0e88"],(function(t,e,n,i,o,a,r,s,c,u,h,d,l,g,m,p,I,E){"use strict";const T=Uint16Array.BYTES_PER_ELEMENT,f=Int32Array.BYTES_PER_ELEMENT,C=Uint32Array.BYTES_PER_ELEMENT,M=Float32Array.BYTES_PER_ELEMENT,x=Float64Array.BYTES_PER_ELEMENT;function N(t,e,n){n=o.defaultValue(n,r.CesiumMath);const i=t.length;for(let o=0;o<i;++o)if(n.equalsEpsilon(t[o],e,r.CesiumMath.EPSILON12))return o;return-1}const b=new i.Cartographic,S=new i.Cartesian3,w=new i.Cartesian3,B=new i.Cartesian3,P=new n.Matrix4;function A(t,e,a,s,c,u,h,d,l,g,m){const p=d.length;for(let I=0;I<p;++I){const E=d[I],T=E.cartographic,f=E.index,C=t.length,M=T.longitude;let x=T.latitude;x=r.CesiumMath.clamp(x,-r.CesiumMath.PI_OVER_TWO,r.CesiumMath.PI_OVER_TWO);const N=T.height-h.skirtHeight;h.hMin=Math.min(h.hMin,N),i.Cartographic.fromRadians(M,x,N,b),g&&(b.longitude+=l),g?I===p-1?b.latitude+=m:0===I&&(b.latitude-=m):b.latitude+=l;const w=h.ellipsoid.cartographicToCartesian(b);t.push(w),e.push(N),a.push(n.Cartesian2.clone(a[f])),s.length>0&&s.push(s[f]),c.length>0&&c.push(c[f]),n.Matrix4.multiplyByPoint(h.toENU,w,S);const B=h.minimum,P=h.maximum;i.Cartesian3.minimumByComponent(S,B,B),i.Cartesian3.maximumByComponent(S,P,P);const A=h.lastBorderPoint;if(o.defined(A)){const t=A.index;u.push(t,C-1,C,C,f,t)}h.lastBorderPoint=E}}return h((function(h,d){h.ellipsoid=i.Ellipsoid.clone(h.ellipsoid),h.rectangle=n.Rectangle.clone(h.rectangle);const l=function(h,d,l,g,m,p,I,E,y,R,_){let W,v,F,O,V,Y;o.defined(g)?(W=g.west,v=g.south,F=g.east,O=g.north,V=g.width,Y=g.height):(W=r.CesiumMath.toRadians(m.west),v=r.CesiumMath.toRadians(m.south),F=r.CesiumMath.toRadians(m.east),O=r.CesiumMath.toRadians(m.north),V=r.CesiumMath.toRadians(g.width),Y=r.CesiumMath.toRadians(g.height));const U=[v,O],k=[W,F],H=e.Transforms.eastNorthUpToFixedFrame(d,l),L=n.Matrix4.inverseTransformation(H,P);let D,G;y&&(D=u.WebMercatorProjection.geodeticLatitudeToMercatorAngle(v),G=1/(u.WebMercatorProjection.geodeticLatitudeToMercatorAngle(O)-D));const j=1!==p,z=new DataView(h);let q=Number.POSITIVE_INFINITY,J=Number.NEGATIVE_INFINITY;const K=w;K.x=Number.POSITIVE_INFINITY,K.y=Number.POSITIVE_INFINITY,K.z=Number.POSITIVE_INFINITY;const Q=B;Q.x=Number.NEGATIVE_INFINITY,Q.y=Number.NEGATIVE_INFINITY,Q.z=Number.NEGATIVE_INFINITY;let X,Z,$=0,tt=0,et=0;for(Z=0;Z<4;++Z){let t=$;X=z.getUint32(t,!0),t+=C;const e=r.CesiumMath.toRadians(180*z.getFloat64(t,!0));t+=x,-1===N(k,e)&&k.push(e);const n=r.CesiumMath.toRadians(180*z.getFloat64(t,!0));t+=x,-1===N(U,n)&&U.push(n),t+=2*x;let i=z.getInt32(t,!0);t+=f,tt+=i,i=z.getInt32(t,!0),et+=3*i,$+=X+C}const nt=[],it=[],ot=new Array(tt),at=new Array(tt),rt=new Array(tt),st=y?new Array(tt):[],ct=j?new Array(tt):[],ut=new Array(et),ht=[],dt=[],lt=[],gt=[];let mt=0,pt=0;for($=0,Z=0;Z<4;++Z){X=z.getUint32($,!0),$+=C;const t=$,e=r.CesiumMath.toRadians(180*z.getFloat64($,!0));$+=x;const o=r.CesiumMath.toRadians(180*z.getFloat64($,!0));$+=x;const a=r.CesiumMath.toRadians(180*z.getFloat64($,!0)),s=.5*a;$+=x;const h=r.CesiumMath.toRadians(180*z.getFloat64($,!0)),d=.5*h;$+=x;const g=z.getInt32($,!0);$+=f;const m=z.getInt32($,!0);$+=f,$+=f;const p=new Array(g);for(let t=0;t<g;++t){const c=e+z.getUint8($++)*a;b.longitude=c;const g=o+z.getUint8($++)*h;b.latitude=g;let m=z.getFloat32($,!0);if($+=M,0!==m&&m<_&&(m*=-Math.pow(2,R)),m*=6371010,b.height=m,-1!==N(k,c)||-1!==N(U,g)){const e=N(nt,b,i.Cartographic);if(-1!==e){p[t]=it[e];continue}nt.push(i.Cartographic.clone(b)),it.push(mt)}p[t]=mt,Math.abs(c-W)<s?ht.push({index:mt,cartographic:i.Cartographic.clone(b)}):Math.abs(c-F)<s?lt.push({index:mt,cartographic:i.Cartographic.clone(b)}):Math.abs(g-v)<d?dt.push({index:mt,cartographic:i.Cartographic.clone(b)}):Math.abs(g-O)<d&&gt.push({index:mt,cartographic:i.Cartographic.clone(b)}),q=Math.min(m,q),J=Math.max(m,J),rt[mt]=m;const I=l.cartographicToCartesian(b);if(ot[mt]=I,y&&(st[mt]=(u.WebMercatorProjection.geodeticLatitudeToMercatorAngle(g)-D)*G),j){const t=l.geodeticSurfaceNormal(I);ct[mt]=t}n.Matrix4.multiplyByPoint(L,I,S),i.Cartesian3.minimumByComponent(S,K,K),i.Cartesian3.maximumByComponent(S,Q,Q);let E=(c-W)/(F-W);E=r.CesiumMath.clamp(E,0,1);let T=(g-v)/(O-v);T=r.CesiumMath.clamp(T,0,1),at[mt]=new n.Cartesian2(E,T),++mt}const I=3*m;for(let t=0;t<I;++t,++pt)ut[pt]=p[z.getUint16($,!0)],$+=T;if(X!==$-t)throw new c.RuntimeError("Invalid terrain tile.")}ot.length=mt,at.length=mt,rt.length=mt,y&&(st.length=mt);j&&(ct.length=mt);const It=mt,Et=pt,Tt={hMin:q,lastBorderPoint:void 0,skirtHeight:E,toENU:L,ellipsoid:l,minimum:K,maximum:Q};ht.sort((function(t,e){return e.cartographic.latitude-t.cartographic.latitude})),dt.sort((function(t,e){return t.cartographic.longitude-e.cartographic.longitude})),lt.sort((function(t,e){return t.cartographic.latitude-e.cartographic.latitude})),gt.sort((function(t,e){return e.cartographic.longitude-t.cartographic.longitude}));const ft=1e-5;if(A(ot,rt,at,st,ct,ut,Tt,ht,-ft*V,!0,-ft*Y),A(ot,rt,at,st,ct,ut,Tt,dt,-ft*Y,!1),A(ot,rt,at,st,ct,ut,Tt,lt,ft*V,!0,ft*Y),A(ot,rt,at,st,ct,ut,Tt,gt,ft*Y,!1),ht.length>0&&gt.length>0){const t=ht[0].index,e=It,n=gt[gt.length-1].index,i=ot.length-1;ut.push(n,i,e,e,t,n)}tt=ot.length;const Ct=e.BoundingSphere.fromPoints(ot);let Mt;o.defined(g)&&(Mt=s.OrientedBoundingBox.fromRectangle(g,q,J,l));const xt=new a.EllipsoidalOccluder(l).computeHorizonCullingPointPossiblyUnderEllipsoid(d,ot,q),Nt=new t.AxisAlignedBoundingBox(K,Q,d),bt=new a.TerrainEncoding(d,Nt,Tt.hMin,J,H,!1,y,j,p,I),St=new Float32Array(tt*bt.stride);let wt=0;for(let t=0;t<tt;++t)wt=bt.encode(St,wt,ot[t],at[t],rt[t],void 0,st[t],ct[t]);const Bt=ht.map((function(t){return t.index})).reverse(),Pt=dt.map((function(t){return t.index})).reverse(),At=lt.map((function(t){return t.index})).reverse(),yt=gt.map((function(t){return t.index})).reverse();return Pt.unshift(At[At.length-1]),Pt.push(Bt[0]),yt.unshift(Bt[Bt.length-1]),yt.push(At[0]),{vertices:St,indices:new Uint16Array(ut),maximumHeight:J,minimumHeight:q,encoding:bt,boundingSphere3D:Ct,orientedBoundingBox:Mt,occludeePointInScaledSpace:xt,vertexCountWithoutSkirts:It,indexCountWithoutSkirts:Et,westIndicesSouthToNorth:Bt,southIndicesEastToWest:Pt,eastIndicesNorthToSouth:At,northIndicesWestToEast:yt}}(h.buffer,h.relativeToCenter,h.ellipsoid,h.rectangle,h.nativeRectangle,h.exaggeration,h.exaggerationRelativeHeight,h.skirtHeight,h.includeWebMercatorT,h.negativeAltitudeExponentBias,h.negativeElevationThreshold),g=l.vertices;d.push(g.buffer);const m=l.indices;return d.push(m.buffer),{vertices:g.buffer,indices:m.buffer,numberOfAttributes:l.encoding.stride,minimumHeight:l.minimumHeight,maximumHeight:l.maximumHeight,boundingSphere3D:l.boundingSphere3D,orientedBoundingBox:l.orientedBoundingBox,occludeePointInScaledSpace:l.occludeePointInScaledSpace,encoding:l.encoding,vertexCountWithoutSkirts:l.vertexCountWithoutSkirts,indexCountWithoutSkirts:l.indexCountWithoutSkirts,westIndicesSouthToNorth:l.westIndicesSouthToNorth,southIndicesEastToWest:l.southIndicesEastToWest,eastIndicesNorthToSouth:l.eastIndicesNorthToSouth,northIndicesWestToEast:l.northIndicesWestToEast}}))}));
