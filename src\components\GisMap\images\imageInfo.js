
import point from '@/components/GisMap/images/map/point.png';

import gas_station from '@/components/GisMap/images/map/gas_station.png';
import gas_pipeline_point from '@/components/GisMap/images/map/gas_pipeline_point.png';
import gas_well from '@/components/GisMap/images/map/gas_well.png';
import gas_dangerous_source from '@/components/GisMap/images/map/gas_dangerous_source.png';
import gas_protection_target from '@/components/GisMap/images/map/gas_protection_target.png';
import gas_station_risk1 from '@/components/GisMap/images/map/gas_station_risk1.png';
import gas_station_risk2 from '@/components/GisMap/images/map/gas_station_risk2.png';
import gas_station_risk3 from '@/components/GisMap/images/map/gas_station_risk3.png';
import gas_flowmeter0 from '@/components/GisMap/images/map/gas_flowmeter0.png';
import gas_flowmeter1 from '@/components/GisMap/images/map/gas_flowmeter1.png';
import gas_flowmeter2 from '@/components/GisMap/images/map/gas_flowmeter2.png';
import gas_manometer0 from '@/components/GisMap/images/map/gas_manometer0.png';
import gas_manometer1 from '@/components/GisMap/images/map/gas_manometer1.png';
import gas_manometer2 from '@/components/GisMap/images/map/gas_manometer2.png';
import gas_combustible0 from '@/components/GisMap/images/map/gas_combustible0.png';
import gas_combustible1 from '@/components/GisMap/images/map/gas_combustible1.png';
import gas_combustible2 from '@/components/GisMap/images/map/gas_combustible2.png';
import gas_temperature0 from '@/components/GisMap/images/map/gas_temperature0.png';
import gas_temperature1 from '@/components/GisMap/images/map/gas_temperature1.png';
import gas_temperature2 from '@/components/GisMap/images/map/gas_temperature2.png';

import drainage_pump_station from '@/components/GisMap/images/map/drainage_pump_station.png';
import drainage_sewage_works from '@/components/GisMap/images/map/drainage_sewage_works.png';
import drainage_water_outlet from '@/components/GisMap/images/map/drainage_water_outlet.png';
import drainage_pipeline_point from '@/components/GisMap/images/map/drainage_pipeline_point.png';
import drainage_well from '@/components/GisMap/images/map/drainage_well.png';
import drainage_flooding_point from '@/components/GisMap/images/map/drainage_flooding_point.png';
import drainage_dangerous_source from '@/components/GisMap/images/map/drainage_dangerous_source.png';
import drainage_protection_target from '@/components/GisMap/images/map/drainage_protection_target.png';
import drainage_sewage_risk1 from '@/components/GisMap/images/map/drainage_sewage_risk1.png';
import drainage_sewage_risk2 from '@/components/GisMap/images/map/drainage_sewage_risk2.png';
import drainage_sewage_risk3 from '@/components/GisMap/images/map/drainage_sewage_risk3.png';
import drainage_pump_risk1 from '@/components/GisMap/images/map/drainage_pump_risk1.png';
import drainage_pump_risk2 from '@/components/GisMap/images/map/drainage_pump_risk2.png';
import drainage_pump_risk3 from '@/components/GisMap/images/map/drainage_pump_risk3.png';
import drainage_level0 from '@/components/GisMap/images/map/drainage_level0.png';
import drainage_level1 from '@/components/GisMap/images/map/drainage_level1.png';
import drainage_level2 from '@/components/GisMap/images/map/drainage_level2.png';
import drainage_flowmeter0 from '@/components/GisMap/images/map/drainage_flowmeter0.png';
import drainage_flowmeter1 from '@/components/GisMap/images/map/drainage_flowmeter1.png';
import drainage_flowmeter2 from '@/components/GisMap/images/map/drainage_flowmeter2.png';
import drainage_rain0 from '@/components/GisMap/images/map/drainage_rain0.png';
import drainage_rain1 from '@/components/GisMap/images/map/drainage_rain1.png';
import drainage_rain2 from '@/components/GisMap/images/map/drainage_rain2.png';
import drainage_water_quality0 from '@/components/GisMap/images/map/drainage_water_quality0.png';
import drainage_water_quality1 from '@/components/GisMap/images/map/drainage_water_quality1.png';
import drainage_water_quality2 from '@/components/GisMap/images/map/drainage_water_quality2.png';

import heating_pipeline_point from '@/components/GisMap/images/map/heating_pipeline_point.png';
import heating_well from '@/components/GisMap/images/map/heating_well.png';
import heating_enterprise from '@/components/GisMap/images/map/heating_enterprise.png';
import heating_source_works from '@/components/GisMap/images/map/heating_source_works.png';
import heating_station from '@/components/GisMap/images/map/heating_station.png';
import heating_user from '@/components/GisMap/images/map/heating_user.png';
import heating_dangerous_source from '@/components/GisMap/images/map/heating_dangerous_source.png';
import heating_protection_target from '@/components/GisMap/images/map/heating_protection_target.png';
import heating_source_risk1 from '@/components/GisMap/images/map/heating_source_risk1.png';
import heating_source_risk2 from '@/components/GisMap/images/map/heating_source_risk2.png';
import heating_source_risk3 from '@/components/GisMap/images/map/heating_source_risk3.png';
import heating_station_risk1 from '@/components/GisMap/images/map/heating_station_risk1.png';
import heating_station_risk2 from '@/components/GisMap/images/map/heating_station_risk2.png';
import heating_station_risk3 from '@/components/GisMap/images/map/heating_station_risk3.png';

import bridge_info from '@/components/GisMap/images/map/bridge_info.png';
import bridge_safety_rating from '@/components/GisMap/images/map/bridge_safety_rating.png';
import bridge_wind_speed0 from '@/components/GisMap/images/map/bridge_wind_speed0.png';
import bridge_wind_speed1 from '@/components/GisMap/images/map/bridge_wind_speed1.png';
import bridge_wind_speed2 from '@/components/GisMap/images/map/bridge_wind_speed2.png';
import bridge_temperature0 from '@/components/GisMap/images/map/bridge_temperature0.png';
import bridge_temperature1 from '@/components/GisMap/images/map/bridge_temperature1.png';
import bridge_temperature2 from '@/components/GisMap/images/map/bridge_temperature2.png';
import bridge_humidity0 from '@/components/GisMap/images/map/bridge_humidity0.png';
import bridge_humidity1 from '@/components/GisMap/images/map/bridge_humidity1.png';
import bridge_humidity2 from '@/components/GisMap/images/map/bridge_humidity2.png';
import bridge_vibration0 from '@/components/GisMap/images/map/bridge_vibration0.png';
import bridge_vibration1 from '@/components/GisMap/images/map/bridge_vibration1.png';
import bridge_vibration2 from '@/components/GisMap/images/map/bridge_vibration2.png';
import bridge_dynamic_weight0 from '@/components/GisMap/images/map/bridge_dynamic_weight0.png';
import bridge_dynamic_weight1 from '@/components/GisMap/images/map/bridge_dynamic_weight1.png';
import bridge_dynamic_weight2 from '@/components/GisMap/images/map/bridge_dynamic_weight2.png';
import bridge_displacement0 from '@/components/GisMap/images/map/bridge_displacement0.png';
import bridge_displacement1 from '@/components/GisMap/images/map/bridge_displacement1.png';
import bridge_displacement2 from '@/components/GisMap/images/map/bridge_displacement2.png';
import bridge_tilt0 from '@/components/GisMap/images/map/bridge_tilt0.png';
import bridge_tilt1 from '@/components/GisMap/images/map/bridge_tilt1.png';
import bridge_tilt2 from '@/components/GisMap/images/map/bridge_tilt2.png';
import bridge_deflection0 from '@/components/GisMap/images/map/bridge_deflection0.png';
import bridge_deflection1 from '@/components/GisMap/images/map/bridge_deflection1.png';
import bridge_deflection2 from '@/components/GisMap/images/map/bridge_deflection2.png';
import bridge_strain0 from '@/components/GisMap/images/map/bridge_strain0.png';
import bridge_strain1 from '@/components/GisMap/images/map/bridge_strain1.png';
import bridge_strain2 from '@/components/GisMap/images/map/bridge_strain2.png';
import bridge_load_cell0 from '@/components/GisMap/images/map/bridge_load_cell0.png';
import bridge_load_cell1 from '@/components/GisMap/images/map/bridge_load_cell1.png';
import bridge_load_cell2 from '@/components/GisMap/images/map/bridge_load_cell2.png';
import bridge_acceleration0 from '@/components/GisMap/images/map/bridge_acceleration0.png';
import bridge_acceleration1 from '@/components/GisMap/images/map/bridge_acceleration1.png';
import bridge_acceleration2 from '@/components/GisMap/images/map/bridge_acceleration2.png';
import bridge_crack0 from '@/components/GisMap/images/map/bridge_crack0.png';
import bridge_crack1 from '@/components/GisMap/images/map/bridge_crack1.png';
import bridge_crack2 from '@/components/GisMap/images/map/bridge_crack2.png';
import bridge_tilt_alarm0 from '@/components/GisMap/images/map/bridge_tilt_alarm0.png';
import bridge_tilt_alarm1 from '@/components/GisMap/images/map/bridge_tilt_alarm1.png';
import bridge_tilt_alarm2 from '@/components/GisMap/images/map/bridge_tilt_alarm2.png';
import bridge_crack_sensor0 from '@/components/GisMap/images/map/bridge_crack_sensor0.png';
import bridge_crack_sensor1 from '@/components/GisMap/images/map/bridge_crack_sensor1.png';
import bridge_crack_sensor2 from '@/components/GisMap/images/map/bridge_crack_sensor2.png';
import bridge_vibration_sensor0 from '@/components/GisMap/images/map/bridge_vibration_sensor0.png';
import bridge_vibration_sensor1 from '@/components/GisMap/images/map/bridge_vibration_sensor1.png';
import bridge_vibration_sensor2 from '@/components/GisMap/images/map/bridge_vibration_sensor2.png';
import bridge_static_level0 from '@/components/GisMap/images/map/bridge_static_level0.png';
import bridge_static_level1 from '@/components/GisMap/images/map/bridge_static_level1.png';
import bridge_static_level2 from '@/components/GisMap/images/map/bridge_static_level2.png';

import alarm1 from '@/components/GisMap/images/map/alarm1.png';
import alarm2 from '@/components/GisMap/images/map/alarm2.png';
import alarm3 from '@/components/GisMap/images/map/alarm3.png';
import alarm4 from '@/components/GisMap/images/map/alarm4.png';
import manhole_cover0 from '@/components/GisMap/images/map/manhole_cover0.png';
import manhole_cover1 from '@/components/GisMap/images/map/manhole_cover1.png';
import manhole_cover2 from '@/components/GisMap/images/map/manhole_cover2.png';
import video0 from '@/components/GisMap/images/map/video0.png';
import video1 from '@/components/GisMap/images/map/video1.png';
import video2 from '@/components/GisMap/images/map/video2.png';

// **监测设备图标 0离线，1在线，2报警
export const imageInfo = {
  point: point,
  gas_station: gas_station,
  gas_pipeline_point: gas_pipeline_point,
  gas_well: gas_well,
  gas_dangerous_source: gas_dangerous_source,
  gas_protection_target: gas_protection_target,
  gas_station_risk1: gas_station_risk1,
  gas_station_risk2: gas_station_risk2,
  gas_station_risk3: gas_station_risk3,
  gas_station_risk4: gas_station,
  gas_flowmeter0: gas_flowmeter0,
  gas_flowmeter1: gas_flowmeter1,
  gas_flowmeter2: gas_flowmeter2,
  gas_manometer0: gas_manometer0,
  gas_manometer1: gas_manometer1,
  gas_manometer2: gas_manometer2,
  gas_combustible0: gas_combustible0,
  gas_combustible1: gas_combustible1,
  gas_combustible2: gas_combustible2,
  gas_temperature0: gas_temperature0,
  gas_temperature1: gas_temperature1,
  gas_temperature2: gas_temperature2,
  gas_manhole_cover0: manhole_cover0,
  gas_manhole_cover1: manhole_cover1,
  gas_manhole_cover2: manhole_cover2,
  gas_video0: video0,
  gas_video1: video1,
  gas_video2: video2,
  gas_alarm1: alarm1,
  gas_alarm2: alarm2,
  gas_alarm3: alarm3,
  drainage_pump_station: drainage_pump_station,
  drainage_sewage_works: drainage_sewage_works,
  drainage_water_outlet: drainage_water_outlet,
  drainage_pipeline_point: drainage_pipeline_point,
  drainage_well: drainage_well,
  drainage_flooding_point: drainage_flooding_point,
  drainage_dangerous_source: drainage_dangerous_source,
  drainage_protection_target: drainage_protection_target,
  drainage_sewage_risk1: drainage_sewage_risk1,
  drainage_sewage_risk2: drainage_sewage_risk2,
  drainage_sewage_risk3: drainage_sewage_risk3,
  drainage_sewage_risk4: drainage_sewage_works,
  drainage_pump_risk1: drainage_pump_risk1,
  drainage_pump_risk2: drainage_pump_risk2,
  drainage_pump_risk3: drainage_pump_risk3,
  drainage_pump_risk4: drainage_pump_station,
  drainage_hidden_risk1: alarm1,
  drainage_hidden_risk2: alarm2,
  drainage_hidden_risk3: alarm3,
  drainage_level0: drainage_level0,
  drainage_level1: drainage_level1,
  drainage_level2: drainage_level2,
  drainage_flowmeter0: drainage_flowmeter0,
  drainage_flowmeter1: drainage_flowmeter1,
  drainage_flowmeter2: drainage_flowmeter2,
  drainage_rain0: drainage_rain0,
  drainage_rain1: drainage_rain1,
  drainage_rain2: drainage_rain2,
  drainage_water_quality0: drainage_water_quality0,
  drainage_water_quality1: drainage_water_quality1,
  drainage_water_quality2: drainage_water_quality2,
  drainage_combustible0: gas_combustible0,
  drainage_combustible1: gas_combustible1,
  drainage_combustible2: gas_combustible2,
  drainage_manhole_cover0: manhole_cover0,
  drainage_manhole_cover1: manhole_cover1,
  drainage_manhole_cover2: manhole_cover2,
  drainage_video0: video0,
  drainage_video1: video1,
  drainage_video2: video2,
  drainage_alarm1: alarm1,
  drainage_alarm2: alarm2,
  drainage_alarm3: alarm3,
  heating_pipeline_point: heating_pipeline_point,
  heating_well: heating_well,
  heating_enterprise: heating_enterprise,
  heating_source_works: heating_source_works,
  heating_station: heating_station,
  heating_user: heating_user,
  heating_dangerous_source: heating_dangerous_source,
  heating_protection_target: heating_protection_target,
  heating_source_risk1: heating_source_risk1,
  heating_source_risk2: heating_source_risk2,
  heating_source_risk3: heating_source_risk3,
  heating_source_risk4: heating_source_works,
  heating_station_risk1: heating_station_risk1,
  heating_station_risk2: heating_station_risk2,
  heating_station_risk3: heating_station_risk3,
  heating_station_risk4: heating_station,
  heating_hidden_risk1: alarm1,
  heating_hidden_risk2: alarm2,
  heating_hidden_risk3: alarm3,
  heating_level0: drainage_level0,
  heating_level1: drainage_level1,
  heating_level2: drainage_level2,
  heating_flowmeter0: drainage_flowmeter0,
  heating_flowmeter1: drainage_flowmeter1,
  heating_flowmeter2: drainage_flowmeter2,
  heating_rain0: drainage_rain0,
  heating_rain1: drainage_rain1,
  heating_rain2: drainage_rain2,
  heating_water_quality0: drainage_water_quality0,
  heating_water_quality1: drainage_water_quality1,
  heating_water_quality2: drainage_water_quality2,
  heating_combustible0: gas_combustible0,
  heating_combustible1: gas_combustible1,
  heating_combustible2: gas_combustible2,
  heating_temperature0: gas_temperature0,
  heating_temperature1: gas_temperature1,
  heating_temperature2: gas_temperature2,
  heating_manhole_cover0: manhole_cover0,
  heating_manhole_cover1: manhole_cover1,
  heating_manhole_cover2: manhole_cover2,
  heating_video0: video0,
  heating_video1: video1,
  heating_video2: video2,
  heating_alarm1: alarm1,
  heating_alarm2: alarm2,
  heating_alarm3: alarm3,
  bridge_info: bridge_info,
  bridge_safety_rating: bridge_safety_rating,
  bridge_wind_speed0: bridge_wind_speed0,
  bridge_wind_speed1: bridge_wind_speed1,
  bridge_wind_speed2: bridge_wind_speed2,
  bridge_temperature0: bridge_temperature0,
  bridge_temperature1: bridge_temperature1,
  bridge_temperature2: bridge_temperature2,
  bridge_humidity0: bridge_humidity0,
  bridge_humidity1: bridge_humidity1,
  bridge_humidity2: bridge_humidity2,
  bridge_vibration0: bridge_vibration0,
  bridge_vibration1: bridge_vibration1,
  bridge_vibration2: bridge_vibration2,
  bridge_dynamic_weight0: bridge_dynamic_weight0,
  bridge_dynamic_weight1: bridge_dynamic_weight1,
  bridge_dynamic_weight2: bridge_dynamic_weight2,
  bridge_displacement0: bridge_displacement0,
  bridge_displacement1: bridge_displacement1,
  bridge_displacement2: bridge_displacement2,
  bridge_tilt0: bridge_tilt0,
  bridge_tilt1: bridge_tilt1,
  bridge_tilt2: bridge_tilt2,
  bridge_deflection0: bridge_deflection0,
  bridge_deflection1: bridge_deflection1,
  bridge_deflection2: bridge_deflection2,
  bridge_strain0: bridge_strain0,
  bridge_strain1: bridge_strain1,
  bridge_strain2: bridge_strain2,
  bridge_load_cell0: bridge_load_cell0,
  bridge_load_cell1: bridge_load_cell1,
  bridge_load_cell2: bridge_load_cell2,
  bridge_acceleration0: bridge_acceleration0,
  bridge_acceleration1: bridge_acceleration1,
  bridge_acceleration2: bridge_acceleration2,
  bridge_crack0: bridge_crack0,
  bridge_crack1: bridge_crack1,
  bridge_crack2: bridge_crack2,
  bridge_tilt_alarm0: bridge_tilt_alarm0,
  bridge_tilt_alarm1: bridge_tilt_alarm1,
  bridge_tilt_alarm2: bridge_tilt_alarm2,
  bridge_crack_sensor0: bridge_crack_sensor0,
  bridge_crack_sensor1: bridge_crack_sensor1,
  bridge_crack_sensor2: bridge_crack_sensor2,
  bridge_vibration_sensor0: bridge_vibration_sensor0,
  bridge_vibration_sensor1: bridge_vibration_sensor1,
  bridge_vibration_sensor2: bridge_vibration_sensor2,
  bridge_static_level0: bridge_static_level0,
  bridge_static_level1: bridge_static_level1,
  bridge_static_level2: bridge_static_level2,
  bridge_video0: video0,
  bridge_video1: video1,
  bridge_video2: video2,
};
