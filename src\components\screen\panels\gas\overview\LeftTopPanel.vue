<template>
  <PanelBox title="基础设施">
    <div class="panel-content">
      <div class="infrastructure-grid">
        <div class="infrastructure-item">
          <div class="item-icon">
            <img src="@/assets/images/screen/gas/jichusheshi.png" alt="管线长度">
          </div>
          <div class="item-info">
            <div class="item-label">管线长度</div>
            <div class="item-value">
              <span class="value-number">{{ infrastructureData.pipelineLength.toFixed(2) }}</span>
              <span class="value-unit">公里</span>
            </div>
          </div>
        </div>
        <div class="infrastructure-item">
          <div class="item-icon">
            <img src="@/assets/images/screen/gas/jichusheshi.png" alt="管点">
          </div>
          <div class="item-info">
            <div class="item-label">管点</div>
            <div class="item-value">
              <span class="value-number">{{ infrastructureData.pipePoints }}</span>
              <span class="value-unit">个</span>
            </div>
          </div>
        </div>
        <div class="infrastructure-item">
          <div class="item-icon">
            <img src="@/assets/images/screen/gas/jichusheshi.png" alt="场站">
          </div>
          <div class="item-info">
            <div class="item-label">场站</div>
            <div class="item-value">
              <span class="value-number">{{ infrastructureData.stations }}</span>
              <span class="value-unit">座</span>
            </div>
          </div>
        </div>
        <div class="infrastructure-item">
          <div class="item-icon">
            <img src="@/assets/images/screen/gas/jichusheshi.png" alt="窨井">
          </div>
          <div class="item-info">
            <div class="item-label">窨井</div>
            <div class="item-value">
              <span class="value-number">{{ infrastructureData.manholes }}</span>
              <span class="value-unit">个</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </PanelBox>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import PanelBox from '@/components/screen/PanelBox.vue'
import { getInfrastructureStatistics } from '@/api/gas'

// 综合态势总览左上面板组件
const infrastructureData = ref({
  pipelineLength: 0,
  pipePoints: 0,
  stations: 0,
  manholes: 0
})

onMounted(async () => {
  try {
    const res = await getInfrastructureStatistics()
    if (res.code == 200) {
      infrastructureData.value = {
        pipelineLength: res.data.pipelineTotalLength, // 转换为公里
        pipePoints: res.data.pointTotalCount,
        stations: res.data.stationTotalCount,
        manholes: res.data.wellTotalCount
      }
    }else{
      console.log(res.message)
    }

  } catch (error) {
    console.error('获取基础设施统计数据失败:', error)
  }
})
</script>

<style scoped>
.panel-content {
  flex: 1;
  padding: 20px 0px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.infrastructure-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 10px;
  width: 100%;
  height: 100%;
}

.infrastructure-item {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 0;
  max-width: 180px;
  margin: 0 auto;
}

.item-icon {
  width: 100px;
  height: 94px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
}

.item-icon img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.item-info {
  display: flex;
  flex-direction: column;
  min-width: 70px;
  overflow: visible;
}

.item-label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 18px;
  color: #D3E5FF;
  margin-bottom: 8px;
  white-space: nowrap;
}

.item-value {
  display: flex;
  align-items: baseline;
  white-space: nowrap;
  justify-content: space-between;
}

.value-number {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 24px;
  color: #FFFFFF;
}

.value-unit {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: rgba(129, 178, 254, 0.6);
  margin-left: 2px;
}

@media (max-width: 1600px) {
  .infrastructure-grid {
    gap: 15px 10px;
  }

  .item-icon {
    width: 90px;
    height: 84px;
    margin-right: 10px;
  }

  .item-label {
    font-size: 16px;
  }

  .value-number {
    font-size: 22px;
  }

  .value-unit {
    font-size: 14px;
  }
}

/* 940px左右高度的屏幕特别优化 */
@media (min-height: 940px) and (max-height: 1055px) {
  .panel-content {
    padding: 10px 0px;
  }

  .infrastructure-grid {
    gap: 30px;
  }

  .infrastructure-item {
    max-width: 160px;
  }

  .item-icon {
    width: 80px;
    height: 74px;
    margin-right: 8px;
  }

  .item-label {
    font-size: 14px;
    margin-bottom: 4px;
  }

  .value-number {
    font-size: 20px;
  }

  .value-unit {
    font-size: 12px;
  }
}
</style>