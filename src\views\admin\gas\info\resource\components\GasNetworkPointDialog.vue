<template>
  <el-dialog v-model="dialogVisible" :title="dialogTitle" width="1000px" :close-on-click-modal="false"
    :before-close="handleClose" class="gas-network-point-dialog">
    <el-form ref="formRef" :model="formData" :rules="formRules" label-width="120px" :disabled="mode === 'view'">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="管点编码" prop="pointCode">
            <el-input v-model="formData.pointCode" placeholder="请输入管点编码" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="管点类型" prop="pointType">
            <el-select v-model="formData.pointType" placeholder="请选择" class="w-full">
              <el-option v-for="item in pointTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="埋深 (m)" prop="depth">
            <el-input v-model="formData.depth" placeholder="请输入埋深" type="number" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="高程 (m)" prop="elevation">
            <el-input v-model="formData.elevation" placeholder="请输入高程" type="number" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="附属物" prop="attachedFacilities">
            <el-input v-model="formData.attachedFacilities" placeholder="请输入附属物" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所在道路" prop="roadName">
            <el-input v-model="formData.roadName" placeholder="请输入所在道路" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="关联管线" prop="connectedPipeline">
            <el-select v-model="formData.connectedPipeline" placeholder="请选择" class="w-full" @change="handlePipelineChange">
              <el-option label="请选择" value="" />
              <el-option v-for="item in pipelineOptions" :key="item.id" :label="item.pipelineCode" :value="item.pipelineCode" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="关联窨井" prop="connectedWell">
            <el-select v-model="formData.connectedWell" placeholder="请选择" class="w-full" @change="handleWellChange">
              <el-option label="请选择" value="" />
              <el-option v-for="item in wellOptions" :key="item.id" :label="item.wellCode" :value="item.wellCode" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="权属单位" prop="managementUnit">
            <el-select v-model="formData.managementUnit" placeholder="请选择" class="w-full">
              <el-option v-for="unit in managementUnits" :key="unit.id" :label="unit.enterpriseName"
                :value="unit.enterpriseName" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="安装时间" prop="installTime">
            <el-date-picker v-model="formData.installTime" type="date" placeholder="请选择安装时间" format="YYYY-MM-DD"
              value-format="YYYY-MM-DD" class="w-full" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="地理位置">
            <div class="flex items-center">
              <el-input v-model="formData.longitude" placeholder="经度" class="mr-2" />
              <el-input v-model="formData.latitude" placeholder="纬度" />
              <el-button type="primary" class="ml-2" icon="Location" circle @click="openMapPicker"
                :disabled="mode === 'view'"></el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="所属区域">
            <div class="flex items-center">
              <el-cascader v-model="formData.town" :options="areaOptions" :props="{
                value: 'code',
                label: 'name',
                children: 'children'
              }" placeholder="请选择所属区域" class="w-full" @change="handleAreaChange" />
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="使用状态" prop="usageStatus">
            <el-select v-model="formData.usageStatus" placeholder="请选择" class="w-full">
              <el-option v-for="item in usageStatusOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="mode !== 'view'">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted, onUnmounted, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import { saveGasNetworkPoint, updateGasNetworkPoint, getManagementUnits, getGasPipelineList,getGasWellList } from '@/api/gas';
import { POINT_TYPE, POINT_TYPE_MAP, USAGE_STATUS, AREA_OPTIONS } from '@/constants/gas';
import moment from 'moment';
import { collectShow } from "@/hooks/gishooks";
import bus from '@/utils/mitt';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add', // 'add', 'edit', 'view'
    validator: (value) => ['add', 'edit', 'view'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'success']);

// 表单引用
const formRef = ref(null);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 对话框标题
const dialogTitle = computed(() => {
  const titles = {
    add: '新增',
    edit: '编辑',
    view: '详情'
  };
  return titles[props.mode] || '管点信息';
});

// 表单数据
const formData = reactive({
  id: '',
  pointCode: '',
  pointType: '',
  pointTypeName: '',
  depth: '',
  elevation: '',
  attachedFacilities: '',
  roadName: '',
  connectedPipeline: '',
  connectedPipelineId: '',
  connectedWell: '',
  connectedWellId: '',
  managementUnit: '',
  managementUnitName: '',
  installTime: '',
  county: '',
  countyName: '',
  town: '',
  townName: '',
  address: '',
  longitude: '',
  latitude: '',
  usageStatus: '',
  usageStatusName: ''
});

// 表单验证规则
const formRules = {
  pointCode: [{ required: true, message: '请输入管点编码', trigger: 'blur' }],
  pointType: [{ required: true, message: '请选择管点类型', trigger: 'change' }],
  depth: [{ required: true, message: '请输入埋深', trigger: 'blur' }],
  managementUnit: [{ required: true, message: '请选择权属单位', trigger: 'change' }],
  county: [{ required: true, message: '请选择区县', trigger: 'change' }],
  town: [{ required: true, message: '请选择乡镇', trigger: 'change' }],
  longitude: [{ required: true, message: '请输入经度', trigger: 'blur' }],
  latitude: [{ required: true, message: '请输入纬度', trigger: 'blur' }],
  usageStatus: [{ required: true, message: '请选择使用状态', trigger: 'change' }]
};

// 管点类型选项
const pointTypeOptions = Object.entries(POINT_TYPE).map(([key, value]) => ({
  label: POINT_TYPE_MAP[value],
  value
}));

// 使用状态选项
const usageStatusOptions = USAGE_STATUS;

// 管线选项
const pipelineOptions = ref([]);

// 获取管线列表
const fetchPipelineList = async () => {
  try {
    const res = await getGasPipelineList({});
    if (res && res.code === 200) {
      pipelineOptions.value = res.data || [];
    }
  } catch (error) {
    console.error('获取管线列表失败', error);
  }
};

// 窨井选项
const wellOptions = ref([]);

// 获取窨井列表
const fetchWellList = async () => {
  try {
    const res = await getGasWellList({});
    if (res && res.code === 200) {
      wellOptions.value = res.data || [];
    }
  } catch (error) {
    console.error('获取窨井列表失败', error);
  }
};

// 处理窨井选择变化
const handleWellChange = (value) => {
  if (value) {
    const selectedWell = wellOptions.value.find(item => item.wellCode === value);
    if (selectedWell) {
      formData.connectedWellId = selectedWell.id;
    }
  } else {
    formData.connectedWellId = '';
  }
};

// 权属单位列表
const managementUnits = ref([]);

// 行政区划选项
const areaOptions = ref(AREA_OPTIONS);

// 获取权属单位列表
const fetchManagementUnits = async () => {
  try {
    const res = await getManagementUnits();
    if (res && res.code === 200) {
      managementUnits.value = res.data || [];
    }
  } catch (error) {
    console.error('获取权属单位列表失败', error);
  }
};

// 处理区域选择变化
const handleAreaChange = (value) => {
  if (value && value.length > 0) {
    formData.town = value[value.length - 1];
    // 更新区域名称
    const selectedArea = findAreaByCode(areaOptions.value, formData.town);
    if (selectedArea) {
      formData.townName = selectedArea.name;
    }
  }
};

// 根据区域代码查找区域信息
const findAreaByCode = (areas, code) => {
  for (const area of areas) {
    if (area.code === code) {
      return area;
    }
    if (area.children) {
      const found = findAreaByCode(area.children, code);
      if (found) return found;
    }
  }
  return null;
};

// 地图选点事件处理函数
const handleCollectLocation = (params) => {
  nextTick(() => {
    formData.longitude = params.longitude;
    formData.latitude = params.latitude;
  });
};

// 打开地图选点
const openMapPicker = () => {
  collectShow.value = true; // 激活采集点位窗口
  // 先移除可能存在的旧监听器
  bus.off("getCollectLocation", handleCollectLocation);
  // 添加新的监听器
  bus.on("getCollectLocation", handleCollectLocation);
};

// 组件卸载时清理事件监听
onUnmounted(() => {
  bus.off("getCollectLocation", handleCollectLocation);
});

// 监听管点类型变化
watch(() => formData.pointType, (newVal) => {
  if (newVal) {
    formData.pointTypeName = POINT_TYPE_MAP[newVal] || '';
  } else {
    formData.pointTypeName = '';
  }
}, { immediate: true });

// 监听使用状态变化
watch(() => formData.usageStatus, (newVal) => {
  if (newVal) {
    const status = USAGE_STATUS.find(item => item.value === newVal);
    if (status) {
      formData.usageStatusName = status.label;
    } else {
      formData.usageStatusName = '';
    }
  } else {
    formData.usageStatusName = '';
  }
}, { immediate: true });

// 监听props.data变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    // 复制数据到表单
    Object.keys(formData).forEach(key => {
      if (newVal[key] !== undefined) {
        formData[key] = newVal[key];
      }
    });
  }
}, { immediate: true, deep: true });

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  // 重置表单
  if (formRef.value) {
    formRef.value.resetFields();
  }
  // 重置表单数据
  Object.keys(formData).forEach(key => {
    if (key === 'usageStatus') {
      formData[key] = 5001;
    } else if (typeof formData[key] === 'number') {
      formData[key] = 0;
    } else if (key === 'installTime') {
      formData[key] = null;
    } else {
      formData[key] = '';
    }
  });
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();

    // 准备提交数据
    const submitData = { ...formData };

    // 确保经纬度为数值类型
    if (submitData.longitude) submitData.longitude = Number(submitData.longitude);
    if (submitData.latitude) submitData.latitude = Number(submitData.latitude);

    // 提交数据
    let res;
    if (props.mode === 'add') {
      res = await saveGasNetworkPoint(submitData);
    } else if (props.mode === 'edit') {
      res = await updateGasNetworkPoint(submitData);
    }

    if (res && res.code === 200) {
      ElMessage.success(props.mode === 'add' ? '新增成功' : '更新成功');
      emit('success');
      handleClose();
    } else {
      ElMessage.error(res?.msg || (props.mode === 'add' ? '新增失败' : '更新失败'));
    }
  } catch (error) {
    console.error('表单验证失败', error);
  }
};

// 处理管线选择变化
const handlePipelineChange = (value) => {
  if (value) {
    const selectedPipeline = pipelineOptions.value.find(item => item.pipelineCode === value);
    if (selectedPipeline) {
      formData.connectedPipelineId = selectedPipeline.id;
    }
  } else {
    formData.connectedPipelineId = '';
  }
};

// 组件挂载时获取数据
onMounted(() => {
  fetchManagementUnits();
  fetchPipelineList();
  fetchWellList();
});
</script>

<style scoped>
.gas-network-point-dialog {
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "Helvetica Neue", Arial, sans-serif;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-form-item__label::before) {
  content: '*';
  color: #ff4d4f;
  margin-right: 4px;
}

:deep(.el-form-item:not([required]) .el-form-item__label::before) {
  content: '';
  margin-right: 0;
}

:deep(.el-input__inner),
:deep(.el-select__input) {
  border-radius: 6px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 6px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  background-color: #0277FD;
  border-color: #0277FD;
}

:deep(.el-button--primary:hover) {
  background-color: #1890ff;
  border-color: #1890ff;
}

.w-full {
  width: 100%;
}

.location-select {
  width: 120px;
}

.coordinate-input {
  width: 120px;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.ml-2 {
  margin-left: 8px;
}
</style>
