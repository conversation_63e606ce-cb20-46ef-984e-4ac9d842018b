<template>
  <div class="gas-info-resource-protection">
    <!-- 搜索区域 -->
    <ProtectionSearch @search="handleSearch" @reset="handleReset" />
    
    <!-- 按钮区域 -->
    <div class="table-header">
      <div class="button-group">
        <el-button type="primary" class="operation-btn" @click="handleAdd">新增</el-button>
        <el-button type="primary" class="operation-btn" @click="handleImport">导入</el-button>
        <el-button type="primary" class="operation-btn" @click="handleExport">导出</el-button>
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="table-container">
      <el-table
        :data="tableData"
        style="width: 100%"
        :header-cell-style="headerCellStyle"
        :row-class-name="tableRowClassName"
        @row-click="handleRowClick"
        height="calc(100vh - 380px)"
        v-loading="loading"
      >
        <el-table-column label="序号" min-width="60">
          <template #default="{ $index }">
            {{ (currentPage - 1) * pageSize + $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="protectCode" label="防护目标编码" min-width="120" />
        <el-table-column prop="protectName" label="防护目标名称" min-width="120" />
        <el-table-column label="建筑类型" min-width="100">
          <template #default="scope">
            {{ getBuildingTypeName(scope.row.buildingType) }}
          </template>
        </el-table-column>
        <el-table-column label="是否重点防护目标" min-width="120">
          <template #default="scope">
            {{ scope.row.isMajor === '1' ? '是' : '否' }}
          </template>
        </el-table-column>
        <el-table-column prop="buildingArea" label="建筑面积" min-width="100" />
        <el-table-column prop="fullPeopleNumber" label="满负荷人数" min-width="100" />
        <el-table-column prop="buildingYear" label="建筑年份" min-width="100" />
        <el-table-column prop="contactUser" label="联系人" min-width="100" />
        <el-table-column prop="contactInfo" label="联系电话" min-width="120" />
        <el-table-column prop="managementUnitName" label="所属单位" min-width="120" />
        <el-table-column label="位置" min-width="120">
          <template #default="scope">
            <span v-if="scope.row.longitude && scope.row.latitude">
              经度: {{ scope.row.longitude }}, 纬度: {{ scope.row.latitude }}
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" min-width="220" fixed="right" align="center">
          <template #default="scope">
            <div class="operation-btns">
              <div class="operation-btn-row">
                <span class="operation-btn-text" @click.stop="handleEdit(scope.row)">编辑</span>
                <span class="operation-divider">|</span>
                <span class="operation-btn-text" @click.stop="handleDetail(scope.row)">详情</span>
                <span class="operation-divider">|</span>
                <span class="operation-btn-text" @click.stop="handleDelete(scope.row)">删除</span>
                <span class="operation-divider">|</span>
                <span class="operation-btn-text" @click.stop="handleLocation(scope.row)">定位</span>
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 30, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :pager-count="5"
      />
    </div>
    
    <!-- 防护目标对话框 -->
    <ProtectionDialog 
      v-model:visible="dialogVisible"
      :mode="dialogMode"
      :data="currentData"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { ElTable, ElTableColumn, ElButton, ElPagination, ElMessageBox, ElMessage } from 'element-plus';
import ProtectionSearch from './components/ProtectionSearch.vue';
import ProtectionDialog from './components/ProtectionDialog.vue';
import { getProtectionPage, deleteProtection, getProtectionDetail } from '@/api/gas';
import {BUILDING_TYPE_MAP} from '@/constants/gas'
import { misPosition } from '@/hooks/gishooks';

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const tableData = ref([]);

// 加载状态
const loading = ref(false);

// 查询参数
const queryParams = ref({});

// 对话框相关
const dialogVisible = ref(false);
const dialogMode = ref('add'); // 'add', 'edit', 'view'
const currentData = ref({});

// 获取建筑类型名称
const getBuildingTypeName = (type) => {
  return BUILDING_TYPE_MAP[type] || '-';
};

// 处理搜索
const handleSearch = (formData) => {
  queryParams.value = formData;
  currentPage.value = 1;
  fetchProtectionData();
};

// 处理重置
const handleReset = () => {
  queryParams.value = {};
  currentPage.value = 1;
  fetchProtectionData();
};

// 获取防护目标数据
const fetchProtectionData = async () => {
  loading.value = true;
  try {
    const params = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      ...queryParams.value
    };
    
    const res = await getProtectionPage(params);
    if (res && res.data) {
      tableData.value = res.data.records || [];
      total.value = res.data.total || 0;
    }
  } catch (error) {
    console.error('获取防护目标数据失败:', error);
    ElMessage.error('获取防护目标数据失败');
  } finally {
    loading.value = false;
  }
};

// 处理分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val;
  currentPage.value = 1;
  fetchProtectionData();
};

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val;
  fetchProtectionData();
};

// 表头样式
const headerCellStyle = {
  background: '#F4F4F4',
  fontFamily: 'PingFangSC, PingFang SC',
  fontWeight: '500',
  fontSize: '14px',
  color: '#282828',
  height: '64px'
};

// 斑马纹和选中行样式
const tableRowClassName = ({ rowIndex }) => {
  if (rowIndex % 2 === 1) {
    return 'zebra-row';
  }
  return '';
};

// 处理行点击
const handleRowClick = (row) => {
  console.log('行数据:', row);
  if (row.longitude && row.longitude !== '' && row.latitude && row.latitude !== '') {
    misPosition.value = {
      longitude: row.longitude,
      latitude: row.latitude
    };
  }
};

// 操作按钮处理函数
const handleAdd = () => {
  dialogMode.value = 'add';
  currentData.value = {};
  dialogVisible.value = true;
};

const handleImport = () => {
  console.log('导入');
};

const handleExport = () => {
  console.log('导出');
};

const handleEdit = async (row) => {
  try {
    loading.value = true;
    const res = await getProtectionDetail(row.id);
    if (res && res.data) {
      currentData.value = res.data;
      dialogMode.value = 'edit';
      dialogVisible.value = true;
    }
  } catch (error) {
    console.error('获取防护目标详情失败:', error);
    ElMessage.error('获取防护目标详情失败');
  } finally {
    loading.value = false;
  }
};

const handleDetail = async (row) => {
  try {
    loading.value = true;
    const res = await getProtectionDetail(row.id);
    if (res && res.data) {
      currentData.value = res.data;
      dialogMode.value = 'view';
      dialogVisible.value = true;
    }
  } catch (error) {
    console.error('获取防护目标详情失败:', error);
    ElMessage.error('获取防护目标详情失败');
  } finally {
    loading.value = false;
  }
};

const handleDelete = (row) => {
  ElMessageBox.confirm('确定要删除该防护目标吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      loading.value = true;
      await deleteProtection(row.id);
      ElMessage.success('删除成功');
      fetchProtectionData();
    } catch (error) {
      console.error('删除失败:', error);
      ElMessage.error('删除失败');
    } finally {
      loading.value = false;
    }
  }).catch(() => {});
};

const handleLocation = (row) => {
  if (row.longitude && row.longitude !== '' && row.latitude && row.latitude !== '') {
    misPosition.value = {
      longitude: row.longitude,
      latitude: row.latitude
    };
  } else {
    ElMessage.warning('没有经纬度，无法定位！');
  }
};

// 对话框成功提交回调
const handleDialogSuccess = () => {
  fetchProtectionData();
};

onMounted(() => {
  fetchProtectionData();
});
</script>

<style scoped>
.gas-info-resource-protection {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  background-color: white;
}

/* 表格头部样式 */
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.button-group {
  display: flex;
  gap: 8px;
}

.operation-btn {
  width: 80px;
  height: 32px;
  background: #0277FD;
  border-radius: 2px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 14px;
  color: #FFFFFF;
  padding: 0;
}

/* 表格样式 */
.table-container {
  margin-bottom: 16px;
  flex: 1;
  width: 100%;
  overflow: hidden;
}

:deep(.el-table) {
  overflow-x: hidden !important;
}

:deep(.el-table th) {
  background-color: #F4F4F4;
  height: 64px;
}

:deep(.el-table tr) {
  height: 48px;
}

:deep(.zebra-row) {
  background-color: #F8FAFD;
}

:deep(.el-table__row:hover) {
  background: #EEF5FF !important;
}

:deep(.el-table__row.selected-row) {
  background: #EEF5FF !important;
}

.operation-btns {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.operation-btn-row {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 4px 0;
}

.operation-btn-text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #0086FF;
  cursor: pointer;
}

.operation-divider {
  margin: 0 4px;
  color: #CED3DA;
}

/* 分页样式 */
.pagination-container {
  display: flex;
  justify-content: flex-end;
  padding: 0;
  margin-top: 16px;
  min-height: 32px;
}

:deep(.el-pagination) {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #222222;
  padding-right: 0;
}

:deep(.el-pagination .el-pager li) {
  width: 24px;
  height: 24px;
  background: rgba(255,255,255,0.99);
  border-radius: 2px;
  border: 1px solid #EEEEEE;
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.el-pagination .el-pager li.is-active) {
  width: 24px;
  height: 24px;
  background: #0086FF;
  border-radius: 2px;
  color: #FFFFFF;
  border: none;
}

/* 防止表格过长时遮挡分页组件 */
:deep(.el-table__body-wrapper) {
  overflow-y: auto !important;
  min-height: 200px;
}

/* 确保操作列文字不换行 */
:deep(.cell) {
  white-space: nowrap;
}
</style>