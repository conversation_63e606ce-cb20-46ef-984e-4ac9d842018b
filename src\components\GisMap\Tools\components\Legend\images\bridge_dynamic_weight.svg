<?xml version="1.0" encoding="UTF-8"?>
<svg width="18px" height="18px" viewBox="0 0 18 18" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>动态称重系统</title>
    <defs>
        <circle id="path-1" cx="7" cy="7" r="7"></circle>
        <filter x="-21.4%" y="-21.4%" width="142.9%" height="142.9%" filterUnits="objectBoundingBox" id="filter-2">
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="1" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.162200418   0 0 0 0 0.568546468   0 0 0 0 0.902202219  0 0 0 0.5 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="27.9288816%" y1="8.11919591%" x2="71.2992224%" y2="91.9872084%" id="linearGradient-3">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#A5E2FF" offset="16.835118%"></stop>
            <stop stop-color="#83BEFF" offset="71.3150675%"></stop>
            <stop stop-color="#C6E3FF" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="图层&amp;图例" transform="translate(-2089.000000, -328.000000)">
            <g id="编组-13备份-2" transform="translate(2067.000000, 30.000000)">
                <g id="编组-45备份" transform="translate(0.000000, 65.000000)">
                    <g id="编组-2备份-2" transform="translate(24.000000, 148.000000)">
                        <g id="动态称重系统" transform="translate(0.000000, 87.000000)">
                            <g id="正常备份-2">
                                <g id="椭圆形">
                                    <use fill="black" fill-opacity="1" filter="url(#filter-2)" xlink:href="#path-1"></use>
                                    <use fill="#0099FF" fill-rule="evenodd" xlink:href="#path-1"></use>
                                </g>
                                <circle id="椭圆形" stroke="url(#linearGradient-3)" stroke-width="0.823529412" cx="7" cy="7" r="6.58823529"></circle>
                            </g>
                            <g id="编组" transform="translate(3.000000, 3.000000)" fill="#FFFFFF" fill-rule="nonzero">
                                <path d="M4,0 C1.79055573,0 0,1.66727269 0,3.72784867 C0,4.75828723 0.447692472,5.68944633 1.17260949,6.36392433 L4,9 L6.82739051,6.36392433 C7.57832744,5.66304542 7.99989292,4.71552378 8,3.72784867 C7.99721598,1.6671723 6.20644609,0 4,0 Z M5.61302067,4.59506335 C5.27679623,4.1135228 4.70521469,3.79239538 4.03276582,3.79239538 C3.66291894,3.79239538 3.32669451,3.88876372 3.05771496,4.04927724 L4.16725559,4.91609039 C4.46975051,4.94821317 4.70510761,5.20499465 4.70510761,5.4938989 C4.70510761,5.81492594 4.43612807,6.0718078 4.09990363,6.0718078 C3.76367919,6.0718078 3.49469965,5.81502632 3.49469965,5.4938989 C3.49469965,5.42975373 3.52832209,5.39763094 3.52832209,5.33338538 L3.02398544,4.04927724 C2.78862833,4.20979076 2.58689367,4.40242705 2.41878145,4.62708575 L1.20837349,3.9529089 C1.77995503,3.05407335 2.82225078,2.47616445 4.03255167,2.47616445 C5.17571474,2.47616445 6.21790342,3.05407335 6.78948496,3.88876372 L5.61302067,4.59506335 Z" id="形状"></path>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>