<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="1000px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="user-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      :disabled="mode === 'view'"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="用户名称" prop="userName">
            <el-input v-model="formData.userName" placeholder="请输入用户名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="用户编号" prop="userCode">
            <el-input v-model="formData.userCode" placeholder="请输入用户编号" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="用户类型" prop="userType">
            <el-select v-model="formData.userType" placeholder="请选择" class="w-full">
              <el-option v-for="item in userTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所属热源" prop="factoryId">
            <el-select v-model="formData.factoryId" placeholder="根据建筑自动获取" class="w-full" disabled>
              <el-option v-for="item in heatFactoryOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="所属换热站" prop="stationId">
            <el-select v-model="formData.stationId" placeholder="根据建筑自动获取" class="w-full" disabled>
              <el-option v-for="item in heatStationOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所属机组" prop="unitId">
            <el-select v-model="formData.unitId" placeholder="根据建筑自动获取" class="w-full" disabled>
              <el-option v-for="item in unitOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="所属建筑" prop="buildingId">
            <el-select v-model="formData.buildingId" placeholder="请选择" class="w-full" @change="handleBuildingChange">
              <el-option v-for="item in buildingOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="楼层号" prop="floorNo">
            <el-input v-model="formData.floorNo" placeholder="请输入楼层号" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="单元号" prop="unitNo">
            <el-input v-model="formData.unitNo" placeholder="请输入单元号" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="门牌号" prop="houseNo">
            <el-input v-model="formData.houseNo" placeholder="请输入门牌号" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="供热企业" prop="ownershipUnit">
            <el-select v-model="formData.ownershipUnit" placeholder="请选择" class="w-full" @change="handleEnterpriseChange">
              <el-option v-for="item in enterpriseOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="采暖面积" prop="heatingArea">
            <div class="flex items-center">
              <el-input-number v-model="formData.heatingArea" :min="0" :precision="2" class="w-full-unit" />
              <span class="unit-label">m²</span>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="供热方式" prop="heatingType">
            <el-select v-model="formData.heatingType" placeholder="请选择" class="w-full">
              <el-option v-for="item in heatingTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系人" prop="contactPerson">
            <el-input v-model="formData.contactPerson" placeholder="请输入联系人" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="联系电话" prop="contactInfo">
            <el-input v-model="formData.contactInfo" placeholder="请输入联系电话" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="所属位置">
            <div class="flex items-center">
              <el-cascader
                v-model="formData.town"
                :options="areaOptions"
                :props="{
                  value: 'code',
                  label: 'name',
                  children: 'children'
                }"
                placeholder="请选择所属区域"
                class="mr-2 w-full"
                @change="handleAreaChange"
              />
              <el-input v-model="formData.address" placeholder="输入详细地址" />
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="定位">
            <div class="flex items-center">
              <el-input v-model="formData.longitude" placeholder="经度" class="mr-2 w-32" />
              <el-input v-model="formData.latitude" placeholder="纬度" class="w-32" />
              <el-button type="primary" class="ml-2" icon="Location" circle @click="openMapPicker"></el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="备注">
            <el-input v-model="formData.remarks" type="textarea" :rows="3" placeholder="请输入备注信息" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="mode !== 'view'">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted, onUnmounted, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import {
  saveUser,
  updateUser,
  getAllEnterpriseList,
  getAllHeatStationList,
  getAllUnitList,
  getAllBuildingList,
  getHeatFactoryList,
  getHeatStationDetail,
  getUnitDetail
} from '@/api/heating';
import { USER_TYPE_OPTIONS, HEATING_TYPE_OPTIONS } from '@/constants/heating';
import { AREA_OPTIONS } from '@/constants/gas';
import { collectShow } from "@/hooks/gishooks";
import bus from '@/utils/mitt';

// 使用从常量文件导入的选项
const userTypeOptions = USER_TYPE_OPTIONS;
const heatingTypeOptions = HEATING_TYPE_OPTIONS;

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add', // 'add', 'edit', 'view'
    validator: (value) => ['add', 'edit', 'view'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'success']);

// 表单引用
const formRef = ref(null);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 对话框标题
const dialogTitle = computed(() => {
  const titles = {
    add: '新增用户信息',
    edit: '编辑用户信息',
    view: '用户信息详情'
  };
  return titles[props.mode] || '用户信息';
});

// 下拉选项数据
const enterpriseOptions = ref([]);
const heatStationOptions = ref([]);
const unitOptions = ref([]);
const buildingOptions = ref([]);
const heatFactoryOptions = ref([]);

// 行政区划选项
const areaOptions = ref(AREA_OPTIONS);

// 表单数据
const formData = reactive({
  id: '',
  userName: '',
  userCode: '',
  userType: '',
  userTypeName: '',
  factoryId: '',
  stationId: '',
  unitId: '',
  buildingId: '',
  buildingName: '',
  floorNo: '',
  unitNo: '',
  houseNo: '',
  ownershipUnit: '',
  ownershipUnitName: '',
  heatingArea: 0,
  heatingType: '',
  contactPerson: '',
  contactInfo: '',
  address: '',
  longitude: '',
  latitude: '',
  remarks: '',
  city: '',
  county: '',
  countyName: '',
  town: '',
  townName: ''
});

// 表单验证规则
const formRules = {
  userName: [{ required: true, message: '请输入用户名称', trigger: 'blur' }],
  userCode: [{ required: true, message: '请输入用户编号', trigger: 'blur' }],
  userType: [{ required: true, message: '请选择用户类型', trigger: 'change' }],
  buildingId: [{ required: true, message: '请选择所属建筑', trigger: 'change' }],
  ownershipUnit: [{ required: true, message: '请选择供热企业', trigger: 'change' }]
};

// 重置表单
const resetForm = () => {
  Object.keys(formData).forEach(key => {
    if (key === 'heatingArea') {
      formData[key] = 0;
    } else if (typeof formData[key] === 'number') {
      formData[key] = 0;
    } else {
      formData[key] = '';
    }
  });
};

// 更新各字段的名称，基于选中的值
const updateNamesByValues = () => {
  // 用户类型
  const selectedUserType = userTypeOptions.find(item => item.value === formData.userType);
  if (selectedUserType) {
    formData.userTypeName = selectedUserType.label;
  }

  // 供热企业
  const selectedEnterprise = enterpriseOptions.value.find(item => item.value === formData.ownershipUnit);
  if (selectedEnterprise) {
    formData.ownershipUnitName = selectedEnterprise.label;
  }

  // 建筑
  const selectedBuilding = buildingOptions.value.find(item => item.value === formData.buildingId);
  if (selectedBuilding) {
    formData.buildingName = selectedBuilding.label;
  }
};

// 监听props.data变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    Object.keys(formData).forEach(key => {
      if (newVal[key] !== undefined) {
        formData[key] = newVal[key];
      }
    });
  } else if (props.mode === 'add') {
    resetForm();
  }
}, { immediate: true, deep: true });

// 监听下拉框值变化，自动更新对应的名称
watch(() => formData.userType, (val) => {
  if (val) {
    const selected = userTypeOptions.find(item => item.value === val);
    if (selected) {
      formData.userTypeName = selected.label;
    }
  }
});

// 处理建筑变化 - 联动获取其他信息
const handleBuildingChange = async (value) => {
  const selected = buildingOptions.value.find(item => item.value === value);
  if (selected) {
    formData.buildingName = selected.label;
    
    // 根据建筑信息获取其关联的机组、换热站、热源信息
    try {
      // 这里需要从建筑的详细信息中获取相关ID
      // 假设建筑数据中包含stationId、unitId等字段
      const buildingData = selected.data; // 需要在获取建筑列表时包含完整数据
      
      if (buildingData?.stationId) {
        formData.stationId = buildingData.stationId;
        // 根据换热站获取热源信息
        const stationRes = await getHeatStationDetail(buildingData.stationId);
        if (stationRes && stationRes.data && stationRes.data.factoryId) {
          formData.factoryId = stationRes.data.factoryId;
        }
      }
      
      if (buildingData?.unitId) {
        formData.unitId = buildingData.unitId;
      }
    } catch (error) {
      console.error('获取建筑关联信息失败:', error);
    }
  }
};

// 处理企业变化
const handleEnterpriseChange = (value) => {
  const selected = enterpriseOptions.value.find(item => item.value === value);
  if (selected) {
    formData.ownershipUnitName = selected.label;
  }
};

// 处理区域选择变化
const handleAreaChange = (value) => {
  if (value && value.length > 0) {
    formData.town = value[value.length - 1];
    // 更新区域名称
    const selectedArea = findAreaByCode(areaOptions.value, formData.town);
    if (selectedArea) {
      formData.townName = selectedArea.name;
    }
  }
};

// 根据区域代码查找区域信息
const findAreaByCode = (areas, code) => {
  for (const area of areas) {
    if (area.code === code) {
      return area;
    }
    if (area.children) {
      const found = findAreaByCode(area.children, code);
      if (found) return found;
    }
  }
  return null;
};

// 获取供热企业列表
const fetchEnterprises = async () => {
  try {
    const res = await getAllEnterpriseList();
    if (res && res.data) {
      enterpriseOptions.value = res.data.map(item => ({
        label: item.enterpriseName,
        value: item.enterpriseName
      }));
    }
  } catch (error) {
    console.error('获取供热企业列表失败', error);
  }
};

// 获取换热站列表
const fetchHeatStations = async () => {
  try {
    const res = await getAllHeatStationList();
    if (res && res.data) {
      heatStationOptions.value = res.data.map(item => ({
        label: item.stationName,
        value: item.id
      }));
    }
  } catch (error) {
    console.error('获取换热站列表失败', error);
  }
};

// 获取机组列表
const fetchUnits = async () => {
  try {
    const res = await getAllUnitList();
    if (res && res.data) {
      unitOptions.value = res.data.map(item => ({
        label: item.unitName,
        value: item.id
      }));
    }
  } catch (error) {
    console.error('获取机组列表失败', error);
  }
};

// 获取建筑列表
const fetchBuildings = async () => {
  try {
    const res = await getAllBuildingList();
    if (res && res.data) {
      buildingOptions.value = res.data.map(item => ({
        label: item.buildingName,
        value: item.id,
        data: item // 保存完整数据用于联动
      }));
    }
  } catch (error) {
    console.error('获取建筑列表失败', error);
  }
};

// 获取热源列表
const fetchHeatFactories = async () => {
  try {
    const res = await getHeatFactoryList();
    if (res && res.data) {
      heatFactoryOptions.value = res.data.map(item => ({
        label: item.factoryName,
        value: item.id
      }));
    }
  } catch (error) {
    console.error('获取热源列表失败', error);
  }
};

// 打开地图选点
const openMapPicker = () => {
  collectShow.value = true;
  bus.off("getCollectLocation", handleCollectLocation);
  bus.on("getCollectLocation", handleCollectLocation);
};

// 地图选点事件处理函数
const handleCollectLocation = (params) => {
  nextTick(() => {
    formData.longitude = params.longitude;
    formData.latitude = params.latitude;
  });
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  if (formRef.value) {
    formRef.value.resetFields();
  }
  resetForm();
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();

    updateNamesByValues();

    const submitData = { ...formData };

    let res;
    if (props.mode === 'add') {
      res = await saveUser(submitData);
    } else if (props.mode === 'edit') {
      res = await updateUser(submitData);
    }

    if (res && res.code === 200) {
      ElMessage.success(props.mode === 'add' ? '新增成功' : '更新成功');
      emit('success');
      handleClose();
    } else {
      ElMessage.error(res?.msg || (props.mode === 'add' ? '新增失败' : '更新失败'));
    }
  } catch (error) {
    console.error('表单验证失败', error);
  }
};

// 组件卸载时清理事件监听
onUnmounted(() => {
  bus.off("getCollectLocation", handleCollectLocation);
});

// 组件挂载时获取数据
onMounted(() => {
  fetchEnterprises();
  fetchHeatStations();
  fetchUnits();
  fetchBuildings();
  fetchHeatFactories();
});
</script>

<style scoped>
.user-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-form-item__label::before) {
  content: '*';
  color: #ff4d4f;
  margin-right: 4px;
}

:deep(.el-form-item:not([required]) .el-form-item__label::before) {
  content: '';
  margin-right: 0;
}

:deep(.el-input__wrapper),
:deep(.el-select__input) {
  border-radius: 2px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  background-color: #0277FD;
  border-color: #0277FD;
}

:deep(.el-button--primary:hover) {
  background-color: #1890ff;
  border-color: #1890ff;
}

.w-full {
  width: 100%;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.ml-2 {
  margin-left: 8px;
}

.mr-2 {
  margin-right: 8px;
}

.w-32 {
  width: 140px;
}

.w-full-unit {
  width: calc(100% - 40px) !important;
}

.unit-label {
  display: inline-block;
  white-space: nowrap;
  width: 35px;
  margin-left: 5px;
}
</style> 