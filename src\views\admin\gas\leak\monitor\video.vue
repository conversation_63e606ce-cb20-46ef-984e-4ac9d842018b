<template>
  <div class="gas-leak-monitor-video" v-if="!showMapMode">
    <!-- 顶部统计信息 -->
    <div class="status-container">
      <div class="status-cards">
        <div class="status-card bg-blue-light">
          <div class="icon-box">
            <span class="text-blue-500 text-xl">
              <i class="icon-devices"></i>
            </span>
          </div>
          <div class="content">
            <div class="title">全部设备</div>
            <div class="number">2198</div>
          </div>
        </div>

        <div class="status-card bg-green-light">
          <div class="icon-box">
            <span class="text-green-500 text-xl">
              <i class="icon-check"></i>
            </span>
          </div>
          <div class="content">
            <div class="title">正常</div>
            <div class="number">1120</div>
          </div>
        </div>

        <div class="status-card bg-yellow-light">
          <div class="icon-box">
            <span class="text-yellow-500 text-xl">
              <i class="icon-warning"></i>
            </span>
          </div>
          <div class="content">
            <div class="title">离线</div>
            <div class="number">12</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 搜索栏和地图模式按钮 -->
    <div class="flex justify-between mb-4">
      <div class="search-box relative">
        <input
          type="text"
          placeholder="输入监控设备名称"
          class="pl-10 pr-3 py-2 rounded-md border border-gray-300 w-64 focus:outline-none"
        />
        <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
          <i class="icon-search"></i>
        </span>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content-area">
      <!-- 左侧站点列表 -->
      <div class="left-sidebar">
        <!-- 站点列表 -->
        <div class="stations-list mb-4">
          <div class="text-gray-600 mb-2 text-sm">
            *****站点名称
          </div>
          <div class="stations">
            <div
              v-for="(station, index) in stations"
              :key="index"
              class="station-item"
            >
              <input type="checkbox" :checked="station.checked" class="mr-3" />
              <span class="text-gray-700">{{ station.name }}</span>
              <span class="ml-auto text-green-500 text-sm" v-if="station.status === 'online'">在线</span>
              <span class="ml-auto text-gray-400 text-sm" v-else>离线</span>
            </div>
          </div>
        </div>

        <!-- 站点分组 -->
        <div class="stations-group">
          <div class="text-gray-600 mb-2 text-sm">
            *****站点名称场站名称
          </div>
        </div>
      </div>

      <!-- 右侧视频展示区域 -->
      <div class="right-content">
        <!-- 视频标签和地图模式按钮 -->
        <div class="video-header">
          <div class="video-tags">
            <span class="video-tag" v-for="(tag, index) in videoTags" :key="index">
              {{ tag.name }}
              <i class="icon-close"></i>
            </span>
          </div>
          <el-tooltip effect="dark" content="地图模式" placement="top">
             <img
                 class="map-icon"
                 src="@/assets/images/mis/show_map.png"
                 @click="hangleMapMode(true)"
                 alt=""
                />
          </el-tooltip>
        </div>
        <!-- 视频监控区域 -->
        <div>
          <!-- 视频监控网格 -->
          <div class="video-grid">
            <div v-for="(video, index) in videos" :key="index" class="video-item">
              <VideoPlayer
                :src="video.src"
                :autoplay="true"
                :muted="true"
                :loop="true"
                :showControls="true"
                :showStatus="true"
                :isOnline="video.isOnline"
                class="video-player mb-2"
              />
              <div class="video-info">
                <span class="video-title">{{ video.title }}</span>
                <span class="video-status" :class="video.isOnline ? 'status-online' : 'status-offline'">
                  {{ video.isOnline ? '在线' : '离线' }}
                </span>
              </div>
            </div>

            <!-- 离线视频示例 -->
            <div class="video-item">
              <div class="video-offline">
                <div class="wifi-icon">
                  <i class="icon-wifi"></i>
                  <div class="offline-text">暂无预览信息</div>
                </div>
              </div>
              <div class="video-info">
                <span class="video-title">xxx监控视频</span>
                <!-- <span class="video-status status-offline">离线</span> -->
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <el-tooltip v-if="showMapMode" effect="dark" content="列表模式" placement="top">
        <img
                class="map-icon top-icon"
                src="@/assets/images/mis/show_list.png"
                @click="hangleMapMode(false)"
                alt=""
        />
  </el-tooltip>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';
import VideoPlayer from '@/components/screen/common/VideoPlayer.vue';
import emitter from "@/utils/mitt.js";

const router = useRouter();
const showMapMode = ref(false);

// 站点数据
const stations = ref([
  { name: 'xxx视频监控', checked: true, status: 'online' },
  { name: 'xxx视频监控', checked: true, status: 'online' },
  { name: 'xxx视频监控', checked: true, status: 'online' },
  { name: 'xxx视频监控', checked: true, status: 'online' },
  { name: 'xxx视频监控', checked: false, status: 'offline' },
  { name: 'xxx视频监控', checked: true, status: 'online' },
  { name: 'xxx视频监控', checked: false, status: 'offline' },
  { name: 'xxx视频监控', checked: true, status: 'online' }
]);

// 视频标签
const videoTags = ref([
  { name: '视频监控名称1' },
  { name: '视频监控名称2' },
  { name: '视频监控名称3' },
  { name: '视频监控名称4' }
]);

// 视频数据
const videos = ref([
  {
    title: 'xxx监控视频',
    src: 'https://bitdash-a.akamaihd.net/content/MI201109210084_1/m3u8s/f08e80da-bf1d-4e3d-8899-f0f6155f6efa.m3u8',
    isOnline: true
  },
  {
    title: 'xxx监控视频',
    src: 'https://bitdash-a.akamaihd.net/content/MI201109210084_1/m3u8s/f08e80da-bf1d-4e3d-8899-f0f6155f6efa.m3u8',
    isOnline: true
  },
  {
    title: 'xxx监控视频',
    src: 'https://test-streams.mux.dev/x36xhzz/x36xhzz.m3u8',
    isOnline: true
  },
  {
    title: 'xxx监控视频',
    src: 'https://test-streams.mux.dev/x36xhzz/x36xhzz.m3u8',
    isOnline: true
  },
  {
    title: 'xxx监控视频',
    src: 'https://test-streams.mux.dev/x36xhzz/x36xhzz.m3u8',
    isOnline: true
  }
]);

// 切换地图模式
const hangleMapMode = (show) => {
  showMapMode.value = show;
  emitter.emit("handleMisMapShow", show);
};

// 组件卸载时，处理资源释放
onUnmounted(() => {
  // 确保所有视频资源都被释放
});
</script>

<style scoped>
.gas-leak-monitor-video {
  padding: 16px;
}

.status-container {
  width: 100%;
  height: 132px;
  background: #FFFFFF;
  padding: 16px;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
}

.status-cards {
  display: flex;
  gap: 16px;
  width: 100%;
}

.status-card {
  width: 380px;
  height: 100px;
  border-radius: 4px;
  padding: 16px;
  display: flex;
  align-items: center;
}

.bg-blue-light {
  background: #F1F8FF;
}

.bg-green-light {
  background: #E7FFFA;
}

.bg-yellow-light {
  background: #FFF7E7;
}

.icon-box {
  margin-right: 16px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
}

.content {
  display: flex;
  flex-direction: column;
}

.title {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 18px;
  color: #000000;
  margin-bottom: 8px;
}

.number {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 30px;
  color: #000000;
}

/* 主内容区域样式 */
.main-content-area {
  display: flex;
  gap: 16px;
  width: 100%;
}

/* 左侧侧边栏样式 */
.left-sidebar {
  width: 350px;
  height: 743px;
  background: #FFFFFF;
  padding: 16px;
  border-radius: 4px;
  flex-shrink: 0;
}

.stations {
  margin-top: 8px;
}

.station-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

/* 右侧内容区域样式 */
.right-content {
  flex: 1;
  min-width: 0;
  height: 743px;
  background: #FFFFFF;
  padding: 16px;
  border-radius: 4px;
  overflow: auto;
}

.video-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.video-tags {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.video-tag {
  background: #F1F8FF;
  padding: 4px 12px;
  border-radius: 4px;
  font-size: 14px;
  color: #1890ff;
  display: flex;
  align-items: center;
  gap: 4px;
}

.icon-close {
  font-size: 12px;
  cursor: pointer;
}

.video-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

.video-item {
  border-radius: 4px;
  overflow: hidden;
}

.video-player {
  height: 220px;
  border-radius: 4px 4px 0 0;
  overflow: hidden;
}

.video-offline {
  height: 220px;
  background: #f7f7f7;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px 4px 0 0;
}

.wifi-icon {
  text-align: center;
}

.offline-text {
  color: #999;
  margin-top: 8px;
}

.video-info {
  display: flex;
  justify-content: space-between;
  padding: 8px 12px;
  background: #f7f7f7;
  border-radius: 0 0 4px 4px;
}

.video-title {
  font-size: 14px;
  color: #333;
}

.video-status {
  font-size: 12px;
}

.status-online {
  color: #52c41a;
}

.status-offline {
  color: #999;
}

/* 图标样式 */
.icon-devices:before {
  content: "\f108"; /* 使用自定义字体图标代码 */
  font-family: 'FontAwesome';
}

.icon-check:before {
  content: "\f00c";
  font-family: 'FontAwesome';
}

.icon-warning:before {
  content: "\f071";
  font-family: 'FontAwesome';
}

.icon-search:before {
  content: "\f002";
  font-family: 'FontAwesome';
}

.icon-map:before {
  content: "\f279";
  font-family: 'FontAwesome';
}

.icon-wifi:before {
  content: "\f1eb";
  font-family: 'FontAwesome';
}

.icon-close:before {
  content: "\f00d";
  font-family: 'FontAwesome';
}

.map-mode-btn {
  background: #F1F8FF;
  color: #1890ff;
  padding: 4px 12px;
  border-radius: 4px;
  font-size: 14px;
  display: flex;
  align-items: center;
  white-space: nowrap;
}

.map-mode-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #f9f9f9;
  border-radius: 4px;
}

.back-btn {
  margin-top: 16px;
  background: #F1F8FF;
  color: #1890ff;
  padding: 4px 12px;
  border-radius: 4px;
  font-size: 14px;
}
</style> 
