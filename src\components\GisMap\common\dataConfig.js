
const relativePath = './SampleData/';

const serverRootUri = "http://117.133.182.90:1151/";
const SpatialService = "http://117.133.182.90:1331/GSpatialServer/dataServer/baseMap/ctbase";

const serverMapUrl = "http://172.20.130.229:80/"

export const gisSource = {
    serverRootUri: serverRootUri,
    key: {
        IonDefaultAccessToken :"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiJmMmM4Yzc0YS02ZTM4LTQwODItOGI4ZC04OTI3Yzk5MjJlMGEiLCJpZCI6MjU5LCJpYXQiOjE2Njk5MDUxNDh9.tcSYhz3-NLfuOdb1w9wHOw-Ps85-AyR_mBYRHDifEi8",
        tianDiTu: '40a623cbe2c72e883a0c0066ef20e8cd', //'1b363f53f86a0e2b3dfa5f7a121d9850', //天地图 Key,需要去天地图公司注册，此key仅做示例，加载人数过多可能会被限制
        arcGis: 'https://services.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer',
        arcGisDark: 'http://cache1.arcgisonline.cn/arcgis/rest/services/ChinaOnlineStreetPurplishBlue/MapServer',//深色
        dark: 'http://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}.png',//深色影像key
        gaoDeStreetImage: 'http://webrd02.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=1&style=8&x={x}&y={y}&z={z}',//高德街道底图key
        gaoDeImage: 'https://webst02.is.autonavi.com/appmaptile?style=6&x={x}&y={y}&z={z}',//高德影像key
        gaoDeMark: 'http://webst02.is.autonavi.com/appmaptile?x={x}&y={y}&z={z}&lang=zh_cn&size=1&scale=1&style=8',//高德标注key
        openStreet: 'https://a.tile.openstreetmap.org/',//OpenStreet Key
        superMap: 'http://www.supermapol.com/realspace/services/map-China400/rest/maps/China400',//SuperMap影像key
    },
    // 地形
    terrain: {
        beiJing: serverRootUri + "data/dem/beijing",
        suZhou: serverRootUri + "data/dem/suZhouTerrain3",
        quanGuo: serverRootUri + 'data/dem/china',
    },
    // 矢量拉伸体
    boxModel: {
        beiJingBuildings: serverRootUri + 'data/blocksystem/beiJingBuildingWhite/tileset.json', //北京建筑白膜
        beiJingBuildingsGreen: serverRootUri + 'data/blocksystem/beiJingBuildingsGreen/tileset.json',//北京建筑白膜（彩色）
        beiJingBuildingsWenLi: serverRootUri + 'data/blocksystem/beiJingBuildingsWenLi/tileset.json',//北京建筑白膜（建筑纹理）
        beiJingBuildingsJianBian: serverRootUri + 'data/blocksystem/beiJingBuildingsJianBian/tileset.json',//北京建筑白膜（蓝色透明渐变）
        beiJingBuildingsFlow: serverRootUri + 'data/blocksystem/BeijingFlowTexture/tileset.json',//北京矢量带流动纹理
        rongCheng: serverRootUri + 'data/blocksystem/rongcheng/tileset.json',//
        //城市生长（矢量拉体）
        beiJingCityGrowthPart1: serverRootUri + 'data/blocksystem/bJRegion4/tileset.json',
        beiJingCityGrowthPart2: serverRootUri + 'data/blocksystem/bJRegion5/tileset.json',
        beiJingCityGrowthPart3: serverRootUri + 'data/blocksystem/bJRegion6/tileset.json',
    },
    //手工模型
    maxModel: {
        dongMingBridgeModel: serverMapUrl + 'data/model/dmql20250417/tileset.json',

        suZhouModelPartA: serverRootUri + 'data/model/suZhouPartA/tileset.json',
        suzhouModelPartB: serverRootUri + 'data/model/suzhouPartB/tileset.json',
        suZhouModelPartC: serverRootUri + 'data/model/suZhouPartC/tileset.json',
        suZhouReflectionWater: serverRootUri + 'data/model/suzhouBshuichi/tileset.json',//宿州手工模型（倒影水） 不变
        suZhouWaterSurface: serverRootUri + 'data/vector/polygon/suzhoudaoyingshui/layer.json', //宿州倒影水面

        beiJingIndustrialParks: serverRootUri + 'data/model/beiJingIndustrialParkTest/tileset.json',//北京正元产业园
        //北京手工模型(雪天和雨天示例用到)
        beiJingModelPart1: serverRootUri + 'data/model/cbddimian0604/tileset.json',
        beiJingModelPart2: serverRootUri + 'data/model/cbdgaojiaqiao0604/tileset.json',
        beiJingModelPart3: serverRootUri + 'data/model/cbdgujia0605/tileset.json',
        beiJingModelPart4: serverRootUri + 'data/model/cbdjianzhu0604/tileset.json',
        beiJingModelPart5: serverRootUri + 'data/model/cbdjianzhuti0604/tileset.json',
        beiJingModelPart6: serverRootUri + 'data/model/erqijianzhuti0618/tileset.json',
        beiJingModelPart7: serverRootUri + 'data/model/erqigujia0618/tileset.json',
        beiJingModelPart8: serverRootUri + 'data/model/erqidimian0618/tileset.json',
        beiJingModelPart9: serverRootUri + 'data/model/yiqijianzhunew0618/tileset.json',
        beiJingModelPart10: serverRootUri + 'data/model/yiqidimiannew06188/tileset.json',
        // 中央电视塔模型
        beiJingCenterTvTowerPart1: relativePath + 'gltf/dakucha_-2238.gltf',
        beiJingCenterTvTowerPart2: relativePath + 'gltf/outside_-1556.gltf',
        beiJingUnderground: serverRootUri + 'data/model/Newark/tileset.json',// 北京地下手工模型
    },
    //倾斜摄影//无
    osgb: {
        dongMing: serverMapUrl + 'data/osgb/dm20250417/tileset.json',
        suZhou: serverRootUri + 'data/osgb/suzhou1027/tileset.json',
        rongCheng: serverRootUri + 'data/osgb/rongcheng202310251831/tileset.json',// 容城倾斜摄影（倾斜摄影楼栋查询）
        rongChengSingle: serverRootUri + 'data/osgb/rongchengquery/tileset.json',//容城倾斜摄影（倾斜摄影分层分户查询）
    },
    // BIM（建筑信息模型）
    bim: {
        suZhou: serverRootUri + 'data/bim/building8/tileset.json',
        suZhouUUID: "b6efdd69-0009-4e31-a66b-afb76d20a739",

        // suZhou: "http://*************:80/data/bim/wenli810271439/tileset.json",
    },
    serverApi: {
        // bimTreeIp: serverRootUri + 'search?t=4&c=bim_bimmodel',//BIM树构建URL
        commonService: serverMapUrl,//查询分析通用IP
        geoalyService: serverMapUrl + 'geoalsycesium?',//地质剖切的IP
    },
    //影像//无
    image: {
        suZhou: serverRootUri + 'data/dom/suzhou1025/{z}/{x}/{y}.webp',
        changTing: serverRootUri + 'data/dom/changting/{z}/{x}/{y}.png',
        spatialWMS:SpatialService,
        spatialWMTS:SpatialService,
    },
    //管线场景
    pipeLineScenario: {
        suZhouGovernment: serverRootUri + "data/suZhouPipe.xml", // 服务端路径,与上一条内容一致
        burtsAnalysis: serverRootUri + "data/suZhouPipeForBurts.xml",//爆管分析
    },
    // 管线
    pipeLine: { // todo 优化命名
        dongMingWSContainer: serverMapUrl + 'data/block/dmgx20250417/ws/container/tileset.json', // 污水管道
        dongMingWSWell: serverMapUrl + 'data/block/dmgx20250417/ws/well/tileset.json', // 污水管井
        dongMingWSiJoint: serverMapUrl + 'data/block/dmgx20250417/ws/joint/tileset.json', // 污水管点
        dongMingYSContainer: serverMapUrl + 'data/block/dmgx20250417/ys/container/tileset.json', // 雨水管道
        dongMingYSWell: serverMapUrl + 'data/block/dmgx20250417/ys/well/tileset.json', // 雨水管井
        dongMingYSiJoint: serverMapUrl + 'data/block/dmgx20250417/ys/joint/tileset.json', // 雨水管点

        // 流向分析 这份数据专门给流向分析用的 todo 可以简化，不必这样存储
        suZhouWuShuiContainer: serverRootUri + 'data/block/suzhou/ws/container/tileset.json',//管道
        suZhouWuShuiWell: serverRootUri + 'data/block/suzhou/ws/well/tileset.json',//管井
        suZhouWuShuiJoint: serverRootUri + 'data/block/suzhou/ws/joint/tileset.json',//管点
    },
    // 点模型
    pointmodel: {
        //山地种树
        changTingMountainPlantTrees: serverRootUri + 'data/matchmodel/mountainTreesNoTerrain/tileset.json',
        //城市小品（路灯，产业园，树）
        xiongAnStreetLamp: serverRootUri + 'data/matchmodel/samplelight/tileset.json',
        //xiongAnIndustrialPark: serverRootUri + 'data/blocksystem/xionganchanqu/tileset.json',
        xiongAnIndustrialParkTrees: serverRootUri + 'data/matchmodel/sampletree/tileset.json',
    },
    // 地质体
    geostructure: {
        hangZhou: serverRootUri + 'data/geostructure/hangzhou/',
        beiJing: serverRootUri + 'data/geostructure/beijing1025/',
        beijing2:serverRootUri+"/data/beijngGeostructor.xml",
    },
    // 标注
    mark: {
        changTing: serverRootUri + 'data/vector/annotation/ctgongjiao/layer.kml',
    },
    //矢量点
    point: {
        bigPonitData: serverRootUri + 'data/vector/poi/vctrPoint//tileset.json',//海量点数据
        beiJingFluorescent: serverRootUri + 'data/vector/poi/beijingditie/layer.json', //北京荧光点
    },
    //矢量线
    polyline: {
        changTing: serverRootUri + 'data/vector/polyline/ctroad/layer.json',
        bigPolylineData: serverRootUri + 'data/vector/polyline/vctrRoad/tileset.json', //海量线数据
        beiJingSubway: serverRootUri + 'data/vector/polyline/beijingditiexian/layer.json',//北京地铁
    },
    //矢量面
    polygon: {
        bigPolygonData: serverRootUri + 'data/vector/polygon/vctrBuilding/tileset.json',//海量面数据
    },
    // 点云//无
    pointCloud: {
        buildingPnts: serverRootUri + 'data/pointcloud/dianyun/tileset.json',
    },
    // gltf模型
    gltf: {
        geniusMan: relativePath + "gltf/GeniusMan.glb",//正元人模型
        geniusBalloon: relativePath + "gltf/GeniusBalloon.glb",//正元热气球
        airPlane: relativePath + "gltf/Airplane.glb",//飞机
        fountainModel: serverRootUri + 'data/model/penquan2/0/penquan.gltf', //喷泉模型
    },
    // 查询的UUID，可在GManager中获取，或在数据查询时获取，也可根据加载XML数据时获取
    UUID: {
        osgbQuery: "2ca6b640-f24b-443b-bb78-b0ee4b5428c6",
        vectorQuery: "6405f293-d798-4d58-b115-cb4f9f5a5176",
    },
    //SHP数据
    vector: {
        shapefile: {
            polygonShpWuHan: "./SampleData/vector/shapefile/polygonhebei.shp",
            polygonDbfWuHan: "./SampleData/vector/shapefile/polygonhebei.dbf",
        },
        geojson: {
            rongjiang: "./SampleData/vector/geojson/rongjiang.json",
        }
    },
    //GPS数据
    gpx: {
        complexTrk: relativePath + "gpx/complexTrk.gpx",
        lamina: relativePath + "gpx/lamina.gpx",
        route: relativePath + "gpx/route.gpx",
        simple: relativePath + "gpx/simple.gpx",
        wpt: relativePath + "gpx/wpt.gpx",
    },
    // 漫游路线
    flyRoute: {
        sample: "./SampleData/flyRoute/sample000.json"
    },
    czml: {
        earthquake2010: relativePath + "czml/earthquake2010.czml",
    },
    skybox: {
        celestrak: {
            px: relativePath + "skybox/celestrak/00h+00.jpg",
            py: relativePath + "skybox/celestrak/06h+00.jpg",
            pz: relativePath + "skybox/celestrak/06h+90.jpg",
            nx: relativePath + "skybox/celestrak/12h+00.jpg",
            ny: relativePath + "skybox/celestrak/18h+00.jpg",
            nz: relativePath + "skybox/celestrak/06h-90.jpg",
        }
    },
    tle: {
        celestrakTLE: relativePath + "tle/celestrakTLE.txt",
        celestrakCat: relativePath + "tle/satcat.csv",
    },
    waterSurface:{
        yitongRiver:serverRootUri + 'data/waterSurface/YiTongRiver2D_500/HydrologySimulation.json',
        ncdepth:serverRootUri + 'data/hotmap/ncp500/hotmap/HydrologySimulation.json',
        ncstatic:serverRootUri + 'data/hotmap/ncp500/hotmap/HotMap/floodTime.hmz',
    },
    voxel:{
        yitongRiver:"./SampleData/voxel/yitong_depth.nc"
    },
    colormap:{
        qgisColormap:"./SampleData/colormap/colormap_0.qml"
    }
}
