<template>
  <PanelBox title="隐患类型统计" class="right-middle-panel">
    <template #extra>
      <div class="com-select">
        <CommonSelect v-model="timeRange" :options="timeOptions" @change="handleTimeChange" />
      </div>
    </template>
    <div class="panel-content">
      <div class="chart-container" ref="chartRef"></div>
    </div>
  </PanelBox>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, nextTick } from 'vue'
import PanelBox from '@/components/screen/PanelBox.vue'
import CommonSelect from '@/components/screen/CommonSelect.vue'
import * as echarts from 'echarts'

// 时间选择
const timeRange = ref('week')
const timeOptions = [
  { label: '近一周', value: 'week' },
  { label: '近一月', value: 'month' },
  { label: '近一年', value: 'year' }
]

// 雷达图实例
const chartRef = ref(null)
let chartInstance = null

// 不同时间范围的数据
const timeRangeData = {
  week: [
    { name: '管线交叉', value: 25 },
    { name: '第三方施工', value: 30 },
    { name: '井盖丢失', value: 20 },
    { name: '其他', value: 15 },
    { name: '交叉穿越', value: 28 },
    { name: '管网老化', value: 32 },
    { name: '违章占压', value: 22 },
    { name: '安全间距不足', value: 26 }
  ],
  month: [
    { name: '管线交叉', value: 28 },
    { name: '第三方施工', value: 25 },
    { name: '井盖丢失', value: 24 },
    { name: '其他', value: 18 },
    { name: '交叉穿越', value: 30 },
    { name: '管网老化', value: 35 },
    { name: '违章占压', value: 26 },
    { name: '安全间距不足', value: 32 }
  ],
  year: [
    { name: '管线交叉', value: 35 },
    { name: '第三方施工', value: 32 },
    { name: '井盖丢失', value: 28 },
    { name: '其他', value: 22 },
    { name: '交叉穿越', value: 34 },
    { name: '管网老化', value: 38 },
    { name: '违章占压', value: 30 },
    { name: '安全间距不足', value: 36 }
  ]
}

// 当前数据
const currentData = ref(timeRangeData.week)

// 初始化雷达图
const initChart = () => {
  if (!chartRef.value) return

  // 销毁之前的实例
  if (chartInstance) {
    chartInstance.dispose()
  }

  // 确保容器已渲染完成并且有尺寸
  setTimeout(() => {
    chartInstance = echarts.init(chartRef.value)
    updateChartData()
    
    // 添加窗口大小变化的监听器
    window.addEventListener('resize', resizeChart)
  }, 100)  // 延迟100ms确保DOM完全渲染
}

// 更新图表数据
const updateChartData = () => {
  if (!chartInstance) return

  // 提取所有指标名称
  const indicators = currentData.value.map(item => ({
    name: item.name,
    max: 50
  }))

  // 雷达图配置
  const option = {
    color: ['#0057FF', '#70FF00', '#AC42FF'],
    radar: {
      indicator: indicators,
      shape: 'polygon',
      center: ['50%', '40%'],
      radius: '60%',
      nameGap: 5,
      splitNumber: 5,
      splitArea: {
        show: true,
        areaStyle: {
          color: ['rgba(0, 87, 255, 0.05)', 'rgba(0, 87, 255, 0.1)', 'rgba(0, 87, 255, 0.2)', 'rgba(0, 87, 255, 0.3)']
        }
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.3)'
        }
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.3)'
        }
      },
      name: {
        textStyle: {
          color: '#FFFFFF',
          fontSize: 12,
          fontFamily: 'PingFangSC, PingFang SC',
          fontWeight: 400
        }
      }
    },
    series: [
      {
        name: '隐患类型',
        type: 'radar',
        data: [
          {
            value: currentData.value.map(item => item.value),
            name: '隐患类型',
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: 'rgba(0, 87, 255, 0.7)'
                },
                {
                  offset: 1,
                  color: 'rgba(112, 255, 0, 0.3)'
                }
              ])
            },
            lineStyle: {
              color: '#0057FF',
              width: 2
            },
            itemStyle: {
              color: '#FFFFFF',
              borderColor: '#0057FF',
              borderWidth: 2
            },
            symbolSize: 6
          },
          {
            value: currentData.value.map(item => item.value * 0.6),
            name: '整改情况',
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: 'rgba(112, 255, 0, 0.7)'
                },
                {
                  offset: 1,
                  color: 'rgba(172, 66, 255, 0.3)'
                }
              ])
            },
            lineStyle: {
              color: '#70FF00',
              width: 2
            },
            itemStyle: {
              color: '#FFFFFF',
              borderColor: '#70FF00',
              borderWidth: 2
            },
            symbolSize: 6
          },
          {
            value: currentData.value.map(item => item.value * 0.3),
            name: '未整改',
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: 'rgba(172, 66, 255, 0.7)'
                },
                {
                  offset: 1,
                  color: 'rgba(255, 99, 71, 0.3)'
                }
              ])
            },
            lineStyle: {
              color: '#AC42FF',
              width: 2
            },
            itemStyle: {
              color: '#FFFFFF',
              borderColor: '#AC42FF',
              borderWidth: 2
            },
            symbolSize: 6
          }
        ]
      }
    ]
  }

  chartInstance.setOption(option)
}

// 响应窗口大小变化
const resizeChart = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

// 处理时间范围变化
const handleTimeChange = (value) => {
  console.log('时间范围变更为:', value)
  currentData.value = timeRangeData[value]
  nextTick(() => {
    updateChartData()
  })
}

// 生命周期钩子
onMounted(() => {
  // 使用setTimeout确保DOM已经渲染完毕
  setTimeout(() => {
    initChart()
  }, 300)
})

onBeforeUnmount(() => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
  window.removeEventListener('resize', resizeChart)
})
</script>

<style scoped>
.right-middle-panel {
  height: 310px;
}

.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
}

.chart-container {
  width: 100%;
  height: 100%;
  flex: 1;
  min-height: 250px;  /* 添加最小高度确保DOM渲染时有尺寸 */
}

.com-select {
  margin-right: 20px;
}

/* 响应式布局适配 */
@media screen and (min-width: 1920px) and (max-width: 2560px) {
  .right-middle-panel {
    height: 310px;
  }
  .panel-content {
    padding: 10px;
  }
  .chart-container {
    height: 340px;
  }
}

@media screen and (max-width: 1919px) {
  .right-middle-panel {
    height: 310px;
  }
  .panel-content {
    padding: 8px;
  }
  .chart-container {
    height: 300px;
  }
}

@media screen and (min-width: 2561px) {
  .right-middle-panel {
    height: 310px;
  }
  .panel-content {
    padding: 15px;
  }
  .chart-container {
    height: 380px;
  }
}

/* 添加全屏模式(1080px高度)的适配 */
@media screen and (min-height: 1056px) and (max-height: 1080px) {
  .right-middle-panel {
    height: 310px;
  }
  .panel-content {
    padding: 15px;
  }
  .chart-container {
    height: 360px;
  }
}

@media screen and (min-height: 940px) and (max-height: 1055px) {
  .right-middle-panel {
    height: 310px;
  }
  .panel-content {
    padding: 10px;
  }
  .chart-container {
    height: 320px;
  }
}

@media screen and (min-height: 900px) and (max-height: 940px) {
  .right-middle-panel {
    height: 252px;
  }
  .panel-content {
    padding: 8px;
  }
  .chart-container {
    height: 280px;
  }
}
</style> 