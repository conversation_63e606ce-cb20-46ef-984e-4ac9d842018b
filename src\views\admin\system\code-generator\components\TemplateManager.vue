<template>
  <el-dialog
    title="模板管理"
    v-model="dialogVisible"
    width="800px"
    :close-on-click-modal="false"
    :before-close="handleClose"
  >
    <div class="template-manager">
      <div class="template-list">
        <div class="list-header">
          <h3>模板列表</h3>
          <div class="header-actions">
            <el-button type="primary" size="small" @click="handleImport">导入模板</el-button>
            <el-button type="success" size="small" @click="handleAddTemplate">新增模板</el-button>
          </div>
        </div>

        <el-table :data="templateList" style="width: 100%" height="400px">
          <el-table-column prop="name" label="模板名称" min-width="150" />
          <el-table-column prop="description" label="描述" min-width="200" />
          <el-table-column prop="author" label="作者" min-width="100" />
          <el-table-column prop="createTime" label="创建时间" min-width="150" />
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button type="primary" size="small" @click="handleUseTemplate(row)">使用</el-button>
              <el-button type="success" size="small" @click="handleExportTemplate(row)">导出</el-button>
              <el-button type="danger" size="small" @click="handleDeleteTemplate(row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 导入模板对话框 -->
    <el-dialog
      v-model="importDialogVisible"
      title="导入模板"
      width="500px"
      append-to-body
    >
      <el-form :model="importForm" label-width="100px">
        <el-form-item label="模板文件">
          <el-upload
            action="#"
            :auto-upload="false"
            :limit="1"
            accept=".json"
            :on-change="handleFileChange"
          >
            <el-button type="primary">选择文件</el-button>
            <template #tip>
              <div class="el-upload__tip">只能上传 JSON 文件</div>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="importDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmImport">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 新增模板对话框 -->
    <el-dialog
      v-model="addDialogVisible"
      title="新增模板"
      width="500px"
      append-to-body
    >
      <el-form :model="addForm" :rules="addRules" ref="addFormRef" label-width="100px">
        <el-form-item label="模板名称" prop="name">
          <el-input v-model="addForm.name" placeholder="请输入模板名称" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="addForm.description" type="textarea" placeholder="请输入描述" />
        </el-form-item>
        <el-form-item label="作者" prop="author">
          <el-input v-model="addForm.author" placeholder="请输入作者" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="addDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmAddTemplate">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </el-dialog>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { saveAs } from 'file-saver'

const props = defineProps({
  visible: {
    type: Boolean,
    required: true
  },
  currentConfig: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update:visible', 'use-template'])

// 对话框可见性
const dialogVisible = ref(false)

// 导入对话框可见性
const importDialogVisible = ref(false)

// 新增对话框可见性
const addDialogVisible = ref(false)

// 导入表单
const importForm = ref({
  file: null
})

// 新增表单
const addForm = ref({
  name: '',
  description: '',
  author: ''
})

// 新增表单验证规则
const addRules = {
  name: [
    { required: true, message: '请输入模板名称', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入描述', trigger: 'blur' }
  ],
  author: [
    { required: true, message: '请输入作者', trigger: 'blur' }
  ]
}

// 新增表单引用
const addFormRef = ref(null)

// 模板列表
const templateList = ref([
  {
    id: '1',
    name: '用户管理模板',
    description: '标准的用户管理页面，包含用户列表、新增、编辑、删除等功能',
    author: '系统管理员',
    createTime: '2023-04-22 10:00:00',
    config: {
      basic: {
        moduleName: 'user',
        apiPrefix: '/api/users',
        description: '用户管理模块，包含用户的增删改查功能',
        author: '系统管理员',
        pageTitle: '用户管理'
      },
      table: {
        columns: [
          {
            label: '用户名',
            prop: 'username',
            width: '120px',
            align: 'left',
            sortable: true,
            fixed: ''
          },
          {
            label: '姓名',
            prop: 'name',
            width: '120px',
            align: 'left',
            sortable: false,
            fixed: ''
          },
          {
            label: '邮箱',
            prop: 'email',
            width: '180px',
            align: 'left',
            sortable: false,
            fixed: ''
          },
          {
            label: '角色',
            prop: 'role',
            width: '120px',
            align: 'center',
            sortable: false,
            fixed: ''
          },
          {
            label: '状态',
            prop: 'status',
            width: '100px',
            align: 'center',
            sortable: false,
            fixed: ''
          },
          {
            label: '创建时间',
            prop: 'createTime',
            width: '180px',
            align: 'center',
            sortable: true,
            fixed: ''
          }
        ]
      },
      search: {
        fields: [
          {
            label: '用户名',
            prop: 'username',
            type: 'input',
            placeholder: '请输入用户名'
          },
          {
            label: '角色',
            prop: 'role',
            type: 'select',
            placeholder: '请选择角色',
            options: [
              {
                label: '管理员',
                value: 'admin'
              },
              {
                label: '普通用户',
                value: 'user'
              }
            ]
          },
          {
            label: '状态',
            prop: 'status',
            type: 'select',
            placeholder: '请选择状态',
            options: [
              {
                label: '启用',
                value: '1'
              },
              {
                label: '禁用',
                value: '0'
              }
            ]
          },
          {
            label: '创建时间',
            prop: 'createTime',
            type: 'daterange',
            placeholder: '请选择创建时间范围'
          }
        ]
      },
      form: {
        fields: [
          {
            label: '用户名',
            prop: 'username',
            type: 'input',
            placeholder: '请输入用户名',
            required: true,
            rule: '',
            errorMsg: '请输入用户名'
          },
          {
            label: '姓名',
            prop: 'name',
            type: 'input',
            placeholder: '请输入姓名',
            required: true,
            rule: '',
            errorMsg: '请输入姓名'
          },
          {
            label: '邮箱',
            prop: 'email',
            type: 'input',
            placeholder: '请输入邮箱',
            required: true,
            rule: 'email',
            errorMsg: '请输入有效的邮箱地址'
          },
          {
            label: '角色',
            prop: 'role',
            type: 'select',
            placeholder: '请选择角色',
            required: true,
            rule: '',
            errorMsg: '请选择角色',
            options: [
              {
                label: '管理员',
                value: 'admin'
              },
              {
                label: '普通用户',
                value: 'user'
              }
            ]
          },
          {
            label: '状态',
            prop: 'status',
            type: 'radio',
            required: true,
            rule: '',
            errorMsg: '请选择状态',
            options: [
              {
                label: '启用',
                value: '1'
              },
              {
                label: '禁用',
                value: '0'
              }
            ]
          },
          {
            label: '备注',
            prop: 'remark',
            type: 'textarea',
            placeholder: '请输入备注',
            required: false
          }
        ]
      },
      api: {
        methods: {
          list: true,
          detail: true,
          create: true,
          update: true,
          delete: true
        },
        customMethods: [
          {
            name: 'resetPassword',
            method: 'post',
            url: '/reset-password',
            params: 'id',
            description: '重置用户密码'
          },
          {
            name: 'exportUsers',
            method: 'get',
            url: '/export',
            params: 'query',
            description: '导出用户数据'
          }
        ]
      }
    }
  },
  {
    id: '2',
    name: '产品管理模板',
    description: '标准的产品管理页面，包含产品列表、新增、编辑、删除等功能',
    author: '系统管理员',
    createTime: '2023-04-22 11:00:00',
    config: {
      basic: {
        moduleName: 'product',
        apiPrefix: '/api/products',
        description: '产品管理模块，包含产品的增删改查功能',
        author: '系统管理员',
        pageTitle: '产品管理'
      },
      table: {
        columns: [
          {
            label: '产品名称',
            prop: 'name',
            width: '150px',
            align: 'left',
            sortable: true,
            fixed: ''
          },
          {
            label: '产品编码',
            prop: 'code',
            width: '120px',
            align: 'left',
            sortable: false,
            fixed: ''
          },
          {
            label: '产品分类',
            prop: 'category',
            width: '120px',
            align: 'left',
            sortable: false,
            fixed: ''
          },
          {
            label: '价格',
            prop: 'price',
            width: '100px',
            align: 'right',
            sortable: true,
            fixed: ''
          },
          {
            label: '库存',
            prop: 'stock',
            width: '100px',
            align: 'right',
            sortable: true,
            fixed: ''
          },
          {
            label: '状态',
            prop: 'status',
            width: '100px',
            align: 'center',
            sortable: false,
            fixed: ''
          },
          {
            label: '创建时间',
            prop: 'createTime',
            width: '180px',
            align: 'center',
            sortable: true,
            fixed: ''
          }
        ]
      },
      search: {
        fields: [
          {
            label: '产品名称',
            prop: 'name',
            type: 'input',
            placeholder: '请输入产品名称'
          },
          {
            label: '产品编码',
            prop: 'code',
            type: 'input',
            placeholder: '请输入产品编码'
          },
          {
            label: '产品分类',
            prop: 'category',
            type: 'select',
            placeholder: '请选择产品分类',
            options: [
              {
                label: '分类1',
                value: 'category1'
              },
              {
                label: '分类2',
                value: 'category2'
              },
              {
                label: '分类3',
                value: 'category3'
              }
            ]
          },
          {
            label: '状态',
            prop: 'status',
            type: 'select',
            placeholder: '请选择状态',
            options: [
              {
                label: '上架',
                value: '1'
              },
              {
                label: '下架',
                value: '0'
              }
            ]
          }
        ]
      },
      form: {
        fields: [
          {
            label: '产品名称',
            prop: 'name',
            type: 'input',
            placeholder: '请输入产品名称',
            required: true,
            rule: '',
            errorMsg: '请输入产品名称'
          },
          {
            label: '产品编码',
            prop: 'code',
            type: 'input',
            placeholder: '请输入产品编码',
            required: true,
            rule: '',
            errorMsg: '请输入产品编码'
          },
          {
            label: '产品分类',
            prop: 'category',
            type: 'select',
            placeholder: '请选择产品分类',
            required: true,
            rule: '',
            errorMsg: '请选择产品分类',
            options: [
              {
                label: '分类1',
                value: 'category1'
              },
              {
                label: '分类2',
                value: 'category2'
              },
              {
                label: '分类3',
                value: 'category3'
              }
            ]
          },
          {
            label: '价格',
            prop: 'price',
            type: 'number',
            placeholder: '请输入价格',
            required: true,
            rule: 'number',
            errorMsg: '请输入有效的价格'
          },
          {
            label: '库存',
            prop: 'stock',
            type: 'number',
            placeholder: '请输入库存',
            required: true,
            rule: 'integer',
            errorMsg: '请输入有效的库存'
          },
          {
            label: '状态',
            prop: 'status',
            type: 'radio',
            required: true,
            rule: '',
            errorMsg: '请选择状态',
            options: [
              {
                label: '上架',
                value: '1'
              },
              {
                label: '下架',
                value: '0'
              }
            ]
          },
          {
            label: '产品描述',
            prop: 'description',
            type: 'textarea',
            placeholder: '请输入产品描述',
            required: false
          }
        ]
      },
      api: {
        methods: {
          list: true,
          detail: true,
          create: true,
          update: true,
          delete: true
        },
        customMethods: [
          {
            name: 'updateStock',
            method: 'post',
            url: '/update-stock',
            params: 'id, stock',
            description: '更新产品库存'
          },
          {
            name: 'exportProducts',
            method: 'get',
            url: '/export',
            params: 'query',
            description: '导出产品数据'
          }
        ]
      }
    }
  }
])

// 监听visible属性变化
watch(() => props.visible, (val) => {
  dialogVisible.value = val
})

// 监听dialogVisible变化
watch(() => dialogVisible.value, (val) => {
  emit('update:visible', val)
})

// 处理关闭
const handleClose = () => {
  dialogVisible.value = false
}

// 处理导入
const handleImport = () => {
  importDialogVisible.value = true
}

// 处理文件变化
const handleFileChange = (file) => {
  importForm.value.file = file.raw
}

// 确认导入
const confirmImport = () => {
  if (!importForm.value.file) {
    ElMessage.warning('请选择文件')
    return
  }

  const reader = new FileReader()
  reader.onload = (e) => {
    try {
      const config = JSON.parse(e.target.result)

      // 验证配置格式
      if (!config.basic || !config.table || !config.search || !config.form || !config.api) {
        ElMessage.error('无效的模板文件格式')
        return
      }

      // 添加到模板列表
      const template = {
        id: Date.now().toString(),
        name: config.basic.moduleName || '导入的模板',
        description: config.basic.description || '导入的模板',
        author: config.basic.author || '未知',
        createTime: new Date().toLocaleString(),
        config
      }

      templateList.value.push(template)

      ElMessage.success('导入成功')
      importDialogVisible.value = false
    } catch (error) {
      console.error('导入失败', error)
      ElMessage.error('导入失败，无效的JSON格式')
    }
  }

  reader.readAsText(importForm.value.file)
}

// 处理新增模板
const handleAddTemplate = () => {
  addForm.value = {
    name: '',
    description: '',
    author: ''
  }
  addDialogVisible.value = true
}

// 确认新增模板
const confirmAddTemplate = async () => {
  if (!addFormRef.value) return

  try {
    await addFormRef.value.validate()

    // 添加到模板列表
    const template = {
      id: Date.now().toString(),
      name: addForm.value.name,
      description: addForm.value.description,
      author: addForm.value.author,
      createTime: new Date().toLocaleString(),
      config: JSON.parse(JSON.stringify(props.currentConfig))
    }

    templateList.value.push(template)

    ElMessage.success('添加成功')
    addDialogVisible.value = false
  } catch (error) {
    console.error('表单验证失败', error)
  }
}

// 处理使用模板
const handleUseTemplate = (row) => {
  ElMessageBox.confirm('使用此模板将覆盖当前配置，是否继续？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    emit('use-template', row.config)
    ElMessage.success('应用模板成功')
    dialogVisible.value = false
  }).catch(() => {
    // 取消操作
  })
}

// 处理导出模板
const handleExportTemplate = (row) => {
  const blob = new Blob([JSON.stringify(row.config, null, 2)], { type: 'application/json' })
  saveAs(blob, `${row.name}.json`)
  ElMessage.success('导出成功')
}

// 处理删除模板
const handleDeleteTemplate = (row) => {
  ElMessageBox.confirm('确认删除此模板吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const index = templateList.value.findIndex(item => item.id === row.id)
    if (index !== -1) {
      templateList.value.splice(index, 1)
      ElMessage.success('删除成功')
    }
  }).catch(() => {
    // 取消操作
  })
}
</script>

<style scoped>
.template-manager {
  width: 100%;
}

.template-list {
  width: 100%;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.header-actions {
  display: flex;
  gap: 8px;
}
</style>
