<template>
  <div class="gas-decision-support-statistics">
    <div class="content-wrapper">
      <!-- 上部分区域 -->
      <div class="top-section">
        <!-- Tab 区域 -->
        <div class="tabs-container">
          <div class="tabs">
            <div 
              v-for="(tab, index) in tabs" 
              :key="index" 
              :class="['tab-item', { active: currentTab === tab.value }]"
              @click="handleTabChange(tab.value)"
            >
              {{ tab.label }}
            </div>
          </div>
          <div class="tabs-border"></div>
        </div>
      </div>

      <!-- 下部分区域 -->
      <div class="bottom-section">
        <!-- 内容区域 -->
        <div class="content-area">
          <component :is="currentComponent" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, shallowRef } from 'vue';
import GasRiskStatistics from './components/GasRiskStatistics.vue';
import GasLeakStatistics from './components/GasLeakStatistics.vue';

// 定义Tab选项
const tabs = [
  { label: '管网风险统计分析', value: 'risk' },
  { label: '燃气泄漏报警统计分析', value: 'leak' }
];

// 当前选中的Tab
const currentTab = ref('risk');

// 动态组件
const componentMap = {
  risk: GasRiskStatistics,
  leak: GasLeakStatistics
};

// 当前显示的组件
const currentComponent = shallowRef(componentMap.risk);

// 处理Tab切换
const handleTabChange = (tab) => {
  currentTab.value = tab;
  currentComponent.value = componentMap[tab];
};
</script>

<style scoped>
.gas-decision-support-statistics {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  overflow: auto;
}

.content-wrapper {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  gap: 16px;
}

/* 上部分样式 */
.top-section {
  background: #fff;
  padding: 16px;
  border-radius: 4px;
}

/* Tab样式 */
.tabs-container {
  width: 100%;
}

.tabs {
  display: flex;
  align-items: center;
}

.tab-item {
  position: relative;
  padding: 0 16px 8px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #222222;
  cursor: pointer;
}

.tab-item.active {
  color: #0277FD;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
  height: 2px;
  background: #2D7EFF;
}

.tabs-border {
  width: 100%;
  height: 1px;
  border: 1px solid #E9E9E9;
  margin-bottom: 16px;
}

/* 下部分样式 */
.bottom-section {
  background: #fff;
  padding: 16px;
  border-radius: 4px;
  flex: 1;
  overflow: auto;
}

/* 内容区域样式 */
.content-area {
  width: 100%;
  height: 100%;
  overflow: auto;
}
</style>