<template>
  <div class="code-highlight">
    <pre><code ref="codeRef" :class="language" v-html="highlightedCode"></code></pre>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import hljs from 'highlight.js/lib/core'
import javascript from 'highlight.js/lib/languages/javascript'
import xml from 'highlight.js/lib/languages/xml'
import 'highlight.js/styles/atom-one-dark.css'

// 注册语言
hljs.registerLanguage('javascript', javascript)
hljs.registerLanguage('xml', xml)

const props = defineProps({
  code: {
    type: String,
    required: true
  },
  language: {
    type: String,
    default: 'javascript'
  }
})

const codeRef = ref(null)

// 高亮代码
const highlightedCode = computed(() => {
  if (!props.code) return ''
  
  try {
    const result = hljs.highlight(props.code, { language: props.language })
    return result.value
  } catch (error) {
    console.error('代码高亮失败', error)
    return props.code
  }
})

// 监听代码变化
watch(() => props.code, () => {
  if (codeRef.value) {
    hljs.highlightElement(codeRef.value)
  }
})

// 组件挂载时高亮代码
onMounted(() => {
  if (codeRef.value) {
    hljs.highlightElement(codeRef.value)
  }
})
</script>

<style scoped>
.code-highlight {
  width: 100%;
  height: 100%;
  overflow: auto;
}

pre {
  margin: 0;
  padding: 0;
}

code {
  font-family: Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.5;
  padding: 16px;
  border-radius: 4px;
  overflow-x: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>
