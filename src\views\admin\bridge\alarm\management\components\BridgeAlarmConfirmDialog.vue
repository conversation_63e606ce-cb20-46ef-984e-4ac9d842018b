<template>
  <el-dialog
    v-model="dialogVisible"
    title="报警确认"
    width="500px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="bridge-alarm-confirm-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
    >
      <el-form-item label="确认结果" prop="confirmResult">
        <el-radio-group v-model="formData.confirmResult">
          <el-radio :label="4002001" size="large">真实报警</el-radio>
          <el-radio :label="4002002" size="large">误报</el-radio>
        </el-radio-group>
      </el-form-item>
      
      <el-form-item label="确认描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="4"
          placeholder="请输入确认描述（100字以内）"
          maxlength="100"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { confirmBridgeAlarm } from '@/api/bridge';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  alarmData: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'success']);

// 表单引用
const formRef = ref(null);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 提交加载状态
const submitLoading = ref(false);

// 表单数据
const formData = reactive({
  confirmResult: 4002001, // 默认选择真实报警
  description: ''
});

// 表单验证规则
const formRules = {
  confirmResult: [
    { required: true, message: '请选择确认结果', trigger: 'change' }
  ],
  description: [
    { required: true, message: '请输入确认描述', trigger: 'blur' },
    { max: 100, message: '确认描述不能超过100个字符', trigger: 'blur' }
  ]
};

// 提交表单
const handleSubmit = async () => {
  try {
    const valid = await formRef.value.validate();
    if (!valid) return;

    submitLoading.value = true;
    
    const params = {
      alarmId: props.alarmData.id,
      confirmResult: formData.confirmResult,
      description: formData.description
    };

    const res = await confirmBridgeAlarm(params);
    if (res.code === 200) {
      ElMessage.success('确认成功');
      emit('success');
      handleClose();
    } else {
      ElMessage.error(res.message || '确认失败');
    }
  } catch (error) {
    console.error('确认报警失败:', error);
    ElMessage.error('确认失败');
  } finally {
    submitLoading.value = false;
  }
};

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false;
  // 重置表单
  formData.confirmResult = 4003901;
  formData.description = '';
  if (formRef.value) {
    formRef.value.clearValidate();
  }
};

// 监听弹窗显示，重置表单
watch(() => props.visible, (visible) => {
  if (visible) {
    formData.confirmResult = 4003901;
    formData.description = '';
  }
});
</script>

<style scoped>
.bridge-alarm-confirm-dialog {
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "Helvetica Neue", Arial, sans-serif;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #333;
}

:deep(.el-radio) {
  margin-right: 20px;
  margin-bottom: 10px;
}

:deep(.el-radio__label) {
  font-size: 14px;
}

:deep(.el-textarea__inner) {
  min-height: 80px;
  resize: vertical;
}

.dialog-footer {
  text-align: right;
  padding-top: 20px;
}
</style> 