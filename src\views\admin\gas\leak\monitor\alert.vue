<template>
  <div class="gas-leak-monitor-alert">
    <!-- 上部分区域：报警统计信息 -->
    <div class="alert-stats-section">
      <!-- 卡片1：全部报警 -->
      <div class="alert-card blue-card">
        <div class="alert-title">全部报警</div>
        <div class="alert-value">
          <span class="value">{{ alarmStats.totalAlarms }}</span>
        </div>
      </div>

      <!-- 卡片2：待确认 -->
      <div class="alert-card orange-card">
        <div class="alert-title">待确认</div>
        <div class="alert-value">
          <span class="value">{{ alarmStats.pendingConfirm }}</span>
          <span class="percentage">{{ alarmStats.pendingConfirmRate }}</span>
        </div>
      </div>

      <!-- 卡片3：待处置 -->
      <div class="alert-card yellow-card">
        <div class="alert-title">待处置</div>
        <div class="alert-value">
          <span class="value">{{ alarmStats.pendingHandle }}</span>
          <span class="percentage">{{ alarmStats.pendingHandleRate }}</span>
        </div>
      </div>

      <!-- 卡片4：处置中 -->
      <div class="alert-card cyan-card">
        <div class="alert-title">处置中</div>
        <div class="alert-value">
          <span class="value">{{ alarmStats.handling }}</span>
          <span class="percentage">{{ alarmStats.handlingRate }}</span>
        </div>
      </div>

      <!-- 卡片5：已处置 -->
      <div class="alert-card gray-card">
        <div class="alert-title">已处置</div>
        <div class="alert-value">
          <span class="value">{{ alarmStats.handled }}</span>
          <span class="percentage">{{ alarmStats.handledRate }}</span>
        </div>
      </div>

      <!-- 卡片6：级别统计 -->
      <div class="alert-card level-card">
        <template v-for="(item, index) in alarmLevelStats" :key="item.alarmLevel">
          <div class="level-item">
            <div class="level-title">{{ item.alarmLevelName }}报警</div>
            <div class="level-value">
              <span
                :class="['level-num', index === 0 ? 'level-first' : index === 1 ? 'level-second' : 'level-third']">{{
                  item.totalCount }}</span>
              <span class="level-percent">| {{ item.percent }}</span>
            </div>
          </div>
          <div class="vertical-divider" v-if="index < alarmLevelStats.length - 1"></div>
        </template>
      </div>
    </div>

    <!-- 中部分区域：查询表单 -->
    <div class="search-section">
      <GasLeakAlertSearch @search="handleSearch" @reset="handleReset" />
    </div>

    <!-- 下部分区域：表格 -->
    <div class="table-section">
      <!-- 表格区域 -->
      <el-table ref="dataTable" :data="tableData" :header-cell-style="headerCellStyle"
        :row-class-name="tableRowClassName" style="width: 100%" height="274" @row-click="handleRowClick">
        <el-table-column label="序号" width="60" align="center">
          <template #default="{ $index }">
            {{ (currentPage - 1) * pageSize + $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column label="报警来源" width="120" align="center">
          <template #default="scope">
            {{ ALARM_SOURCE_MAP[scope.row.alarmSource] || scope.row.alarmSource }}
          </template>
        </el-table-column>
        <el-table-column prop="alarmCode" label="报警编号" width="120" align="center" />
        <el-table-column prop="alarmTime" label="报警时间" width="150" align="center" />
        <el-table-column prop="deviceCode" label="报警设备编码" width="120" align="center" />
        <el-table-column label="报警类型" width="120" align="center">
          <template #default="scope">
            {{ ALARM_TYPE_MAP[scope.row.alarmType] || scope.row.alarmTypeName }}
          </template>
        </el-table-column>
        <el-table-column prop="monitorObjectName" label="监测对象" width="120" align="center" />
        <el-table-column prop="alarmValue" label="报警值" width="100" align="center" />
        <el-table-column prop="alarmLocation" label="报警位置" min-width="150" align="center" />
        <el-table-column label="报警级别" width="120" align="center">
          <template #default="scope">
            <div :class="getAlarmLevelClass(scope.row.alarmLevel)">
              {{ getAlarmLevelText(scope.row.alarmLevel) || scope.row.alarmLevelName }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="ownershipUnitName" label="权属单位" width="120" align="center" />
        <el-table-column label="报警状态" width="120" align="center">
          <template #default="scope">
            {{ ALARM_STATUS_MAP[scope.row.alarmStatus] || scope.row.alarmStatusName }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right" align="center">
          <template #default="scope">
            <div class="operation-btns">
              <div class="operation-btn-row">
                <span class="operation-btn-text" @click.stop="handleDetail(scope.row)">详情</span>
                <span class="operation-divider">|</span>
                <span class="operation-btn-text" @click.stop="handleLocation(scope.row)">定位</span>
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页区域 -->
      <div class="pagination-container">
        <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[10, 20, 30, 50]"
          layout="total, prev, pager, next, jumper, sizes" :total="total" @size-change="handleSizeChange"
          @current-change="handleCurrentChange" />
      </div>
    </div>

    <!-- 报警详情弹窗 -->
    <GasLeakAlertDialog v-model:visible="dialogVisible" :alarm-id="currentAlarmId" />
  </div>
</template>

<script setup>
import { ref, onMounted, reactive } from 'vue';
import { ElTable, ElTableColumn, ElPagination } from 'element-plus';
import GasLeakAlertSearch from './components/GasLeakAlertSearch.vue';
import GasLeakAlertDialog from './components/GasLeakAlertDialog.vue';
import { getAlarmStatusStatistics, getAlarmLevelStatistics, getAlarmList } from '@/api/gas';
import { ALARM_LEVEL_MAP, ALARM_STATUS_MAP, ALARM_SOURCES, ALARM_SOURCE_MAP, ALARM_TYPES, ALARM_TYPE_MAP } from '@/constants/gas';
import { misPosition } from '@/hooks/gishooks' //地图定位
// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const tableData = ref([]);

// 查询参数
const queryParams = ref({});

// 报警统计数据
const alarmStats = reactive({
  totalAlarms: 0,
  pendingConfirm: 0,
  pendingConfirmRate: '0%',
  pendingHandle: 0,
  pendingHandleRate: '0%',
  handling: 0,
  handlingRate: '0%',
  handled: 0,
  handledRate: '0%'
});

// 报警等级统计数据
const alarmLevelStats = ref([]);

// 获取报警等级文本
const getAlarmLevelText = (level) => {
  return ALARM_LEVEL_MAP[level] || '';
};

// 获取报警等级样式
const getAlarmLevelClass = (level) => {
  const map = {
    '9101': 'alarm-level-first',
    '9102': 'alarm-level-second',
    '9103': 'alarm-level-third',
    '9104': 'alarm-level-third'
  };
  return ['alarm-level-tag', map[level]];
};

// 处理搜索
const handleSearch = (formData) => {
  // 转换查询参数
  const params = {};

  if (formData.alarmSource && formData.alarmSource !== 'all') {
    // 查找对应的数值
    const source = ALARM_SOURCES.find(item => item.label === formData.alarmSource);
    if (source) {
      params.alarmSource = source.value;
    }
  }

  if (formData.alarmLevel && formData.alarmLevel !== 'all') {
    params.alarmLevel = parseInt(formData.alarmLevel);
  }

  if (formData.alarmType && formData.alarmType !== 'all') {
    params.alarmType = formData.alarmType;
  }

  if (formData.timeRange && formData.timeRange.length === 2) {
    params.startTime = formData.timeRange[0];
    params.endTime = formData.timeRange[1];
  }

  if (formData.alarmStatus && formData.alarmStatus !== 'all') {
    // 转换状态为对应的数值
    const statusMap = {
      'pending': 9201, // 待确认
      'processing': 9204, // 处置中
      'completed': 9205 // 已处置
    };
    params.alarmStatus = statusMap[formData.alarmStatus];
  }

  if (formData.code) {
    params.code = formData.code;
  }

  queryParams.value = params;
  fetchAlarmData();
};

// 处理重置
const handleReset = () => {
  queryParams.value = {};
  fetchAlarmData();
};

// 获取报警处置状态统计数据
const fetchAlarmStatusStatistics = async () => {
  try {
    const params = {
      startDate: queryParams.value.startTime || '',
      endDate: queryParams.value.endTime || ''
    };
    const res = await getAlarmStatusStatistics(params);
    if (res.code === 200 && res.data) {
      Object.assign(alarmStats, res.data);
    }
  } catch (error) {
    console.error('获取报警处置状态统计数据失败:', error);
  }
};

// 获取报警等级统计数据
const fetchAlarmLevelStatistics = async () => {
  try {
    const params = {
      startDate: queryParams.value.startTime || '',
      endDate: queryParams.value.endTime || ''
    };
    const res = await getAlarmLevelStatistics(params);
    if (res.code === 200 && res.data && res.data.statistics) {
      alarmLevelStats.value = res.data.statistics;
    }
  } catch (error) {
    console.error('获取报警等级统计数据失败:', error);
  }
};

// 获取报警数据
const fetchAlarmData = async () => {
  try {
    const res = await getAlarmList(currentPage.value, pageSize.value, queryParams.value);
    if (res.code === 200 && res.data) {
      tableData.value = res.data.rows || [];
      total.value = res.data.total || 0;
    }
  } catch (error) {
    console.error('获取报警数据失败:', error);
    tableData.value = [];
    total.value = 0;
  }
};

// 处理分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val;
  currentPage.value = 1;
  fetchAlarmData();
};

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val;
  fetchAlarmData();
};

// 表头样式
const headerCellStyle = {
  background: '#F4F4F4',
  fontFamily: 'PingFangSC, PingFang SC',
  fontWeight: '500',
  fontSize: '14px',
  color: '#282828',
  height: '64px'
};

// 斑马纹和选中行样式
const tableRowClassName = ({ rowIndex }) => {
  return rowIndex % 2 === 0 ? 'even-row' : 'odd-row';
};

// 处理行点击
const handleRowClick = (row) => {
  console.log('行数据:', row);
};

// 操作按钮处理函数
// 详情弹窗相关
const dialogVisible = ref(false);
const currentAlarmId = ref('');

const handleDetail = (row) => {
  currentAlarmId.value = row.id + "";
  dialogVisible.value = true;
};

const handleLocation = (row) => {
  if (
    row.longitude &&
    row.longitude != '' &&
    row.latitude &&
    row.latitude != ''
  ) {
    misPosition.value = {
      longitude: row.longitude, //经度
      latitude: row.latitude //维度
    }
  } else {
    ElMessage.warning('没有经纬度，无法定位！')
  }
};

onMounted(() => {
  fetchAlarmStatusStatistics();
  fetchAlarmLevelStatistics();
  fetchAlarmData();
});
</script>

<style scoped>
.gas-leak-monitor-alert {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  background-color: #F0F2F5;
  gap: 16px;
  overflow: hidden;
}

/* 报警统计区域样式 */
.alert-stats-section {
  width: 100%;
  background: #FFFFFF;
  padding: 16px;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  margin-bottom: 0;
}

.alert-card {
  height: 100px;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 0 24px;
}

.blue-card {
  width: 210px;
  background: #3B98FF;
}

.orange-card {
  width: 210px;
  background: #FF8D5B;
}

.yellow-card {
  width: 210px;
  background: #FFD466;
}

.cyan-card {
  width: 210px;
  background: #40C3FA;
}

.gray-card {
  width: 210px;
  background: #AAAAAA;
}

.level-card {
  width: 500px;
  background: #FFFFFF;
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  align-items: center;
  padding: 0;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.alert-title {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 18px;
  color: #FFFFFF;
  margin-bottom: 8px;
}

.alert-value {
  display: flex;
  align-items: baseline;
}

.value {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 30px;
  color: #FFFFFF;
  margin-right: 4px;
}

.percentage {
  font-size: 14px;
  color: #FFFFFF;
}

/* 级别卡片内部样式 */
.level-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0 20px;
}

.level-title {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 16px;
  color: #000000;
  margin-bottom: 8px;
}

.level-value {
  display: flex;
  align-items: baseline;
}

.level-num {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 26px;
  margin-right: 4px;
}

.level-first {
  color: #FF0000;
}

.level-second {
  color: #FF6400;
}

.level-third {
  color: #FFC600;
}

.level-percent {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 18px;
  color: #000000;
}

.vertical-divider {
  width: 1px;
  height: 40px;
  border: 1px solid #D6E7E9;
}

/* 搜索表单区域 */
.search-section {
  background: #FFFFFF;
  padding: 16px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  margin-bottom: 0;
}

/* 表格区域样式 */
.table-section {
  width: 100%;
  background: white;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  padding: 16px;
}

/* 表格样式 */
:deep(.el-table) {
  --el-table-border-color: #EBEEF5;
  --el-table-header-bg-color: #F5F8FA;
  height: 500px;
}

:deep(.el-table__inner-wrapper),
:deep(.el-scrollbar),
:deep(.el-scrollbar__wrap) {
  height: 274px !important;
}

:deep(.el-scrollbar__wrap) {
  overflow-x: hidden;
}

:deep(.el-table__body-wrapper) {
  overflow-y: auto !important;
}

/* 表格行样式 */
:deep(.el-table .even-row) {
  background-color: #FFFFFF;
}

:deep(.el-table .odd-row) {
  background-color: #F5F8FA;
}

:deep(.el-table th) {
  background-color: #F5F8FA;
  color: #333333;
  font-weight: bold;
  height: 40px;
  padding: 0;
  border-bottom: 1px solid #EBEEF5;
}

:deep(.el-table tr) {
  height: 48px;
}

:deep(.el-table__row:hover) {
  background: #EEF5FF !important;
}

:deep(.el-table__row.selected-row) {
  background: #EEF5FF !important;
}

/* 其他表格样式 */
:deep(.el-table__empty-block) {
  min-height: 60px;
  text-align: center;
  width: 100%;
}

:deep(.el-table__empty-text) {
  line-height: 60px;
  width: 50%;
  color: #909399;
}

/* 报警等级标签样式 */
.alarm-level-tag {
  width: 72px;
  height: 32px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  margin: 0 auto;
}

.alarm-level-first {
  background: rgba(255, 0, 0, 0.1);
  border: 1px solid #FF0000;
  color: #FF0000;
}

.alarm-level-second {
  background: rgba(255, 133, 0, 0.1);
  border: 1px solid #FF8500;
  color: #FF8500;
}

.alarm-level-third {
  background: rgba(255, 211, 0, 0.1);
  border: 1px solid #FFD300;
  color: #FFD300;
}

.operation-btns {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.operation-btn-row {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 4px 0;
}

.operation-btn-text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #0086FF;
  cursor: pointer;
}

.operation-divider {
  margin: 0 4px;
  color: #CED3DA;
}

/* 分页样式 */
.pagination-container {
  display: flex;
  justify-content: flex-end;
  padding: 16px;
  border-top: 1px solid #EBEEF5;
}

:deep(.el-pagination .el-pagination__total) {
  margin-right: 16px;
}

:deep(.el-pagination .el-input__wrapper) {
  box-shadow: 0 0 0 1px #CED3DA inset;
}

:deep(.el-pagination .el-select .el-input) {
  margin: 0 8px;
}

:deep(.el-pagination button:disabled) {
  background-color: #F5F8FA;
}

:deep(.el-pagination .el-pagination__jump) {
  margin-left: 16px;
}

/* 确保操作列文字不换行 */
:deep(.cell) {
  white-space: nowrap;
}

/* 响应式适配 */
@media screen and (max-width: 1920px) {
  .alert-stats-section {
    width: 100%;
  }
}
</style>