<!-- 设备信息 -->
<template>
  <div class="device-base-info">
    <div
      class="normal-row"
      v-for="(item, index) in baseInfo"
      :key="index"
      :class="{ 'normal-row-100': item.type && item.type === 'row' }"
    >
      <div class="title-item" :style="{ width: maxWidth + 'px' }">
        {{ item.label }}：
      </div>

      <div v-if="item.isImg" class="value-item">
        <div class="file-list flex items-center">
          <el-image
            v-for="(img, i) in item.fileList"
            :key="i"
            class="img-item"
            :src="img.url"
            preview-teleported
            :zoom-rate="1.2"
            :max-scale="7"
            :min-scale="0.2"
            :preview-src-list="[img.url]"
            fit="cover"
          />
        </div>
      </div>
      <template v-else-if="item.isFile">
        <div class="file-list" v-show="item.fileList && item.fileList.length">
          <div
            class="file-item"
            v-for="(file, fileIndex) in item.fileList"
            :key="fileIndex"
            @click="handleViewFile(file.url, file.name)"
          >
            {{ file.name }}
          </div>
        </div>
        <span
          class="value-item"
          v-if="!item.fileList || !item.fileList.length"
          style="flex: 1"
          >--</span
        >
      </template>
      <div v-else class="value-item">
        {{
          item.dataMap ? item.dataMap[item?.value ?? ""] : item.value || "--"
        }}
        <span>
          {{ item.unit ?? "" }}
        </span>
      </div>
    </div>
    <FilePreview
      customClass="screen-file-preview"
      v-model="filrPrev.visible"
      :file-url="filrPrev.url"
    />
  </div>
</template>

<script setup>

import {onMounted, reactive, ref, watch} from "vue";
import {ElMessage} from "element-plus";

const props = defineProps({
    baseInfo: {
        type: Array,
        default: () => [],
    },
});

const maxWidth = ref(0);

const calculateMaxWidth = () => {
  const labels = props.baseInfo.map((item) => item.label + "：");
  const tempDiv = document.createElement("div");
  tempDiv.style.position = "absolute";
  tempDiv.style.float = "left";
  tempDiv.style.whiteSpace = "nowrap";
  tempDiv.style.visibility = "hidden";
  tempDiv.style.font = "inherit"; // 确保字体和页面一致
  document.body.appendChild(tempDiv);
  let maxWidthValue = 0;
  labels.forEach((label) => {
    tempDiv.textContent = label;
    maxWidthValue = Math.max(maxWidthValue, tempDiv.scrollWidth);
  });
  document.body.removeChild(tempDiv);
  maxWidth.value = maxWidthValue;
};

const filrPrev = reactive({
  visible: false,
  url: "", //文件地址
});

// 查看处置报告
const handleViewFile = (url, name) => {
  if (url) {
    filrPrev.visible = true;
    filrPrev.url = url;
  } else {
    ElMessage.info("无相关附件");
  }
};

onMounted(calculateMaxWidth);

// 如果 list 动态变化，重新计算最大宽度
watch(() => props.baseInfo, calculateMaxWidth, {
  deep: true,
});
</script>
<style lang="scss" scoped>
.device-base-info {
  height: 360px;
  overflow-y: auto;
  display: flex;
  flex-wrap: wrap;
  align-content: flex-start;
  .normal-row {
    width: 50%;
    display: flex;
    margin-bottom: 15px;
    justify-content: space-between;
    font-size: 14px;
    &-100 {
      width: 100%;
    }
    .title-item {
      color: rgba(255, 255, 255, 0.6);
      text-align: right;
      min-width: 70px;
    }
    .value-item {
      flex: 1;
      color: #fff;
      overflow: hidden;
    }
  }
  .file-list {
    flex-wrap: wrap;
    flex: 1;
    .img-item {
      width: 80px;
      height: 80px;
      margin: 0 16px 10px 0;
      border: 1px solid #eee;
    }
    .file-item {
      cursor: pointer;
      width: 100%;
      margin-bottom: 10px;
      color: var(--el-color-primary);
      &:hover {
        text-decoration: underline;
      }
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}
</style>
