<template>
  <div 
    class="resizable-panel-box"
    :class="{ 'resize-active': isResizing }"
    :style="{ height: `${height}px` }"
  >
    <div class="panel-header">
      <div class="panel-title">
        <h3 class="title-text">{{ title }}</h3>
      </div>
      <div class="panel-extra" v-if="$slots.extra">
        <slot name="extra"></slot>
      </div>
    </div>
    <div class="panel-body">
      <slot></slot>
    </div>
    <div 
      class="resize-handle" 
      @mousedown="startResize"
      @touchstart="startResize"
    ></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue';

const props = defineProps({
  title: {
    type: String,
    required: true
  },
  defaultHeight: {
    type: Number,
    default: 280
  },
  minHeight: {
    type: Number,
    default: 200
  },
  maxHeight: {
    type: Number,
    default: 600
  }
});

const height = ref(props.defaultHeight);
const isResizing = ref(false);
let startY = 0;
let startHeight = 0;

// 强制更新高度的函数
const updateHeight = () => {
  console.log(`设置面板[${props.title}]高度为: ${props.defaultHeight}px`);
  height.value = props.defaultHeight;
  
  // 延迟再次设置，确保在DOM完全渲染后生效
  setTimeout(() => {
    height.value = props.defaultHeight;
  }, 100);
};

// 监听props变化
watch(() => props.defaultHeight, (newValue) => {
  console.log(`面板[${props.title}]高度更新为: ${newValue}px`);
  height.value = newValue;
}, { immediate: true });

const startResize = (event) => {
  isResizing.value = true;
  startY = event.clientY || (event.touches && event.touches[0].clientY);
  startHeight = height.value;
  
  document.addEventListener('mousemove', resize);
  document.addEventListener('touchmove', resize);
  document.addEventListener('mouseup', stopResize);
  document.addEventListener('touchend', stopResize);
  
  // 阻止默认事件，防止触摸设备上的滚动
  event.preventDefault();
};

const resize = (event) => {
  if (!isResizing.value) return;
  
  const currentY = event.clientY || (event.touches && event.touches[0].clientY);
  const diff = currentY - startY;
  
  // 计算新高度，并限制在最小和最大高度之间
  let newHeight = startHeight + diff;
  newHeight = Math.max(props.minHeight, Math.min(props.maxHeight, newHeight));
  
  height.value = newHeight;
  
  // 阻止默认事件，防止触摸设备上的滚动
  event.preventDefault();
};

const stopResize = () => {
  isResizing.value = false;
  document.removeEventListener('mousemove', resize);
  document.removeEventListener('touchmove', resize);
  document.removeEventListener('mouseup', stopResize);
  document.removeEventListener('touchend', stopResize);
};

// 组件卸载时移除事件监听
onUnmounted(() => {
  document.removeEventListener('mousemove', resize);
  document.removeEventListener('touchmove', resize);
  document.removeEventListener('mouseup', stopResize);
  document.removeEventListener('touchend', stopResize);
});

// 组件挂载时设置初始高度
onMounted(() => {
  updateHeight();
  
  // 为了应对可能的DOM渲染延迟问题，再次延迟设置高度
  setTimeout(updateHeight, 300);
});
</script>

<style scoped>
.resizable-panel-box {
  background: rgba(3, 24, 55, 0.6);
  backdrop-filter: blur(2px);
  border-radius: 8px;
  width: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
  transition: box-shadow 0.2s ease;
  /* 移除height: 100%，避免覆盖内联样式 */
}

.resizable-panel-box::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, 
    rgba(0, 242, 241, 0) 0%,
    rgba(0, 242, 241, 0.5) 50%,
    rgba(0, 242, 241, 0) 100%
  );
}

.resize-active {
  box-shadow: 0 0 8px rgba(0, 242, 241, 0.5);
}

.panel-header {
  width: 100%;
  height: 49px;
  background-image: url('@/assets/images/screen/panel_box_title.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  padding: 0 20px;
  flex-shrink: 0; /* 确保标题不会被压缩 */
}

.panel-title {
  flex: 1;
  display: flex;
  align-items: center;
}

.title-text {
  font-family: 'YouSheBiaoTiHei', sans-serif;
  font-size: 18px;
  font-weight: normal;
  color: #FFFFFF;
  line-height: 23px;
  letter-spacing: 1px;
  text-align: left;
  font-style: normal;
  margin: 0;
  background: linear-gradient(90deg, #E8F9FF 0%, #61D9FF 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.panel-extra {
  padding-left: 46px;
  display: flex;
  align-items: center;
}

.panel-body {
  flex: 1;
  padding: 8px 12px 12px;
  overflow: hidden;
}

.resize-handle {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 6px;
  background: transparent;
  cursor: ns-resize;
  z-index: 10;
}

.resize-handle:hover {
  background: rgba(0, 242, 241, 0.3);
}

.resize-handle:after {
  content: '';
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 30px;
  height: 3px;
  background: rgba(0, 242, 241, 0.5);
  border-radius: 2px;
}
</style> 