<template>
  <div class="form-config-step">
    <div class="step-header">
      <h3>表单字段配置</h3>
      <el-button type="primary" size="small" @click="addField">添加字段</el-button>
    </div>
    
    <div class="fields-container">
      <el-empty v-if="!config.form.fields.length" description="暂无表单字段配置，请添加" />
      
      <el-card v-for="(field, index) in config.form.fields" :key="index" class="field-card">
        <template #header>
          <div class="field-header">
            <span>字段 {{ index + 1 }}: {{ field.label || '未命名' }}</span>
            <div class="field-actions">
              <el-button type="danger" size="small" circle @click="removeField(index)">
                <el-icon><Delete /></el-icon>
              </el-button>
            </div>
          </div>
        </template>
        
        <el-form :model="field" label-width="100px">
          <el-form-item label="字段标签" required>
            <el-input v-model="field.label" placeholder="请输入字段标签" />
          </el-form-item>
          
          <el-form-item label="属性名" required>
            <el-input v-model="field.prop" placeholder="请输入属性名" />
          </el-form-item>
          
          <el-form-item label="组件类型" required>
            <el-select v-model="field.type" placeholder="请选择组件类型">
              <el-option label="输入框" value="input" />
              <el-option label="选择器" value="select" />
              <el-option label="日期选择器" value="date" />
              <el-option label="时间选择器" value="time" />
              <el-option label="数字输入框" value="number" />
              <el-option label="文本域" value="textarea" />
              <el-option label="开关" value="switch" />
              <el-option label="单选框组" value="radio" />
              <el-option label="复选框组" value="checkbox" />
              <el-option label="上传" value="upload" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="占位文本">
            <el-input v-model="field.placeholder" placeholder="请输入占位文本" />
          </el-form-item>
          
          <el-form-item label="是否必填">
            <el-switch v-model="field.required" />
          </el-form-item>
          
          <el-form-item v-if="['select', 'radio', 'checkbox'].includes(field.type)" label="选项">
            <div v-for="(option, optIndex) in field.options || []" :key="optIndex" class="option-item">
              <el-input v-model="option.label" placeholder="选项标签" class="option-input" />
              <el-input v-model="option.value" placeholder="选项值" class="option-input" />
              <el-button type="danger" size="small" circle @click="removeOption(field, optIndex)">
                <el-icon><Delete /></el-icon>
              </el-button>
            </div>
            <el-button type="primary" size="small" @click="addOption(field)">添加选项</el-button>
          </el-form-item>
          
          <el-form-item v-if="field.required" label="验证规则">
            <el-select v-model="field.rule" placeholder="请选择验证规则">
              <el-option label="无" value="" />
              <el-option label="邮箱" value="email" />
              <el-option label="手机号" value="phone" />
              <el-option label="URL" value="url" />
              <el-option label="数字" value="number" />
              <el-option label="整数" value="integer" />
              <el-option label="自定义正则" value="regexp" />
            </el-select>
          </el-form-item>
          
          <el-form-item v-if="field.rule === 'regexp'" label="正则表达式">
            <el-input v-model="field.regexp" placeholder="请输入正则表达式" />
          </el-form-item>
          
          <el-form-item v-if="field.required" label="错误提示">
            <el-input v-model="field.errorMsg" placeholder="请输入验证失败时的错误提示" />
          </el-form-item>
        </el-form>
      </el-card>
    </div>
    
    <div class="step-actions">
      <el-button @click="handlePrev">上一步</el-button>
      <el-button type="primary" @click="handleNext">下一步</el-button>
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue'
import { Delete } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  config: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update:config', 'next', 'prev'])

// 添加字段
const addField = () => {
  if (!props.config.form.fields) {
    props.config.form.fields = []
  }
  
  props.config.form.fields.push({
    label: '',
    prop: '',
    type: 'input',
    placeholder: '',
    required: false,
    rule: '',
    errorMsg: '',
    options: []
  })
}

// 移除字段
const removeField = (index) => {
  props.config.form.fields.splice(index, 1)
}

// 添加选项
const addOption = (field) => {
  if (!field.options) {
    field.options = []
  }
  
  field.options.push({
    label: '',
    value: ''
  })
}

// 移除选项
const removeOption = (field, index) => {
  field.options.splice(index, 1)
}

// 上一步
const handlePrev = () => {
  emit('prev')
}

// 下一步
const handleNext = () => {
  // 验证表单字段配置
  if (!props.config.form.fields.length) {
    ElMessage.warning('请至少添加一个表单字段')
    return
  }
  
  // 验证每个字段的必填项
  for (let i = 0; i < props.config.form.fields.length; i++) {
    const field = props.config.form.fields[i]
    if (!field.label) {
      ElMessage.warning(`第 ${i + 1} 个字段的标签不能为空`)
      return
    }
    if (!field.prop) {
      ElMessage.warning(`第 ${i + 1} 个字段的属性名不能为空`)
      return
    }
    if (!field.type) {
      ElMessage.warning(`第 ${i + 1} 个字段的组件类型不能为空`)
      return
    }
    
    // 验证选择器类型的选项
    if (['select', 'radio', 'checkbox'].includes(field.type)) {
      if (!field.options || !field.options.length) {
        ElMessage.warning(`第 ${i + 1} 个字段是 ${field.type} 类型，请至少添加一个选项`)
        return
      }
      
      for (let j = 0; j < field.options.length; j++) {
        const option = field.options[j]
        if (!option.label) {
          ElMessage.warning(`第 ${i + 1} 个字段的第 ${j + 1} 个选项的标签不能为空`)
          return
        }
        if (!option.value) {
          ElMessage.warning(`第 ${i + 1} 个字段的第 ${j + 1} 个选项的值不能为空`)
          return
        }
      }
    }
    
    // 验证自定义正则
    if (field.rule === 'regexp' && !field.regexp) {
      ElMessage.warning(`第 ${i + 1} 个字段使用了自定义正则验证，请输入正则表达式`)
      return
    }
    
    // 验证错误提示
    if (field.required && !field.errorMsg) {
      ElMessage.warning(`第 ${i + 1} 个字段是必填的，请输入验证失败时的错误提示`)
      return
    }
  }
  
  emit('next')
}
</script>

<style scoped>
.form-config-step {
  padding: 0 20px;
}

.step-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.fields-container {
  margin-bottom: 20px;
  max-height: calc(100vh - 400px);
  overflow-y: auto;
}

.field-card {
  margin-bottom: 16px;
}

.field-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.field-actions {
  display: flex;
  gap: 8px;
}

.option-item {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
  align-items: center;
}

.option-input {
  flex: 1;
}

.step-actions {
  margin-top: 30px;
  display: flex;
  justify-content: space-between;
}
</style>
