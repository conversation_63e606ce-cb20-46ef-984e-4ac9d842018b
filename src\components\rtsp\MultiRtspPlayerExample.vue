<template>
  <div class="multi-rtsp-example">
    <h2>多路RTSP播放器示例</h2>
    
    <!-- 播放器配置 -->
    <div class="config-panel">
      <div class="config-item">
        <label>视频ID:</label>
        <input v-model="videoId" type="text" placeholder="输入视频ID" />
      </div>
      
      <div class="config-item">
        <label>WebSocket地址:</label>
        <input v-model="wsUrl" type="text" placeholder="WebSocket服务地址" />
      </div>
      
      <div class="config-item">
        <label>播放器尺寸:</label>
        <select v-model="selectedSize" @change="updateSize">
          <option value="small">小 (320x180)</option>
          <option value="medium">中 (640x360)</option>
          <option value="large">大 (1280x720)</option>
        </select>
      </div>
      
      <div class="config-item">
        <label>
          <input v-model="showControls" type="checkbox" />
          显示控制按钮
        </label>
      </div>
      
      <div class="config-item">
        <label>
          <input v-model="autoplay" type="checkbox" />
          自动播放
        </label>
      </div>
    </div>
    
    <!-- 播放器容器 -->
    <div class="player-container">
      <MultiRtspPlayer
        ref="playerRef"
        :video-id="videoId"
        :ws-url="wsUrl"
        :width="playerWidth"
        :height="playerHeight"
        :show-controls="showControls"
        :autoplay="autoplay"
        @play="onPlay"
        @stop="onStop"
        @error="onError"
      />
    </div>
    
    <!-- 控制面板 -->
    <div class="control-panel">
      <button @click="handlePlay" :disabled="isPlaying">开始播放</button>
      <button @click="handleStop" :disabled="!isPlaying">停止播放</button>
      <button @click="handleRestart">重新播放</button>
    </div>
    
    <!-- 状态信息 -->
    <div class="status-panel">
      <h3>状态信息</h3>
      <div class="status-item">
        <strong>播放状态:</strong> 
        <span :class="{ playing: isPlaying, stopped: !isPlaying }">
          {{ isPlaying ? '播放中' : '已停止' }}
        </span>
      </div>
      <div class="status-item">
        <strong>当前视频ID:</strong> {{ videoId }}
      </div>
      <div class="status-item">
        <strong>WebSocket地址:</strong> {{ wsUrl }}
      </div>
      <div v-if="lastError" class="status-item error">
        <strong>最后错误:</strong> {{ lastError }}
      </div>
    </div>
    
    <!-- 多设备示例 -->
    <div class="multi-device-example">
      <h3>多设备播放示例</h3>
      <div class="device-grid">
        <div 
          v-for="device in devices" 
          :key="device.id"
          class="device-player"
        >
          <h4>{{ device.name }}</h4>
          <MultiRtspPlayer
            :video-id="device.id"
            :ws-url="wsUrl"
            :width="240"
            :height="135"
            :show-controls="true"
            :autoplay="false"
            @play="() => onDevicePlay(device.id)"
            @stop="() => onDeviceStop(device.id)"
            @error="(error) => onDeviceError(device.id, error)"
          />
          <div class="device-status">
            ID: {{ device.id }} | 
            状态: {{ deviceStatus[device.id] || '未连接' }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import MultiRtspPlayer from './MultiRtspPlayer.vue';

// 播放器配置
const videoId = ref('c1002'); // 默认视频ID
const wsUrl = ref('ws://**************:32021/basic/jsmpeg2');
const selectedSize = ref('medium');
const showControls = ref(true);
const autoplay = ref(false);

// 播放器尺寸
const playerWidth = ref(640);
const playerHeight = ref(360);

// 播放器引用
const playerRef = ref(null);

// 状态管理
const isPlaying = ref(false);
const lastError = ref('');

// 多设备示例数据
const devices = ref([
  { id: 'c1001', name: '设备1' },
  { id: 'c1002', name: '设备2' },
  { id: 'c1003', name: '设备3' },
  { id: 'c1004', name: '设备4' }
]);

const deviceStatus = reactive({});

// 尺寸配置
const sizeConfig = {
  small: { width: 320, height: 180 },
  medium: { width: 640, height: 360 },
  large: { width: 1280, height: 720 }
};

// 更新播放器尺寸
const updateSize = () => {
  const config = sizeConfig[selectedSize.value];
  playerWidth.value = config.width;
  playerHeight.value = config.height;
};

// 播放控制
const handlePlay = () => {
  if (playerRef.value) {
    playerRef.value.play();
  }
};

const handleStop = () => {
  if (playerRef.value) {
    playerRef.value.stop();
  }
};

const handleRestart = () => {
  handleStop();
  setTimeout(() => {
    handlePlay();
  }, 1000);
};

// 事件处理
const onPlay = () => {
  isPlaying.value = true;
  lastError.value = '';
  console.log('播放开始');
};

const onStop = () => {
  isPlaying.value = false;
  console.log('播放停止');
};

const onError = (error) => {
  isPlaying.value = false;
  lastError.value = error.message || '播放出错';
  console.error('播放错误:', error);
};

// 多设备事件处理
const onDevicePlay = (deviceId) => {
  deviceStatus[deviceId] = '播放中';
  console.log(`设备 ${deviceId} 开始播放`);
};

const onDeviceStop = (deviceId) => {
  deviceStatus[deviceId] = '已停止';
  console.log(`设备 ${deviceId} 停止播放`);
};

const onDeviceError = (deviceId, error) => {
  deviceStatus[deviceId] = '错误';
  console.error(`设备 ${deviceId} 播放错误:`, error);
};

// 组件挂载时初始化
onMounted(() => {
  // 初始化设备状态
  devices.value.forEach(device => {
    deviceStatus[device.id] = '未连接';
  });
});
</script>

<style scoped>
.multi-rtsp-example {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

h2, h3, h4 {
  color: #333;
  margin-bottom: 15px;
}

.config-panel {
  background: #f5f5f5;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
}

.config-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.config-item label {
  font-weight: 500;
  color: #555;
}

.config-item input[type="text"],
.config-item select {
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.config-item input[type="checkbox"] {
  margin-right: 8px;
}

.player-container {
  background: #000;
  padding: 10px;
  border-radius: 8px;
  margin-bottom: 20px;
  display: flex;
  justify-content: center;
}

.control-panel {
  text-align: center;
  margin-bottom: 20px;
}

.control-panel button {
  margin: 0 10px;
  padding: 10px 20px;
  font-size: 16px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.control-panel button:not(:disabled) {
  background: #007bff;
  color: white;
}

.control-panel button:not(:disabled):hover {
  background: #0056b3;
}

.control-panel button:disabled {
  background: #6c757d;
  color: white;
  cursor: not-allowed;
}

.status-panel {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.status-item {
  margin-bottom: 10px;
  padding: 5px 0;
}

.status-item.error {
  color: #dc3545;
}

.status-item .playing {
  color: #28a745;
  font-weight: bold;
}

.status-item .stopped {
  color: #6c757d;
}

.multi-device-example {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
}

.device-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-top: 15px;
}

.device-player {
  background: white;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.device-player h4 {
  margin: 0 0 10px 0;
  color: #333;
  text-align: center;
}

.device-status {
  margin-top: 10px;
  font-size: 12px;
  color: #666;
  text-align: center;
  padding: 5px;
  background: #f5f5f5;
  border-radius: 4px;
}

@media (max-width: 768px) {
  .config-panel {
    grid-template-columns: 1fr;
  }
  
  .device-grid {
    grid-template-columns: 1fr;
  }
  
  .control-panel button {
    margin: 5px;
    padding: 8px 16px;
    font-size: 14px;
  }
}
</style> 