<template>
  <div class="simple-rtsp-player">
    <div class="video-container">
      <canvas ref="videoCanvas" :width="width" :height="height"></canvas>
      <!-- 状态指示器 - 只在播放中时显示，位置在右上角 -->
      <div v-if="isPlaying" class="status-indicator playing">
        ● 播放中
      </div>
      <!-- 错误信息显示在中间 -->
      <div v-if="statusMessage && isError" class="status-overlay error">
        {{ statusMessage }}
      </div>
    </div>
    <div v-if="showControls" class="controls">
      <button @click="handlePlay" :disabled="isPlaying">播放</button>
      <button @click="handleStop" :disabled="!isPlaying">停止</button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue';

const props = defineProps({
  // 视频ID，对应RTSP流标识
  videoId: {
    type: String,
    required: true
  },
  // WebSocket服务器地址
  wsUrl: {
    type: String,
    default: 'ws://**************:32021/basic/jsmpeg'  // 确保是jsmpeg服务
  },
  // RTSP流地址（如果需要的话）
  rtspUrl: {
    type: String,
    default: ''
  },
  // 播放器尺寸
  width: {
    type: Number,
    default: 320
  },
  height: {
    type: Number,
    default: 180
  },
  // 是否显示控制按钮
  showControls: {
    type: Boolean,
    default: false
  },
  // 是否自动播放
  autoplay: {
    type: Boolean,
    default: true
  }
});

const emit = defineEmits(['error', 'play', 'stop']);

// 引用和状态
const videoCanvas = ref(null);
const player = ref(null);
const isPlaying = ref(false);
const statusMessage = ref('');
const isError = ref(false);
const jsMpegLoaded = ref(false);
const websocket = ref(null); // 添加WebSocket引用
const shouldAutoplay = ref(false); // 记录是否应该自动播放

// 页面可见性状态
const isPageVisible = ref(true);

// 监听页面可见性变化
const handleVisibilityChange = () => {
  isPageVisible.value = !document.hidden;
  
  if (document.hidden) {
    // 页面不可见时，断开连接
    console.log('页面不可见，断开WebSocket连接');
    if (isPlaying.value) {
      shouldAutoplay.value = true; // 记录当前正在播放
    }
    handleStop();
  } else {
    // 页面重新可见时，如果之前在播放则重新连接
    console.log('页面重新可见');
    if (shouldAutoplay.value || props.autoplay) {
      setTimeout(() => {
        console.log('恢复视频播放');
        handlePlay();
      }, 1000); // 延迟1秒重新连接
    }
  }
};

// 加载JSMpeg库
const loadJSMpeg = () => {
  return new Promise((resolve, reject) => {
    if (window.JSMpeg) {
      jsMpegLoaded.value = true;
      resolve();
      return;
    }

    const script = document.createElement('script');
    script.src = 'https://cdn.jsdelivr.net/gh/phoboslab/jsmpeg@master/jsmpeg.min.js';
    script.onload = () => {
      jsMpegLoaded.value = true;
      console.log('JSMpeg 加载成功');
      resolve();
    };
    script.onerror = () => {
      console.error('JSMpeg 加载失败');
      reject(new Error('JSMpeg 加载失败'));
    };
    document.head.appendChild(script);
  });
};

// 更新状态
const updateStatus = (message, error = false) => {
  statusMessage.value = message;
  isError.value = error;
  if (error) {
    console.error(message);
  } else {
    console.log(message);
  }
};

// 播放视频
const handlePlay = async () => {
  // 如果页面不可见，不开始播放
  if (!isPageVisible.value) {
    console.log('页面不可见，暂停播放');
    return;
  }

  if (!jsMpegLoaded.value) {
    updateStatus('正在加载 JSMpeg...');
    try {
      await loadJSMpeg();
    } catch (error) {
      updateStatus('JSMpeg 加载失败', true);
      emit('error', error);
      return;
    }
  }

  if (!videoCanvas.value) {
    updateStatus('视频画布未准备好', true);
    return;
  }

  try {
    // 如果已有播放器实例，先销毁
    if (player.value) {
      player.value.destroy();
      player.value = null;
    }

    // 如果已有WebSocket连接，先关闭
    if (websocket.value) {
      websocket.value.close();
      websocket.value = null;
    }

    updateStatus('正在连接视频流...');

    // 建立WebSocket连接
    const ws = new WebSocket(props.wsUrl);
    ws.binaryType = 'arraybuffer';
    websocket.value = ws; // 保存WebSocket引用

    let isFirstData = true;

    ws.onopen = () => {
      console.log('WebSocket连接成功，发送播放请求');
      updateStatus('连接成功，请求视频流...');
      
      // 发送JSON播放请求
      const message = {
        type: 110,
        data: props.videoId
      };
      console.log('发送播放请求:', message);
      ws.send(JSON.stringify(message));
    };

    ws.onmessage = async (event) => {
      try {
        // 如果页面不可见，不处理数据
        if (!isPageVisible.value) {
          return;
        }

        console.log(`收到消息，类型: ${event.data.constructor.name}`);
        
        if (event.data instanceof ArrayBuffer) {
          // 处理二进制视频数据 - 这是最常见的情况
          console.log(`🔍 [jsmpeg服务] 收到ArrayBuffer数据: ${event.data.byteLength} 字节`);
          
          // 分析数据特征
          const uint8Array = new Uint8Array(event.data);
          console.log('🔍 [jsmpeg服务] 数据特征分析:', {
            大小: event.data.byteLength,
            前20字节: Array.from(uint8Array.slice(0, 20)),
            前20字节HEX: Array.from(uint8Array.slice(0, 20)).map(b => `0x${b.toString(16).padStart(2, '0')}`).join(' '),
            是否以MPEG同步字节开头: uint8Array[0] === 0x47,
            数据类型: analyzeDirectData(uint8Array)
          });
          
          if (isFirstData) {
            // 第一次收到数据时创建播放器
            console.log('🔍 [jsmpeg服务] 首次收到数据，创建直接播放器');
            await createDirectPlayer();
            isFirstData = false;
            updateStatus('');
          }
          
          // 直接将数据写入播放器
          if (player.value && player.value.write) {
            try {
              console.log('🔍 [jsmpeg服务] 直接写入ArrayBuffer到播放器:', {
                播放器类型: player.value.constructor.name,
                数据大小: uint8Array.length,
                播放器状态: {
                  存在: !!player.value,
                  有write方法: !!player.value.write
                }
              });
              
              player.value.write(uint8Array);
              console.log('🔍 [jsmpeg服务] ArrayBuffer写入成功');
              
              if (!isPlaying.value) {
                isPlaying.value = true;
                emit('play');
                console.log('🔍 [jsmpeg服务] 开始播放状态');
              }
            } catch (writeError) {
              console.error('🔍 [jsmpeg服务] 写入数据错误:', writeError);
            }
          } else {
            console.warn('🔍 [jsmpeg服务] 播放器未准备好');
          }
          
        } else if (typeof event.data === 'string') {
          // 处理字符串消息
          console.log('收到字符串消息:', event.data);
          
          try {
            const response = JSON.parse(event.data);
            console.log('解析JSON响应:', response);
            
            if (response.type === 110) {
              updateStatus('');
              
              // 如果响应包含视频流数据
              if (response.data && response.data.stream) {
                console.log('响应包含视频流数据');
                await handleStructuredVideoData(response.data.stream);
              }
            }
          } catch (e) {
            console.log('普通文本消息:', event.data);
          }
        } else if (event.data instanceof Blob) {
          // 处理Blob数据
          console.log(`收到Blob数据: ${event.data.size} 字节`);
          
          if (isFirstData) {
            await createDirectPlayer();
            isFirstData = false;
          }
          
          // 将Blob转换为ArrayBuffer
          const arrayBuffer = await event.data.arrayBuffer();
          if (player.value && player.value.write) {
            const uint8Array = new Uint8Array(arrayBuffer);
            player.value.write(uint8Array);
            
            if (!isPlaying.value) {
              isPlaying.value = true;
              emit('play');
            }
          }
        }
        
      } catch (error) {
        console.error('处理消息错误:', error);
        updateStatus('数据处理错误: ' + error.message, true);
      }
    };

    ws.onerror = (error) => {
      updateStatus('WebSocket连接错误', true);
      emit('error', error);
      console.error('WebSocket错误:', error);
    };

    ws.onclose = (event) => {
      console.log('WebSocket连接关闭:', event);
      if (isPlaying.value) {
        isPlaying.value = false;
        emit('stop');
      }
      websocket.value = null;
    };

    // 创建直接处理二进制数据的播放器
    const createDirectPlayer = async () => {
      try {
        console.log('🔍 [jsmpeg服务] 创建直接播放器...');
        
        // 使用JSMpeg的VideoDecoder直接解码方式
        if (window.JSMpeg && window.JSMpeg.VideoDecoder) {
          // 创建视频解码器
          const decoder = new JSMpeg.VideoDecoder();
          
          // 创建播放器，但不使用WebSocket source
          player.value = {
            canvas: videoCanvas.value,
            decoder: decoder,
            write: function(data) {
              try {
                // 直接解码数据到canvas
                const decoded = this.decoder.decode(data);
                if (decoded && this.canvas) {
                  const ctx = this.canvas.getContext('2d');
                  if (ctx && decoded.width && decoded.height) {
                    // 创建ImageData并绘制
                    const imageData = new ImageData(decoded.y, decoded.width, decoded.height);
                    ctx.putImageData(imageData, 0, 0);
                  }
                }
              } catch (decodeError) {
                console.warn('解码错误:', decodeError);
              }
            },
            destroy: function() {
              if (this.decoder) {
                this.decoder = null;
              }
              this.canvas = null;
            }
          };
        } else {
          // 如果没有VideoDecoder，使用传统方式
          const dummyUrl = `ws://localhost:0/${props.videoId}`;
          
          player.value = new JSMpeg.Player(dummyUrl, {
            canvas: videoCanvas.value,
            autoplay: true,
            audio: false,
            videoBufferSize: 1024 * 1024,
            onConnectionCreate: () => {
              // 返回一个自定义的WebSocket对象
              const mockWs = {
                onopen: null,
                onclose: null,
                onmessage: null,
                onerror: null,
                send: () => {},
                close: () => {},
                readyState: 1,
                constructor: { prototype: { OPEN: 1 } }
              };
              
              // 添加自定义write方法
              mockWs.write = (data) => {
                if (mockWs.onmessage) {
                  mockWs.onmessage({ data: data });
                }
              };
              
              return mockWs;
            }
          });
          
          // 重写player的write方法
          if (player.value && player.value.source) {
            const originalSource = player.value.source;
            player.value.write = function(data) {
              try {
                if (originalSource && originalSource.socket && originalSource.socket.onmessage) {
                  originalSource.socket.onmessage({ data: data });
                }
              } catch (e) {
                console.warn('写入数据到播放器失败:', e);
              }
            };
            
            // 模拟连接建立
            originalSource.established = true;
            originalSource.completed = false;
          }
        }
        
        console.log('🔍 [jsmpeg服务] 播放器创建完成，类型:', player.value.constructor.name);
        
      } catch (error) {
        console.error('🔍 [jsmpeg服务] 创建播放器失败:', error);
        updateStatus('创建播放器失败: ' + error.message, true);
      }
    };

    // 🔥 新增：分析直接数据类型
    const analyzeDirectData = (data) => {
      if (!data || data.length === 0) return '空数据';
      
      const firstByte = data[0];
      const analysis = {
        firstByte: `0x${firstByte.toString(16).padStart(2, '0')}`,
        type: '未知'
      };
      
      if (firstByte === 0x47) {
        analysis.type = 'MPEG-TS包（标准视频流）';
      } else if (firstByte === 0x00 && data.length > 3) {
        if (data[1] === 0x00 && data[2] === 0x00 && data[3] === 0x01) {
          analysis.type = 'H.264 NAL单元';
        } else if (data[1] === 0x00 && data[2] === 0x01) {
          analysis.type = 'MPEG视频流';
        } else {
          analysis.type = '可能是视频帧数据';
        }
      } else if (firstByte >= 0x01 && firstByte <= 0x09) {
        analysis.type = '可能是H.264 NAL';
      } else if (firstByte === 0xFF) {
        analysis.type = '填充数据或音频帧';
      } else {
        analysis.type = '压缩视频数据';
      }
      
      return analysis;
    };

    // 处理结构化的视频数据
    const handleStructuredVideoData = async (streamData) => {
      try {
        console.log('处理结构化视频数据:', typeof streamData);
        
        if (isFirstData) {
          await createDirectPlayer();
          isFirstData = false;
        }
        
        let uint8Array;
        
        if (typeof streamData === 'string') {
          // Base64解码
          console.log('解码Base64数据...');
          const binaryString = atob(streamData);
          uint8Array = new Uint8Array(binaryString.length);
          for (let i = 0; i < binaryString.length; i++) {
            uint8Array[i] = binaryString.charCodeAt(i);
          }
        } else if (streamData instanceof ArrayBuffer) {
          uint8Array = new Uint8Array(streamData);
        } else if (streamData instanceof Uint8Array) {
          uint8Array = streamData;
        } else {
          console.warn('未知的数据格式:', typeof streamData);
          return;
        }
        
        console.log(`处理数据大小: ${uint8Array.length} 字节`);
        
        if (player.value && player.value.write) {
          player.value.write(uint8Array);
          
          if (!isPlaying.value) {
            isPlaying.value = true;
            emit('play');
          }
        }
        
      } catch (error) {
        console.error('处理结构化视频数据失败:', error);
      }
    };

  } catch (error) {
    updateStatus('播放失败: ' + error.message, true);
    emit('error', error);
    console.error('播放失败:', error);
  }
};

// 停止播放
const handleStop = () => {
  // 关闭WebSocket连接
  if (websocket.value) {
    websocket.value.close();
    websocket.value = null;
  }
  
  // 销毁播放器
  if (player.value) {
    player.value.destroy();
    player.value = null;
  }
  
  isPlaying.value = false;
  updateStatus('');
  emit('stop');
};

// 监听videoId变化
watch(() => props.videoId, () => {
  if (isPlaying.value) {
    handleStop();
    setTimeout(() => {
      handlePlay();
    }, 500);
  }
});

// 组件挂载
onMounted(async () => {
  // 添加页面可见性监听
  document.addEventListener('visibilitychange', handleVisibilityChange);
  
  // 加载JSMpeg
  try {
    await loadJSMpeg();
    
    // 如果自动播放，延迟一点时间开始播放
    if (props.autoplay) {
      setTimeout(() => {
        handlePlay();
      }, 1000);
    }
  } catch (error) {
    updateStatus('初始化失败: ' + error.message, true);
  }
});

// 组件卸载
onUnmounted(() => {
  // 移除页面可见性监听
  document.removeEventListener('visibilitychange', handleVisibilityChange);
  
  // 断开连接并清理资源
  handleStop();
});

// 暴露方法给父组件
defineExpose({
  play: handlePlay,
  stop: handleStop,
  destroy: handleStop
});
</script>

<style scoped>
.simple-rtsp-player {
  width: 100%;
  height: 100%;
}

.video-container {
  position: relative;
  width: 100%;
  height: 100%;
}

canvas {
  width: 100%;
  height: 100%;
  background-color: #000;
  display: block;
}

.status-indicator {
  position: absolute;
  top: 8px;
  right: 8px;
  padding: 4px 8px;
  background: rgba(34, 197, 94, 0.9);
  color: white;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  pointer-events: none;
  z-index: 15;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(4px);
}

.status-indicator.playing {
  background: rgba(34, 197, 94, 0.9);
  color: white;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 0.9; }
  50% { opacity: 0.7; }
  100% { opacity: 0.9; }
}

.status-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(220, 38, 38, 0.9);
  color: white;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 12px;
  pointer-events: none;
  z-index: 20;
  max-width: 80%;
  text-align: center;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.status-overlay.error {
  background: rgba(255, 0, 0, 0.7);
  color: white;
}

.controls {
  margin-top: 8px;
  text-align: center;
}

.controls button {
  margin: 0 4px;
  padding: 4px 12px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.controls button:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.controls button:hover:not(:disabled) {
  background: #0056b3;
}
</style> 