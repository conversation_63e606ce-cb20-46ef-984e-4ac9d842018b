<template>
  <PanelBox title="服务监管">
    <template #extra>
      <div class="tab-buttons">
        <div class="tab-btn" :class="{ active: activeTab === 'temperature' }" @click="changeTab('temperature')">
          室温
        </div>
        <div class="divider"></div>
        <div class="tab-btn" :class="{ active: activeTab === 'complaint' }" @click="changeTab('complaint')">
          投诉
        </div>
      </div>
    </template>
    <div class="panel-content">
      <!-- 室温面板 -->
      <div v-if="activeTab === 'temperature'" class="temperature-panel">
        <!-- 室温数值显示 -->
        <div class="temperature-value-container">
          <div class="temperature-value-box">
            <span class="temperature-label">平均室温</span>
            <div class="temperature-value">
              <span class="temperature-number">19.5</span>
              <span class="temperature-unit">°C</span>
            </div>
          </div>
        </div>
        
        <!-- 当前室温分布 -->
        <div class="temperature-distribution">
          <div class="distribution-title">当前室温分布</div>
          <div class="distribution-content">
            <div class="chart-temperature-bar">
              <TemperatureBarChart />
            </div>
            <div class="chart-target-rate">
              <TargetRateChart />
            </div>
          </div>
        </div>
      </div>

      <!-- 投诉面板 -->
      <div v-else class="complaint-panel">
        <!-- 数据统计区域 -->
        <div class="stats-row">
          <div class="stat-item">
            <div class="stat-info">
              <span class="stat-dot"><span class="stat-dot-inner"></span></span>
              <span class="stat-label">总数</span>
            </div>
            <span class="stat-value">100</span>
          </div>
          <div class="stat-item">
            <div class="stat-info">
              <span class="stat-dot"><span class="stat-dot-inner"></span></span>
              <span class="stat-label">未受理</span>
            </div>
            <span class="stat-value">100</span>
          </div>
          <div class="stat-item">
            <div class="stat-info">
              <span class="stat-dot"><span class="stat-dot-inner"></span></span>
              <span class="stat-label">已受理</span>
            </div>
            <span class="stat-value">100</span>
          </div>
          <div class="stat-item">
            <div class="stat-info">
              <span class="stat-dot"><span class="stat-dot-inner"></span></span>
              <span class="stat-label">已办结</span>
            </div>
            <span class="stat-value">100</span>
          </div>
        </div>

        <!-- 图表区域 -->
        <div class="chart-container">
          <div class="chart-left">
            <WorkOrderTypeChart />
          </div>
          <div class="chart-right">
            <WorkOrderCompleteChart />
          </div>
        </div>
      </div>
    </div>
  </PanelBox>
</template>

<script setup>
import { ref, watch } from 'vue'
import PanelBox from '@/components/screen/PanelBox.vue'
import TemperatureBarChart from './components/TemperatureBarChart.vue'
import TargetRateChart from './components/TargetRateChart.vue'
import WorkOrderTypeChart from './components/WorkOrderTypeChart.vue'
import WorkOrderCompleteChart from './components/WorkOrderCompleteChart.vue'

// 当前激活的标签页
const activeTab = ref('temperature')

// 切换标签页
const changeTab = (tab) => {
  activeTab.value = tab
  
  // 切换到投诉标签页时，延迟触发一次window的resize事件
  // 这样可以让echarts图表重新计算大小
  if (tab === 'complaint') {
    setTimeout(() => {
      window.dispatchEvent(new Event('resize'))
    }, 50)
  }
}

// 监听activeTab变化
watch(activeTab, (newVal) => {
  if (newVal === 'complaint') {
    setTimeout(() => {
      window.dispatchEvent(new Event('resize'))
    }, 50)
  }
})
</script>

<style scoped>
.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
}

.tab-buttons {
  display: flex;
  align-items: center;
  margin-right: 30px;
}

.divider {
  width: 1px;
  height: 16px;
  background-color: rgba(255, 255, 255, 0.8);
  margin: 0 10px;
}

.tab-btn {
  cursor: pointer;
  font-family: PingFangSC, 'PingFang SC';
  font-weight: 500;
  font-size: 16px;
  color: rgba(255, 255, 255, 0.3);
  line-height: 22px;
}

.tab-btn.active {
  color: rgba(255, 255, 255, 0.8);
}

/* 室温面板样式 */
.temperature-panel {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.temperature-value-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20px;
}

.temperature-value-box {
  width: 350px;
  height: 40px;
  background-image: url('@/assets/images/screen/heating/pinjunshiwen_bg.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  display: flex;
  justify-content: center;
  align-items: center;
}

.temperature-label {
  font-family: SourceHanSansCN, SourceHanSansCN;
  font-weight: 500;
  font-size: 16px;
  color: #FFFFFF;
  margin-right: 10px;
}

.temperature-value {
  display: flex;
  align-items: baseline;
}

.temperature-number {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 24px;
  color: #FFFFFF;
}

.temperature-unit {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 16px;
  color: #FFFFFF;
  margin-left: 2px;
}

.temperature-distribution {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.distribution-title {
  font-family: SourceHanSansCN, SourceHanSansCN;
  font-weight: 400;
  font-size: 14px;
  color: #FFFFFF;
  line-height: 21px;
  text-align: left;
  font-style: normal;
  margin-bottom: 9px;
}

.distribution-content {
  display: flex;
  flex: 1;
  gap: 2rem;
}

.chart-temperature-bar {
  width: 271px;
  height: 175px;
}

.chart-target-rate {
  width: 120px;
  height: 150px;
}

/* 投诉面板样式 */
.complaint-panel {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 250px;
  height: 100%;
}

.stats-row {
  display: flex;
  justify-content: space-around;
  padding: 0 5px 10px;
  flex-shrink: 0;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
}

.stat-info {
  display: flex;
  align-items: center;
  gap: 4px;
}

.stat-dot {
  position: relative;
  width: 9px;
  height: 9px;
  border-radius: 50%;
  background: rgba(5, 90, 219, 0.4);
}

.stat-dot-inner {
  width: 5px;
  height: 5px;
  border-radius: 50%;
  position: absolute;
  top: 2px;
  left: 2px;
  background: #055ADB;
}

.stat-label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
}

.stat-value {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 24px;
  color: #FFFFFF;
}

.chart-container {
  display: flex;
  flex: 1;
  justify-content: space-between;
  min-height: 200px;
}

.chart-left, .chart-right {
  width: 48%;
  height: 100%;
  min-height: 180px;
}

/* 响应式适配 */
@media screen and (min-width: 1920px) and (max-width: 2560px) {
  .temperature-value-box {
    width: 330px;
    height: 38px;
  }
  
  .chart-temperature-bar {
    width: 260px;
    height: 165px;
  }
  
  .chart-target-rate {
    width: 115px;
    height: 140px;
  }
}

@media screen and (max-width: 1919px) {
  .temperature-value-box {
    width: 310px;
    height: 36px;
  }
  
  .temperature-label {
    font-size: 15px;
  }
  
  .temperature-number {
    font-size: 22px;
  }
  
  .temperature-unit {
    font-size: 15px;
  }
  
  .chart-temperature-bar {
    width: 240px;
    height: 155px;
  }
  
  .chart-target-rate {
    width: 110px;
    height: 130px;
  }
  
  .stat-value {
    font-size: 22px;
  }
}

@media screen and (min-height: 910px) and (max-height: 940px) {
  .panel-content {
    padding: 10px;
  }
  
  .temperature-value-container {
    margin-bottom: 15px;
  }
  .distribution-content {
    gap: 3rem;
  }
  .temperature-value-box {
    width: 290px;
    height: 34px;
  }
  
  .temperature-label {
    font-size: 14px;
  }
  
  .temperature-number {
    font-size: 20px;
  }
  
  .temperature-unit {
    font-size: 14px;
  }
  
  .distribution-title {
    font-size: 13px;
    margin-bottom: 6px;
  }
  
  .chart-temperature-bar {
    width: 220px;
    height: 145px;
  }
  
  .chart-target-rate {
    width: 105px;
    height: 120px;
  }
  
  .stats-row {
    padding: 0 5px 10px;
  }
  
  .stat-value {
    font-size: 20px;
  }
}
</style> 