<template>
  <teleport to="body">
    <transition name="fade">
      <div v-if="modelValue" class="modal-overlay" @click.self="closeModal">
        <div class="modal-container">
          <div class="modal-header">
            <div class="modal-title">场站详情</div>
            <div class="close-icon" @click="closeModal">×</div>
          </div>
          <div class="modal-content">
            <div class="station-detail">
              <div class="detail-row">
                <div class="detail-item">
                  <span class="info-label">风险等级:</span>
                  <span class="info-value" :style="{ color: getRiskColor(stationData.riskLevel) }">{{ stationData.riskLevel }}</span>
                </div>
                <div class="detail-item">
                  <span class="info-label">风险值:</span>
                  <span class="info-value">98</span>
                </div>
              </div>
              <div class="detail-row">
                <div class="detail-item">
                  <span class="info-label">场站编号:</span>
                  <span class="info-value">DFX-001</span>
                </div>
              </div>
              <div class="detail-row">
                <div class="detail-item">
                  <span class="info-label">功能类别:</span>
                  <span class="info-value">高压管线</span>
                </div>
                <div class="detail-item">
                  <span class="info-label">管材:</span>
                  <span class="info-value">PE100</span>
                </div>
              </div>
              <div class="detail-row">
                <div class="detail-item">
                  <span class="info-label">管径:</span>
                  <span class="info-value">400mm</span>
                </div>
                <div class="detail-item">
                  <span class="info-label">管长:</span>
                  <span class="info-value">1.151km</span>
                </div>
              </div>
              <div class="detail-row">
                <div class="detail-item full-width">
                  <span class="info-label">位置路径:</span>
                  <span class="info-value">道路名称多名称</span>
                </div>
              </div>
              <div class="detail-row">
                <div class="detail-item">
                  <span class="info-label">评估时间:</span>
                  <span class="info-value">2025年4月1日</span>
                </div>
                <div class="detail-item">
                  <span class="info-label">评估日期:</span>
                  <span class="info-value">2025年4月1日</span>
                </div>
              </div>
              <div class="detail-row">
                <div class="detail-item full-width">
                  <span class="info-label">监控状态:</span>
                  <span class="info-value">未管控</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </transition>
  </teleport>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue'

const props = defineProps({
  modelValue: Boolean,
  stationData: Object
})

const emit = defineEmits(['update:model-value'])

const closeModal = () => {
  emit('update:model-value', false)
}

// 根据风险等级获取对应的颜色
const getRiskColor = (riskLevel) => {
  switch (riskLevel) {
    case '重大风险':
      return '#FF2330'
    case '较大风险':
      return '#FF9000'
    case '一般风险':
      return '#FFD11B'
    case '低风险':
      return '#00B0FF'
    default:
      return '#FFFFFF'
  }
}
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.modal-container {
  width: 550px;
  background: linear-gradient(180deg, rgba(0, 22, 72, 0.9) 0%, rgba(0, 35, 91, 0.9) 100%);
  border: 1px solid rgba(59, 141, 242, 0.5);
  box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.3);
  border-radius: 4px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid rgba(59, 141, 242, 0.3);
}

.modal-title {
  font-family: PingFangSC, 'PingFang SC';
  font-weight: 500;
  font-size: 16px;
  color: #FFFFFF;
}

.close-icon {
  font-size: 20px;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
}

.close-icon:hover {
  color: #FFFFFF;
}

.modal-content {
  padding: 15px 20px;
}

.station-detail {
  width: 100%;
  padding: 10px 0;
}

.detail-row {
  display: flex;
  margin-bottom: 15px;
}

.detail-item {
  flex: 1;
  display: flex;
  align-items: center;
}

.full-width {
  flex: 2;
}

.info-label {
  font-family: PingFangSC, 'PingFang SC';
  font-weight: 500;
  font-size: 14px;
  color: #D3E5FF;
  margin-right: 5px;
  white-space: nowrap;
}

.info-value {
  font-family: PingFangSC, 'PingFang SC';
  font-weight: 500;
  font-size: 14px;
  color: #FFFFFF;
  flex: 1;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style> 