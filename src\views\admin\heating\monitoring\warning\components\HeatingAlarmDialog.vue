<template>
  <el-dialog
    v-model="dialogVisible"
    :title="'报警详情'"
    width="80%"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    class="heating-alarm-dialog"
  >
    <el-tabs v-model="activeTab">
      <!-- 报警详情标签页 -->
      <el-tab-pane label="报警详情" name="detail">
        <div class="detail-container">
          <div class="detail-left">
            <div class="detail-item">
              <span class="item-label">报警编号：</span>
              <span class="item-value">{{ alarmDetail.alarmCode }}</span>
            </div>
            <div class="detail-item">
              <span class="item-label">报警时间：</span>
              <span class="item-value">{{ formatAlarmTime(alarmDetail.alarmTime) }}</span>
            </div>
            <div class="detail-item">
              <span class="item-label">报警值：</span>
              <span class="item-value">{{ alarmDetail.alarmValue }} {{ alarmDetail.alarmValueUnit }}</span>
            </div>
            <div class="detail-item">
              <span class="item-label">报警位置：</span>
              <span class="item-value">{{ alarmDetail.alarmLocation }}</span>
            </div>
            <div class="detail-item">
              <span class="item-label">报警级别：</span>
              <span class="item-value" :class="getAlarmLevelClass(alarmDetail.alarmLevel)">
                {{ getAlarmLevelText(alarmDetail.alarmLevel) }}
              </span>
            </div>
            <div class="detail-item">
              <span class="item-label">报警状态：</span>
              <span class="item-value">{{ alarmDetail.alarmStatusName }}</span>
            </div>
            <div class="detail-item">
              <span class="item-label">设备编码：</span>
              <span class="item-value">{{ alarmDetail.deviceId }}</span>
            </div>
            <div class="detail-item">
              <span class="item-label">监测对象：</span>
              <span class="item-value">{{ alarmDetail.monitorObjectName }}</span>
            </div>
            <div class="detail-item">
              <span class="item-label">报警类型：</span>
              <span class="item-value">{{ alarmDetail.alarmTypeName }}</span>
            </div>
            <div class="detail-item">
              <span class="item-label">监测指标：</span>
              <span class="item-value">{{ alarmDetail.monitorIndexName }}</span>
            </div>
            <div class="detail-item">
              <span class="item-label">持续时间：</span>
              <span class="item-value">{{ alarmDetail.duration }}</span>
            </div>
          </div>
          <div class="detail-right">
            <div class="timeline-container">
              <el-timeline>
                <el-timeline-item
                  v-for="(activity, index) in activities"
                  :key="index"
                  :type="activity.type"
                  :size="activity.size"
                  :hollow="activity.hollow"
                >
                  <h4>{{ activity.title }}</h4>
                  <p v-if="activity.content">{{ activity.content }}</p>
                  <p v-if="activity.extra.confirmResult">确认结果：{{ activity.extra.confirmResult }}</p>
                  <p v-if="activity.extra.handleStatus">处置状态：{{ activity.extra.handleStatus }}</p>
                  <p v-if="activity.extra.handleUser">处置人：{{ activity.extra.handleUser }}</p>
                  <p v-if="activity.extra.unit">处置单位：{{ activity.extra.unit }}</p>
                  <p>{{ activity.timestamp }}</p>
                </el-timeline-item>
              </el-timeline>
            </div>
          </div>
        </div>
      </el-tab-pane>

      <!-- 监测曲线标签页 -->
      <el-tab-pane label="监测曲线" name="curve">
        <div class="curve-container">
          <div class="curve-header">
            <el-radio-group v-model="selectedTimeRange" @change="handleTimeRangeChange">
              <el-radio-button :value="'24h'">近24小时</el-radio-button>
              <el-radio-button :value="'7d'">近7天</el-radio-button>
              <el-radio-button :value="'30d'">近30天</el-radio-button>
            </el-radio-group>
          </div>
          <div class="curve-chart" ref="chartRef"></div>
        </div>
      </el-tab-pane>

      <!-- 报警记录标签页 -->
      <el-tab-pane label="报警记录" name="record">
        <div class="record-container">
          <el-table :data="alarmRecords" style="width: 100%">
            <el-table-column prop="alarmTime" label="报警时间" min-width="180">
              <template #default="scope">
                {{ formatRecordTime(scope.row.alarmTime) }}
              </template>
            </el-table-column>
            <el-table-column prop="alarmValue" label="报警值" min-width="120">
              <template #default="scope">
                {{ scope.row.alarmValue }} {{ scope.row.alarmValueUnit || '' }}
              </template>
            </el-table-column>
            <el-table-column prop="alarmLevelName" label="报警级别" min-width="120">
              <template #default="scope">
                <span :class="getAlarmLevelClass(scope.row.alarmLevel)">
                  {{ scope.row.alarmLevelName }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="alarmStatusName" label="报警状态" min-width="120" />
          </el-table>
        </div>
      </el-tab-pane>
    </el-tabs>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, onMounted, watch, onUnmounted, nextTick } from 'vue';
import { ElDialog, ElTabs, ElTabPane, ElButton, ElTimeline, ElTimelineItem, ElRadioGroup, ElRadioButton, ElTable, ElTableColumn } from 'element-plus';
import { getHeatingAlarmDetail, getHeatingAlarmMonitorCurve, getHeatingAlarmRecords, getHeatingAlarmStatusList } from '@/api/heating';
import { HEATING_ALARM_LEVEL_MAP } from '@/constants/heating';
import * as echarts from 'echarts';

// 接收父组件传递的参数
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  alarmId: {
    type: String,
    default: ''
  }
});

// 向父组件发送事件
const emit = defineEmits(['update:visible']);

// 弹窗显示状态
const dialogVisible = ref(false);

// 监听visible属性变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal;
  if (newVal && props.alarmId) {
    fetchAlarmDetail();
  }
});

// 监听弹窗显示状态变化
watch(() => dialogVisible.value, (newVal) => {
  emit('update:visible', newVal);
});

// 当前激活的标签页
const activeTab = ref('detail');

// 监听标签页切换
watch(() => activeTab.value, (newVal) => {
  if (newVal === 'curve') {
    nextTick(() => {
      // 重新初始化图表
      if (chart) {
        chart.dispose();
        chart = null;
      }
      fetchMonitorCurve(getTimeRange(selectedTimeRange.value));
    });
  }
});

// 报警详情数据
const alarmDetail = ref({});

// 时间轴数据
const activities = ref([]);

// 获取报警状态记录
const fetchAlarmStatusList = async (alarmId) => {
  try {
    const res = await getHeatingAlarmStatusList({ alarmId });
    if (res.code === 200 && res.data) {
      activities.value = res.data.map(item => ({
        title: item.alarmStatusName,
        content: item.description || '',
        timestamp: item.createTime,
        type: getStatusType(item.alarmStatus),
        size: 'normal',
        hollow: false,
        extra: {
          confirmResult: item.confirmResultName,
          handleStatus: item.handleStatusName,
          handleUser: item.handleUser,
          unit: item.unit
        }
      }));
    }
  } catch (error) {
    console.error('获取报警状态记录失败:', error);
    // 使用模拟数据作为备用
    activities.value = [
      {
        title: '【核实】处理了报警',
        content: '确认处理',
        timestamp: '2022-08-10 15:58',
        type: 'success',
        size: 'normal',
        hollow: false,
        extra: {
          confirmResult: '确认处理',
          handleStatus: '待报修',
          handleUser: '张三',
          unit: '******'
        }
      },
      {
        title: '【核实】确认了报警',
        content: '确认结果：真实报警',
        timestamp: '2022-08-10 15:58',
        type: 'warning',
        size: 'normal',
        hollow: false,
        extra: {
          confirmResult: '确认',
          handleUser: '张三'
        }
      },
      {
        title: '发生报警',
        content: '',
        timestamp: '2022-04-10 15:35',
        type: 'danger',
        size: 'normal',
        hollow: false,
        extra: {}
      }
    ];
  }
};

// 获取状态类型
const getStatusType = (status) => {
  const typeMap = {
    2001901: 'danger',    // 发生报警
    2001902: 'warning',   // 确认报警
    2001903: 'success',   // 处置报警
  };
  return typeMap[status] || 'info';
};

// 监测曲线相关
const selectedTimeRange = ref('24h');
const chartRef = ref(null);
let chart = null;

// 格式化日期为 YYYY-MM-DD HH:mm:ss
const formatDate = (date) => {
  const pad = (num) => String(num).padStart(2, '0');
  return `${date.getFullYear()}-${pad(date.getMonth() + 1)}-${pad(date.getDate())} ${pad(date.getHours())}:${pad(date.getMinutes())}:${pad(date.getSeconds())}`;
};

// 格式化报警时间
const formatAlarmTime = (timeObj) => {
  if (!timeObj) return '';
  // 如果是时间戳对象格式
  if (typeof timeObj === 'object' && timeObj.time) {
    return new Date(timeObj.time).toLocaleString('zh-CN');
  }
  // 如果是字符串格式
  if (typeof timeObj === 'string') {
    return timeObj;
  }
  return '';
};

// 格式化报警记录时间（新接口返回的是字符串格式）
const formatRecordTime = (timeStr) => {
  if (!timeStr) return '';
  // 新接口直接返回格式化的时间字符串
  if (typeof timeStr === 'string') {
    return timeStr;
  }
  // 兼容其他格式
  return formatAlarmTime(timeStr);
};

// 计算时间范围
const getTimeRange = (type) => {
  const endTime = new Date();
  let startTime = new Date();
  
  switch (type) {
    case '7d':
      startTime.setDate(endTime.getDate() - 7);
      break;
    case '30d':
      startTime.setDate(endTime.getDate() - 30);
      break;
    default: // 24h
      startTime.setDate(endTime.getDate() - 1);
      break;
  }
  
  return {
    startTime: formatDate(startTime),
    endTime: formatDate(endTime)
  };
};

// 报警记录数据
const alarmRecords = ref([]);

// 获取报警详情
const fetchAlarmDetail = async () => {
  try {
    const res = await getHeatingAlarmDetail(props.alarmId);
    if (res.code === 200 && res.data) {
      alarmDetail.value = res.data;
      // 获取时间线数据
      fetchAlarmStatusList(props.alarmId);
      // 获取该设备的报警记录
      if (res.data.deviceId) {
        fetchAlarmRecords(res.data.deviceId);
      }
      // 设置默认时间范围为24小时
      selectedTimeRange.value = '24h';
      // 获取监测曲线数据
      fetchMonitorCurve(getTimeRange('24h'));
    }
  } catch (error) {
    console.error('获取报警详情失败:', error);
  }
};

// 获取监测曲线数据
const fetchMonitorCurve = async (timeRange) => {
  if (!alarmDetail.value.deviceId || !timeRange) return;
  
  try {
    const params = {
      deviceId: alarmDetail.value.deviceId,
      startTime: timeRange.startTime,
      endTime: timeRange.endTime
    };
    const res = await getHeatingAlarmMonitorCurve(params);
    if (res.code === 200 && res.data) {
      initChart(res.data);
    }
  } catch (error) {
    console.error('获取监测曲线数据失败:', error);
    // 模拟数据
    const mockData = [];
    const now = new Date();
    for (let i = 0; i < 24; i++) {
      const time = new Date(now.getTime() - (23 - i) * 60 * 60 * 1000);
      mockData.push({
        monitorTime: formatDate(time),
        monitorValue: (Math.random() * 100).toFixed(2)
      });
    }
    initChart(mockData);
  }
};

// 获取报警记录
const fetchAlarmRecords = async (deviceId) => {
  try {
    const res = await getHeatingAlarmRecords(deviceId);
    if (res.code === 200 && res.data) {
      alarmRecords.value = Array.isArray(res.data) ? res.data : [res.data];
    }
  } catch (error) {
    console.error('获取报警记录失败:', error);
    // 模拟数据
    alarmRecords.value = [
      {
        alarmTime: '2025-05-23 13:04:40',
        alarmValue: '29.9',
        alarmLevel: 2001702,
        alarmLevelName: '二级',
        alarmStatusName: '待确认'
      }
    ];
  }
};

// 初始化图表
const initChart = (data) => {
  if (!chartRef.value || !Array.isArray(data)) return;

  // 确保数据格式正确
  const validData = data.filter(item => item && item.monitorTime && item.monitorValue !== undefined);
  if (validData.length === 0) {
    console.warn('没有有效的监测数据');
    return;
  }

  // 使用nextTick确保DOM已经更新
  nextTick(() => {
    if (!chart) {
      chart = echarts.init(chartRef.value);
    }

    // 处理数据
    const xData = validData.map(item => item.monitorTime);
    const yData = validData.map(item => {
      const value = parseFloat(item.monitorValue);
      return isNaN(value) ? null : value;
    });

    const option = {
      tooltip: {
        trigger: 'axis',
        formatter: (params) => {
          const item = params[0];
          return `${item.name}<br/>${item.marker}监测值：${item.value}`;
        }
      },
      grid: {
        top: 30,
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: xData,
        axisLabel: {
          rotate: 30,
          formatter: (value) => value.split(' ')[1] // 只显示时间部分
        }
      },
      yAxis: {
        type: 'value',
        name: '监测值',
        nameTextStyle: {
          padding: [0, 30, 0, 0]
        },
        splitLine: {
          lineStyle: {
            type: 'dashed'
          }
        }
      },
      series: [{
        name: '监测值',
        data: yData,
        type: 'line',
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        itemStyle: {
          color: '#40C3FA'
        },
        lineStyle: {
          width: 2
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0,
              color: 'rgba(64,195,250,0.2)'
            }, {
              offset: 1,
              color: 'rgba(64,195,250,0)'
            }]
          }
        }
      }]
    };

    chart.setOption(option);
    chart.resize();
  });
};

// 处理时间范围变化
const handleTimeRangeChange = (value) => {
  fetchMonitorCurve(getTimeRange(value));
};

// 获取报警等级文本
const getAlarmLevelText = (level) => {
  return HEATING_ALARM_LEVEL_MAP[level] || '';
};

// 获取报警等级样式
const getAlarmLevelClass = (level) => {
  const map = {
    2001701: 'alarm-level-first',
    2001702: 'alarm-level-second',
    2001703: 'alarm-level-third',
    2001704: 'alarm-level-third'
  };
  return ['alarm-level-tag', map[level]];
};

// 处理关闭
const handleClose = () => {
  dialogVisible.value = false;
};

// 监听窗口大小变化，重绘图表
const handleResize = () => {
  if (chart) {
    chart.resize();
  }
};

onMounted(() => {
  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  if (chart) {
    chart.dispose();
    chart = null;
  }
});
</script>

<style scoped>
.heating-alarm-dialog {
  :deep(.el-dialog__body) {
    padding: 20px;
    max-height: calc(94vh - 120px);
    overflow-y: auto;
  }
}

.detail-container {
  display: flex;
  gap: 20px;
  min-height: 400px;
}

.detail-left {
  flex: 1;
  padding: 20px;
  background: #F8F9FA;
  border-radius: 4px;
}

.detail-right {
  flex: 1;
  padding: 20px;
  background: #F8F9FA;
  border-radius: 4px;
}

.detail-item {
  margin-bottom: 16px;
  display: flex;
  align-items: center;
}

.item-label {
  width: 100px;
  color: #606266;
  font-size: 14px;
}

.item-value {
  flex: 1;
  color: #303133;
  font-size: 14px;
}

.timeline-container {
  height: 400px;
  overflow-y: auto;
}

.curve-container {
  padding: 20px;
  min-height: 500px;
}

.curve-header {
  margin-bottom: 20px;
}

.curve-chart {
  height: 400px;
}

.record-container {
  padding: 20px;
  min-height: 400px;
}

/* 报警等级标签样式 */
.alarm-level-tag {
  padding: 2px 10px;
  border-radius: 4px;
  font-size: 12px;
  display: inline-block;
  white-space: nowrap;
  flex: unset;
}

.alarm-level-first {
  background: rgba(255,0,0,0.1);
  border: 1px solid #FF0000;
  color: #FF0000;
}

.alarm-level-second {
  background: rgba(255,133,0,0.1);
  border: 1px solid #FF8500;
  color: #FF8500;
}

.alarm-level-third {
  background: rgba(255,211,0,0.1);
  border: 1px solid #FFD300;
  color: #FFD300;
}

.dialog-footer {
  text-align: right;
  padding-top: 20px;
}
</style> 