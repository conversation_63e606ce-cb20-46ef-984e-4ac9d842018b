import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// 获取当前文件路径
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 页面模板
const generatePageTemplate = (title) => `<template>
  <div class="bridge-page-container">
    <h2 class="text-2xl font-bold mb-4">${title}</h2>
    <div class="bg-white p-6 rounded-lg shadow">
      <div class="text-gray-700">内容开发中...</div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

onMounted(() => {
  console.log('${title}组件已挂载')
})
</script>

<style scoped>
.bridge-page-container {
  padding: 20px;
}
</style>`;

// 要创建的页面
const pagesToCreate = [
  // 实时监测页面
  { path: 'monitoring/realtime/static.vue', title: '静态响应监测与分析' },
  { path: 'monitoring/realtime/dynamic.vue', title: '动态响应监测与分析' },
  { path: 'monitoring/realtime/vibration.vue', title: '意外震动监测与分析' },
  { path: 'monitoring/realtime/traffic.vue', title: '交通荷载监测与分析' },
  
  // 桥梁报警管理
  { path: 'alarm/management/info.vue', title: '桥梁报警信息管理' },
  { path: 'alarm/management/disposal.vue', title: '桥梁报警信息处置' },
  
  // 桥梁检测养护管理
  { path: 'inspection/maintenance/plan.vue', title: '检测养护计划管理' },
  { path: 'inspection/maintenance/record.vue', title: '检测养护记录管理' },
  { path: 'inspection/maintenance/report.vue', title: '检测报告管理' },
  { path: 'inspection/maintenance/data.vue', title: '维修养护数据管理' },
  { path: 'inspection/maintenance/disease.vue', title: '病害数据管理' },
  
  // 桥梁数据分析
  { path: 'analysis/data/extreme.vue', title: '极值分析' },
  { path: 'analysis/data/comparison.vue', title: '对比分析' },
  { path: 'analysis/data/correlation.vue', title: '关联分析' },
  { path: 'analysis/data/multi-channel.vue', title: '多通道分析' },
  { path: 'analysis/data/deflection.vue', title: '挠度分析' },
  { path: 'analysis/data/multi-device.vue', title: '多设备频谱分析' },
  { path: 'analysis/data/multi-period.vue', title: '多时段频谱分析' },
  { path: 'analysis/data/filter.vue', title: '滤波分析' },
  
  // 桥梁数据统计分析
  { path: 'statistics/analysis/info.vue', title: '桥梁信息统计与分析' },
  { path: 'statistics/analysis/alarm.vue', title: '报警信息统计与分析' },
  { path: 'statistics/analysis/safety.vue', title: '安全评估统计与分析' },
  { path: 'statistics/analysis/overload.vue', title: '超载数据统计与分析' },
  { path: 'statistics/analysis/monitoring.vue', title: '监测数据统计与分析' }
];

// 基础路径
const basePath = path.join(__dirname, '../src/views/admin/bridge');

// 创建文件夹和文件
pagesToCreate.forEach(page => {
  const filePath = path.join(basePath, page.path);
  const dirPath = path.dirname(filePath);
  
  // 创建目录
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
    console.log(`创建目录: ${dirPath}`);
  }
  
  // 创建文件
  fs.writeFileSync(filePath, generatePageTemplate(page.title));
  console.log(`创建文件: ${filePath}`);
});

console.log('所有桥梁页面生成完成!');