import axios from 'axios'
import { ElMessage } from 'element-plus'

const TOKEN = 'Admin-Token'

// 创建 axios 实例
const service = axios.create({
  baseURL: '/api',
  timeout: 50000,
  headers: {
    "Content-Type": "application/json;charset=utf-8",
    "NowTime": new Date().getTime()
  },
  // 禁用浏览器默认的重定向行为
  maxRedirects: 0
})

// 阻止axios自动转换内容类型
axios.defaults.transformRequest = [
  (data, headers) => {
    // 确保headers不被覆盖
    headers['Content-Type'] = 'application/json;charset=utf-8';
    headers['NowTime'] = new Date().getTime();
    // 如果数据是对象，转换为JSON字符串
    if (data && typeof data === 'object') {
      return JSON.stringify(data);
    }
    return data;
  }
];

const resetToken = () => {
  localStorage.setItem(TOKEN, '')
  location.reload()
}

// 请求拦截器
service.interceptors.request.use(
  config => {
    const token = localStorage.getItem(TOKEN)
    if (token) {
      config.headers.Authorization = token
    }
    // 强制设置这些头，防止被覆盖
    config.headers['Content-Type'] = 'application/json;charset=utf-8';
    config.headers['NowTime'] = new Date().getTime();
    
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  response => {
    // 处理特殊响应，如文件下载
    if (
      response?.headers?.['content-type'] === 
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ) {
      return response;
    }
    
    // 二进制数据直接返回
    if (response.data instanceof ArrayBuffer) {
      return response;
    }
    
    // 统一处理200响应，直接返回data
    if (response.status === 200) {
      return response.data;
    }
    
    return response;
  },
  error => {
    // 处理重定向问题
    if (error.response && error.response.status === 302) {
      const location = error.response.headers.location
      if (location) {
        // 手动处理重定向
        window.location.href = location
        return new Promise(() => {}) // 中断当前请求链
      }
    }
    
    if (error.response && error.response.status === 401) {
      resetToken()
    }
    
    ElMessage.error(error.message || '系统出错')
    return Promise.reject(error)
  }
)

export default service