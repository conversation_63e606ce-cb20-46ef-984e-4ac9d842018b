/**
 *    ████████                   ██                   ██       ██                  ██      ██
 *   ██░░░░░░██                 ░░                   ░██      ░██                 ░██     ░██
 *  ██      ░░   █████  ███████  ██ ██   ██  ██████  ░██   █  ░██  ██████  ██████ ░██     ░██
 * ░██          ██░░░██░░██░░░██░██░██  ░██ ██░░░░   ░██  ███ ░██ ██░░░░██░░██░░█ ░██  ██████
 * ░██    █████░███████ ░██  ░██░██░██  ░██░░█████   ░██ ██░██░██░██   ░██ ░██ ░  ░██ ██░░░██
 * ░░██  ░░░░██░██░░░░  ░██  ░██░██░██  ░██ ░░░░░██  ░████ ░░████░██   ░██ ░██    ░██░██  ░██
 *  ░░████████ ░░██████ ███  ░██░██░░██████ ██████   ░██░   ░░░██░░██████ ░███    ███░░██████
 *   ░░░░░░░░   ░░░░░░ ░░░   ░░ ░░  ░░░░░░ ░░░░░░    ░░       ░░  ░░░░░░  ░░░    ░░░  ░░░░░░
 *
 * @license
 * GeniusWorld Web SDK - http://zydlxx.cn:1234/
 * Version 3.5.0#develop-c1086f5b@202310131719
 *
 *
 * Copyright © 2020 - 2023 正元地理信息集团股份有限公司. All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
define(["exports","./Matrix3-5f307711","./Matrix2-0f368b7a","./defaultValue-c9f7e7c2","./Math-a28f0fcd"],(function(n,e,a,t,r){"use strict";function i(n,a){this.normal=e.Cartesian3.clone(n),this.distance=a}i.fromPointNormal=function(n,a,r){const s=-e.Cartesian3.dot(a,n);return t.defined(r)?(e.Cartesian3.clone(a,r.normal),r.distance=s,r):new i(a,s)};const s=new e.Cartesian3;i.fromCartesian4=function(n,a){const r=e.Cartesian3.fromCartesian4(n,s),o=n.w;return t.defined(a)?(e.Cartesian3.clone(r,a.normal),a.distance=o,a):new i(r,o)},i.getPointDistance=function(n,a){return e.Cartesian3.dot(n.normal,a)+n.distance};const o=new e.Cartesian3;i.projectPointOntoPlane=function(n,a,r){t.defined(r)||(r=new e.Cartesian3);const s=i.getPointDistance(n,a),c=e.Cartesian3.multiplyByScalar(n.normal,s,o);return e.Cartesian3.subtract(a,c,r)};const c=new a.Matrix4,l=new a.Cartesian4,d=new e.Cartesian3;i.transform=function(n,t,r){const s=n.normal,o=n.distance,m=a.Matrix4.inverseTranspose(t,c);let f=a.Cartesian4.fromElements(s.x,s.y,s.z,o,l);f=a.Matrix4.multiplyByVector(m,f,f);const C=e.Cartesian3.fromCartesian4(f,d);return f=a.Cartesian4.divideByScalar(f,e.Cartesian3.magnitude(C),f),i.fromCartesian4(f,r)},i.clone=function(n,a){return t.defined(a)?(e.Cartesian3.clone(n.normal,a.normal),a.distance=n.distance,a):new i(n.normal,n.distance)},i.equals=function(n,a){return n.distance===a.distance&&e.Cartesian3.equals(n.normal,a.normal)},i.ORIGIN_XY_PLANE=Object.freeze(new i(e.Cartesian3.UNIT_Z,0)),i.ORIGIN_YZ_PLANE=Object.freeze(new i(e.Cartesian3.UNIT_X,0)),i.ORIGIN_ZX_PLANE=Object.freeze(new i(e.Cartesian3.UNIT_Y,0)),i.intersect=function(n,a,i){const s=n.origin,o=n.direction;let c=0;const l=e.Cartesian3.dot(o,a.normal);return Math.abs(l)<r.CesiumMath.EPSILON8?s.x*a.normal.x*s.y*a.normal.y+s.z*a.normal.z+a.distance<r.CesiumMath.EPSILON8?i=t.defined(i)?e.Cartesian3.fromElements(s.x,s.y,s.z,i):new e.Cartesian3(s.x,s.y,s.z):void 0:(c=-(a.normal.x*s.x+a.normal.y*s.y+a.normal.z*s.z+a.distance),c/=l,t.defined(i)||(i=new e.Cartesian3),i=e.Cartesian3.multiplyByScalar(o,c,i),e.Cartesian3.add(s,i,i))},n.Plane=i}));
