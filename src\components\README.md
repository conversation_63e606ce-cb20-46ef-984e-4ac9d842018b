# SVG图标组件使用指南

## 简介

`SvgIcon` 组件是一个通用的SVG图标显示组件，支持多种方式显示SVG图标，包括：

1. 通过名称引用项目内置的SVG图标
2. 通过外部URL加载SVG图标
3. 直接使用SVG字符串内联显示

## 安装和配置

组件已经在项目中全局注册，可以直接使用。SVG图标存放在 `src/assets/icons/svg` 目录下。

## 使用方法

### 1. 使用名称引用图标

```vue
<template>
  <!-- 基本使用 -->
  <SvgIcon name="example" />
  
  <!-- 自定义颜色和大小 -->
  <SvgIcon name="user" color="red" size="24px" />
  
  <!-- 带旋转动画 -->
  <SvgIcon name="loading" :spin="true" />
</template>
```

### 2. 使用外部SVG路径

```vue
<template>
  <SvgIcon src="/path/to/icon.svg" size="32px" />
</template>
```

### 3. 使用原始SVG字符串

```vue
<template>
  <SvgIcon :raw="svgString" color="#2196F3" />
</template>

<script setup>
const svgString = `
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.95-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z" fill="currentColor" />
</svg>`;
</script>
```

## 组件属性

| 属性名 | 类型   | 默认值        | 说明                                     |
|--------|--------|---------------|------------------------------------------|
| name   | String | ''            | 图标名称，对应assets/icons/svg目录下的文件名（不含.svg后缀） |
| color  | String | 'currentColor'| 图标颜色，可以是任何有效的CSS颜色值     |
| size   | String | '1em'         | 图标大小，可以是任何有效的CSS尺寸值     |
| src    | String | ''            | 外部SVG图标的URL路径                    |
| raw    | String | ''            | 原始SVG字符串                           |
| spin   | Boolean| false         | 是否添加旋转动画                         |

## 添加新图标

1. 将SVG文件放到 `src/assets/icons/svg` 目录下
2. 文件名即为使用时的图标名称（不含.svg后缀）
3. 确保SVG文件内有 `viewBox` 属性和 `path` 元素
4. 建议将SVG中的 `fill` 属性设为 `currentColor`，以便通过组件的 `color` 属性控制颜色

## 优势与注意事项

- 支持任意颜色修改和尺寸调整
- 图标颜色跟随文本颜色（使用currentColor时）
- 支持通过CSS变量动态修改颜色
- 可以通过添加类名使用项目中的动画效果
- SVG图标比图片更清晰，无失真放大

## 示例演示

可以查看 `SvgIconDemo.vue` 组件，了解各种使用方式的具体示例。 