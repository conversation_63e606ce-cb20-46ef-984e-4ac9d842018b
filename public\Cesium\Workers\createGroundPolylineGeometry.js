/**
 *    ████████                   ██                   ██       ██                  ██      ██
 *   ██░░░░░░██                 ░░                   ░██      ░██                 ░██     ░██
 *  ██      ░░   █████  ███████  ██ ██   ██  ██████  ░██   █  ░██  ██████  ██████ ░██     ░██
 * ░██          ██░░░██░░██░░░██░██░██  ░██ ██░░░░   ░██  ███ ░██ ██░░░░██░░██░░█ ░██  ██████
 * ░██    █████░███████ ░██  ░██░██░██  ░██░░█████   ░██ ██░██░██░██   ░██ ░██ ░  ░██ ██░░░██
 * ░░██  ░░░░██░██░░░░  ░██  ░██░██░██  ░██ ░░░░░██  ░████ ░░████░██   ░██ ░██    ░██░██  ░██
 *  ░░████████ ░░██████ ███  ░██░██░░██████ ██████   ░██░   ░░░██░░██████ ░███    ███░░██████
 *   ░░░░░░░░   ░░░░░░ ░░░   ░░ ░░  ░░░░░░ ░░░░░░    ░░       ░░  ░░░░░░  ░░░    ░░░  ░░░░░░
 *
 * @license
 * GeniusWorld Web SDK - http://zydlxx.cn:1234/
 * Version 4.0.0.B#develop-d3ff2330@202405231107
 *
 *
 * Copyright © 2020 - 2023 正元地理信息集团股份有限公司. All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
define(["./Transforms-6a5d79d3","./Matrix2-d550732e","./Matrix3-79d15570","./defaultValue-7b61670d","./Math-6acd1674","./ArcType-378e21f1","./arrayRemoveDuplicates-3cf34348","./ComponentDatatype-e95dda25","./EllipsoidGeodesic-c3042500","./EllipsoidRhumbLine-997e9b1a","./EncodedCartesian3-e2f2e578","./GeometryAttribute-d24f9032","./IntersectionTests-044bd161","./Plane-e4eb0e88","./WebMercatorProjection-c5288372","./combine-bc3d0d90","./RuntimeError-7dc4ea5a","./WebGLConstants-68839929"],(function(e,t,a,n,i,r,s,o,l,c,u,C,d,p,h,g,f,m){"use strict";function w(i){i=n.defaultValue(i,n.defaultValue.EMPTY_OBJECT),this._ellipsoid=n.defaultValue(i.ellipsoid,a.Ellipsoid.WGS84),this._rectangle=n.defaultValue(i.rectangle,t.Rectangle.MAX_VALUE),this._projection=new e.GeographicProjection(this._ellipsoid),this._numberOfLevelZeroTilesX=n.defaultValue(i.numberOfLevelZeroTilesX,2),this._numberOfLevelZeroTilesY=n.defaultValue(i.numberOfLevelZeroTilesY,1)}Object.defineProperties(w.prototype,{ellipsoid:{get:function(){return this._ellipsoid}},rectangle:{get:function(){return this._rectangle}},projection:{get:function(){return this._projection}}}),w.prototype.getNumberOfXTilesAtLevel=function(e){return this._numberOfLevelZeroTilesX<<e},w.prototype.getNumberOfYTilesAtLevel=function(e){return this._numberOfLevelZeroTilesY<<e},w.prototype.rectangleToNativeRectangle=function(e,a){const r=i.CesiumMath.toDegrees(e.west),s=i.CesiumMath.toDegrees(e.south),o=i.CesiumMath.toDegrees(e.east),l=i.CesiumMath.toDegrees(e.north);return n.defined(a)?(a.west=r,a.south=s,a.east=o,a.north=l,a):new t.Rectangle(r,s,o,l)},w.prototype.tileXYToNativeRectangle=function(e,t,a,n){const r=this.tileXYToRectangle(e,t,a,n);return r.west=i.CesiumMath.toDegrees(r.west),r.south=i.CesiumMath.toDegrees(r.south),r.east=i.CesiumMath.toDegrees(r.east),r.north=i.CesiumMath.toDegrees(r.north),r},w.prototype.tileXYToRectangle=function(e,a,i,r){const s=this._rectangle,o=this.getNumberOfXTilesAtLevel(i),l=this.getNumberOfYTilesAtLevel(i),c=s.width/o,u=e*c+s.west,C=(e+1)*c+s.west,d=s.height/l,p=s.north-a*d,h=s.north-(a+1)*d;return n.defined(r)||(r=new t.Rectangle(u,h,C,p)),r.west=u,r.south=h,r.east=C,r.north=p,r},w.prototype.positionToTileXY=function(e,a,r){const s=this._rectangle;if(!t.Rectangle.contains(s,e))return;const o=this.getNumberOfXTilesAtLevel(a),l=this.getNumberOfYTilesAtLevel(a),c=s.width/o,u=s.height/l;let C=e.longitude;s.east<s.west&&(C+=i.CesiumMath.TWO_PI);let d=(C-s.west)/c|0;d>=o&&(d=o-1);let p=(s.north-e.latitude)/u|0;return p>=l&&(p=l-1),n.defined(r)?(r.x=d,r.y=p,r):new t.Cartesian2(d,p)};const y=new a.Cartesian3,M=new a.Cartesian3,T=new a.Cartographic,E=new a.Cartesian3,_=new a.Cartesian3,O=new e.BoundingSphere,b=new w,P=[new a.Cartographic,new a.Cartographic,new a.Cartographic,new a.Cartographic],A=new t.Cartesian2,k={};function L(e){a.Cartographic.fromRadians(e.east,e.north,0,P[0]),a.Cartographic.fromRadians(e.west,e.north,0,P[1]),a.Cartographic.fromRadians(e.east,e.south,0,P[2]),a.Cartographic.fromRadians(e.west,e.south,0,P[3]);let t=0,n=0,i=0,r=0;const s=k._terrainHeightsMaxLevel;let o;for(o=0;o<=s;++o){let e=!1;for(let t=0;t<4;++t){const a=P[t];if(b.positionToTileXY(a,o,A),0===t)i=A.x,r=A.y;else if(i!==A.x||r!==A.y){e=!0;break}}if(e)break;t=i,n=r}if(0!==o)return{x:t,y:n,level:o>s?s:o-1}}k.initialize=function(){let t=k._initPromise;return n.defined(t)||(t=e.Resource.fetchJson(e.buildModuleUrl("Assets/approximateTerrainHeights.json")).then((function(e){k._terrainHeights=e})),k._initPromise=t),t},k.getMinimumMaximumHeights=function(e,i){i=n.defaultValue(i,a.Ellipsoid.WGS84);const r=L(e);let s=k._defaultMinTerrainHeight,o=k._defaultMaxTerrainHeight;if(n.defined(r)){const l=`${r.level}-${r.x}-${r.y}`,c=k._terrainHeights[l];n.defined(c)&&(s=c[0],o=c[1]),i.cartographicToCartesian(t.Rectangle.northeast(e,T),y),i.cartographicToCartesian(t.Rectangle.southwest(e,T),M),a.Cartesian3.midpoint(M,y,E);const u=i.scaleToGeodeticSurface(E,_);if(n.defined(u)){const e=a.Cartesian3.distance(E,u);s=Math.min(s,-e)}else s=k._defaultMinTerrainHeight}return s=Math.max(k._defaultMinTerrainHeight,s),{minimumTerrainHeight:s,maximumTerrainHeight:o}},k.getBoundingSphere=function(t,i){i=n.defaultValue(i,a.Ellipsoid.WGS84);const r=L(t);let s=k._defaultMaxTerrainHeight;if(n.defined(r)){const e=`${r.level}-${r.x}-${r.y}`,t=k._terrainHeights[e];n.defined(t)&&(s=t[1])}const o=e.BoundingSphere.fromRectangle3D(t,i,0);return e.BoundingSphere.fromRectangle3D(t,i,s,O),e.BoundingSphere.union(o,O,o)},k._terrainHeightsMaxLevel=6,k._defaultMaxTerrainHeight=9e3,k._defaultMinTerrainHeight=-1e5,k._terrainHeights=void 0,k._initPromise=void 0,Object.defineProperties(k,{initialized:{get:function(){return n.defined(k._terrainHeights)}}});var S=k;const x=[e.GeographicProjection,h.WebMercatorProjection],I=x.length,N=Math.cos(i.CesiumMath.toRadians(30)),R=Math.cos(i.CesiumMath.toRadians(150)),D=0,v=1e3;function z(e){const t=(e=n.defaultValue(e,n.defaultValue.EMPTY_OBJECT)).positions;this.width=n.defaultValue(e.width,1),this._positions=t,this.granularity=n.defaultValue(e.granularity,9999),this.loop=n.defaultValue(e.loop,!1),this.arcType=n.defaultValue(e.arcType,r.ArcType.GEODESIC),this._ellipsoid=a.Ellipsoid.WGS84,this._projectionIndex=0,this._workerName="createGroundPolylineGeometry",this._scene3DOnly=!1}Object.defineProperties(z.prototype,{packedLength:{get:function(){return 1+3*this._positions.length+1+1+1+a.Ellipsoid.packedLength+1+1}}}),z.setProjectionAndEllipsoid=function(e,t){let a=0;for(let e=0;e<I;e++)if(t instanceof x[e]){a=e;break}e._projectionIndex=a,e._ellipsoid=t.ellipsoid};const H=new a.Cartesian3,B=new a.Cartesian3,V=new a.Cartesian3;function j(e,t,n,i,r){const s=U(i,e,0,H),o=U(i,e,n,B),l=U(i,t,0,V),c=Z(o,s,B),u=Z(l,s,V);return a.Cartesian3.cross(u,c,r),a.Cartesian3.normalize(r,r)}const G=new a.Cartographic,Y=new a.Cartesian3,F=new a.Cartesian3,q=new a.Cartesian3;function X(e,t,n,i,s,o,u,C,d,p,h){if(0===s)return;let g;o===r.ArcType.GEODESIC?g=new l.EllipsoidGeodesic(e,t,u):o===r.ArcType.RHUMB&&(g=new c.EllipsoidRhumbLine(e,t,u));const f=g.surfaceDistance;if(f<s)return;const m=j(e,t,i,u,q),w=Math.ceil(f/s),y=f/w;let M=y;const T=w-1;let E=C.length;for(let e=0;e<T;e++){const e=g.interpolateUsingSurfaceDistance(M,G),t=U(u,e,n,Y),r=U(u,e,i,F);a.Cartesian3.pack(m,C,E),a.Cartesian3.pack(t,d,E),a.Cartesian3.pack(r,p,E),h.push(e.latitude),h.push(e.longitude),E+=3,M+=y}}const W=new a.Cartographic;function U(e,t,n,i){return a.Cartographic.clone(t,W),W.height=n,a.Cartographic.toCartesian(W,e,i)}function Z(e,t,n){return a.Cartesian3.subtract(e,t,n),a.Cartesian3.normalize(n,n),n}function $(e,t,n,i){return i=Z(e,t,i),i=a.Cartesian3.cross(i,n,i),i=a.Cartesian3.normalize(i,i),i=a.Cartesian3.cross(n,i,i)}z.pack=function(e,t,i){let r=n.defaultValue(i,0);const s=e._positions,o=s.length;t[r++]=o;for(let e=0;e<o;++e){const n=s[e];a.Cartesian3.pack(n,t,r),r+=3}return t[r++]=e.granularity,t[r++]=e.loop?1:0,t[r++]=e.arcType,a.Ellipsoid.pack(e._ellipsoid,t,r),r+=a.Ellipsoid.packedLength,t[r++]=e._projectionIndex,t[r++]=e._scene3DOnly?1:0,t},z.unpack=function(e,t,i){let r=n.defaultValue(t,0);const s=e[r++],o=new Array(s);for(let t=0;t<s;t++)o[t]=a.Cartesian3.unpack(e,r),r+=3;const l=e[r++],c=1===e[r++],u=e[r++],C=a.Ellipsoid.unpack(e,r);r+=a.Ellipsoid.packedLength;const d=e[r++],p=1===e[r++];return n.defined(i)||(i=new z({positions:o})),i._positions=o,i.granularity=l,i.loop=c,i.arcType=u,i._ellipsoid=C,i._projectionIndex=d,i._scene3DOnly=p,i};const J=new a.Cartesian3,Q=new a.Cartesian3,K=new a.Cartesian3,ee=new a.Cartesian3;function te(e,t,n,r,s){const o=Z(n,t,ee),l=$(e,t,o,J),c=$(r,t,o,Q);if(i.CesiumMath.equalsEpsilon(a.Cartesian3.dot(l,c),-1,i.CesiumMath.EPSILON5))return s=a.Cartesian3.cross(o,l,s),s=a.Cartesian3.normalize(s,s);s=a.Cartesian3.add(c,l,s),s=a.Cartesian3.normalize(s,s);const u=a.Cartesian3.cross(o,s,K);return a.Cartesian3.dot(c,u)<0&&(s=a.Cartesian3.negate(s,s)),s}const ae=p.Plane.fromPointNormal(a.Cartesian3.ZERO,a.Cartesian3.UNIT_Y),ne=new a.Cartesian3,ie=new a.Cartesian3,re=new a.Cartesian3,se=new a.Cartesian3,oe=new a.Cartesian3,le=new a.Cartesian3,ce=new a.Cartographic,ue=new a.Cartographic,Ce=new a.Cartographic;z.createGeometry=function(l){const p=!l._scene3DOnly;let h=l.loop;const g=l._ellipsoid,f=l.granularity,m=l.arcType,w=new x[l._projectionIndex](g),y=D,M=v;let T,E;const _=l._positions,O=_.length;let b,P,A,k;2===O&&(h=!1);const L=new c.EllipsoidRhumbLine(void 0,void 0,g);let I,R,z;const H=[_[0]];for(E=0;E<O-1;E++)b=_[E],P=_[E+1],I=d.IntersectionTests.lineSegmentPlane(b,P,ae,le),!n.defined(I)||a.Cartesian3.equalsEpsilon(I,b,i.CesiumMath.EPSILON7)||a.Cartesian3.equalsEpsilon(I,P,i.CesiumMath.EPSILON7)||(l.arcType===r.ArcType.GEODESIC?H.push(a.Cartesian3.clone(I)):l.arcType===r.ArcType.RHUMB&&(z=g.cartesianToCartographic(I,ce).longitude,A=g.cartesianToCartographic(b,ce),k=g.cartesianToCartographic(P,ue),L.setEndPoints(A,k),R=L.findIntersectionWithLongitude(z,Ce),I=g.cartographicToCartesian(R,le),!n.defined(I)||a.Cartesian3.equalsEpsilon(I,b,i.CesiumMath.EPSILON7)||a.Cartesian3.equalsEpsilon(I,P,i.CesiumMath.EPSILON7)||H.push(a.Cartesian3.clone(I)))),H.push(P);h&&(b=_[O-1],P=_[0],I=d.IntersectionTests.lineSegmentPlane(b,P,ae,le),!n.defined(I)||a.Cartesian3.equalsEpsilon(I,b,i.CesiumMath.EPSILON7)||a.Cartesian3.equalsEpsilon(I,P,i.CesiumMath.EPSILON7)||(l.arcType===r.ArcType.GEODESIC?H.push(a.Cartesian3.clone(I)):l.arcType===r.ArcType.RHUMB&&(z=g.cartesianToCartographic(I,ce).longitude,A=g.cartesianToCartographic(b,ce),k=g.cartesianToCartographic(P,ue),L.setEndPoints(A,k),R=L.findIntersectionWithLongitude(z,Ce),I=g.cartographicToCartesian(R,le),!n.defined(I)||a.Cartesian3.equalsEpsilon(I,b,i.CesiumMath.EPSILON7)||a.Cartesian3.equalsEpsilon(I,P,i.CesiumMath.EPSILON7)||H.push(a.Cartesian3.clone(I)))));let B=H.length,V=new Array(B);for(E=0;E<B;E++){const e=a.Cartographic.fromCartesian(H[E],g);e.height=0,V[E]=e}if(V=s.arrayRemoveDuplicates(V,a.Cartographic.equalsEpsilon),B=V.length,B<2)return;const G=[],Y=[],F=[],q=[];let W=ne,$=ie,J=re,Q=se,K=oe;const ee=V[0],de=V[1];for(W=U(g,V[B-1],y,W),Q=U(g,de,y,Q),$=U(g,ee,y,$),J=U(g,ee,M,J),K=h?te(W,$,J,Q,K):j(ee,de,M,g,K),a.Cartesian3.pack(K,Y,0),a.Cartesian3.pack($,F,0),a.Cartesian3.pack(J,q,0),G.push(ee.latitude),G.push(ee.longitude),X(ee,de,y,M,f,m,g,Y,F,q,G),E=1;E<B-1;++E){W=a.Cartesian3.clone($,W),$=a.Cartesian3.clone(Q,$);const e=V[E];U(g,e,M,J),U(g,V[E+1],y,Q),te(W,$,J,Q,K),T=Y.length,a.Cartesian3.pack(K,Y,T),a.Cartesian3.pack($,F,T),a.Cartesian3.pack(J,q,T),G.push(e.latitude),G.push(e.longitude),X(V[E],V[E+1],y,M,f,m,g,Y,F,q,G)}const pe=V[B-1],he=V[B-2];if($=U(g,pe,y,$),J=U(g,pe,M,J),h){const e=V[0];W=U(g,he,y,W),Q=U(g,e,y,Q),K=te(W,$,J,Q,K)}else K=j(he,pe,M,g,K);if(T=Y.length,a.Cartesian3.pack(K,Y,T),a.Cartesian3.pack($,F,T),a.Cartesian3.pack(J,q,T),G.push(pe.latitude),G.push(pe.longitude),h){for(X(pe,ee,y,M,f,m,g,Y,F,q,G),T=Y.length,E=0;E<3;++E)Y[T+E]=Y[E],F[T+E]=F[E],q[T+E]=q[E];G.push(ee.latitude),G.push(ee.longitude)}return function(n,r,s,l,c,d,p){let h,g;const f=r._ellipsoid,m=s.length/3-1,w=8*m,y=4*w,M=36*m,T=w>65535?new Uint32Array(M):new Uint16Array(M),E=new Float64Array(3*w),_=new Float32Array(y),O=new Float32Array(y),b=new Float32Array(y),P=new Float32Array(y),A=new Float32Array(y);let k,L,x,I;p&&(k=new Float32Array(y),L=new Float32Array(y),x=new Float32Array(y),I=new Float32Array(2*w));const R=d.length/2;let D=0;const v=Pe;v.height=0;const z=Ae;z.height=0;let H=ke,B=Le;if(p)for(g=0,h=1;h<R;h++)v.latitude=d[g],v.longitude=d[g+1],z.latitude=d[g+2],z.longitude=d[g+3],H=r.project(v,H),B=r.project(z,B),D+=a.Cartesian3.distance(H,B),g+=2;const V=l.length/3;B=a.Cartesian3.unpack(l,0,B);let j,G=0;for(g=3,h=1;h<V;h++)H=a.Cartesian3.clone(B,H),B=a.Cartesian3.unpack(l,g,B),G+=a.Cartesian3.distance(H,B),g+=3;g=3;let Y=0,F=0,q=0,X=0,W=!1,U=a.Cartesian3.unpack(s,0,xe),$=a.Cartesian3.unpack(l,0,Le),J=a.Cartesian3.unpack(c,0,Ne);if(n){ge(J,a.Cartesian3.unpack(s,s.length-6,Se),U,$)&&(J=a.Cartesian3.negate(J,J))}let Q=0,K=0,ee=0;for(h=0;h<m;h++){const e=a.Cartesian3.clone(U,Se),n=a.Cartesian3.clone($,ke);let o,C,h,m,w=a.Cartesian3.clone(J,Ie);if(W&&(w=a.Cartesian3.negate(w,w)),U=a.Cartesian3.unpack(s,g,xe),$=a.Cartesian3.unpack(l,g,Le),J=a.Cartesian3.unpack(c,g,Ne),W=ge(J,e,U,$),v.latitude=d[Y],v.longitude=d[Y+1],z.latitude=d[Y+2],z.longitude=d[Y+3],p){const e=be(v,z);o=r.project(v,Ve),C=r.project(z,je);const t=Z(C,o,Qe);t.y=Math.abs(t.y),h=Ge,m=Ye,0===e||a.Cartesian3.dot(t,a.Cartesian3.UNIT_Y)>N?(h=ye(r,v,w,o,Ge),m=ye(r,z,J,C,Ye)):1===e?(m=ye(r,z,J,C,Ye),h.x=0,h.y=i.CesiumMath.sign(v.longitude-Math.abs(z.longitude)),h.z=0):(h=ye(r,v,w,o,Ge),m.x=0,m.y=i.CesiumMath.sign(v.longitude-z.longitude),m.z=0)}const y=a.Cartesian3.distance(n,$),M=u.EncodedCartesian3.fromCartesian(e,$e),T=a.Cartesian3.subtract(U,e,Fe),R=a.Cartesian3.normalize(T,We);let H=a.Cartesian3.subtract(n,e,qe);H=a.Cartesian3.normalize(H,H);let B=a.Cartesian3.cross(R,H,We);B=a.Cartesian3.normalize(B,B);let V=a.Cartesian3.cross(H,w,Ue);V=a.Cartesian3.normalize(V,V);let te=a.Cartesian3.subtract($,U,Xe);te=a.Cartesian3.normalize(te,te);let ae=a.Cartesian3.cross(J,te,Ze);ae=a.Cartesian3.normalize(ae,ae);const ne=y/G,ie=Q/G;let re,se,oe,le=0,ce=0,ue=0;if(p){le=a.Cartesian3.distance(o,C),re=u.EncodedCartesian3.fromCartesian(o,Je),se=a.Cartesian3.subtract(C,o,Qe),oe=a.Cartesian3.normalize(se,Ke);const e=oe.x;oe.x=oe.y,oe.y=-e,ce=le/D,ue=K/D}for(j=0;j<8;j++){const e=X+4*j,t=F+2*j,n=e+3,i=j<4?1:-1,r=2===j||3===j||6===j||7===j?1:-1;a.Cartesian3.pack(M.high,_,e),_[n]=T.x,a.Cartesian3.pack(M.low,O,e),O[n]=T.y,a.Cartesian3.pack(V,b,e),b[n]=T.z,a.Cartesian3.pack(ae,P,e),P[n]=ne*i,a.Cartesian3.pack(B,A,e);let s=ie*r;0===s&&r<0&&(s=9),A[n]=s,p&&(k[e]=re.high.x,k[e+1]=re.high.y,k[e+2]=re.low.x,k[e+3]=re.low.y,x[e]=-h.y,x[e+1]=h.x,x[e+2]=m.y,x[e+3]=-m.x,L[e]=se.x,L[e+1]=se.y,L[e+2]=oe.x,L[e+3]=oe.y,I[t]=ce*i,s=ue*r,0===s&&r<0&&(s=9),I[t+1]=s)}const Ce=He,de=Be,pe=ve,he=ze,fe=t.Rectangle.fromCartographicArray(Re,De),me=S.getMinimumMaximumHeights(fe,f),we=me.minimumTerrainHeight,Me=me.maximumTerrainHeight;ee+=we,ee+=Me,Ee(e,n,we,Me,Ce,pe),Ee(U,$,we,Me,de,he);let Te=a.Cartesian3.multiplyByScalar(B,i.CesiumMath.EPSILON5,et);a.Cartesian3.add(Ce,Te,Ce),a.Cartesian3.add(de,Te,de),a.Cartesian3.add(pe,Te,pe),a.Cartesian3.add(he,Te,he),Oe(Ce,de),Oe(pe,he),a.Cartesian3.pack(Ce,E,q),a.Cartesian3.pack(de,E,q+3),a.Cartesian3.pack(he,E,q+6),a.Cartesian3.pack(pe,E,q+9),Te=a.Cartesian3.multiplyByScalar(B,-2*i.CesiumMath.EPSILON5,et),a.Cartesian3.add(Ce,Te,Ce),a.Cartesian3.add(de,Te,de),a.Cartesian3.add(pe,Te,pe),a.Cartesian3.add(he,Te,he),Oe(Ce,de),Oe(pe,he),a.Cartesian3.pack(Ce,E,q+12),a.Cartesian3.pack(de,E,q+15),a.Cartesian3.pack(he,E,q+18),a.Cartesian3.pack(pe,E,q+21),Y+=2,g+=3,F+=16,q+=24,X+=32,Q+=y,K+=le}g=0;let te=0;for(h=0;h<m;h++){for(j=0;j<nt;j++)T[g+j]=at[j]+te;te+=8,g+=nt}const ae=tt;e.BoundingSphere.fromVertices(s,a.Cartesian3.ZERO,3,ae[0]),e.BoundingSphere.fromVertices(l,a.Cartesian3.ZERO,3,ae[1]);const ne=e.BoundingSphere.fromBoundingSpheres(ae);ne.radius+=ee/(2*m);const ie={position:new C.GeometryAttribute({componentDatatype:o.ComponentDatatype.DOUBLE,componentsPerAttribute:3,normalize:!1,values:E}),startHiAndForwardOffsetX:it(_),startLoAndForwardOffsetY:it(O),startNormalAndForwardOffsetZ:it(b),endNormalAndTextureCoordinateNormalizationX:it(P),rightNormalAndTextureCoordinateNormalizationY:it(A)};p&&(ie.startHiLo2D=it(k),ie.offsetAndRight2D=it(L),ie.startEndNormals2D=it(x),ie.texcoordNormalization2D=new C.GeometryAttribute({componentDatatype:o.ComponentDatatype.FLOAT,componentsPerAttribute:2,normalize:!1,values:I}));return new C.Geometry({attributes:ie,indices:T,boundingSphere:ne})}(h,w,F,q,Y,G,p)};const de=new a.Cartesian3,pe=new a.Matrix3,he=new e.Quaternion;function ge(t,n,r,s){const o=Z(r,n,de),l=a.Cartesian3.dot(o,t);if(l>N||l<R){const n=Z(s,r,ee),o=l<R?i.CesiumMath.PI_OVER_TWO:-i.CesiumMath.PI_OVER_TWO,c=e.Quaternion.fromAxisAngle(n,o,he),u=a.Matrix3.fromQuaternion(c,pe);return a.Matrix3.multiplyByVector(u,t,t),!0}return!1}const fe=new a.Cartographic,me=new a.Cartesian3,we=new a.Cartesian3;function ye(e,t,n,r,s){const o=a.Cartographic.toCartesian(t,e._ellipsoid,me);let l=a.Cartesian3.add(o,n,we),c=!1;const u=e._ellipsoid;let C=u.cartesianToCartographic(l,fe);Math.abs(t.longitude-C.longitude)>i.CesiumMath.PI_OVER_TWO&&(c=!0,l=a.Cartesian3.subtract(o,n,we),C=u.cartesianToCartographic(l,fe)),C.height=0;const d=e.project(C,s);return(s=a.Cartesian3.subtract(d,r,s)).z=0,s=a.Cartesian3.normalize(s,s),c&&a.Cartesian3.negate(s,s),s}const Me=new a.Cartesian3,Te=new a.Cartesian3;function Ee(e,t,n,i,r,s){const o=a.Cartesian3.subtract(t,e,Me);a.Cartesian3.normalize(o,o);const l=n-D;let c=a.Cartesian3.multiplyByScalar(o,l,Te);a.Cartesian3.add(e,c,r);const u=i-v;c=a.Cartesian3.multiplyByScalar(o,u,Te),a.Cartesian3.add(t,c,s)}const _e=new a.Cartesian3;function Oe(e,t){const n=p.Plane.getPointDistance(ae,e),r=p.Plane.getPointDistance(ae,t);let s=_e;i.CesiumMath.equalsEpsilon(n,0,i.CesiumMath.EPSILON2)?(s=Z(t,e,s),a.Cartesian3.multiplyByScalar(s,i.CesiumMath.EPSILON2,s),a.Cartesian3.add(e,s,e)):i.CesiumMath.equalsEpsilon(r,0,i.CesiumMath.EPSILON2)&&(s=Z(e,t,s),a.Cartesian3.multiplyByScalar(s,i.CesiumMath.EPSILON2,s),a.Cartesian3.add(t,s,t))}function be(e,t){const a=Math.abs(e.longitude),n=Math.abs(t.longitude);if(i.CesiumMath.equalsEpsilon(a,i.CesiumMath.PI,i.CesiumMath.EPSILON11)){const n=i.CesiumMath.sign(t.longitude);return e.longitude=n*(a-i.CesiumMath.EPSILON11),1}if(i.CesiumMath.equalsEpsilon(n,i.CesiumMath.PI,i.CesiumMath.EPSILON11)){const a=i.CesiumMath.sign(e.longitude);return t.longitude=a*(n-i.CesiumMath.EPSILON11),2}return 0}const Pe=new a.Cartographic,Ae=new a.Cartographic,ke=new a.Cartesian3,Le=new a.Cartesian3,Se=new a.Cartesian3,xe=new a.Cartesian3,Ie=new a.Cartesian3,Ne=new a.Cartesian3,Re=[Pe,Ae],De=new t.Rectangle,ve=new a.Cartesian3,ze=new a.Cartesian3,He=new a.Cartesian3,Be=new a.Cartesian3,Ve=new a.Cartesian3,je=new a.Cartesian3,Ge=new a.Cartesian3,Ye=new a.Cartesian3,Fe=new a.Cartesian3,qe=new a.Cartesian3,Xe=new a.Cartesian3,We=new a.Cartesian3,Ue=new a.Cartesian3,Ze=new a.Cartesian3,$e=new u.EncodedCartesian3,Je=new u.EncodedCartesian3,Qe=new a.Cartesian3,Ke=new a.Cartesian3,et=new a.Cartesian3,tt=[new e.BoundingSphere,new e.BoundingSphere],at=[0,2,1,0,3,2,0,7,3,0,4,7,0,5,4,0,1,5,5,7,4,5,6,7,5,2,6,5,1,2,3,6,2,3,7,6],nt=at.length;function it(e){return new C.GeometryAttribute({componentDatatype:o.ComponentDatatype.FLOAT,componentsPerAttribute:4,normalize:!1,values:e})}return z._projectNormal=ye,function(e,t){return S.initialize().then((function(){return n.defined(t)&&(e=z.unpack(e,t)),z.createGeometry(e)}))}}));
