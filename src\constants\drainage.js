// 管线类型选项
export const PIPELINE_TYPE_OPTIONS = [
  { label: '全部', value: '' },
  { label: '雨水管线', value: 3000102 },
  { label: '污水管线', value: 3000101 },
  { label: '合流管线', value: 3000103 },
];

// 埋设类型选项
export const BURIED_TYPE_OPTIONS = [
  { label: '全部', value: '' },
  { label: '地埋式铺设', value: 3000201 },
  { label: '架空铺设', value: 3000202 },
  { label: '水平定向钻铺设', value: 3000203 },
  { label: '管道隧道铺设', value: 3000204 }
];

// 排口类型选项
export const OUTLET_TYPE_OPTIONS = [
  { label: '全部', value: '' },
  { label: '污水排水', value: 3000801 },
  { label: '雨水排水', value: 3000802 },
  { label: '合流排水', value: 3000803 },
  { label: '工业废水', value: 3000804 }
];

// 管材选项
// 3000301：PE管，3000302：PVC管，3000303：RCP管，3000304：FRP管，3000305：球墨铸铁管，3000306：不锈钢管，3000307：钢塑复合管，3000308：其他
export const MATERIAL_OPTIONS = [
  { label: '全部', value: '' },
  { label: 'PE管', value: 3000301 },
  { label: 'PVC管', value: 3000302 },
  { label: 'RCP管', value: 3000303 },
  { label: 'FRP管', value: 3000304 },
  { label: '球墨铸铁管', value: 3000305 },
  { label: '不锈钢管', value: 3000306 },
  { label: '钢塑复合管', value: 3000307 },
  { label: '其他', value: 3000308 }
];

// 管径选项
export const PIPE_DIAMETER_OPTIONS = [
  { label: '全部', value: '' },
  { label: 'DN50', value: 50 },
  { label: 'DN80', value: 80 },
  { label: 'DN100', value: 100 },
  { label: 'DN150', value: 150 },
  { label: 'DN200', value: 200 },
  { label: 'DN300', value: 300 },
  { label: 'DN400', value: 400 },
  { label: 'DN500', value: 500 }
];
// 使用状态
export const USE_TYPE = [
  { label: '使用中', value: 3000501 },
  { label: '报废', value: 3000502 },
  { label: '未使用', value: 3000503 },
];
// 断面类型选项 3000401：圆形，3000402：梯形，3000403：三角形，3000404：椭圆形，3000405：矩形，3000406：马蹄形，3000407：不规则形状
export const SECTION_TYPE = [
  { label: '圆形', value: 3000401 },
  { label: '梯形', value: 3000402 },
  { label: '三角形', value: 3000403 },
  { label: '椭圆形', value: 3000404 },
  { label: '矩形', value: 3000405 },
  { label: '马蹄形', value: 3000406 },
  { label: '不规则形状', value: 3000407 },
];

// 管点类型选项
export const POINT_TYPE_OPTIONS = [
  { label: '全部', value: '' },
  { label: '阀门', value: 3000601 },
  { label: '弯头', value: 3000602 },
  { label: '变径点', value: 3000603 },
  { label: '变材点', value: 3000604 },
  { label: '多通点', value: 3000605 },
  { label: '检测点', value: 3000606 },
  { label: '探测点', value: 3000607 },
  { label: '预留口', value: 3000608 },
  { label: '非普查区', value: 3000609 },
  { label: '变深点', value: 3000610 },
  { label: '其他', value: 3000611 }
];

// 维修结果选项
export const REPAIR_RESULTS = [
  { label: '已完成', value: 3000701 },
  { label: '未完成', value: 3000702 }
];

// 雨水篦子材质选项
export const GRATE_MATERIAL_OPTIONS = [
  { label: '铸铁', value: 3000901 },
  { label: '钢制', value: 3000902 },
  { label: '复合材料', value: 3000903 },
  { label: '混凝土', value: 3000904 },
  { label: '其他', value: 3000905 }
];

// 雨水篦子形状选项
export const GRATE_SHAPE_OPTIONS = [
  { label: '矩形', value: 3001001 },
  { label: '圆形', value: 3001002 },
  { label: '异形', value: 3001003 }
];

// 窨井类型选项
export const WELL_TYPE_OPTIONS = [
  { label: '全部', value: '' },
  { label: '雨水井', value: 3001101 },
  { label: '污水井', value: 3001102 },
  { label: '合流窨井', value: 3001103 }
];

// 窨井形状选项
export const WELL_SHAPE_OPTIONS = [
  { label: '全部', value: '' },
  { label: '圆形', value: 3001201 },
  { label: '方形', value: 3001202 },
  { label: '其他', value: 3001203 }
];

// 窨井材质选项
export const WELL_MATERIAL_OPTIONS = [
  { label: '全部', value: '' },
  { label: '铸铁', value: 3001301 },
  { label: '复合材料', value: 3001302 },
  { label: '钢', value: 3001303 },
  { label: '不锈钢', value: 3001304 },
  { label: '聚乙烯', value: 3001305 },
  { label: '铝合金', value: 3001306 },
  { label: '混凝土', value: 3001307 },
  { label: '其他', value: 3001308 }
];

// 泵站类型选项
export const PUMP_STATION_TYPE_OPTIONS = [
  { label: '全部', value: '' },
  { label: '污水泵站', value: 3001401 },
  { label: '雨水泵站', value: 3001402 },
  { label: '合流泵站', value: 3001403 },
  { label: '污泥泵站', value: 3001404 }
];

// 缺陷等级选项
export const DEFECT_LEVEL_OPTIONS = [
  { label: '全部', value: '' },
  { label: 'Ⅰ级', value: 3001701 },
  { label: 'Ⅱ级', value: 3001702 },
  { label: 'Ⅲ级', value: 3001703 },
  { label: 'Ⅳ级', value: 3001704 }
];

// 管道结构性缺陷类型选项
export const DEFECT_TYPE_OPTIONS = [
  { label: '全部', value: '' },
  { label: '破裂', value: 3001601 },
  { label: '变形', value: 3001602 },
  { label: '腐蚀', value: 3001603 },
  { label: '错口', value: 3001604 },
  { label: '脱节', value: 3001605 },
  { label: '塌陷', value: 3001606 },
  { label: '支管暗接', value: 3001607 },
  { label: '渗漏', value: 3001608 }
];

// 污水厂类型选项
export const SEWAGE_PLANT_TYPE_OPTIONS = [
  { label: '全部', value: '' },
  { label: '市政污水厂', value: 3001501 },
  { label: '工业废水厂', value: 3001502 },
  { label: '农村分散式污水厂', value: 3001503 },
  { label: '再生水厂', value: 3001504 }
];

// 易涝点风险选项
export const FLOOD_RISK_OPTIONS = [
  { label: '全部', value: '' },
  { label: '重大风险', value: 3001801 },
  { label: '较大风险', value: 3001802 },
  { label: '一般风险', value: 3001803 },
  { label: '低风险', value: 3001804 }
];

// 整改状态选项
export const RECTIFICATION_STATUS_OPTIONS = [
  { label: '全部', value: '' },
  { label: '已整改', value: 3001901 },
  { label: '未整改', value: 3001902 }
];

// 监测设备类型选项
export const DEVICE_TYPE_OPTIONS = [
  { label: '全部', value: '' },
  { label: '污水溢流', value: 3002001 },
  { label: '管网流量', value: 3002002 },
  { label: '雨量', value: 3002003 },
  { label: '易涝点积水', value: 3002004 },
  { label: '水质', value: 3002005 },
  { label: '井盖', value: 3002006 },
  { label: '可燃气体', value: 3002007 }
];

// 设备在线状态选项
export const ONLINE_STATUS_OPTIONS = [
  { label: '全部', value: '' },
  { label: '离线', value: 0 },
  { label: '在线', value: 1 }
];

// 监测指标编码选项
export const MONITOR_INDEX_OPTIONS = [
  { label: '全部', value: '' },
  { label: '污水溢流监测（液位：m）', value: 3002101 },
  { label: '管网流量监测（流量：m3/h）', value: 3002102 },
  { label: '雨量监测（雨量：mm）', value: 3002103 },
  { label: '易涝点积水监测（水位：m）', value: 3002104 },
  { label: '排污水质监测（PH，氨氮：mg/L，CODcr：mg/L，总磷：mg/L）', value: 3002105 },
  { label: '井盖状态监测（位移：mm，沉降：mm，倾斜：°，振动：HZ）', value: 3002106 },
  { label: '可燃气体监测（CH₄：%LEL，H₂S：mg/m³，CO：mg/m³）', value: 3002107 }
];

// 监测对象编码选项
export const MONITOR_OBJECT_OPTIONS = [
  { label: '全部', value: '' },
  { label: '管线', value: 3002201 },
  { label: '水厂', value: 3002202 },
  { label: '泵站', value: 3002203 },
  { label: '窨井', value: 3002204 }
];

// 生效状态选项
export const ENABLED_STATUS_OPTIONS = [
  { label: '是', value: true },
  { label: '否', value: false }
];

// 生效状态名称映射
export const ENABLED_STATUS_MAP = {
  true: '是',
  false: '否'
};

// 监管部门选项（引用燃气模块的监管部门）
export const SUPERVISE_DEPARTMENTS = [
  { label: '应急管理局', value: 'emergency' },
  { label: '城管局', value: 'cityManagement' },
  { label: '燃气管理处', value: 'gasManagement' },
  { label: '公安局', value: 'publicSecurity' },
  { label: '消防大队', value: 'fireDepartment' }
];

// ============ 排水报警相关常量 ============

// 报警级别选项
export const DRAIN_ALARM_LEVEL_OPTIONS = [
  { label: '全部', value: '' },
  { label: '一级', value: 3003601 },
  { label: '二级', value: 3003602 },
  { label: '三级', value: 3003603 },
  { label: '四级', value: 3003604 }
];

// 报警级别映射
export const DRAIN_ALARM_LEVEL_MAP = {
  3003601: '一级',
  3003602: '二级',
  3003603: '三级',
  3003604: '四级'
};

// 报警状态选项
export const DRAIN_ALARM_STATUS_OPTIONS = [
  { label: '全部', value: '' },
  { label: '待确认', value: 3003701 },
  { label: '误报', value: 3003702 },
  { label: '待处置', value: 3003703 },
  { label: '处置中', value: 3003704 },
  { label: '已处置', value: 3003705 },
  { label: '已归档', value: 3003706 }
];

// 报警状态映射
export const DRAIN_ALARM_STATUS_MAP = {
  3003701: '待确认',
  3003702: '误报',
  3003703: '待处置',
  3003704: '处置中',
  3003705: '已处置',
  3003706: '已归档'
};

// 报警类型选项
export const DRAIN_ALARM_TYPE_OPTIONS = [
  { label: '全部', value: '' },
  { label: '污水溢流监测报警', value: 3003101 },
  { label: '管网流量监测报警', value: 3003102 },
  { label: '雨量监测报警', value: 3003103 },
  { label: '易涝点积水监测报警', value: 3003104 },
  { label: '排污水质监测报警', value: 3003105 },
  { label: '井盖状态监测报警', value: 3003106 },
  { label: '可燃气体监测报警', value: 3003107 }
];

// 报警类型映射
export const DRAIN_ALARM_TYPE_MAP = {
  3003101: '污水溢流监测报警',
  3003102: '管网流量监测报警',
  3003103: '雨量监测报警',
  3003104: '易涝点积水监测报警',
  3003105: '排污水质监测报警',
  3003106: '井盖状态监测报警',
  3003107: '可燃气体监测报警'
};

// 报警来源选项（与燃气保持一致）
export const DRAIN_ALARM_SOURCE_OPTIONS = [
  { label: '全部', value: '' },
  { label: '设备监测报警', value: '设备监测报警' },
  { label: '企业自报报警', value: '企业自报报警' },
  { label: '系统监测', value: '系统监测' }
];

// 报警来源映射
export const DRAIN_ALARM_SOURCE_MAP = {
  '设备监测报警': '设备监测报警',
  '企业自报报警': '企业自报报警',
  '系统监测': '系统监测'
};

// 监测指标编码选项
export const DRAIN_MONITOR_INDEX_CODE_OPTIONS = [
  { label: '全部', value: '' },
  { label: '水位', value: 5002001 },
  { label: '温度', value: 5002002 },
  { label: '液位', value: 5002003 },
  { label: '浓度', value: 5002004 },
  { label: '井盖状态', value: 5002005 },
  { label: '流量', value: 5002006 },
  { label: '流速', value: 5002007 },
  { label: '角度', value: 5002008 },
  { label: '水浸状态', value: 5002009 },
  { label: '化学需氧量（COD）', value: 5002010 },
  { label: '浊度', value: 5002011 }
];

// 监测指标编码映射
export const DRAIN_MONITOR_INDEX_CODE_MAP = {
  5002001: '水位',
  5002002: '温度',
  5002003: '液位',
  5002004: '浓度',
  5002005: '井盖状态',
  5002006: '流量',
  5002007: '流速',
  5002008: '角度',
  5002009: '水浸状态',
  5002010: '化学需氧量（COD）',
  5002011: '浊度'
};

// 处置状态选项
export const DRAIN_HANDLE_STATUS_OPTIONS = [
  { label: '处置中', value: 3004001 },
  { label: '已处置', value: 3004002 }
];

// 处置状态映射
export const DRAIN_HANDLE_STATUS_MAP = {
  3004001: '处置中',
  3004002: '已处置'
};

// 确认结果选项
export const DRAIN_CONFIRM_RESULT_OPTIONS = [
  { label: '真实报警', value: 3003901 },
  { label: '误报', value: 3003902 }
];

// 确认结果映射
export const DRAIN_CONFIRM_RESULT_MAP = {
  3003901: '真实报警',
  3003902: '误报'
};

// ============ 防汛物资管理相关常量 ============

// 物资类型选项
export const MATERIAL_TYPE_OPTIONS = [
  { label: '抢险设备', value: 3003801 },
  { label: '应急物料', value: 3003802 },
  { label: '防护用品', value: 3003803 },
  { label: '救援器材', value: 3003804 },
  { label: '其他物资', value: 3003805 }
];

// 物资类型映射
export const MATERIAL_TYPE_MAP = {
  3003801: '抢险设备',
  3003802: '应急物料',
  3003803: '防护用品',
  3003804: '救援器材',
  3003805: '其他物资'
};

// 管理单位选项
export const MANAGEMENT_UNIT_OPTIONS = [
  { label: '市防汛办', value: '市防汛办' },
  { label: '区防汛办', value: '区防汛办' },
  { label: '应急管理局', value: '应急管理局' },
  { label: '城管局', value: '城管局' },
  { label: '水务局', value: '水务局' },
  { label: '建设局', value: '建设局' },
  { label: '消防救援支队', value: '消防救援支队' },
  { label: '其他', value: '其他' }
];

// ============ 危险源信息管理相关常量 ============

// 建筑类型选项（危险源）
export const DANGER_BUILDING_TYPE_OPTIONS = [
  { label: '危险化学品工厂', value: 6003401 },
  { label: '饭店', value: 6003402 },
  { label: '锅炉站', value: 6003403 },
  { label: '放射源', value: 6003404 },
  { label: '加气站', value: 6003405 },
  { label: '加油站', value: 6003406 },
  { label: '其他', value: 6003407 }
];

// 建筑类型映射（危险源）
export const DANGER_BUILDING_TYPE_MAP = {
  6003401: '危险化学品工厂',
  6003402: '饭店',
  6003403: '锅炉站',
  6003404: '放射源',
  6003405: '加气站',
  6003406: '加油站',
  6003407: '其他'
};

// 是否重大危险源选项
export const IS_MAJOR_DANGER_OPTIONS = [
  { label: '是', value: '1' },
  { label: '否', value: '0' }
];

// 是否重大危险源映射
export const IS_MAJOR_DANGER_MAP = {
  '1': '是',
  '0': '否'
};
 