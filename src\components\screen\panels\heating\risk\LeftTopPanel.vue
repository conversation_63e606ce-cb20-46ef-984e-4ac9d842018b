<template>
  <PanelBox title="风险统计">
    <template #extra>
      <div class="com-select">
        <CommonSelect v-model="selectedPipeType" :options="pipeTypeOptions" @change="handlePipeTypeChange" />
      </div>
    </template>
    <div class="panel-content">
      <div class="content-wrapper">
        <div class="risk-chart">
          <div class="chart-container" ref="chartRef"></div>
          <div class="center-text">
            <div class="risk-total">{{ totalRisk }}</div>
            <div class="unit">KM</div>
          </div>
        </div>
        <div class="risk-list">
          <div class="risk-item" v-for="(item, index) in riskItems" :key="index">
            <div class="risk-indicator" :style="{ background: item.color }"></div>
            <div class="risk-name">{{ item.name }}</div>
            <div class="risk-value">{{ item.value }} <span class="unit-text">KM</span></div>
          </div>
        </div>
      </div>
    </div>
  </PanelBox>
</template>

<script setup>
import { ref, onMounted, nextTick, computed, watch } from 'vue'
import * as echarts from 'echarts'
import PanelBox from '@/components/screen/PanelBox.vue'
import CommonSelect from '@/components/screen/CommonSelect.vue'

// 管线类型选项
const pipeTypeOptions = [
  { value: 'rain', label: '管线' },
  { value: 'plant', label: '热源厂' },
  { value: 'station', label: '换热站' }
]

// 默认选择雨水管线
const selectedPipeType = ref('rain')

// 定义数据源
const chartRef = ref(null)
let chartInstance = null

// 模拟数据
const mockData = {
  rain: [
    { name: '重大风险', value: 0, color: '#DE6970' },
    { name: '较大风险', value: 0, color: '#FE9150' },
    { name: '一般风险', value: 0, color: '#D8F115' },
    { name: '低风险', value: 0, color: '#00E1B9' }
  ],
  plant: [
    { name: '重大风险', value: 0, color: '#DE6970' },
    { name: '较大风险', value: 0, color: '#FE9150' },
    { name: '一般风险', value: 0, color: '#D8F115' },
    { name: '低风险', value: 0, color: '#00E1B9' }
  ],
  station: [
    { name: '重大风险', value:0, color: '#DE6970' },
    { name: '较大风险', value: 0, color: '#FE9150' },
    { name: '一般风险', value: 0, color: '#D8F115' },
    { name: '低风险', value: 0, color: '#00E1B9' }
  ]
}

// 计算当前展示数据
const riskItems = computed(() => {
  return mockData[selectedPipeType.value] || []
})

// 计算总风险值
const totalRisk = computed(() => {
  return riskItems.value.reduce((sum, item) => sum + item.value, 0)
})

// 处理管线类型变化
const handlePipeTypeChange = () => {
  if (chartInstance) {
    updateChart()
  }
}

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return

  chartInstance = echarts.init(chartRef.value)
  updateChart()

  window.addEventListener('resize', () => {
    chartInstance && chartInstance.resize()
  })
}

// 更新图表数据
const updateChart = () => {
  if (!chartInstance) return

  const data = riskItems.value
  const colorList = data.map(item => item.color)
  const valueList = data.map(item => item.value)

  const option = {
    backgroundColor: 'transparent',
    series: [{
      type: 'pie',
      radius: ['75%', '85%'],
      center: ['50%', '50%'],
      startAngle: 0,
      itemStyle: {
        borderRadius: 0,
        borderColor: 'transparent',
        borderWidth: 0
      },
      label: {
        show: false
      },
      silent: true,
      data: valueList.map((value, index) => ({
        value,
        name: data[index].name,
        itemStyle: {
          color: colorList[index]
        }
      }))
    }]
  }

  chartInstance.setOption(option)
}

// 从后端获取数据的方法
const fetchData = async (pipeType) => {
  try {
    // TODO: 实际项目中替换为API调用
    // const response = await api.getRiskData(pipeType)
    // if (response.code === 200 && response.data) {
    //   mockData[pipeType] = response.data
    //   if (chartInstance) {
    //     updateChart()
    //   }
    // }

    // 目前使用模拟数据
    console.log(`获取${pipeType}风险数据`)
  } catch (error) {
    console.error('获取风险数据失败:', error)
  }
}

// 监听管线类型变化
watch(selectedPipeType, (newValue) => {
  fetchData(newValue)
})

onMounted(async () => {
  await nextTick()
  initChart()
  fetchData(selectedPipeType.value)
})
</script>

<style scoped>
.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
}

.com-select {
  margin-right: 20px;
}

.content-wrapper {
  display: flex;
  align-items: center;
  gap: 20px;
  height: 100%;
}

.risk-chart {
  width: 190px;
  height: 190px;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.chart-container {
  width: 190px;
  height: 190px;
  background-image: url('@/assets/images/screen/gas/guanwangfengxian.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  position: relative;
}

.center-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #fff;
}

.risk-total {
  font-size: 24px;
  font-weight: bold;
  color: #22CBFF;
  margin-bottom: 4px;
}

.unit {
  font-size: 12px;
  color: #85A5C3;
}

.risk-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.risk-item {
  display: flex;
  align-items: center;
  width: 225px;
  height: 34px;
  background: linear-gradient(270deg, rgba(48, 71, 104, 0.5) 0%, #304768 50%, rgba(48, 71, 104, 0.5) 100%);
  border: 1px solid;
  opacity: 1;
  border-image: linear-gradient(270deg, rgba(171, 204, 255, 0), rgba(171, 204, 255, 0.5), rgba(171, 204, 255, 0)) 1 1;
  padding: 0 15px;
}

.risk-indicator {
  width: 9px;
  height: 8px;
  transform: skew(-20deg);
  margin-right: 8px;
}

.risk-name {
  width: 70px;
  color: #D3E5FF;
  font-size: 14px;
}

.risk-value {
  color: #ffffff;
  font-size: 14px;
  width: 80px;
  text-align: right;
  margin-left: auto;
}

.unit-text {
  color: #85A5C3;
  font-size: 12px;
}

/* 媒体查询适配不同分辨率 */
@media screen and (min-width: 1920px) and (max-width: 2560px) {
  .left-top-panel {
    height: 310px;
  }
}

@media screen and (max-width: 1919px) {
  .left-top-panel {
    height: 310px;
  }
}

@media screen and (min-width: 2561px) {
  .left-top-panel {
    height: 310px;
  }
}

/* 添加全屏模式(1080px高度)的适配 */
@media screen and (min-height: 1056px) and (max-height: 1080px) {
  .left-top-panel {
    height: 310px;
  }
}

@media screen and (min-height: 940px) and (max-height: 1055px) {
  .left-top-panel {
    height: 310px;
  }

  .panel-content {
    padding: 10px;
  }
}

@media screen and (min-height: 900px) and (max-height: 940px) {
  .left-top-panel {
    height: 252px;
  }

  .panel-content {
    padding: 8px;
  }

  .risk-chart {
    width: 180px;
    height: 180px;
  }

  .chart-container {
    width: 180px;
    height: 180px;
  }
}
</style>