<template>
  <div class="panel-box">
    <div class="panel-header">
      <div class="panel-title">
        <h3 class="title-text">{{ title }}</h3>
      </div>
      <div class="panel-extra" v-if="$slots.extra">
        <slot name="extra"></slot>
      </div>
    </div>
    <div class="panel-body">
      <slot></slot>
    </div>
  </div>
</template>

<script setup>
defineProps({
  title: {
    type: String,
    required: true
  }
})
</script>

<style scoped>
.panel-box {
  background: rgba(3, 24, 55, 0.6);
  backdrop-filter: blur(2px);
  border-radius: 8px;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
}

.panel-box::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, 
    rgba(0, 242, 241, 0) 0%,
    rgba(0, 242, 241, 0.5) 50%,
    rgba(0, 242, 241, 0) 100%
  );
}

.panel-header {
  width: 100%;
  height: 49px;
  background-image: url('@/assets/images/screen/panel_box_title.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  padding: 0 20px;
}

.panel-title {
  flex: 1;
  display: flex;
  align-items: center;
}

.title-text {
  font-family: 'YouSheBiaoTiHei', sans-serif;
  font-size: 18px;
  font-weight: normal;
  color: #FFFFFF;
  line-height: 23px;
  letter-spacing: 1px;
  text-align: left;
  font-style: normal;
  margin: 0;
  background: linear-gradient(90deg, #E8F9FF 0%, #61D9FF 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.panel-extra {
  padding-left: 46px;
  display: flex;
  align-items: center;
}

.panel-body {
  flex: 1;
  /* padding: 8px 12px 12px; */
  overflow: hidden;
}

/* 低高度屏幕适配 */
@media screen and (max-height: 940px) {
  .panel-header {
    height: 40px;
    padding: 0 15px;
  }
  
  .title-text {
    font-size: 16px;
  }
  
  /* .panel-body {
    padding: 0px 10px 0px;
  } */
}
</style> 