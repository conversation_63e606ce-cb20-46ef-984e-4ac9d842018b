@use './mixins/rem' as *;

@tailwind base;
@tailwind components;
@tailwind utilities;

// 全局样式
html, body {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

#app {
  width: 100%;
  height: 100%;
}

// 大屏容器样式
.screen-container {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  overflow: hidden;
  background-color: #000;
  color: #fff;
}

// 只在screen页面添加背景色
body.screen-page {
  background-color: #0d2341;
}

// 滚动条样式
::-webkit-scrollbar {
  width: rem(6px);
  height: rem(6px);
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: rem(3px);
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: rem(3px);
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
} 