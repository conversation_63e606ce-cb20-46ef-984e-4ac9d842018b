<template>
  <div class="bar-chart-container">
    <div ref="chartRef" class="chart-content"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  title: {
    type: String,
    required: true
  },
  data: {
    type: Array,
    required: true
  },
  xAxisData: {
    type: Array,
    default: () => []
  },
  isHorizontal: {
    type: Boolean,
    default: false
  },
  colorMap: {
    type: Object,
    default: () => ({})
  },
  showLabel: {
    type: Boolean,
    default: true
  },
  unit: {
    type: String,
    default: ''
  }
})

const chartRef = ref(null)
let chartInstance = null

onMounted(async () => {
  // 使用nextTick确保DOM已更新
  await nextTick()
  if (chartRef.value) {
    console.log('初始化图表:', props.title)
    initChart()
  }
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
  window.removeEventListener('resize', handleResize)
})

watch(() => props.data, () => {
  if (chartInstance) {
    console.log('数据变化，更新图表:', props.title)
    updateChart()
  }
}, { deep: true })

watch(() => props.xAxisData, () => {
  if (chartInstance) {
    console.log('x轴数据变化，更新图表:', props.title)
    updateChart()
  }
}, { deep: true })

watch(() => props.isHorizontal, () => {
  if (chartInstance) {
    console.log('方向变化，更新图表:', props.title)
    updateChart()
  }
})

const initChart = () => {
  try {
    // 如果已存在实例，先销毁
    if (chartInstance) {
      chartInstance.dispose()
    }
    
    console.log('创建echarts实例，容器大小:', chartRef.value.offsetWidth, chartRef.value.offsetHeight)
    // 创建新实例
    chartInstance = echarts.init(chartRef.value)
    
    // 添加窗口大小变化监听
    window.addEventListener('resize', handleResize)
    
    // 立即更新图表
    updateChart()
    // 强制重新计算大小
    chartInstance.resize()
  } catch (error) {
    console.error('初始化图表失败:', error)
  }
}

const handleResize = () => {
  if (chartInstance) {
    try {
      chartInstance.resize()
    } catch (error) {
      console.error('调整图表大小失败:', error)
    }
  }
}

const updateChart = () => {
  if (!chartInstance) {
    console.warn('图表实例不存在，无法更新')
    return
  }
  
  try {
    // 创建本地副本，避免直接使用props
    const localData = JSON.parse(JSON.stringify(props.data))
    const localTitle = String(props.title)
    const localXAxisData = JSON.parse(JSON.stringify(props.xAxisData))
    
    // 准备数据
    const seriesData = localData.map(item => {
      return typeof item === 'object' ? item.value : item
    })
    
    // 获取系列名称（如果数据是对象数组）
    const seriesNames = localData.map(item => {
      return typeof item === 'object' ? (item.name || '') : ''
    })
    
    // 横向柱状图和纵向柱状图的配置
    const option = {
      backgroundColor: 'transparent',
      title: {
        text: localTitle,
        left: 'center',
        top: 10,
        textStyle: {
          color: '#FFFFFF',
          fontSize: 14,
          fontWeight: 'normal',
          fontFamily: 'PingFangSC, PingFang SC'
        }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        formatter: function(params) {
          const dataIndex = params[0].dataIndex
          const value = params[0].value
          const name = localXAxisData[dataIndex] || seriesNames[dataIndex] || ''
          return `${name}: ${value}${props.unit}`
        }
      },
      grid: {
        top: props.title ? '15%' : '5%',
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: props.isHorizontal ? {
        type: 'value',
        axisLine: {
          lineStyle: { color: 'rgba(255,255,255,0.5)' }
        },
        axisLabel: { 
          color: 'rgba(255,255,255,0.7)',
          fontSize: 10
        },
        splitLine: {
          lineStyle: { color: 'rgba(255,255,255,0.1)' }
        }
      } : {
        type: 'category',
        data: localXAxisData.length > 0 ? localXAxisData : seriesNames,
        axisLine: {
          lineStyle: { color: 'rgba(255,255,255,0.5)' }
        },
        axisLabel: { 
          color: 'rgba(255,255,255,0.7)',
          fontSize: 10,
          rotate: localXAxisData.length > 5 ? 45 : 0
        }
      },
      yAxis: props.isHorizontal ? {
        type: 'category',
        data: localXAxisData.length > 0 ? localXAxisData : seriesNames,
        axisLine: {
          lineStyle: { color: 'rgba(255,255,255,0.5)' }
        },
        axisLabel: { 
          color: 'rgba(255,255,255,0.7)',
          fontSize: 10
        }
      } : {
        type: 'value',
        axisLine: {
          lineStyle: { color: 'rgba(255,255,255,0.5)' }
        },
        axisLabel: { 
          color: 'rgba(255,255,255,0.7)',
          fontSize: 10
        },
        splitLine: {
          lineStyle: { color: 'rgba(255,255,255,0.1)' }
        }
      },
      series: [{
        name: localTitle,
        type: 'bar',
        data: seriesData,
        barWidth: '40%',
        itemStyle: {
          color: {
            type: 'linear',
            x: 0, y: 0, x2: props.isHorizontal ? 1 : 0, y2: props.isHorizontal ? 0 : 1,
            colorStops: [{
              offset: 0,
              color: '#00F2F1'  // 浅蓝色
            }, {
              offset: 1,
              color: '#0066FF'  // 深蓝色
            }]
          },
          borderRadius: 4
        },
        label: {
          show: props.showLabel,
          position: props.isHorizontal ? 'right' : 'top',
          color: '#fff',
          fontSize: 10,
          formatter: `{c}${props.unit}`
        }
      }]
    }
    
    console.log('设置图表选项:', props.title)
    chartInstance.setOption(option, true)
  } catch (error) {
    console.error('更新图表失败:', error, props.title)
  }
}
</script>

<style scoped>
.bar-chart-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 160px;
}

.chart-content {
  width: 100%;
  height: 100%;
  min-height: 160px;
}
</style> 