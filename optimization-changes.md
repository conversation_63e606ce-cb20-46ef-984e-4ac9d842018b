# 报警阈值管理页面优化修改

## 修改内容总结

### 1. 搜索组件优化 (ThresholdSearch.vue)

**问题**：查询区域存在重复的规则名称输入框

**解决方案**：
- ✅ 删除了第一个"规则名称"输入框
- ✅ 保留第二个输入框作为查询字段，绑定到 `ruleName` 
- ✅ 添加了无边框样式类 `search-input-no-border`
- ✅ 简化了搜索表单数据结构，移除了多余的 `keyword` 字段

**修改详情**：
```javascript
// 修改前
const searchForm = reactive({
  ruleName: '',
  isEnabled: '',
  keyword: ''  // 多余字段
});

// 修改后  
const searchForm = reactive({
  ruleName: '',    // 直接使用ruleName作为查询字段
  isEnabled: ''
});
```

**样式优化**：
```css
/* 新增无边框搜索输入框样式 */
:deep(.search-input-no-border .el-input__wrapper) {
  border: none;
  box-shadow: none;
  background-color: transparent;
  padding: 0;
}
```

### 2. 列表生效状态优化 (threshold.vue)

**问题**：生效状态开关具有更新功能，可能引起报错

**解决方案**：
- ✅ 将开关设置为 `disabled` 状态，只做展示用
- ✅ 移除了 `@change="handleStatusChange(row)"` 事件监听
- ✅ 删除了 `handleStatusChange` 函数及相关逻辑

**修改详情**：
```vue
<!-- 修改前 -->
<el-switch 
  v-model="row.isEnabled" 
  :active-value="1" 
  :inactive-value="0"
  @change="handleStatusChange(row)"
/>

<!-- 修改后 -->
<el-switch 
  v-model="row.isEnabled" 
  :active-value="1" 
  :inactive-value="0"
  disabled
/>
```

### 3. 弹窗数据回显优化 (ThresholdDialog.vue)

**问题**：编辑和查看模式下，设备类型、监测指标、设备选择三个字段回显异常

**解决方案**：

#### 3.1 设备类型去重处理
```javascript
// 修改前：直接映射，可能存在重复
deviceTypeOptions.value = (res.data || []).map(item => ({
  label: item.deviceTypeName,
  value: item.deviceType
}));

// 修改后：去重处理
const uniqueDeviceTypes = [];
const deviceTypeSet = new Set();
data.forEach(item => {
  if (item.deviceType && item.deviceTypeName && !deviceTypeSet.has(item.deviceType)) {
    deviceTypeSet.add(item.deviceType);
    uniqueDeviceTypes.push({
      label: item.deviceTypeName,
      value: item.deviceType
    });
  }
});
```

#### 3.2 设备列表获取优化
```javascript
// 修改前：只使用formData.deviceType
const fetchDeviceList = async () => {
  if (!formData.deviceType) return;
  // ...
};

// 修改后：支持传入deviceType参数
const fetchDeviceList = async (deviceType) => {
  const targetDeviceType = deviceType || formData.deviceType;
  if (!targetDeviceType) return;
  // ...
};
```

#### 3.3 详情数据回显优化
```javascript
const fetchThresholdDetail = async (id) => {
  const data = res.data;
  
  // 1. 基础数据复制
  Object.keys(formData).forEach(key => {
    if (data[key] !== undefined) {
      formData[key] = data[key];
    }
  });
  
  // 2. 处理字符串形式的数组数据
  if (typeof data.deviceIds === 'string' && data.deviceIds) {
    formData.deviceIds = data.deviceIds.split(',').map(id => id.trim()).filter(id => id);
  }
  
  // 3. 处理监管部门ID字段
  ['notifySuperviseDeptIds1', 'notifySuperviseDeptIds2', 'notifySuperviseDeptIds3'].forEach(field => {
    if (typeof data[field] === 'string' && data[field]) {
      formData[field] = data[field].split(',').map(id => id.trim()).filter(id => id);
    }
  });
  
  // 4. 回显时重新加载联动数据
  if (formData.deviceType) {
    await fetchMonitorIndexes(formData.deviceType);
    await fetchDeviceList(formData.deviceType);
  }
};
```

## 优化效果

### ✅ 用户体验提升
- 搜索区域布局更清晰，无重复字段
- 生效状态显示稳定，避免误操作
- 编辑回显数据准确，操作流畅

### ✅ 功能稳定性提升  
- 消除了开关更新可能的报错
- 修复了数据回显异常问题
- 改善了下拉选项的数据质量

### ✅ 代码质量提升
- 简化了数据结构
- 优化了异步数据加载逻辑
- 增强了数据处理的健壮性

## 测试建议

1. **搜索功能测试**：验证规则名称搜索和生效状态筛选
2. **列表展示测试**：确认生效状态开关只展示不可操作
3. **编辑回显测试**：重点测试设备类型、监测指标、设备选择的数据回显
4. **新增功能测试**：验证下拉联动是否正常工作
5. **表单提交测试**：确认数据保存和更新功能正常 