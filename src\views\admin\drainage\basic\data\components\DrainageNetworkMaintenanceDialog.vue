<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="1000px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="drainage-network-maintenance-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      :disabled="mode === 'view'"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="维修单号" prop="repairCode">
            <el-input v-model="formData.repairCode" placeholder="请输入维修单号" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="关联管线" prop="connectedPipeline">
            <el-select v-model="formData.connectedPipeline" placeholder="请选择关联管线" class="w-full" @change="handlePipelineChange">
              <el-option v-for="item in pipelineList" :key="item.id" :label="item.pipelineCode" :value="item.pipelineCode" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="维修类型" prop="repairTypeName">
            <el-input v-model="formData.repairTypeName" placeholder="请输入维修类型" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="附属设施" prop="attachedFacilities">
            <el-input v-model="formData.attachedFacilities" placeholder="请输入附属设施" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="维修内容" prop="repairDesc">
            <el-input
              v-model="formData.repairDesc"
              type="textarea"
              :rows="3"
              placeholder="请输入维修内容"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="维修位置" prop="address">
            <el-input v-model="formData.address" placeholder="请输入维修位置" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="位置坐标" prop="location">
            <div class="flex items-center">
              <el-input v-model="formData.longitude" placeholder="输入选择经度" class="mr-2" />
              <el-input v-model="formData.latitude" placeholder="输入选择纬度" />
              <el-button type="primary" class="ml-2" icon="Location" circle @click="openMapPicker" :disabled="mode === 'view'"></el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="维修方案" prop="repairScheme">
            <el-input
              v-model="formData.repairScheme"
              type="textarea"
              :rows="3"
              placeholder="请输入维修方案"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="维修结果" prop="repairResult">
            <el-select v-model="formData.repairResult" placeholder="请选择维修结果" class="w-full">
              <el-option v-for="item in REPAIR_RESULTS" :key="item.value" :label="item.label" :value="item.value.toString()" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="维修时间" prop="repairTime">
            <el-date-picker
              v-model="formData.repairTime"
              type="datetime"
              placeholder="请选择维修时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              class="w-full"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="维修人" prop="repairUser">
            <el-input v-model="formData.repairUser" placeholder="请输入维修人" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系电话" prop="contactInfo">
            <el-input v-model="formData.contactInfo" placeholder="请输入联系电话" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="权属单位" prop="managementUnit">
            <el-select v-model="formData.managementUnit" placeholder="请选择" class="w-full">
              <el-option v-for="unit in enterpriseList" :key="unit.id" :label="unit.enterpriseName"
                :value="unit.enterpriseName"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="维修前照片">
            <template v-if="mode !== 'view'">
              <el-upload
                class="upload-demo"
                :http-request="handleBeforeUpload"
                :file-list="beforeFileList"
                :on-error="handleUploadError"
              >
                <el-button type="primary">上传照片</el-button>
              </el-upload>
            </template>
            <template v-else>
              <el-image
                v-if="beforeFileList.length > 0"
                :src="beforeFileList[0].url"
                :preview-src-list="beforeFileList.map(file => file.url)"
                style="width: 100px; height: 100px"
                fit="cover"
              />
            </template>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="维修后照片">
            <template v-if="mode !== 'view'">
              <el-upload
                class="upload-demo"
                :http-request="handleAfterUpload"
                :file-list="afterFileList"
                :on-error="handleUploadError"
              >
                <el-button type="primary">上传照片</el-button>
              </el-upload>
            </template>
            <template v-else>
              <el-image
                v-if="afterFileList.length > 0"
                :src="afterFileList[0].url"
                :preview-src-list="afterFileList.map(file => file.url)"
                style="width: 100px; height: 100px"
                fit="cover"
              />
            </template>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="附件">
            <template v-if="mode !== 'view'">
              <el-upload
                class="upload-demo"
                :http-request="handleFileUpload"
                :file-list="fileList"
                :on-error="handleUploadError"
              >
                <el-button type="primary">上传附件</el-button>
              </el-upload>
            </template>
            <template v-else>
              <el-link
                v-if="fileList.length > 0"
                :href="fileList[0].url"
                target="_blank"
                type="primary"
              >
                {{ fileList[0].name }}
              </el-link>
            </template>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="mode !== 'view'">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, nextTick, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { saveRepair, updateRepair, getRepairDetail, getEnterpriseList, getPipelineList } from '@/api/drainage';
import { REPAIR_RESULTS } from '@/constants/drainage';
import { collectShow } from '@/hooks/gishooks';
import bus from '@/utils/mitt';
import { uploadFile } from '@/api/upload';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add', // 'add', 'edit', 'view'
    validator: (value) => ['add', 'edit', 'view'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'success']);

// 表单引用
const formRef = ref(null);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 对话框标题
const dialogTitle = computed(() => {
  const titles = {
    add: '新增维修记录',
    edit: '编辑维修记录',
    view: '维修记录详情'
  };
  return titles[props.mode] || '维修记录';
});

// 表单数据
const formData = reactive({
  id: '',
  repairCode: '',
  connectedPipeline: '',
  connectedPipelineId: '',
  repairType: '',
  repairTypeName: '',
  attachedFacilities: '',
  repairDesc: '',
  repairScheme: '',
  repairResult: '',
  repairResultLabel: '',
  repairTime: '',
  repairUser: '',
  repairUserId: '',
  contactInfo: '',
  managementUnit: '',
  managementUnitName: '',
  address: '',
  city: '',
  county: '',
  countyName: '',
  town: '',
  townName: '',
  longitude: '',
  latitude: '',
  beforePicUrl: '',
  afterPicUrl: '',
  fileUrl: ''
});

// 表单验证规则
const formRules = {
  repairCode: [{ required: true, message: '请输入维修单号', trigger: 'blur' }],
  connectedPipeline: [{ required: true, message: '请选择关联管线', trigger: 'change' }],
  repairTypeName: [{ required: true, message: '请输入维修类型', trigger: 'blur' }],
  repairDesc: [{ required: true, message: '请输入维修内容', trigger: 'blur' }],
  repairResult: [{ required: true, message: '请选择维修结果', trigger: 'change' }],
  repairTime: [{ required: true, message: '请选择维修时间', trigger: 'change' }],
  repairUser: [{ required: true, message: '请输入维修人', trigger: 'blur' }],
  managementUnit: [{ required: true, message: '请选择权属单位', trigger: 'change' }],
  address: [{ required: true, message: '请输入维修位置', trigger: 'blur' }]
};

// 权属单位列表
const enterpriseList = ref([]);
// 管线列表
const pipelineList = ref([]);

// 文件列表
const beforeFileList = ref([]);
const afterFileList = ref([]);
const fileList = ref([]);

// 获取权属单位列表
const fetchEnterpriseList = async () => {
  try {
    const res = await getEnterpriseList();
    if (res && res.code === 200) {
      enterpriseList.value = res.data || [];
    }
  } catch (error) {
    console.error('获取权属单位列表失败', error);
  }
};

// 获取管线列表
const fetchPipelineList = async () => {
  try {
    const res = await getPipelineList({});
    if (res && res.code === 200) {
      pipelineList.value = res.data || [];
    }
  } catch (error) {
    console.error('获取管线列表失败', error);
  }
};

// 监听权属单位变化
watch(() => formData.managementUnit, (newVal) => {
  formData.managementUnitName = newVal || '';
}, { immediate: true });

// 地图选点事件处理函数
const handleCollectLocation = (params) => {
  nextTick(() => {
    formData.longitude = params.longitude;
    formData.latitude = params.latitude;
  });
};

// 打开地图选点
const openMapPicker = () => {
  collectShow.value = true; // 激活采集点位窗口
  // 先移除可能存在的旧监听器
  bus.off("getCollectLocation", handleCollectLocation);
  // 添加新的监听器
  bus.on("getCollectLocation", handleCollectLocation);
};

// 文件上传处理
const handleBeforeUpload = async (options) => {
  try {
    const res = await uploadFile(options.file);
    if (res.status === 200) {
      formData.beforePicUrl = res.data.url;
      beforeFileList.value = [{ name: options.file.name, url: res.data.url }];
      ElMessage.success('上传成功');
    } else {
      ElMessage.error(res.message || '上传失败');
    }
  } catch (error) {
    console.error('上传维修前照片失败:', error);
    ElMessage.error('上传失败');
  }
};

const handleAfterUpload = async (options) => {
  try {
    const res = await uploadFile(options.file);
    if (res.status === 200) {
      formData.afterPicUrl = res.data.url;
      afterFileList.value = [{ name: options.file.name, url: res.data.url }];
      ElMessage.success('上传成功');
    } else {
      ElMessage.error(res.message || '上传失败');
    }
  } catch (error) {
    console.error('上传维修后照片失败:', error);
    ElMessage.error('上传失败');
  }
};

const handleFileUpload = async (options) => {
  try {
    const res = await uploadFile(options.file);
    if (res.status === 200) {
      formData.fileUrl = res.data.url;
      fileList.value = [{ name: options.file.name, url: res.data.url }];
      ElMessage.success('上传成功');
    } else {
      ElMessage.error(res.message || '上传失败');
    }
  } catch (error) {
    console.error('上传相关附件失败:', error);
    ElMessage.error('上传失败');
  }
};

const handleUploadError = () => {
  ElMessage.error('上传失败');
};

// 获取维修详情
const fetchRepairDetail = async (id) => {
  try {
    const res = await getRepairDetail(id);
    if (res && res.code === 200) {
      const data = res.data;
      
      // 将详情数据映射到表单
      Object.keys(formData).forEach(key => {
        if (data[key] !== undefined) {
          formData[key] = data[key];
        }
      });

      // 处理维修结果
      if (data.repairResult) {
        formData.repairResult = data.repairResult.toString();
        const resultItem = REPAIR_RESULTS.find(item => item.value === Number(data.repairResult));
        formData.repairResultLabel = resultItem ? resultItem.label : '';
      }
      
      // 设置文件列表
      if (data.beforePicUrl) {
        beforeFileList.value = [{ name: '维修前照片', url: data.beforePicUrl }];
      }
      if (data.afterPicUrl) {
        afterFileList.value = [{ name: '维修后照片', url: data.afterPicUrl }];
      }
      if (data.fileUrl) {
        fileList.value = [{ name: '相关附件', url: data.fileUrl }];
      }
    } else {
      ElMessage.error(res?.msg || '获取维修详情失败');
    }
  } catch (error) {
    console.error('获取维修详情失败:', error);
    ElMessage.error('获取维修详情失败');
  }
};

// 组件挂载时获取数据
onMounted(() => {
  fetchEnterpriseList();
  fetchPipelineList();
});

// 监听props.data变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    if (props.mode === 'edit' || props.mode === 'view') {
      // 编辑模式或查看模式，需要获取详情
      if (newVal.id) {
        fetchRepairDetail(newVal.id);
      }
    } else {
      // 新增模式，直接使用传入的默认数据
      Object.keys(formData).forEach(key => {
        if (newVal[key] !== undefined) {
          formData[key] = newVal[key];
        }
      });
    }
  }
}, { immediate: true, deep: true });

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  // 重置表单
  if (formRef.value) {
    formRef.value.resetFields();
  }
  // 重置表单数据
  Object.keys(formData).forEach(key => {
    formData[key] = '';
  });
  // 重置文件列表
  beforeFileList.value = [];
  afterFileList.value = [];
  fileList.value = [];
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;
  
  try {
    await formRef.value.validate();
    
    // 准备提交数据
    const submitData = { ...formData };
    
    // 确保数值类型字段为数值
    if (submitData.longitude) submitData.longitude = Number(submitData.longitude);
    if (submitData.latitude) submitData.latitude = Number(submitData.latitude);
    if (submitData.repairType) submitData.repairType = Number(submitData.repairType);
    if (submitData.repairResult) submitData.repairResult = Number(submitData.repairResult);
    
    // 提交数据
    let res;
    if (props.mode === 'add') {
      res = await saveRepair(submitData);
    } else if (props.mode === 'edit') {
      res = await updateRepair(submitData);
    }
    
    if (res && res.code === 200) {
      ElMessage.success(props.mode === 'add' ? '新增成功' : '更新成功');
      emit('success');
      handleClose();
    } else {
      ElMessage.error(res?.msg || (props.mode === 'add' ? '新增失败' : '更新失败'));
    }
  } catch (error) {
    console.error('表单验证失败', error);
  }
};

// 处理管线选择变化
const handlePipelineChange = (value) => {
  const selectedPipeline = pipelineList.value.find(item => item.pipelineCode === value);
  if (selectedPipeline) {
    formData.connectedPipelineId = selectedPipeline.id;
  }
};
</script>

<style scoped>
.drainage-network-maintenance-dialog {
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "Helvetica Neue", Arial, sans-serif;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-input__inner),
:deep(.el-select__input) {
  border-radius: 6px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 6px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  background-color: #0277FD;
  border-color: #0277FD;
}

:deep(.el-button--primary:hover) {
  background-color: #1890ff;
  border-color: #1890ff;
}

.w-full {
  width: 100%;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.mr-2 {
  margin-right: 8px;
}

.ml-2 {
  margin-left: 8px;
}

.upload-demo {
  :deep(.el-upload-list) {
    width: 100%;
  }
}
</style> 