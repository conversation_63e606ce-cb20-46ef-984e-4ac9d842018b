/**
 *    ████████                   ██                   ██       ██                  ██      ██
 *   ██░░░░░░██                 ░░                   ░██      ░██                 ░██     ░██
 *  ██      ░░   █████  ███████  ██ ██   ██  ██████  ░██   █  ░██  ██████  ██████ ░██     ░██
 * ░██          ██░░░██░░██░░░██░██░██  ░██ ██░░░░   ░██  ███ ░██ ██░░░░██░░██░░█ ░██  ██████
 * ░██    █████░███████ ░██  ░██░██░██  ░██░░█████   ░██ ██░██░██░██   ░██ ░██ ░  ░██ ██░░░██
 * ░░██  ░░░░██░██░░░░  ░██  ░██░██░██  ░██ ░░░░░██  ░████ ░░████░██   ░██ ░██    ░██░██  ░██
 *  ░░████████ ░░██████ ███  ░██░██░░██████ ██████   ░██░   ░░░██░░██████ ░███    ███░░██████
 *   ░░░░░░░░   ░░░░░░ ░░░   ░░ ░░  ░░░░░░ ░░░░░░    ░░       ░░  ░░░░░░  ░░░    ░░░  ░░░░░░
 *
 * @license
 * GeniusWorld Web SDK - http://zydlxx.cn:1234/
 * Version 4.0.0.B#develop-d3ff2330@202405231107
 *
 *
 * Copyright © 2020 - 2023 正元地理信息集团股份有限公司. All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
define(["./AttributeCompression-aa106b76","./Matrix3-79d15570","./Color-e280f578","./defaultValue-7b61670d","./IndexDatatype-7c192505","./Math-6acd1674","./OrientedBoundingBox-82cae8c7","./Matrix2-d550732e","./createTaskProcessorWorker","./ComponentDatatype-e95dda25","./WebGLConstants-68839929","./Transforms-6a5d79d3","./combine-bc3d0d90","./RuntimeError-7dc4ea5a","./EllipsoidTangentPlane-d7ae8406","./AxisAlignedBoundingBox-5054a700","./IntersectionTests-044bd161","./Plane-e4eb0e88"],(function(e,t,n,r,a,o,i,s,c,f,d,l,u,h,g,p,b,y){"use strict";const m=new t.Cartesian3,x=new t.Ellipsoid,I=new s.Rectangle,w={min:void 0,max:void 0,indexBytesPerElement:void 0};function C(e,t,r){const a=t.length,o=2+a*i.OrientedBoundingBox.packedLength+1+function(e){const t=e.length;let r=0;for(let a=0;a<t;++a)r+=n.Color.packedLength+3+e[a].batchIds.length;return r}(r),s=new Float64Array(o);let c=0;s[c++]=e,s[c++]=a;for(let e=0;e<a;++e)i.OrientedBoundingBox.pack(t[e],s,c),c+=i.OrientedBoundingBox.packedLength;const f=r.length;s[c++]=f;for(let e=0;e<f;++e){const t=r[e];n.Color.pack(t.color,s,c),c+=n.Color.packedLength,s[c++]=t.offset,s[c++]=t.count;const a=t.batchIds,o=a.length;s[c++]=o;for(let e=0;e<o;++e)s[c++]=a[e]}return s}const A=new t.Cartesian3,E=new t.Cartesian3,N=new t.Cartesian3,T=new t.Cartesian3,B=new t.Cartographic,k=new s.Rectangle;return c((function(c,f){let d;!function(e){const n=new Float64Array(e);let r=0;w.indexBytesPerElement=n[r++],w.min=n[r++],w.max=n[r++],t.Cartesian3.unpack(n,r,m),r+=t.Cartesian3.packedLength,t.Ellipsoid.unpack(n,r,x),r+=t.Ellipsoid.packedLength,s.Rectangle.unpack(n,r,I)}(c.packedBuffer),d=2===w.indexBytesPerElement?new Uint16Array(c.indices):new Uint32Array(c.indices);const l=new Uint16Array(c.positions),u=new Uint32Array(c.counts),h=new Uint32Array(c.indexCounts),g=new Uint32Array(c.batchIds),p=new Uint32Array(c.batchTableColors),b=new Array(u.length),y=m,L=x;let O=I;const U=w.min,P=w.max;let F,M,D,R=c.minimumHeights,S=c.maximumHeights;r.defined(R)&&r.defined(S)&&(R=new Float32Array(R),S=new Float32Array(S));const _=l.length/2,V=l.subarray(0,_),G=l.subarray(_,2*_);e.AttributeCompression.zigZagDeltaDecode(V,G);const Y=new Float64Array(3*_);for(F=0;F<_;++F){const e=V[F],n=G[F],r=o.CesiumMath.lerp(O.west,O.east,e/32767),a=o.CesiumMath.lerp(O.south,O.north,n/32767),i=t.Cartographic.fromRadians(r,a,0,B),s=L.cartographicToCartesian(i,A);t.Cartesian3.pack(s,Y,3*F)}const v=u.length,H=new Array(v),W=new Array(v);let z=0,Z=0;for(F=0;F<v;++F)H[F]=z,W[F]=Z,z+=u[F],Z+=h[F];const j=new Float32Array(3*_*1),q=new Uint32Array(1*_),J=new Uint32Array(W.length),K=new Uint32Array(h.length);let Q=[];const X=new Uint32Array(1*v),$={};for(F=0;F<v;++F)D=p[F],r.defined($[D])?($[D].positionLength+=u[F],$[D].indexLength+=h[F],$[D].batchIds.push(F)):$[D]={positionLength:u[F],indexLength:h[F],offset:0,indexOffset:0,batchIds:[F]};let ee,te=0,ne=0;for(D in $)if($.hasOwnProperty(D)){ee=$[D],ee.offset=te,ee.indexOffset=ne;const e=1*ee.positionLength,t=1*ee.indexLength+3*ee.positionLength;te+=e,ne+=t,ee.indexLength=t}const re=[];for(D in $)$.hasOwnProperty(D)&&(ee=$[D],re.push({color:n.Color.fromRgba(parseInt(D)),offset:ee.indexOffset,count:ee.indexLength,batchIds:ee.batchIds}));for(F=0;F<v;++F){D=p[F],ee=$[D];const e=ee.offset;let n=3*e,a=e;const o=H[F],s=u[F],c=g[F];let f=U,l=P;r.defined(R)&&r.defined(S)&&(f=R[F],l=S[F]);let m=Number.POSITIVE_INFINITY,x=Number.NEGATIVE_INFINITY,I=Number.POSITIVE_INFINITY,w=Number.NEGATIVE_INFINITY;for(M=0;M<s;++M){const e=t.Cartesian3.unpack(Y,3*o+3*M,A),r=L.cartesianToCartographic(e,B),i=r.latitude,s=r.longitude;m=Math.min(i,m),x=Math.max(i,x),I=Math.min(s,I),w=Math.max(s,w);const d=L.geodeticSurfaceNormal(e,E),u=t.Cartesian3.multiplyByScalar(d,.5*(f+l),N),h=t.Cartesian3.add(e,u,T);t.Cartesian3.subtract(h,y,e),t.Cartesian3.pack(e,j,n),q[a]=c,n+=3,a+=1}O=k,O.west=I,O.east=w,O.south=m,O.north=x,b[F]=i.OrientedBoundingBox.fromRectangle(O,U,P,L);let C=ee.indexOffset;const _=W[F],V=h[F];for(J[F]=C,M=0;M<V;M+=3){const e=d[_+M]-o,t=d[_+M+1]-o,n=d[_+M+2]-o;Q[C++]=e,Q[C++]=t,Q[C++]=n}ee.offset+=1*s,ee.indexOffset=C,X[F]=ee.offset,K[F]=C-J[F]}Q=a.IndexDatatype.createTypedArray(j.length/3,Q);const ae=re.length;for(let e=0;e<ae;++e){const t=re[e].batchIds;let n=0;const r=t.length;for(let e=0;e<r;++e)n+=K[t[e]];re[e].count=n}const oe=C(2===Q.BYTES_PER_ELEMENT?a.IndexDatatype.UNSIGNED_SHORT:a.IndexDatatype.UNSIGNED_INT,b,re);return f.push(j.buffer,Q.buffer,J.buffer,K.buffer,q.buffer,oe.buffer,X.buffer,u.buffer),{positions:j.buffer,indices:Q.buffer,indexOffsets:J.buffer,indexCounts:K.buffer,batchIds:q.buffer,packedBuffer:oe.buffer,batchedPolygonVectexOffset:X.buffer,counts:u.buffer}}))}));
