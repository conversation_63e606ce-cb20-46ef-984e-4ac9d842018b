import { defineStore } from 'pinia'
import { getUserInfo<PERSON>pi, logout<PERSON>pi, updateUserPassword } from '@/api/user'
import { useRouter } from 'vue-router'
import { login as loginAuth } from '@/api/user'
import { JSEncrypt } from 'jsencrypt'
import { ref } from 'vue'

const PUBLIC_KEY = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCqcotiDYlqu2dNtH7Qi+GkU8QqmwjNtcxme0B18a9aXb7VZJp9ICUeVfDU7nOO3AuIOmJXBg6WHxgBP6EnvRyi3rAEyBgdVWRYZNIuMCtymQrjDjkzS1QIOpNldDgV9P1/o/MkcvWLaZpRRdbzJl8H1YMhIKWR+FF7lE9LWj8+7QIDAQAB";
const TOKEN = 'Admin-Token'

export const useUserStore = defineStore('user', () => {
  const user = ref({
    permission: {
      menus: []
    }
  })
  
  const token = ref(localStorage.getItem(TOKEN) || '')
  const userInfo = ref(null)

  function setTimeStamp() {
    localStorage.setItem('timeStamp', Date.now().toString())
  }

  const login = (loginData) => {
    return new Promise((resolve, reject) => {
      const { username, password } = loginData
      const encrypt = new JSEncrypt()
      encrypt.setPublicKey(PUBLIC_KEY)
      const newPassword = encrypt.encrypt(password)
      
      loginAuth({
        username,
        password: newPassword
      })
        .then(response => {
          console.log('登录响应:', response)
          
          // 处理嵌套的response.data.data结构
          if (response.data && response.data.data && response.data.data.accessToken) {
            const accessToken = response.data.data.accessToken
            localStorage.setItem(TOKEN, accessToken)
            token.value = accessToken
            setTimeStamp()
            resolve()
          } else if (response.data && response.data.accessToken) {
            // 兼容处理直接在response.data中的accessToken
            const accessToken = response.data.accessToken
            localStorage.setItem(TOKEN, accessToken)
            token.value = accessToken
            setTimeStamp()
            resolve()
          } else {
            console.error('登录响应中未找到token', response)
            reject(new Error('登录响应格式错误'))
          }
        })
        .catch(error => {
          reject(error)
        })
    })
  }

  const logout = () => {
    return new Promise((resolve) => {
      localStorage.setItem(TOKEN, '')
      token.value = ''
      location.reload()
      resolve()
    })
  }

  const resetToken = () => {
    return new Promise((resolve) => {
      localStorage.setItem(TOKEN, '')
      token.value = ''
      resolve()
    })
  }

  // 获取用户信息
  const getUserInfo = async () => {
    try {
      const res = await getUserInfoApi()
      if (res.data?.data) {
        userInfo.value = res.data.data
      }
      return res.data
    } catch (error) {
      console.error('获取用户信息失败:', error)
      return null
    }
  }

  // 修改密码
  const updatePassword = async (userId, newPassword) => {
    try {
      await updateUserPassword(userId, newPassword)
      return true
    } catch (error) {
      console.error('修改密码失败:', error)
      return false
    }
  }

  // 清除用户信息
  const clearUserInfo = () => {
    userInfo.value = null
    token.value = ''
    localStorage.removeItem(TOKEN)
  }

  return {
    user,
    token,
    userInfo,
    login,
    logout,
    resetToken,
    getUserInfo,
    updatePassword,
    clearUserInfo
  }
}) 
