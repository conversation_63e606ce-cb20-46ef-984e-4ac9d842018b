<template>
  <teleport to="body">
    <transition name="fade">
      <div v-if="modelValue" class="modal-overlay" @click.self="handleClose">
        <div class="modal-container">
          <div class="modal-header">
            <div class="modal-title">管线风险</div>
            <div class="close-icon" @click="handleClose">×</div>
          </div>
          <div class="modal-content">
            <div class="search-area">
              <div class="search-row">
                <div class="search-item">
                  <span class="label">风险等级:</span>
                  <div class="risk-level-select">
                    <div 
                      v-for="level in riskLevels" 
                      :key="level.value"
                      class="risk-level-option"
                      :class="{ active: searchForm.riskLevel === level.value }"
                      @click="handleRiskLevelSelect(level.value)"
                    >
                      {{ level.label }}
                    </div>
                  </div>
                </div>
              </div>
              <div class="search-row">
                <div class="search-item">
                  <span class="label">所属区域:</span>
                  <div class="area-select">
                    <div 
                      v-for="area in areas" 
                      :key="area.value"
                      class="area-option"
                      :class="{ active: searchForm.area === area.value }"
                      @click="handleAreaSelect(area.value)"
                    >
                      {{ area.label }}
                    </div>
                  </div>
                </div>
              </div>
              <div class="search-row">
                <div class="search-item">
                  <span class="label">管线编码:</span>
                  <el-input v-model="searchForm.code" placeholder="请输入管线编码" class="search-input" />
                </div>
                <div class="button-container">
                  <el-button type="primary" class="search-btn" @click="handleSearch">查 询</el-button>
                  <el-button class="reset-btn" @click="handleReset">重 置</el-button>
                </div>
              </div>
            </div>

            <div class="risk-table">
              <div class="table-header">
                <div class="header-item seq">序号</div>
                <div class="header-item code">管线编码</div>
                <div class="header-item age">管龄</div>
                <div class="header-item material">材质</div>
                <div class="header-item diameter">管径</div>
                <div class="header-item risk-level">风险等级</div>
                <div class="header-item area">所属区域</div>
                <div class="header-item position">定位</div>
              </div>
              <div class="table-body">
                <div
                  v-for="(item, index) in pipelineList"
                  :key="index"
                  class="table-row"
                >
                  <div class="row-item seq">{{ index + 1 }}</div>
                  <div class="row-item code" :style="{ color: getRiskColor(item.riskLevel) }">{{ item.code }}</div>
                  <div class="row-item age" :style="{ color: getRiskColor(item.riskLevel) }">{{ item.age }}</div>
                  <div class="row-item material" :style="{ color: getRiskColor(item.riskLevel) }">{{ item.material }}</div>
                  <div class="row-item diameter" :style="{ color: getRiskColor(item.riskLevel) }">{{ item.diameter }}</div>
                  <div class="row-item risk-level" :style="{ color: getRiskColor(item.riskLevel) }">{{ item.riskLevel }}</div>
                  <div class="row-item area" :style="{ color: getRiskColor(item.riskLevel) }">{{ item.area }}</div>
                  <div class="row-item position">
                    <div class="position-icon" @click="handlePosition(item)"></div>
                  </div>
                </div>
              </div>
              <div class="pagination">
                <div class="page-info">共 {{ totalItems }} 条记录，每页 {{ pageSize }} 条</div>
                <div class="page-controls">
                  <span class="page-btn" :class="{ disabled: currentPage === 1 }" @click="changePage(currentPage - 1)">上一页</span>
                  <span class="page-number" v-for="page in pageNumbers" :key="page" :class="{ active: currentPage === page }" @click="changePage(page)">{{ page }}</span>
                  <span class="page-btn" :class="{ disabled: currentPage === totalPages }" @click="changePage(currentPage + 1)">下一页</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </transition>
  </teleport>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch, computed } from 'vue'
import { ElInput, ElButton } from 'element-plus'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  pipeType: {
    type: String,
    default: 'rain'
  }
})

const emit = defineEmits(['update:modelValue', 'position'])

// 搜索表单
const searchForm = ref({
  riskLevel: '',
  area: '',
  code: ''
})

// 模拟管网风险数据
const allPipelineList = ref([])

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const totalItems = ref(0)
const totalPages = computed(() => Math.ceil(totalItems.value / pageSize.value))

// 当前页的数据
const pipelineList = computed(() => {
  const startIndex = (currentPage.value - 1) * pageSize.value
  const endIndex = startIndex + pageSize.value
  return allPipelineList.value.slice(startIndex, endIndex)
})

// 风险等级选项
const riskLevels = [
  { label: '全部', value: '' },
  { label: '重大风险', value: '重大风险' },
  { label: '较大风险', value: '较大风险' },
  { label: '一般风险', value: '一般风险' },
  { label: '低风险', value: '低风险' }
]

// 区域选项
const areas = [
  { label: '全部', value: '' },
  { label: '区域1', value: '区域1' },
  { label: '区域2', value: '区域2' }
]

// 监听外部modelValue变化
watch(() => props.modelValue, (val) => {
  if (val) {
    fetchRiskList()
  }
})

// 关闭弹窗
const handleClose = () => {
  emit('update:modelValue', false)
}

// 初始化数据
const initPipelineData = () => {
  const mockData = {
    rain: [
      { code: 'GX10322', age: '3年', material: 'PE', diameter: '0.5M', riskLevel: '重大风险', area: '区域1' },
      { code: 'GX10322', age: '5年', material: 'PE', diameter: '0.5M', riskLevel: '较大风险', area: '区域1' },
      { code: 'GX10322', age: '8年', material: 'PE', diameter: '0.5M', riskLevel: '较大风险', area: '区域2' },
      { code: 'GX10322', age: '5年', material: 'PE', diameter: '0.5M', riskLevel: '较大风险', area: '区域2' },
      { code: 'GX10322', age: '8年', material: 'PE', diameter: '0.5M', riskLevel: '较大风险', area: '区域1' },
      { code: 'GX10322', age: '6年', material: 'PE', diameter: '0.5M', riskLevel: '一般风险', area: '区域1' },
      { code: 'GX10322', age: '6年', material: 'PE', diameter: '0.5M', riskLevel: '一般风险', area: '区域2' }
    ],
    sewage: [
      { code: 'GX10323', age: '4年', material: 'PE', diameter: '0.5M', riskLevel: '重大风险', area: '区域1' },
      { code: 'GX10323', age: '6年', material: 'PE', diameter: '0.5M', riskLevel: '较大风险', area: '区域2' },
      { code: 'GX10323', age: '7年', material: 'PE', diameter: '0.5M', riskLevel: '较大风险', area: '区域1' },
      { code: 'GX10323', age: '5年', material: 'PE', diameter: '0.5M', riskLevel: '一般风险', area: '区域2' },
      { code: 'GX10323', age: '9年', material: 'PE', diameter: '0.5M', riskLevel: '一般风险', area: '区域1' },
      { code: 'GX10323', age: '3年', material: 'PE', diameter: '0.5M', riskLevel: '低风险', area: '区域2' }
    ],
    plant: [
      { code: 'GX10324', age: '2年', material: 'PE', diameter: '0.5M', riskLevel: '较大风险', area: '区域1' },
      { code: 'GX10324', age: '5年', material: 'PE', diameter: '0.5M', riskLevel: '一般风险', area: '区域2' },
      { code: 'GX10324', age: '7年', material: 'PE', diameter: '0.5M', riskLevel: '一般风险', area: '区域1' },
      { code: 'GX10324', age: '4年', material: 'PE', diameter: '0.5M', riskLevel: '低风险', area: '区域2' }
    ],
    station: [
      { code: 'GX10325', age: '3年', material: 'PE', diameter: '0.5M', riskLevel: '重大风险', area: '区域1' },
      { code: 'GX10325', age: '6年', material: 'PE', diameter: '0.5M', riskLevel: '较大风险', area: '区域2' },
      { code: 'GX10325', age: '8年', material: 'PE', diameter: '0.5M', riskLevel: '一般风险', area: '区域1' },
      { code: 'GX10325', age: '4年', material: 'PE', diameter: '0.5M', riskLevel: '低风险', area: '区域2' }
    ]
  }
  return mockData[props.pipeType] || []
}

// 根据风险等级获取对应的颜色
const getRiskColor = (riskLevel) => {
  switch (riskLevel) {
    case '重大风险':
      return '#FF2330'
    case '较大风险':
      return '#FF9000'
    case '一般风险':
      return '#FFD11B'
    case '低风险':
      return '#00B0FF'
    default:
      return '#FFFFFF'
  }
}

// 查询处理
const handleSearch = () => {
  fetchRiskList()
}

// 重置查询条件
const handleReset = () => {
  searchForm.value = {
    riskLevel: '',
    area: '',
    code: ''
  }
  fetchRiskList()
}

// 定位处理
const handlePosition = (item) => {
  emit('position', item)
}

// 计算页码显示
const pageNumbers = computed(() => {
  const pages = []
  let startPage = Math.max(1, currentPage.value - 2)
  let endPage = Math.min(totalPages.value, startPage + 4)
  
  if (endPage - startPage + 1 < 5 && totalPages.value >= 5) {
    startPage = Math.max(1, endPage - 4)
  }
  
  for (let i = startPage; i <= endPage; i++) {
    pages.push(i)
  }
  return pages
})

// 切换页码
const changePage = (page) => {
  if (page < 1 || page > totalPages.value || page === currentPage.value) return
  currentPage.value = page
}

// 处理风险等级选择
const handleRiskLevelSelect = (value) => {
  searchForm.value.riskLevel = value
}

// 处理区域选择
const handleAreaSelect = (value) => {
  searchForm.value.area = value
}

// 获取风险列表数据
const fetchRiskList = async () => {
  try {
    let filteredData = initPipelineData()

    // 根据查询条件过滤
    if (searchForm.value.riskLevel) {
      filteredData = filteredData.filter(item => item.riskLevel === searchForm.value.riskLevel)
    }
    if (searchForm.value.area) {
      filteredData = filteredData.filter(item => item.area === searchForm.value.area)
    }
    if (searchForm.value.code) {
      filteredData = filteredData.filter(item => item.code && item.code.includes(searchForm.value.code))
    }

    // 更新总数和列表
    allPipelineList.value = filteredData
    totalItems.value = filteredData.length
    
    // 如果当前页超出范围，重置为第一页
    if (currentPage.value > Math.ceil(totalItems.value / pageSize.value)) {
      currentPage.value = 1
    }
  } catch (error) {
    console.error('获取风险列表数据失败', error)
  }
}
</script>

<style scoped>
/* 弹窗基础样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.modal-container {
  width: 850px;
  background: linear-gradient(180deg, rgba(0, 22, 72, 0.9) 0%, rgba(0, 35, 91, 0.9) 100%);
  border: 1px solid rgba(59, 141, 242, 0.5);
  box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.3);
  border-radius: 4px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid rgba(59, 141, 242, 0.3);
}

.modal-title {
  font-family: PingFangSC, 'PingFang SC';
  font-weight: 500;
  font-size: 16px;
  color: #FFFFFF;
}

.close-icon {
  font-size: 20px;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
}

.close-icon:hover {
  color: #FFFFFF;
}

.modal-content {
  padding: 15px 20px;
}

/* 搜索区域样式 */
.search-area {
  background: rgba(3, 24, 55, 0.5);
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 20px;
  border-radius: 4px;
}

.search-row {
  display: flex;
  align-items: center;
  gap: 15px;
}

.search-item {
  flex: 1;
  display: flex;
  align-items: center;
}

.search-item .label {
  margin-right: 10px;
  color: #D3E5FF;
  font-size: 14px;
  white-space: nowrap;
  min-width: 70px;
}

/* 输入框样式 */
.search-input {
  flex: 1;
}

.search-input :deep(.el-input__wrapper) {
  background: rgba(0, 19, 47, 0.35);
  box-shadow: none !important;
  border: 1px solid rgba(59, 141, 242, 0.5);
}

.search-input :deep(.el-input__inner) {
  color: #FFFFFF;
  height: 32px;
  line-height: 32px;
}

.search-input :deep(.el-input__inner::placeholder) {
  color: rgba(255, 255, 255, 0.3);
}

/* 按钮样式 */
.button-container {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-left: auto;
}

.search-btn {
  background: #1890FF;
  border: none;
  color: #FFFFFF;
  height: 32px;
  padding: 0 15px;
  border-radius: 2px;
}

.reset-btn {
  background: rgba(24, 144, 255, 0.1);
  border: 1px solid #1890FF;
  color: #1890FF;
  height: 32px;
  padding: 0 15px;
  border-radius: 2px;
}

.search-btn:hover {
  background: #40A9FF;
}

.reset-btn:hover {
  background: rgba(24, 144, 255, 0.2);
}

/* 表格样式 */
.risk-table {
  width: 100%;
}

.table-header {
  display: flex;
  width: 100%;
  height: 40px;
  background: linear-gradient(90deg, rgba(0,80,167,0.2) 0%, rgba(0,80,167,0.4) 100%);
  font-family: PingFangSC, 'PingFang SC';
  font-weight: 500;
  font-size: 14px;
  color: #FFFFFF;
  line-height: 40px;
  border-radius: 4px 4px 0 0;
}

.table-body {
  max-height: 400px;
  overflow-y: auto;
}

.table-row {
  display: flex;
  width: 100%;
  height: 40px;
  line-height: 40px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.table-row:hover {
  background-color: rgba(59, 141, 242, 0.1);
}

.header-item,
.row-item {
  text-align: center;
  font-family: PingFangSC, 'PingFang SC';
  font-weight: 500;
  font-size: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 表格列宽设置 */
.seq {
  flex: 0.5;
  color: #40CDFF;
}

.code {
  flex: 1.2;
}

.age {
  flex: 0.8;
}

.material {
  flex: 0.8;
}

.diameter {
  flex: 0.8;
}

.risk-level {
  flex: 1;
}

.area {
  flex: 1;
}

.position {
  flex: 0.5;
  display: flex;
  justify-content: center;
  align-items: center;
}

.position-icon {
  width: 24px;
  height: 24px;
  cursor: pointer;
  background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0iI2ZmZmZmZiI+PHBhdGggZD0iTTEyIDJDOC4xMyAyIDUgNS4xMyA1IDljMCA1LjI1IDcgMTMgNyAxM3M3LTcuNzUgNy0xM2MwLTMuODctMy4xMy03LTctN3ptMCA5LjVjLTEuMzggMC0yLjUtMS4xMi0yLjUtMi41czEuMTItMi41IDIuNS0yLjUgMi41IDEuMTIgMi41IDIuNS0xLjEyIDIuNS0yLjUgMi41eiIvPjwvc3ZnPg==');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

/* 分页样式 */
.pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
}

.page-info {
  font-family: PingFangSC, 'PingFang SC';
  font-weight: 400;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
}

.page-controls {
  display: flex;
  align-items: center;
  gap: 5px;
}

.page-btn,
.page-number {
  min-width: 30px;
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  border-radius: 2px;
  font-family: PingFangSC, 'PingFang SC';
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
}

.page-number:hover {
  background: rgba(59, 141, 242, 0.2);
}

.page-number.active {
  background: #1890FF;
  color: #FFFFFF;
}

.page-btn.disabled {
  cursor: not-allowed;
  color: rgba(255, 255, 255, 0.3);
}

/* 过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 风险等级选择器样式 */
.risk-level-select,
.area-select {
  display: flex;
  gap: 10px;
  flex: 1;
}

.risk-level-option,
.area-option {
  padding: 0 15px;
  height: 32px;
  line-height: 32px;
  background: rgba(0, 19, 47, 0.35);
  border: 1px solid rgba(59, 141, 242, 0.5);
  border-radius: 2px;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;
  text-align: center;
  white-space: nowrap;
}

.risk-level-option:hover,
.area-option:hover {
  background: rgba(59, 141, 242, 0.2);
  color: #FFFFFF;
}

.risk-level-option.active,
.area-option.active {
  background: rgba(59, 141, 242, 0.3);
  color: #FFFFFF;
  border-color: #1890FF;
}
</style>