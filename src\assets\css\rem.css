/* REM适配方案 - 基于1920*1080分辨率 */

/* 基准值设置 - 1920px宽度下 1rem = 16px */
:root {
  --design-width: 1920px;   /* 设计稿宽度 */
  --design-height: 1080px;  /* 设计稿高度 */
  --base-font-size: 16px;   /* 基准字体大小 */
}

/* 设置基础字体大小 */
html {
  font-size: var(--base-font-size);
}

/* 媒体查询适配不同屏幕尺寸 */
/* 1280px屏幕 */
@media screen and (max-width: 1280px) {
  html {
    font-size: calc(16px * (1280 / 1920));  /* 约10.67px */
  }
}

/* 1366px屏幕 */
@media screen and (min-width: 1281px) and (max-width: 1366px) {
  html {
    font-size: calc(16px * (1366 / 1920));  /* 约11.4px */
  }
}

/* 1440px屏幕 */
@media screen and (min-width: 1367px) and (max-width: 1440px) {
  html {
    font-size: calc(16px * (1440 / 1920));  /* 约12px */
  }
}

/* 1600px屏幕 */
@media screen and (min-width: 1441px) and (max-width: 1600px) {
  html {
    font-size: calc(16px * (1600 / 1920));  /* 约13.3px */
  }
}

/* 1680px屏幕 */
@media screen and (min-width: 1601px) and (max-width: 1680px) {
  html {
    font-size: calc(16px * (1680 / 1920));  /* 约14px */
  }
}

/* 1920px屏幕 - 基准分辨率 */
@media screen and (min-width: 1681px) and (max-width: 1920px) {
  html {
    font-size: 16px;
  }
}

/* 2K屏幕 (2560px) */
@media screen and (min-width: 1921px) and (max-width: 2560px) {
  html {
    font-size: calc(16px * (2560 / 1920));  /* 约21.3px */
  }
}

/* 超宽屏 (3440px) */
@media screen and (min-width: 2561px) and (max-width: 3440px) {
  html {
    font-size: calc(16px * (3440 / 1920));  /* 约28.7px */
  }
}

/* 4K屏幕 (3840px及以上) */
@media screen and (min-width: 3441px) {
  html {
    font-size: calc(16px * (3840 / 1920));  /* 约32px */
  }
}

/* 动态计算方案 - 适用于更精确的响应式场景 */
@media screen and (min-width: 1280px) {
  html.dynamic-rem {
    font-size: calc(16px * (100vw / 1920));
  }
}

/* 额外的高度适配 */
@media screen and (max-height: 720px) {
  html {
    --vertical-scale: 0.85;
  }
}

@media screen and (min-height: 721px) and (max-height: 900px) {
  html {
    --vertical-scale: 0.9;
  }
}

@media screen and (min-height: 901px) and (max-height: 1080px) {
  html {
    --vertical-scale: 1;
  }
}

@media screen and (min-height: 1081px) {
  html {
    --vertical-scale: calc(100vh / 1080);
  }
} 