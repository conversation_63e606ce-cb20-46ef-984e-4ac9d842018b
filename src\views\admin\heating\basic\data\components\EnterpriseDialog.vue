<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="1000px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="enterprise-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      :disabled="mode === 'view'"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="企业名称" prop="enterpriseName">
            <el-input v-model="formData.enterpriseName" placeholder="请输入企业名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="热源信息" prop="factoryId">
            <el-select v-model="formData.factoryId" placeholder="请选择" class="w-full" @change="handleFactoryChange">
              <el-option 
                v-for="item in factoryOptions" 
                :key="item.id" 
                :label="item.factoryName" 
                :value="item.id" 
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="统一社会代码" prop="unifiedSocialCreditCode">
            <el-input v-model="formData.unifiedSocialCreditCode" placeholder="请输入统一社会代码" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="额定负荷" prop="ratedLoad">
            <el-input v-model="formData.ratedLoad" placeholder="请输入额定负荷" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="供热使用面积" prop="heatUseArea">
            <el-input v-model="formData.heatUseArea" placeholder="请输入供热使用面积">
              <template #append>万m²</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="供热建筑面积" prop="heatBuildingArea">
              <el-input v-model="formData.heatBuildingArea" placeholder="请输入供热建筑面积">
              <template #append>万m²</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="联系人" prop="contactPerson">
            <el-input v-model="formData.contactPerson" placeholder="请输入联系人" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系方式" prop="contactInfo">
            <el-input v-model="formData.contactInfo" placeholder="请输入联系方式" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="企业地址">
            <div class="flex items-center">
              <el-cascader
                v-model="formData.town"
                :options="areaOptions"
                :props="{
                  value: 'code',
                  label: 'name',
                  children: 'children'
                }"
                placeholder="请选择所属区域"
                class="area-select"
                @change="handleAreaChange"
              />
              <el-input 
                v-model="formData.address" 
                placeholder="输入详细地址" 
                class="address-input" 
              />
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="位置坐标">
            <div class="flex items-center">
              <el-input v-model="formData.longitude" placeholder="经度" class="mr-2 w-32" />
              <el-input v-model="formData.latitude" placeholder="纬度" class="w-32" />
              <el-button type="primary" class="ml-2" icon="Location" circle @click="openMapPicker"></el-button>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="审核状态" prop="status">
            <el-select v-model="formData.status" placeholder="请选择" class="w-full">
              <el-option 
                v-for="item in AUDIT_STATUS_OPTIONS" 
                :key="item.value" 
                :label="item.label" 
                :value="item.value" 
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="备注">
            <el-input
              v-model="formData.remarks"
              type="textarea"
              :rows="4"
              placeholder="请输入备注"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="mode !== 'view'">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import { saveEnterprise, updateEnterprise, getHeatFactoryList } from '@/api/heating';
import { AUDIT_STATUS_OPTIONS } from '@/constants/heating';
import { AREA_OPTIONS } from '@/constants/gas';
import { collectShow } from "@/hooks/gishooks";
import bus from '@/utils/mitt';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add', // 'add', 'edit', 'view'
    validator: (value) => ['add', 'edit', 'view'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'success']);

// 表单引用
const formRef = ref(null);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 对话框标题
const dialogTitle = computed(() => {
  const titles = {
    add: '新增企业',
    edit: '编辑企业',
    view: '企业详情'
  };
  return titles[props.mode] || '企业信息';
});

// 行政区划选项
const areaOptions = ref(AREA_OPTIONS);

// 热源工厂选项
const factoryOptions = ref([]);

// 获取热源工厂列表
const fetchFactoryOptions = async () => {
  try {
    const res = await getHeatFactoryList({});
    if (res && res.code === 200 && res.data) {
      factoryOptions.value = res.data;
    }
  } catch (error) {
    console.error('获取热源工厂列表失败:', error);
  }
};

// 表单数据
const formData = reactive({
  id: '',
  enterpriseName: '',
  factoryId: '',
  factoryName: '',
  unifiedSocialCreditCode: '',
  ratedLoad: '',
  heatUseArea: '',
  heatBuildingArea: '',
  contactPerson: '',
  contactInfo: '',
  longitude: '', // 经度
  latitude: '', // 纬度
  address: '',
  city: '',
  county: '371728',
  countyName: '东明县',
  town: '',
  townName: '',
  status: 0, // 默认待审核
  remarks: '',
  geomText: ''
});

// 表单验证规则
const formRules = {
  enterpriseName: [{ required: true, message: '请输入企业名称', trigger: 'blur' }],
  factoryId: [{ required: true, message: '请选择热源信息', trigger: 'change' }],
  unifiedSocialCreditCode: [{ required: true, message: '请输入统一社会代码', trigger: 'blur' }],
  ratedLoad: [{ required: true, message: '请输入额定负荷', trigger: 'blur' }],
  heatUseArea: [{ required: true, message: '请输入供热使用面积', trigger: 'blur' }],
  heatBuildingArea: [{ required: true, message: '请输入供热建筑面积', trigger: 'blur' }],
  contactPerson: [{ required: true, message: '请输入联系人', trigger: 'blur' }],
  contactInfo: [{ required: true, message: '请输入联系方式', trigger: 'blur' }],
  status: [{ required: true, message: '请选择审核状态', trigger: 'change' }]
};

// 重置表单
const resetForm = () => {
  Object.keys(formData).forEach(key => {
    if (['longitude', 'latitude'].includes(key)) {
      formData[key] = '';
    } else if (key === 'status') {
      formData[key] = 0; // 默认待审核
    } else {
      formData[key] = '';
    }
  });
  
  // 保留默认值
  formData.county = '371728';
  formData.countyName = '东明县';
};

// 监听props.data变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    // 复制数据到表单
    Object.keys(formData).forEach(key => {
      if (newVal[key] !== undefined) {
        formData[key] = newVal[key];
      }
    });
  } else if (props.mode === 'add') {
    // 新增模式清空表单，保留默认值
    resetForm();
  }
}, { immediate: true, deep: true });

// 处理热源工厂选择变化
const handleFactoryChange = (value) => {
  if (value) {
    const selectedFactory = factoryOptions.value.find(item => item.id === value);
    if (selectedFactory) {
      formData.factoryName = selectedFactory.factoryName;
    }
  } else {
    formData.factoryName = '';
  }
};

// 处理区域选择变化
const handleAreaChange = (value) => {
  if (value && value.length > 0) {
    formData.town = value[value.length - 1];
    // 更新区域名称
    const selectedArea = findAreaByCode(areaOptions.value, formData.town);
    if (selectedArea) {
      formData.townName = selectedArea.name;
    }
  }
};

// 根据区域代码查找区域信息
const findAreaByCode = (areas, code) => {
  for (const area of areas) {
    if (area.code === code) {
      return area;
    }
    if (area.children) {
      const found = findAreaByCode(area.children, code);
      if (found) return found;
    }
  }
  return null;
};

// 地图选点事件处理函数
const handleCollectLocation = (params) => {
  nextTick(() => {
    formData.longitude = params.longitude;
    formData.latitude = params.latitude;
  });
};

// 打开地图选点
const openMapPicker = () => {
  collectShow.value = true; // 激活采集点位窗口
  // 先移除可能存在的旧监听器
  bus.off("getCollectLocation", handleCollectLocation);
  // 添加新的监听器
  bus.on("getCollectLocation", handleCollectLocation);
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  // 重置表单
  if (formRef.value) {
    formRef.value.resetFields();
  }
  resetForm();
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;
  
  try {
    await formRef.value.validate();
    
    // 准备提交数据
    const submitData = { ...formData };
    
    // 提交数据
    let res;
    if (props.mode === 'add') {
      res = await saveEnterprise(submitData);
    } else if (props.mode === 'edit') {
      res = await updateEnterprise(submitData);
    }
    
    if (res && res.code === 200) {
      ElMessage.success(props.mode === 'add' ? '新增成功' : '更新成功');
      emit('success');
      handleClose();
    } else {
      ElMessage.error(res?.msg || (props.mode === 'add' ? '新增失败' : '更新失败'));
    }
  } catch (error) {
    console.error('表单验证失败', error);
  }
};

// 组件卸载时清理事件监听
onMounted(() => {
  fetchFactoryOptions();
  bus.on("getCollectLocation", handleCollectLocation);
  return () => {
    bus.off("getCollectLocation", handleCollectLocation);
  };
});
</script>

<style scoped>
.enterprise-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-form-item__label::before) {
  content: '*';
  color: #ff4d4f;
  margin-right: 4px;
}

:deep(.el-form-item:not([required]) .el-form-item__label::before) {
  content: '';
  margin-right: 0;
}

:deep(.el-input__wrapper),
:deep(.el-select__input) {
  border-radius: 2px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  background-color: #0277FD;
  border-color: #0277FD;
}

:deep(.el-button--primary:hover) {
  background-color: #1890ff;
  border-color: #1890ff;
}

.w-full {
  width: 100%;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.ml-2 {
  margin-left: 8px;
}

.mr-2 {
  margin-right: 8px;
}

.w-32 {
  width: 140px;
}

.area-select {
  width: 180px;
  margin-right: 8px;
}

.address-input {
  flex: 1;
}
</style> 