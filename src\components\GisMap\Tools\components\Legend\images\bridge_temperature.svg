<?xml version="1.0" encoding="UTF-8"?>
<svg width="18px" height="18px" viewBox="0 0 18 18" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>温度传感器</title>
    <defs>
        <circle id="path-1" cx="7" cy="7" r="7"></circle>
        <filter x="-21.4%" y="-21.4%" width="142.9%" height="142.9%" filterUnits="objectBoundingBox" id="filter-2">
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="1" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.162200418   0 0 0 0 0.568546468   0 0 0 0 0.902202219  0 0 0 0.5 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="27.9288816%" y1="8.11919591%" x2="71.2992224%" y2="91.9872084%" id="linearGradient-3">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#A5E2FF" offset="16.835118%"></stop>
            <stop stop-color="#83BEFF" offset="71.3150675%"></stop>
            <stop stop-color="#C6E3FF" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="图层&amp;图例" transform="translate(-2089.000000, -264.000000)">
            <g id="编组-13备份-2" transform="translate(2067.000000, 30.000000)">
                <g id="编组-45备份" transform="translate(0.000000, 65.000000)">
                    <g id="编组-2备份-2" transform="translate(24.000000, 148.000000)">
                        <g id="温度传感器" transform="translate(0.000000, 23.000000)">
                            <g id="正常备份-3">
                                <g id="椭圆形">
                                    <use fill="black" fill-opacity="1" filter="url(#filter-2)" xlink:href="#path-1"></use>
                                    <use fill="#0099FF" fill-rule="evenodd" xlink:href="#path-1"></use>
                                </g>
                                <circle id="椭圆形" stroke="url(#linearGradient-3)" stroke-width="0.823529412" cx="7" cy="7" r="6.58823529"></circle>
                            </g>
                            <g id="编组" transform="translate(5.000000, 3.000000)" fill="#FFFFFF" fill-rule="nonzero">
                                <path d="M1.60650913,8 C0.904090076,7.99774328 0.283945844,7.48716191 0.0738600015,6.7381289 C-0.136225841,5.98909588 0.112179043,5.17429024 0.687777713,4.72438583 L0.687777713,1.02669252 C0.687777707,0.659890616 0.862886923,0.320951673 1.14714341,0.137550717 C1.43139991,-0.045850239 1.78161835,-0.045850239 2.06587484,0.137550717 C2.35013133,0.320951673 2.52524055,0.659890616 2.52524054,1.02669252 L2.52524054,4.72438583 C2.71512696,4.87311638 2.87440966,5.06543841 2.99243762,5.2884916 C3.28444969,5.84458422 3.28805368,6.5327307 3.00188619,7.09260485 C2.7157187,7.65247901 2.18347728,7.99858853 1.60650913,8 Z M1.60650913,0.512596132 C1.35283833,0.513147249 1.14738958,0.742962162 1.14714342,1.02644248 L1.14714342,5.01318998 L1.03258169,5.08820405 C0.582296125,5.37857527 0.362698801,5.97074657 0.497196513,6.53193627 C0.631694225,7.09312596 1.08674353,7.48337453 1.606621,7.48337453 C2.12649848,7.48337453 2.58154778,7.09312596 2.71604549,6.53193627 C2.85054321,5.97074657 2.63094588,5.37857527 2.18066032,5.08820405 L2.06609859,5.01318998 L2.06609859,1.02644248 C2.06585213,0.742864733 1.86026731,0.513009578 1.60650913,0.512596132 Z" id="形状"></path>
                                <path d="M0.954939259,6.19116085 C0.954939259,6.5952329 1.24805952,6.92279803 1.60964167,6.92279803 C1.97122383,6.92279803 2.26434409,6.5952329 2.26434409,6.19116085 C2.26434409,5.78708879 1.97122383,5.45952367 1.60964167,5.45952367 C1.24805952,5.45952367 0.954939259,5.78708879 0.954939259,6.19116085 L0.954939259,6.19116085 Z" id="路径"></path>
                                <path d="M0.724473269,6.19116085 C0.724596837,5.64446708 1.12125547,5.20138324 1.61046207,5.20147528 C2.09966867,5.20156735 2.49619375,5.64480044 2.49615257,6.19149422 C2.49611139,6.73818801 2.09951954,7.1813465 1.61031293,7.1813465 C1.1212455,7.1806574 0.72496639,6.73769926 0.724473269,6.19116085 L0.724473269,6.19116085 Z" id="路径"></path>
                                <path d="M1.60874666,6.46721273 C1.54740646,6.46721273 1.48858186,6.43996481 1.44522878,6.39147036 C1.4018757,6.34297592 1.37755003,6.27721245 1.3776093,6.20866413 L1.3776093,2.42920549 C1.38149552,2.28961076 1.48388235,2.17869652 1.60885854,2.17869652 C1.73383473,2.17869652 1.83622156,2.28961076 1.84010777,2.42920549 L1.84010777,6.20866413 C1.84016711,6.27725579 1.81581074,6.34305722 1.77240924,6.39155887 C1.72900774,6.44006052 1.67012562,6.46727904 1.60874666,6.46721273 L1.60874666,6.46721273 Z M3.77803576,2.04788399 L2.96894351,2.04788399 C2.84524989,2.04356276 2.74697928,1.93025753 2.74697928,1.79196101 C2.74697928,1.65366449 2.84524989,1.54035926 2.96894351,1.53603802 L3.77803576,1.53603802 C3.90172939,1.54035926 4,1.65366449 4,1.79196101 C4,1.93025753 3.90172939,2.04356276 3.77803576,2.04788399 L3.77803576,2.04788399 Z M3.77803576,2.78377197 L2.96894351,2.78377197 C2.84524989,2.77945073 2.74697928,2.66614551 2.74697928,2.52784899 C2.74697928,2.38955247 2.84524989,2.27624724 2.96894351,2.271926 L3.77803576,2.271926 C3.90172939,2.27624724 4,2.38955247 4,2.52784899 C4,2.66614551 3.90172939,2.77945073 3.77803576,2.78377197 L3.77803576,2.78377197 Z M3.77803576,3.5194099 L2.96894351,3.5194099 C2.84524989,3.51508866 2.74697928,3.40178344 2.74697928,3.26348692 C2.74697928,3.1251904 2.84524989,3.01188517 2.96894351,3.00756393 L3.77803576,3.00756393 C3.90172939,3.01188517 4,3.1251904 4,3.26348692 C4,3.40178344 3.90172939,3.51508866 3.77803576,3.5194099 L3.77803576,3.5194099 Z M3.77803576,4.25504783 L2.96894351,4.25504783 C2.84524989,4.25072659 2.74697928,4.13742137 2.74697928,3.99912485 C2.74697928,3.86082833 2.84524989,3.7475231 2.96894351,3.74320186 L3.77803576,3.74320186 C3.90172939,3.7475231 4,3.86082833 4,3.99912485 C4,4.13742137 3.90172939,4.25072659 3.77803576,4.25504783 L3.77803576,4.25504783 Z" id="形状"></path>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>