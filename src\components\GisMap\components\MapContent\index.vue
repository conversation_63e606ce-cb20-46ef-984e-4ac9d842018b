<template>
  <map-dialog-base
    title="地图定位"
    v-if="state.mapVisible"
    :visible="state.mapVisible"
    width="800px"
    :hasFooter="state.mapContentFooter"
  >
    <template #content>
      <div class="map-content">
        <map-point
          :point="point"
          :startPoint="startPoint"
          :endPoint="endPoint"
          :lineColor="state.lineColor"
        />
      </div>
    </template>
  </map-dialog-base>
</template>

<script setup>
import {reactive, watch} from "vue";
import MapDialogBase from "../MapDialogBase/index.vue";
import MapPoint from "../MapPoint/index.vue";
import { misPosition, collectShow, collectLocation } from "@/hooks/gishooks";
import bus from "@/utils/mitt";

const state = reactive({
  mapVisible: false,
  mapContentFooter: false,
  lineColor: "#DA12FF",
});

let point = [];
let startPoint = [];
let endPoint = [];

//监听是否启动定位弹框
watch(
  () => misPosition.value,
  (m) => {
    if (m.longitude && m.latitude) {
      state.mapVisible = true;
      point = [parseFloat(m.longitude), parseFloat(m.latitude)];
    } else {
      state.mapVisible = false;
    }
  },
  {
    immediate: true,
    deep: true,
  }
);

//监听是否开启地图采集
watch(
  () => collectShow.value,
  (m) => {
    if (m) {
      state.mapVisible = true;
      state.mapContentFooter = true;
      collectLocation.value = {
        longitude: "",
        latitude: "",
      };
    } else {
      state.mapVisible = false;
      state.mapContentFooter = false;
    }
  },
  {
    immediate: true,
    deep: true,
  }
);

//获取地图采集坐标信息
bus.on("misPolylinePosition", (params) => {
  if (
    params?.longitudeStart &&
    params?.latitudeStart &&
    params?.longitudeEnd &&
    params?.latitudeEnd
  ) {
    state.mapVisible = true;
    startPoint = [
      parseFloat(params.longitudeStart),
      parseFloat(params.latitudeStart),
    ];
    endPoint = [
      parseFloat(params.longitudeEnd),
      parseFloat(params.latitudeEnd),
    ];
    if (params?.color) {
      state.lineColor = params.color;
    }
  } else {
    state.mapVisible = false;
  }
});

//获取地图采集坐标信息
bus.on("getCollectLocation", (params) => {
  console.log("getCollectLocation", params);
});
</script>

<style lang="scss" scoped>
.map-content {
  width: 100%;
  height: 60vh;
  overflow: hidden;
}
</style>
