<template>
  <div class="monitoring-points-container">
    <!-- 查询条件区域 -->
    <div class="search-section">
      <el-form :model="searchForm" :inline="true" label-width="80px">
        <el-form-item label="设备名称">
          <el-input
            v-model="searchForm.deviceName"
            placeholder="请输入设备名称"
            style="width: 200px"
            clearable
          />
        </el-form-item>
        <el-form-item label="设备类型">
          <el-select
            v-model="searchForm.deviceType"
            placeholder="请选择设备类型"
            style="width: 200px"
            clearable
          >
            <el-option
              v-for="item in deviceTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="使用状态">
          <el-select
            v-model="searchForm.onlineStatus"
            placeholder="请选择使用状态"
            style="width: 200px"
            clearable
          >
            <el-option
              v-for="item in deviceStatusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-input
            v-model="searchForm.indexCode"
            placeholder="请输入设备编码搜索"
            style="width: 250px"
            clearable
          >
            <template #append>
              <el-button icon="Search" @click="handleSearch" />
            </template>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch" :loading="loading">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class="action-section">
      <!-- <el-button type="primary" icon="Plus" @click="handleAdd">新增</el-button> -->
      <el-button type="success" icon="Download" @click="handleImport">导入</el-button>
      <el-button type="warning" icon="Upload" @click="handleExport">导出</el-button>
    </div>

    <!-- 表格区域 -->
    <div class="table-section">
      <div class="table-container">
        <el-table
          ref="dataTable"
          :data="tableData"
          :header-cell-style="headerCellStyle"
          :row-class-name="tableRowClassName"
          v-loading="loading"
          style="width: 100%"
          @selection-change="handleSelectionChange"
          @row-click="handleRowClick"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column label="序号" width="60" align="center">
            <template #default="{ $index }">
              {{ (pagination.current - 1) * pagination.size + $index + 1 }}
            </template>
          </el-table-column>
          <el-table-column prop="deviceName" label="设备名称" min-width="120" show-overflow-tooltip />
          <el-table-column prop="indexCode" label="设备编码" min-width="150" show-overflow-tooltip />
          <el-table-column prop="monitorIndexName" label="监测指标" min-width="140" align="center">
          </el-table-column>
          <el-table-column prop="collectFrequency" label="采集频率" width="100" align="center">
            <template #default="{ row }">
              {{ row.collectFrequency }}分钟/次
            </template>
          </el-table-column>
          <el-table-column prop="bridgeName" label="桥梁名称" min-width="120" show-overflow-tooltip>
          </el-table-column>
          <el-table-column prop="monitorObjectName" label="监测对象" width="100" align="center">
          </el-table-column>
          <el-table-column prop="address" label="位置" min-width="150" show-overflow-tooltip />
          <el-table-column prop="onlineStatus" label="使用状态" width="100" align="center">
            <template #default="{ row }">
              <el-tag :type="row.onlineStatus === 1 ? 'success' : 'danger'" size="small">
                {{ getDeviceStatusName(row.onlineStatus) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150" fixed="right" align="center">
            <template #default="{ row }">
              <div class="operation-btns">
                <div class="operation-btn-row">
                  <!-- <span class="operation-btn-text" @click.stop="handleEdit(row)">编辑</span> -->
                  <!-- <span class="operation-divider">|</span> -->
                  <!-- <span class="operation-btn-text" @click.stop="handleDelete(row)">删除</span> -->
                </div>
                <div class="operation-btn-row">
                  <span class="operation-btn-text" @click.stop="handleView(row)">详情</span>
                  <span class="operation-divider">|</span>
                  <span class="operation-btn-text" @click.stop="handleLocation(row)">定位</span>
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页区域 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 30, 50]"
          layout="total, prev, pager, next, jumper, sizes"
          :total="pagination.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 弹窗组件 -->
    <PipelineDialog
      v-model="dialogVisible"
      :mode="dialogMode"
      :id="currentId"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  DEVICE_TYPE_OPTIONS,
  DEVICE_STATUS_OPTIONS,
  MONITOR_INDEX_MAP,
  DEVICE_STATUS_MAP,
  MONITOR_OBJECT_MAP
} from '@/constants/bridge'
import {
  getPipelineInfoPage,
  deletePipelineInfo
} from '@/api/bridge'
import PipelineDialog from './PipelineDialog.vue'

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const selectedRows = ref([])
const dialogVisible = ref(false)
const dialogMode = ref('add')
const currentId = ref(null)

// 搜索表单
const searchForm = reactive({
  deviceName: '',
  deviceType: '',
  onlineStatus: '',
  indexCode: ''
})

// 分页数据
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 下拉选项
const deviceTypeOptions = DEVICE_TYPE_OPTIONS
const deviceStatusOptions = DEVICE_STATUS_OPTIONS

// 获取监测指标名称
const getMonitorIndexName = (value) => {
  return MONITOR_INDEX_MAP[value] || value
}

// 获取设备状态名称
const getDeviceStatusName = (value) => {
  return DEVICE_STATUS_MAP[value] || value
}

// 获取监测对象名称
const getMonitorObjectName = (value) => {
  return MONITOR_OBJECT_MAP[value] || value
}

// 表头样式
const headerCellStyle = {
  background: '#F4F4F4',
  fontFamily: 'PingFangSC, PingFang SC',
  fontWeight: '500',
  fontSize: '14px',
  color: '#282828',
  height: '64px'
}

// 斑马纹和选中行样式
const tableRowClassName = ({ rowIndex }) => {
  return rowIndex % 2 === 0 ? 'even-row' : 'odd-row'
}

// 处理行点击
const handleRowClick = (row) => {
  console.log('行数据:', row)
}

// 加载列表数据
const loadData = async () => {
  try {
    loading.value = true
    
    const params = {
      deviceName: searchForm.deviceName,
      deviceType: searchForm.deviceType,
      onlineStatus: searchForm.onlineStatus ? parseInt(searchForm.onlineStatus) : undefined,
      // 其他搜索参数
      indexCode: searchForm.indexCode,
    }
    
    const response = await getPipelineInfoPage(pagination.current, pagination.size, params)
    
    if (response.code === 200) {
      const data = response.data
      tableData.value = data.records || []
      pagination.total = data.total || 0
    } else {
      ElMessage.error(response.message || '获取数据失败')
    }
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  loadData()
}

// 重置
const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  pagination.current = 1
  loadData()
}

// 新增
const handleAdd = () => {
  dialogMode.value = 'add'
  currentId.value = null
  dialogVisible.value = true
}

// 编辑
const handleEdit = (row) => {
  dialogMode.value = 'edit'
  currentId.value = row.id
  dialogVisible.value = true
}

// 查看详情
const handleView = (row) => {
  dialogMode.value = 'view'
  currentId.value = row.id
  dialogVisible.value = true
}

// 删除
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除设备"${row.deviceName}"吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const response = await deletePipelineInfo(row.id)
    if (response.code === 200) {
      ElMessage.success('删除成功')
      loadData()
    } else {
      ElMessage.error(response.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 定位
const handleLocation = (row) => {
  ElMessage.info('定位功能开发中...')
}

// 导入
const handleImport = () => {
  ElMessage.info('导入功能开发中...')
}

// 导出
const handleExport = () => {
  ElMessage.info('导出功能开发中...')
}

// 选择变化
const handleSelectionChange = (selection) => {
  selectedRows.value = selection
}

// 分页大小变化
const handleSizeChange = (size) => {
  pagination.size = size
  pagination.current = 1
  loadData()
}

// 当前页变化
const handleCurrentChange = (current) => {
  pagination.current = current
  loadData()
}

// 弹窗成功回调
const handleDialogSuccess = () => {
  loadData()
}

// 组件挂载时加载数据
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.monitoring-points-container {
  width: 100%;
  height: calc(100vh - 230px);
  display: flex;
  flex-direction: column;
  padding: 16px;
  background-color: #F0F2F5;
  gap: 16px;
  overflow: hidden;
}

/* 搜索表单区域 */
.search-section {
  background: #FFFFFF;
  padding: 16px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  margin-bottom: 0;
  flex-shrink: 0;
}

/* 操作按钮区域 */
.action-section {
  background: #FFFFFF;
  padding: 16px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  margin-bottom: 0;
  flex-shrink: 0;
}

/* 表格区域样式 */
.table-section {
  width: 100%;
  flex: 1;
  background: white;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  padding: 16px;
  min-height: 0;
  overflow: hidden;
}

/* 表格样式 */
.table-container {
  flex: 1;
  width: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 200px;
}

:deep(.el-table) {
  --el-table-border-color: #EBEEF5;
  --el-table-header-bg-color: #F5F8FA;
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

:deep(.el-table__inner-wrapper) {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

:deep(.el-table__header-wrapper) {
  flex-shrink: 0;
}

:deep(.el-table__body-wrapper) {
  flex: 1;
  overflow-y: auto !important;
}

:deep(.el-scrollbar) {
  flex: 1;
  display: flex;
  flex-direction: column;
}

:deep(.el-scrollbar__wrap) {
  flex: 1;
  overflow-x: hidden;
}

/* 表格行样式 */
:deep(.el-table .even-row) {
  background-color: #FFFFFF;
}

:deep(.el-table .odd-row) {
  background-color: #F5F8FA;
}

:deep(.el-table th) {
  background-color: #F5F8FA;
  color: #333333;
  font-weight: bold;
  height: 40px;
  padding: 0;
  border-bottom: 1px solid #EBEEF5;
}

:deep(.el-table tr) {
  height: 48px;
}

:deep(.el-table__row:hover) {
  background: #EEF5FF !important;
}

:deep(.el-table__row.selected-row) {
  background: #EEF5FF !important;
}

/* 其他表格样式 */
:deep(.el-table__empty-block) {
  min-height: 60px;
  text-align: center;
  width: 100%;
}

:deep(.el-table__empty-text) {
  line-height: 60px;
  width: 50%;
  color: #909399;
}

/* 操作按钮样式 */
.operation-btns {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.operation-btn-row {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 4px 0;
}

.operation-btn-text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #0086FF;
  cursor: pointer;
}

.operation-divider {
  margin: 0 4px;
  color: #CED3DA;
}

/* 分页样式 */
.pagination-container {
  display: flex;
  justify-content: flex-end;
  padding: 16px 0 0 0;
  border-top: 1px solid #EBEEF5;
  flex-shrink: 0;
}

:deep(.el-pagination .el-pagination__total) {
  margin-right: 16px;
}

:deep(.el-pagination .el-input__wrapper) {
  box-shadow: 0 0 0 1px #CED3DA inset;
}

:deep(.el-pagination .el-select .el-input) {
  margin: 0 8px;
}

:deep(.el-pagination button:disabled) {
  background-color: #F5F8FA;
}

:deep(.el-pagination .el-pagination__jump) {
  margin-left: 16px;
}

/* 确保操作列文字不换行 */
:deep(.cell) {
  white-space: nowrap;
}

/* 响应式适配 */
@media screen and (max-width: 1920px) {
  .monitoring-points-container {
    width: 100%;
  }
}
</style> 