<template>
  <div class="drainage-network-line">
    <!-- 搜索区域 -->
    <div class="drainage-network-search">
      <div class="search-form">
        <div class="form-item">
          <span class="label">管线类型:</span>
          <el-select v-model="formData.pipelineType" class="form-input" placeholder="请选择">
            <el-option v-for="item in PIPELINE_TYPE_OPTIONS" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <span class="label">埋设类型:</span>
          <el-select v-model="formData.buriedType" class="form-input" placeholder="请选择">
            <el-option v-for="item in BURIED_TYPE_OPTIONS" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <span class="label">管材:</span>
          <el-select v-model="formData.material" class="form-input" placeholder="请选择">
            <el-option v-for="item in MATERIAL_OPTIONS" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <span class="label">管径:</span>
          <el-select v-model="formData.pipeDiameter" class="form-input" placeholder="请选择">
            <el-option v-for="item in PIPE_DIAMETER_OPTIONS" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <span class="label">权属单位:</span>
          <el-select v-model="formData.managementUnit" class="form-input" placeholder="请选择">
            <el-option v-for="item in managementUnits" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <span class="label">管线编码:</span>
          <el-input v-model="formData.pipelineCode" class="form-input" placeholder="请输入管线编码" />
        </div>
        <div class="form-item">
          <el-button type="primary" class="search-btn" @click="handleSearch">查询</el-button>
          <el-button class="reset-btn" @click="handleReset">重置</el-button>
        </div>
      </div>
    </div> 
    
    <!-- 按钮和总长度区域 -->
    <div class="table-header">
      <div class="button-group">
        <el-button type="primary" class="operation-btn" @click="handleAdd">新增</el-button>
        <el-button type="primary" class="operation-btn" @click="handleImport">导入</el-button>
        <el-button type="primary" class="operation-btn" @click="handleExport">导出</el-button>
      </div>
    </div>
    
    <!-- 表格区域 -->
    <div class="table-container">
      <el-table :data="tableData" style="width: 100%" :header-cell-style="headerCellStyle"
        :row-class-name="tableRowClassName" @row-click="handleRowClick" height="calc(100vh - 380px)"
        empty-text="暂无数据">
        <el-table-column label="序号" min-width="60">
          <template #default="{ $index }">
            {{ (currentPage - 1) * pageSize + $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="pipelineCode" label="管线编码" min-width="100" />
        <el-table-column prop="pipelineTypeName" label="管线类型" min-width="100" />
        <el-table-column prop="materialName" label="管材" min-width="80" />
        <el-table-column prop="designPressure" label="压力 (Mpa)" min-width="100" />
        <el-table-column prop="pipeDiameter" label="管径 (mm)" min-width="100" />
        <el-table-column prop="pipeLength" label="长度 (KM)" min-width="100" />
        <el-table-column prop="roadName" label="所在位置" min-width="150" />
        <el-table-column prop="startPointDepth" label="起点埋深 (m)" min-width="120" />
        <el-table-column prop="endPointDepth" label="终点埋深 (m)" min-width="120" />
        <el-table-column prop="constructionTime" label="建设时间" min-width="120" />
        <el-table-column prop="usageStatusName" label="使用状态" min-width="100" />
        <el-table-column label="操作" fixed="right" min-width="200">
          <template #default="{ row }">
            <div class="operation-btns">
              <el-button type="primary" link @click.stop="handleEdit(row)">编辑</el-button>
              <el-button type="primary" link @click.stop="handleDetail(row)">详情</el-button>
              <el-button type="primary" link @click.stop="handleDelete(row)">删除</el-button>
              <el-button type="primary" link @click.stop="handleLocation(row)">定位</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    
    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 管线对话框 -->
    <DrainageNetworkDialog
      v-model:visible="dialogVisible"
      :mode="dialogMode"
      :data="dialogData"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { ElTable, ElTableColumn, ElButton, ElPagination, ElMessageBox, ElMessage } from 'element-plus';
import { getPipelinePage, getEnterpriseList, getPipelineDetail, deletePipeline } from '@/api/drainage';
import { misPosition } from '@/hooks/gishooks';
import {
  PIPELINE_TYPE_OPTIONS,
  BURIED_TYPE_OPTIONS,
  MATERIAL_OPTIONS,
  PIPE_DIAMETER_OPTIONS
} from '@/constants/drainage';
import DrainageNetworkDialog from './DrainageNetworkDialog.vue';

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const tableData = ref([]);
// 表单数据
const formData = ref({
  pipelineType: '',
  buriedType: '',
  material: '',
  pipeDiameter: '',
  managementUnit: '',
  pipelineCode: ''
});

// 权属单位列表
const managementUnits = ref([{ label: '全部', value: '' }]);

// 对话框相关
const dialogVisible = ref(false);
const dialogMode = ref('add'); // 'add' | 'edit' | 'view'
const dialogData = ref({});

// 获取权属单位
const fetchEnterpriseList = async () => {
  try {
    const res = await getEnterpriseList();
    managementUnits.value = [
      { label: '全部', value: '' },
      ...res.data.map(item => ({
        label: item.enterpriseName,
        value: item.id
      }))
    ];
  } catch (error) {
    console.error('获取权属单位失败:', error);
  }
};

// 表格样式
const headerCellStyle = {
  background: '#F5F7FA',
  color: '#606266',
  height: '40px',
  fontWeight: 'bold'
};

// 表格行样式
const tableRowClassName = ({ rowIndex }) => {
  return rowIndex % 2 === 0 ? 'even-row' : 'odd-row';
};

// 处理查询
const handleSearch = () => {
  fetchPipelineData();
};

// 处理重置
const handleReset = () => {
  formData.value = {
    pipelineType: '',
    buriedType: '',
    material: '',
    pipeDiameter: '',
    managementUnit: '',
    pipelineCode: ''
  };
  fetchPipelineData();
};

// 获取管网分页数据
const fetchPipelineData = async () => {
  try {
    const params = {
      pipelineType: formData.value.pipelineType,
      buriedType: formData.value.buriedType,
      material: formData.value.material,
      pipeDiameter: formData.value.pipeDiameter,
      managementUnit: formData.value.managementUnit,
      pipelineCode: formData.value.pipelineCode,
      pageNum: currentPage.value,
      pageSize: pageSize.value
    };
    
    const res = await getPipelinePage(params);
    
    if (res && res.code === 200) {
      tableData.value = res.data.records;
      total.value = res.data.total;
    }
  } catch (error) {
    console.error('获取管线数据失败:', error);
    ElMessage.error('获取管线数据失败');
    tableData.value = [];
    total.value = 0;
  }
};

// 处理分页大小变化
const handleSizeChange = (size) => {
  pageSize.value = size;
  fetchPipelineData();
};

// 处理页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page;
  fetchPipelineData();
};

// 处理行点击
const handleRowClick = (row) => {
  console.log('行点击', row);
};

// 处理新增
const handleAdd = () => {
  dialogMode.value = 'add';
  dialogData.value = {};
  dialogVisible.value = true;
};

// 处理编辑
const handleEdit = async (row) => {
  try {
    // 获取详细数据
    const res = await getPipelineDetail(row.id);
    if (res && res.code === 200) {
      dialogMode.value = 'edit';
      dialogData.value = res.data;
      dialogVisible.value = true;
    } else {
      ElMessage.error('获取管线详情失败');
    }
  } catch (error) {
    console.error('获取管线详情失败:', error);
    ElMessage.error('获取管线详情失败');
  }
};

// 处理详情
const handleDetail = async (row) => {
  try {
    // 获取详细数据
    const res = await getPipelineDetail(row.id);
    if (res && res.code === 200) {
      dialogMode.value = 'view';
      dialogData.value = res.data;
      dialogVisible.value = true;
    } else {
      ElMessage.error('获取管线详情失败');
    }
  } catch (error) {
    console.error('获取管线详情失败:', error);
    ElMessage.error('获取管线详情失败');
  }
};

// 处理删除
const handleDelete = (row) => {
  ElMessageBox.confirm('确定要删除该管线吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await deletePipeline(row.id);
      if (res && res.code === 200) {
        ElMessage.success('删除成功');
        fetchPipelineData();
      } else {
        ElMessage.error(res?.msg || '删除失败');
      }
    } catch (error) {
      console.error('删除管线失败:', error);
      ElMessage.error('删除管线失败');
    }
  }).catch(() => {
    // 取消删除
  });
};

// 处理定位
const handleLocation = (row) => {
  if (
    row.startPointLatitude &&
    row.startPointLatitude != '' &&
    row.startPointLongitude &&
    row.startPointLongitude != ''
  ) {
    misPosition.value = {
      longitude: row.startPointLongitude, //经度
      latitude: row.startPointLatitude //纬度
    }
  } else {
    ElMessage.warning('没有经纬度，无法定位！')
  }
};

// 处理对话框成功提交
const handleDialogSuccess = () => {
  fetchPipelineData();
};

// 处理导入
const handleImport = () => {
  console.log('导入');
};

// 处理导出
const handleExport = () => {
  console.log('导出');
};

// 在组件挂载后获取数据
onMounted(async () => {
  try {
    await fetchEnterpriseList();
    await fetchPipelineData();
  } catch (error) {
    console.error('初始化数据失败:', error);
    ElMessage.error('初始化数据失败');
  }
});
</script>

<style scoped>
.drainage-network-line {
  width: 97%;
  height: 85%;
  display: flex;
  flex-direction: column;
}

/* 搜索区域样式 */
.drainage-network-search {
  width: 100%;
  margin-bottom: 16px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.form-item {
  display: flex;
  align-items: center;
  margin-right: 16px;
  margin-bottom: 16px;
}

.label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #282828;
  margin-right: 8px;
  white-space: nowrap;
}

.form-input {
  width: 180px;
  height: 32px;
}

:deep(.el-input__wrapper) {
  background: #FFFFFF;
  border-radius: 2px;
  border: 1px solid #CED3DA;
  height: 32px;
  box-shadow: none !important;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #0066FF inset !important;
}

.search-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  margin-right: 8px;
}

.reset-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  border: 1px solid #DCDFE6;
}

/* 表格头部样式 */
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.button-group {
  display: flex;
  gap: 8px;
}

.operation-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
}

.total-info {
  font-size: 14px;
  color: #606266;
}

/* 表格样式 */
.table-container {
  flex: 1;
  overflow: hidden;
}

:deep(.el-table) {
  --el-table-border-color: #EBEEF5;
  --el-table-header-bg-color: #F5F7FA;
}

:deep(.el-table .even-row) {
  background-color: #FAFAFA;
}

:deep(.el-table .odd-row) {
  background-color: #FFFFFF;
}

.operation-btns {
  display: flex;
  gap: 8px;
}

/* 分页样式 */
.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}
</style>