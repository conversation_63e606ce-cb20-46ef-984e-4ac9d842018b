<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="800px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="drainage-grate-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      :disabled="mode === 'view'"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="雨水篦子名称" prop="grateName">
            <el-input v-model="formData.grateName" placeholder="请输入雨水篦子名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="雨水篦子编码" prop="grateCode">
            <el-input v-model="formData.grateCode" placeholder="请输入雨水篦子编码" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="材质" prop="materialType">
            <el-select v-model="formData.materialType" placeholder="请选择" class="w-full">
              <el-option v-for="item in materialOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="形状" prop="shapeType">
            <el-select v-model="formData.shapeType" placeholder="请选择" class="w-full">
              <el-option v-for="item in shapeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="尺寸 (mm)" prop="grateSize">
            <el-input v-model="formData.grateSize" placeholder="请输入尺寸" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="权属单位" prop="managementUnit">
            <el-select v-model="formData.managementUnit" placeholder="请选择" class="w-full">
              <el-option v-for="unit in managementUnits" :key="unit.id" :label="unit.enterpriseName" :value="unit.enterpriseName" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="位置" prop="address">
            <div class="flex items-center">
              <el-cascader
                v-model="formData.county"
                :options="areaOptions"
                :props="{
                  value: 'code',
                  label: 'name',
                  children: 'children'
                }"
                placeholder="请选择所属区域"
                class="w-full"
                @change="handleAreaChange"
              />
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="经纬度">
            <div class="flex items-center">
              <el-input v-model="formData.longitude" placeholder="经度" class="mr-2 w-32" />
              <el-input v-model="formData.latitude" placeholder="纬度" class="w-32" />
              <el-button type="primary" class="ml-2" icon="Location" circle @click="openMapPicker"></el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="备注" prop="remarks">
            <el-input
              type="textarea"
              v-model="formData.remarks"
              :rows="3"
              placeholder="请输入备注信息"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="mode !== 'view'">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import { getEnterpriseList, saveGrate, updateGrate } from '@/api/drainage';
import { GRATE_MATERIAL_OPTIONS, GRATE_SHAPE_OPTIONS } from '@/constants/drainage';
import { AREA_OPTIONS } from '@/constants/gas';
import { collectShow } from "@/hooks/gishooks";
import bus from '@/utils/mitt';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add', // 'add', 'edit', 'view'
    validator: (value) => ['add', 'edit', 'view'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'success']);

// 表单引用
const formRef = ref(null);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 对话框标题
const dialogTitle = computed(() => {
  const titles = {
    add: '新增雨水篦子',
    edit: '编辑雨水篦子',
    view: '雨水篦子详情'
  };
  return titles[props.mode] || '雨水篦子信息';
});

// 材质选项
const materialOptions = ref(GRATE_MATERIAL_OPTIONS);

// 形状选项
const shapeOptions = ref(GRATE_SHAPE_OPTIONS);

// 权属单位列表
const managementUnits = ref([]);

// 行政区划选项
const areaOptions = ref(AREA_OPTIONS);

// 表单数据
const formData = reactive({
  id: '',
  grateName: '',
  grateCode: '',
  materialType: '',
  materialTypeName: '',
  shapeType: '',
  shapeTypeName: '',
  grateSize: '',
  managementUnit: '',
  managementUnitName: '',
  city: '',
  county: '371728',
  countyName: '东明县',
  town: '',
  townName: '',
  address: '',
  longitude: '',
  latitude: '',
  remarks: ''
});

// 表单验证规则
const formRules = {
  grateName: [{ required: true, message: '请输入雨水篦子名称', trigger: 'blur' }],
  grateCode: [{ required: true, message: '请输入雨水篦子编码', trigger: 'blur' }],
  materialType: [{ required: true, message: '请选择材质', trigger: 'change' }],
  shapeType: [{ required: true, message: '请选择形状', trigger: 'change' }],
  managementUnit: [{ required: true, message: '请选择权属单位', trigger: 'change' }]
};

// 重置表单
const resetForm = () => {
  // 重置表单数据
  Object.keys(formData).forEach(key => {
    formData[key] = '';
  });
};

// 监听props.data变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    // 复制数据到表单
    Object.keys(formData).forEach(key => {
      if (newVal[key] !== undefined) {
        formData[key] = newVal[key];
      }
    });
    
    // 处理枚举值
    if (newVal.materialType) {
      const selectedMaterial = materialOptions.value.find(item => item.value === newVal.materialType);
      if (selectedMaterial) {
        formData.materialType = selectedMaterial.value;
        formData.materialTypeName = selectedMaterial.label;
      }
    }
    
    if (newVal.shapeType) {
      const selectedShape = shapeOptions.value.find(item => item.value === newVal.shapeType);
      if (selectedShape) {
        formData.shapeType = selectedShape.value;
        formData.shapeTypeName = selectedShape.label;
      }
    }
    
    if (newVal.managementUnit) {
      formData.managementUnit = newVal.managementUnit;
      formData.managementUnitName = newVal.managementUnitName || newVal.managementUnit;
    }
  } else if (props.mode === 'add') {
    // 新增模式清空表单
    resetForm();
  }
}, { immediate: true, deep: true });

// 监听下拉框值变化，自动更新对应的名称
watch(() => formData.materialType, (val) => {
  if (val) {
    const selected = materialOptions.value.find(item => item.value === val);
    if (selected) {
      formData.materialTypeName = selected.label;
    }
  }
});

watch(() => formData.shapeType, (val) => {
  if (val) {
    const selected = shapeOptions.value.find(item => item.value === val);
    if (selected) {
      formData.shapeTypeName = selected.label;
    }
  }
});

watch(() => formData.managementUnit, (val) => {
  if (val) {
    formData.managementUnitName = val;
  }
});

watch(() => formData.town, (val) => {
  if (val) {
    const selected = townOptions.value.find(item => item.value === val);
    if (selected) {
      formData.townName = selected.label;
    }
  }
});

// 获取权属单位列表
const fetchManagementUnits = async () => {
  try {
    const res = await getEnterpriseList();
    if (res && res.data) {
      managementUnits.value = res.data || [];
    }
  } catch (error) {
    console.error('获取权属单位列表失败', error);
  }
};

// 地图选点事件处理函数
const handleCollectLocation = (params) => {
  nextTick(() => {
    formData.longitude = params.longitude;
    formData.latitude = params.latitude;
  });
};

// 打开地图选点
const openMapPicker = () => {
  collectShow.value = true; // 激活采集点位窗口
  // 先移除可能存在的旧监听器
  bus.off("getCollectLocation", handleCollectLocation);
  // 添加新的监听器
  bus.on("getCollectLocation", handleCollectLocation);
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  // 重置表单
  if (formRef.value) {
    formRef.value.resetFields();
  }
  resetForm();
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;
  
  try {
    await formRef.value.validate();
    
    // 准备提交数据
    const submitData = { ...formData };
    
    // 设置枚举值对应的名称
    submitData.materialTypeName = materialOptions.value.find(item => item.value === formData.materialType)?.label || '';
    submitData.shapeTypeName = shapeOptions.value.find(item => item.value === formData.shapeType)?.label || '';
    
    // 权属单位使用名称作为值，直接赋值
    submitData.managementUnitName = formData.managementUnit;
    
    // 提交数据
    let res;
    if (props.mode === 'add') {
      res = await saveGrate(submitData);
    } else if (props.mode === 'edit') {
      res = await updateGrate(submitData);
    }
    
    if (res && res.code === 200) {
      ElMessage.success(props.mode === 'add' ? '新增成功' : '更新成功');
      emit('success');
      handleClose();
    } else {
      ElMessage.error(res?.msg || (props.mode === 'add' ? '新增失败' : '更新失败'));
    }
  } catch (error) {
    console.error('表单验证失败', error);
  }
};

// 处理区域选择变化
const handleAreaChange = (value) => {
  if (value && value.length > 0) {
    formData.county = value[value.length - 1];
    // 更新区域名称
    const selectedArea = findAreaByCode(areaOptions.value, formData.county);
    if (selectedArea) {
      formData.countyName = selectedArea.name;
    }
  }
};

// 根据区域代码查找区域信息
const findAreaByCode = (areas, code) => {
  for (const area of areas) {
    if (area.code === code) {
      return area;
    }
    if (area.children) {
      const found = findAreaByCode(area.children, code);
      if (found) return found;
    }
  }
  return null;
};

// 组件挂载时获取数据
onMounted(() => {
  fetchManagementUnits();
  
  // 解绑之前可能存在的事件监听
  bus.off("getCollectLocation", handleCollectLocation);
  // 添加事件监听
  bus.on("getCollectLocation", handleCollectLocation);
  
  // 组件卸载时清理事件监听
  return () => {
    bus.off("getCollectLocation", handleCollectLocation);
  };
});
</script>

<style scoped>
.drainage-grate-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-form-item__label::before) {
  content: '*';
  color: #ff4d4f;
  margin-right: 4px;
}

:deep(.el-form-item:not([required]) .el-form-item__label::before) {
  content: '';
  margin-right: 0;
}

:deep(.el-input__wrapper),
:deep(.el-select__input) {
  border-radius: 2px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  background-color: #0277FD;
  border-color: #0277FD;
}

:deep(.el-button--primary:hover) {
  background-color: #1890ff;
  border-color: #1890ff;
}

.w-full {
  width: 100%;
}

.flex {
  display: flex;
}

.flex-1 {
  flex: 1;
}

.items-center {
  align-items: center;
}

.ml-2 {
  margin-left: 8px;
}

.mr-2 {
  margin-right: 8px;
}

.w-32 {
  width: 140px;
}
</style> 