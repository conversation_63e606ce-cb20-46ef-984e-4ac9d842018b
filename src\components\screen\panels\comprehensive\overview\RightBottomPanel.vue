<template>
  <PanelBox title="事件处置监管">
    <template #extra>
      <div class="com-select">
        <CommonSelect v-model="selectedType" :options="typeOptions" @change="handleTypeChange" />
      </div>
    </template>
    <div class="panel-content">
      <!-- 顶部统计数据展示 -->
      <div class="event-stats">
        <div class="stat-item">
          <div class="stat-indicator">
            <div class="indicator-outer blue-outer"></div>
            <div class="indicator-inner blue-inner"></div>
          </div>
          <div class="stat-info">
            <div class="stat-label">事件总数</div>
            <div class="stat-value-wrapper">
              <div class="stat-value blue-gradient">{{ statsData.totalEvents }}</div>
              <div class="stat-unit">件</div>
            </div>
          </div>
        </div>
        <div class="stat-item">
          <div class="stat-indicator">
            <div class="indicator-outer cyan-outer"></div>
            <div class="indicator-inner cyan-inner"></div>
          </div>
          <div class="stat-info">
            <div class="stat-label">今日事件</div>
            <div class="stat-value-wrapper">
              <div class="stat-value cyan-gradient">{{ statsData.todayEvents }}</div>
              <div class="stat-unit">件</div>
            </div>
          </div>
        </div>
        <div class="stat-item">
          <div class="stat-indicator">
            <div class="indicator-outer cyan-outer"></div>
            <div class="indicator-inner cyan-inner"></div>
          </div>
          <div class="stat-info">
            <div class="stat-label">本月事件</div>
            <div class="stat-value-wrapper">
              <div class="stat-value cyan-gradient">{{ statsData.monthEvents }}</div>
              <div class="stat-unit">件</div>
            </div>
          </div>
        </div>
        <div class="stat-item">
          <div class="stat-indicator">
            <div class="indicator-outer cyan-outer"></div>
            <div class="indicator-inner cyan-inner"></div>
          </div>
          <div class="stat-info">
            <div class="stat-label">已处置</div>
            <div class="stat-value-wrapper">
              <div class="stat-value cyan-gradient">{{ statsData.handledEvents }}</div>
              <div class="stat-unit">件</div>
            </div>
          </div>
        </div>
        <div class="stat-item">
          <div class="stat-indicator">
            <div class="indicator-outer green-outer"></div>
            <div class="indicator-inner green-inner"></div>
          </div>
          <div class="stat-info">
            <div class="stat-label">处置完成率</div>
            <div class="stat-value-wrapper">
              <div class="stat-value green-gradient">{{ statsData.handleRate }}</div>
              <div class="stat-unit"></div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 折线图区域 -->
      <div class="chart-container" ref="chartRef"></div>
    </div>
  </PanelBox>
</template>

<script setup>
import { ref, reactive, onMounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'
import PanelBox from '@/components/screen/PanelBox.vue'
import CommonSelect from '@/components/screen/CommonSelect.vue'

// 类型选择
const selectedType = ref('all')
const typeOptions = [
  { label: '全部', value: 'all' },
  { label: '燃气', value: 'gas' },
  { label: '排水', value: 'water' },
  { label: '供热', value: 'heating' },
  { label: '桥梁', value: 'bridge' }
]

// 统计数据
const statsData = reactive({
  totalEvents: 100,
  todayEvents: 12,
  monthEvents: 8,
  handledEvents: 91,
  handleRate: '91%'
})

// 图表数据
const chartData = reactive({
  all: {
    xAxis: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'],
    values: [72, 75, 85, 78, 88, 95, 98, 85, 87, 92, 85, 96]
  },
  gas: {
    xAxis: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'],
    values: [70, 73, 80, 75, 82, 90, 95, 80, 83, 88, 80, 92]
  },
  water: {
    xAxis: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'],
    values: [68, 70, 75, 72, 78, 85, 90, 75, 80, 85, 75, 88]
  },
  heating: {
    xAxis: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'],
    values: [65, 68, 72, 70, 75, 80, 85, 70, 75, 80, 70, 85]
  },
  bridge: {
    xAxis: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'],
    values: [60, 65, 70, 68, 72, 75, 82, 65, 70, 75, 65, 80]
  }
})

// 图表实例
const chartRef = ref(null)
let chartInstance = null

// 处理类型变化
const handleTypeChange = (value) => {
  // 根据选择的类型更新数据
  updateStatsData(value)
  // 更新图表
  updateChart()
}

// 根据类型更新统计数据
const updateStatsData = (type) => {
  switch (type) {
    case 'all':
      statsData.totalEvents = 100
      statsData.todayEvents = 12
      statsData.monthEvents = 8
      statsData.handledEvents = 91
      statsData.handleRate = '91%'
      break
    case 'gas':
      statsData.totalEvents = 45
      statsData.todayEvents = 5
      statsData.monthEvents = 3
      statsData.handledEvents = 40
      statsData.handleRate = '89%'
      break
    case 'water':
      statsData.totalEvents = 30
      statsData.todayEvents = 4
      statsData.monthEvents = 2
      statsData.handledEvents = 28
      statsData.handleRate = '93%'
      break
    case 'heating':
      statsData.totalEvents = 20
      statsData.todayEvents = 2
      statsData.monthEvents = 1
      statsData.handledEvents = 18
      statsData.handleRate = '90%'
      break
    case 'bridge':
      statsData.totalEvents = 5
      statsData.todayEvents = 1
      statsData.monthEvents = 0
      statsData.handledEvents = 5
      statsData.handleRate = '100%'
      break
  }
}

// 更新图表
const updateChart = () => {
  if (!chartInstance) return
  
  const data = chartData[selectedType.value] || chartData.all
  const option = createChartOption(data)
  chartInstance.setOption(option)
}

// 创建图表配置
const createChartOption = (data) => {
  return {
    backgroundColor: 'transparent',
    grid: {
      top: '15%',
      left: '3%',
      right: '3%',
      bottom: '15%',
      containLabel: true
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0, 19, 40, 0.8)',
      borderColor: 'rgba(0, 109, 232, 0.2)',
      textStyle: {
        color: '#fff',
        fontSize: 12,
        fontFamily: 'PingFangSC, PingFang SC'
      },
      formatter: function(params) {
        const param = params[0]
        return `${param.name}<br/>${param.marker}事件数量：${param.value}个`
      }
    },
    xAxis: {
      type: 'category',
      data: data.xAxis,
      axisLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.3)',
          width: 1
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        fontFamily: 'PingFangSC, PingFang SC',
        fontSize: 12,
        color: '#FFFFFF',
        margin: 12
      }
    },
    yAxis: {
      type: 'value',
      name: '单位（个）',
      nameTextStyle: {
        color: "rgba(255, 255, 255, 0.6)",
        fontSize: 12,
        fontFamily: 'PingFangSC, PingFang SC',
        padding: [0, 30, 0, 0]
      },
      min: 60,
      max: 100,
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)',
          type: 'dashed'
        }
      },
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        fontFamily: 'PingFangSC, PingFang SC',
        fontSize: 12,
        color: '#FFFFFF',
        margin: 12
      }
    },
    series: [
      {
        type: 'line',
        data: data.values,
        smooth: true,
        symbol: 'circle',
        symbolSize: 10,
        itemStyle: {
          color: 'rgba(22, 47, 146, 0.98)',
          borderColor: '#18C4D5',
          borderWidth: 3
        },
        lineStyle: {
          color: '#18C4D5',
          width: 2,
          type: 'dashed'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(24, 196, 213, 0.3)' },
              { offset: 1, color: 'rgba(24, 196, 213, 0.05)' }
            ]
          }
        }
      }
    ]
  }
}

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return
  
  chartInstance = echarts.init(chartRef.value)
  const data = chartData[selectedType.value] || chartData.all
  const option = createChartOption(data)
  chartInstance.setOption(option)
  
  // 响应式调整图表大小
  window.addEventListener('resize', () => {
    chartInstance && chartInstance.resize()
  })
}

// 监听类型变化
watch(selectedType, (newValue) => {
  handleTypeChange(newValue)
})

// 初始化
onMounted(async () => {
  await nextTick()
  // 初始化图表
  initChart()
  // 获取初始数据
  updateStatsData(selectedType.value)
})
</script>

<style scoped>
.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.com-select {
  margin-right: 20px;
}

/* 统计数据区域 */
.event-stats {
  display: flex;
  justify-content: space-between;
  padding: 0 10px;
}

.stat-item {
  display: flex;
  align-items: flex-start;
}

.stat-indicator {
  position: relative;
  width: 9px;
  height: 9px;
  margin-right: 6px;
  margin-top: 2px;
}

.indicator-outer {
  width: 9px;
  height: 9px;
  border-radius: 50%;
  position: absolute;
  top: 0;
  left: 0;
}

.indicator-inner {
  width: 5px;
  height: 5px;
  border-radius: 50%;
  position: absolute;
  top: 2px;
  left: 2px;
}

.blue-outer {
  background: rgba(5, 90, 219, 0.4);
}

.blue-inner {
  background: #055ADB;
}

.cyan-outer {
  background: rgba(35, 202, 255, 0.4);
}

.cyan-inner {
  background: #23CAFF;
}

.green-outer {
  background: rgba(63, 216, 124, 0.4);
}

.green-inner {
  background: #3FD87C;
}

.stat-info {
  display: flex;
  flex-direction: column;
}

.stat-label {
  font-family: PingFangSC, 'PingFang SC';
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
  margin-bottom: 2px;
}

.stat-value-wrapper {
  display: flex;
  align-items: baseline;
}

.stat-value {
  font-family: 'D-DIN', 'D-DIN';
  font-weight: bold;
  font-size: 24px;
  line-height: 26px;
  text-align: left;
  font-style: normal;
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
}

.blue-gradient {
  background: linear-gradient(90deg, #FFFFFF 0%, #055ADB 100%);
  -webkit-background-clip: text;
}

.cyan-gradient {
  background: linear-gradient(90deg, #E2FBFF 0%, #23CAFF 100%);
  -webkit-background-clip: text;
}

.green-gradient {
  background: linear-gradient(90deg, #FFFFFF 0%, #36F281 100%);
  -webkit-background-clip: text;
}

.stat-unit {
  font-family: PingFangSC, 'PingFang SC';
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
  margin-left: 4px;
}

/* 图表容器 */
.chart-container {
  flex: 1;
  width: 100%;
  min-height: 220px;
}

/* 响应式适配 */
@media (min-height: 940px) and (max-height: 1050px) {
  .panel-content {
    padding: 10px;
    gap: 10px;
  }
  
  .event-stats {
    padding: 0 5px;
  }
  
  .stat-indicator {
    width: 7px;
    height: 7px;
  }
  
  .indicator-outer {
    width: 7px;
    height: 7px;
  }
  
  .indicator-inner {
    width: 3px;
    height: 3px;
    top: 2px;
    left: 2px;
  }
  
  .stat-label {
    font-size: 11px;
  }
  
  .stat-value {
    font-size: 20px;
    line-height: 22px;
  }
  
  .stat-unit {
    font-size: 10px;
  }
}

@media (min-height: 910px) and (max-height: 940px) {
  .panel-content {
    padding: 8px;
    gap: 8px;
  }
  
  .event-stats {
    padding: 0 3px;
  }
  
  .stat-indicator {
    width: 6px;
    height: 6px;
    margin-right: 4px;
    margin-top: 2px;
  }
  
  .indicator-outer {
    width: 6px;
    height: 6px;
  }
  
  .indicator-inner {
    width: 3px;
    height: 3px;
    top: 1.5px;
    left: 1.5px;
  }
  
  .stat-label {
    font-size: 10px;
    margin-bottom: 1px;
  }
  
  .stat-value {
    font-size: 18px;
    line-height: 20px;
  }
  
  .stat-unit {
    font-size: 9px;
    margin-left: 2px;
  }
  
  .chart-container {
    min-height: 180px;
  }
}
</style> 