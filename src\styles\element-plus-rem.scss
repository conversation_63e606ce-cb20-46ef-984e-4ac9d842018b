@use './mixins/rem' as *;

/* Element Plus组件rem适配 */

/* 按钮组件 */
.el-button {
  padding: rem(8px) rem(16px);
  font-size: rem(14px);
  border-radius: rem(4px);
  
  &--small {
    padding: rem(6px) rem(12px);
    font-size: rem(12px);
  }
  
  &--large {
    padding: rem(12px) rem(20px);
    font-size: rem(16px);
  }
}

/* 输入框组件 */
.el-input {
  &__inner {
    height: rem(40px);
    padding: 0 rem(12px);
    font-size: rem(14px);
  }
  
  &--small {
    .el-input__inner {
      height: rem(32px);
      font-size: rem(12px);
    }
  }
  
  &--large {
    .el-input__inner {
      height: rem(48px);
      font-size: rem(16px);
    }
  }
}

/* 表格组件 */
.el-table {
  font-size: rem(14px);
  
  th, td {
    padding: rem(12px) rem(8px);
  }
}

/* 卡片组件 */
.el-card {
  border-radius: rem(4px);
  
  &__header {
    padding: rem(16px) rem(20px);
  }
  
  &__body {
    padding: rem(20px);
  }
}

/* 对话框组件 */
.el-dialog {
  border-radius: rem(4px);
  
  &__header {
    padding: rem(16px) rem(20px);
  }
  
  &__body {
    padding: rem(20px);
  }
  
  &__footer {
    padding: rem(12px) rem(20px) rem(20px);
  }
}

/* 菜单组件 */
.el-menu {
  &-item {
    height: rem(56px);
    line-height: rem(56px);
    font-size: rem(14px);
  }
  
  &--horizontal {
    > .el-menu-item {
      height: rem(60px);
      line-height: rem(60px);
    }
  }
}

/* 表单组件 */
.el-form {
  &-item {
    margin-bottom: rem(20px);
    
    &__label {
      font-size: rem(14px);
      line-height: rem(40px);
    }
    
    &__error {
      font-size: rem(12px);
    }
  }
}

/* 分页组件 */
.el-pagination {
  button {
    min-width: rem(32px);
    height: rem(32px);
  }
  
  .el-pagination__editor {
    height: rem(32px);
    
    .el-input__inner {
      height: rem(32px);
    }
  }
}

/* 标签组件 */
.el-tag {
  padding: 0 rem(8px);
  height: rem(24px);
  border-radius: rem(4px);
  font-size: rem(12px);
  
  &--small {
    height: rem(20px);
    padding: 0 rem(6px);
  }
  
  &--large {
    height: rem(32px);
    padding: 0 rem(12px);
    font-size: rem(14px);
  }
}

/* 消息提示组件 */
.el-message {
  min-width: rem(300px);
  padding: rem(12px) rem(16px);
  border-radius: rem(4px);
  font-size: rem(14px);
} 