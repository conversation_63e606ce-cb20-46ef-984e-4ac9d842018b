<template>
  <div class="home">
    <Header />
    <!-- 系统管理和用户信息栏 -->
    <div class="system-user-bar">
      <div class="left" @click="goToSystemManagement">
        <img class="icon" src="@/assets/images/screen/system-icon.png" />
        <span class="text">运营系统管理</span>
      </div>
      <div class="right">
        <img class="avatar" src="@/assets/images/screen/avatar.png" />
        <span class="username">{{ userName }}</span>
        <div class="dropdown" @click="toggleDropdown" ref="dropdownRef">
          <img class="arrow" src="@/assets/images/screen/arrow-down.png" />
          <!-- 下拉菜单 -->
          <div class="dropdown-menu" v-show="showDropdown">
            <div class="menu-item" @click="showPasswordDialog">
              <span>密码修改</span>
            </div>
            <div class="menu-item" @click="downloadApp">
              <span>下载APP</span>
            </div>
            <div class="menu-item" @click="handleLogout">
              <span>退出登录</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 密码修改弹窗 -->
    <el-dialog
      v-model="showDialog"
      title="修改密码"
      width="400px"
    >
      <el-form
        ref="passwordFormRef"
        :model="passwordForm"
        :rules="passwordRules"
        label-width="100px"
      >
        <el-form-item label="新密码" prop="newPassword">
          <el-input
            v-model="passwordForm.newPassword"
            type="password"
            show-password
          />
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input
            v-model="passwordForm.confirmPassword"
            type="password"
            show-password
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showDialog = false">取消</el-button>
          <el-button type="primary" @click="handleUpdatePassword">
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>

    <div class="content">
      <!-- 驾驶舱 -->
      <div class="nav-item center-item" @click="goToScreen" :class="{ active: isActive }">
        <div class="title-bg jiashicang">
          <span>驾驶舱</span>
        </div>
        <div class="platform-base jiashicang"></div>
      </div>
      
      <!-- 燃气专项 -->
      <div class="nav-item left-top" @click="handleClick('ranqi')">
        <div class="title-bg other">
          <span>燃气专项</span>
        </div>
        <div class="platform-base ranqi"></div>
      </div>
      
      <!-- 排水专项 -->
      <div class="nav-item right-top" @click="handleClick('paishui')">
        <div class="title-bg other">
          <span>排水专项</span>
        </div>
        <div class="platform-base paishui"></div>
      </div>
      
      <!-- 供热专项 -->
      <div class="nav-item left-bottom" @click="handleClick('gongre')">
        <div class="title-bg other">
          <span>供热专项</span>
        </div>
        <div class="platform-base gongre"></div>
      </div>
      
      <!-- 桥梁专项 -->
      <div class="nav-item middle-bottom" @click="handleClick('qiaoliang')">
        <div class="title-bg other">
          <span>桥梁专项</span>
        </div>
        <div class="platform-base qiaoliang"></div>
      </div>
      
      <!-- 综合专项 -->
      <div class="nav-item right-bottom" @click="handleClick('zonghe')">
        <div class="title-bg other">
          <span>综合专项</span>
        </div>
        <div class="platform-base zonghe"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'
import Header from '@/components/common/Header.vue'

const router = useRouter()
const userStore = useUserStore()
const isActive = ref(false)
const showDropdown = ref(false)
const dropdownRef = ref(null)
const showDialog = ref(false)

const passwordForm = ref({
  newPassword: '',
  confirmPassword: ''
})

const passwordRules = {
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能小于6位', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.value.newPassword) {
          callback(new Error('两次输入密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 计算用户名称
const userName = computed(() => {
  return userStore.userInfo?.nickName || '系统管理员'
})

// 获取用户信息
onMounted(async () => {
  await userStore.getUserInfo()
  document.addEventListener('click', handleClickOutside)
})

onBeforeUnmount(() => {
  document.removeEventListener('click', handleClickOutside)
})

const handleClickOutside = (event) => {
  if (dropdownRef.value && !dropdownRef.value.contains(event.target)) {
    showDropdown.value = false
  }
}

const toggleDropdown = () => {
  showDropdown.value = !showDropdown.value
}

const showPasswordDialog = () => {
  showDialog.value = true
  showDropdown.value = false
}

const handleUpdatePassword = async () => {
  if (!userStore.userInfo?.userId) {
    ElMessage.error('用户信息不完整')
    return
  }
  
  const success = await userStore.updatePassword(
    userStore.userInfo.userId,
    passwordForm.value.newPassword
  )
  
  if (success) {
    ElMessage.success('密码修改成功')
    showDialog.value = false
    passwordForm.value = {
      newPassword: '',
      confirmPassword: ''
    }
  }
}

const downloadApp = () => {
  // TODO: 实现APP下载功能
  ElMessage.info('APP下载功能开发中')
  showDropdown.value = false
}

const handleLogout = async () => {
  await userStore.logout()
}

const goToScreen = () => {
  isActive.value = true
  setTimeout(() => {
    router.push('/screen')
  }, 300)
}

const handleClick = (type) => {
  // 根据专项类型跳转到对应的mis端首页路由
  const routeMap = {
    'ranqi': '/gas/home',
    'paishui': '/drainage/home',
    'gongre': '/heating/home',
    'qiaoliang': '/bridge/home',
    'zonghe': '/comprehensive/home'
  }
  
  if (routeMap[type]) {
    router.push(routeMap[type])
  } else {
    console.log('未找到对应路由:', type)
  }
}

// 运营系统管理跳转
const goToSystemManagement = () => {
  router.push('/system/settings/user/list')
}
</script>

<style scoped>
.home {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  background-image: url('@/assets/images/screen/home_bg.png');
  background-size: 100% 100%;
}

/* 系统管理和用户信息栏样式 */
.system-user-bar {
  height: 24px;
  display: flex;
  justify-content: end;
  align-items: center;
  padding: 0 24px;
  margin: -35px 0 0 0;
}

.system-user-bar .left {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.system-user-bar .icon {
  width: 14px;
  height: 14px;
  margin-right: 8px;
}

.system-user-bar .text {
  color: #FFFFFF;
  font-size: 14px;
  font-family: PingFangTC-Regular;
}

.system-user-bar .right {
  display: flex;
  align-items: center;
}

.system-user-bar .avatar {
  width: 24px;
  height: 24px;
  margin: 0 10px 0 24px;
}

.system-user-bar .username {
  color: #FFFFFF;
  font-size: 14px;
  margin-right: 9px;
}

.system-user-bar .arrow {
  width: 16px;
  height: 16px;
  cursor: pointer;
}

/* 下拉菜单样式 */
.dropdown {
  position: relative;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  width: 120px;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 4px;
  padding: 8px 0;
  margin-top: 8px;
  z-index: 1000;
}

.menu-item {
  height: 40px;
  line-height: 40px;
  padding: 0 16px;
  color: #FFFFFF;
  cursor: pointer;
  transition: background-color 0.3s;
}

.menu-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.content {
  position: relative;
  width: 100%;
  height: calc(100% - 92px);
}

.nav-item {
  position: absolute;
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.nav-item:hover {
  transform: scale(1.05);
}

.nav-item.active {
  transform: scale(0.95);
}

/* 驾驶舱位置 */
.center-item {
  top: 74px;
  left: 50%;
  transform: translateX(-50%);
}

.center-item:hover {
  transform: translateX(-50%) scale(1.05);
}

/* 第一行专项位置 */
.left-top {
  top: 340px;
  left: 55px;
}

.right-top {
  top: 340px;
  right: 55px;
}

/* 第二行专项位置 */
.left-bottom {
  bottom: 120px;
  left: 325px;
}

.middle-bottom {
  bottom: 44px;
  left: 50%;
  transform: translateX(-50%);
}

.middle-bottom:hover {
  transform: translateX(-50%) scale(1.05);
}

.right-bottom {
  bottom: 120px;
  right: 325px;
}

.title-bg {
  width: 200px;
  height: 60px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  margin-bottom: 10px;
}

.title-bg.jiashicang {
  background-image: url('@/assets/images/screen/jiashicangwenzi.png');
}

.title-bg.other {
  background-image: url('@/assets/images/screen/qitawenzi.png');
}

.title-bg span {
  font-family: PingFangSC, PingFang SC;
  font-weight: 600;
  font-size: 28px;
  color: #FFFFFF;
  line-height: 40px;
  text-align: left;
  font-style: normal;
}

.platform-base {
  width: 344px;
  height: 230px;
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.center-item .platform-base {
  background-image: url('@/assets/images/screen/jiashicang.png');
  width: 480px;
  height: 384px;
}

.platform-base.ranqi {
  background-image: url('@/assets/images/screen/ranqi.png');
}

.platform-base.paishui {
  background-image: url('@/assets/images/screen/paishui.png');
}

.platform-base.gongre {
  background-image: url('@/assets/images/screen/gongre.png');
}

.platform-base.qiaoliang {
  background-image: url('@/assets/images/screen/qiaoliang.png');
}

.platform-base.zonghe {
  background-image: url('@/assets/images/screen/zonghe.png');
}
</style> 