import { createRouter, createWebHistory } from 'vue-router'
import { h } from 'vue'

// 布局组件
const AdminLayout = () => import('@/views/admin/index.vue')

// 首页组件
const Dashboard = () => import('@/views/admin/dashboard.vue')

// 从utils导入创建示例页面组件的函数
import { createDemoPage } from '@/utils/render'

// 引入燃气首页组件
const GasHome = () => import('@/views/admin/gas/home.vue')

// 四级路由结构
export const adminRoutes = [
  {
    path: '/admin',
    component: AdminLayout,
    redirect: '/comprehensive/industry',
    children: [
      // 燃气路由
      {
        path: '/gas/home',
        name: 'GasHome',
        component: GasHome,
        meta: { title: '燃气首页', icon: 'HomeFilled', keepAlive: true }
      },
      {
        path: '/gas/info',
        name: 'GasInfo',
        redirect: '/gas/info/resource/network',
        meta: { title: '燃气信息资源管理', icon: 'DataLine' },
        children: [
          {
            path: '/gas/info/resource',
            name: 'GasInfoResource',
            redirect: '/gas/info/resource/network',
            meta: { title: '燃气信息资源管理', icon: 'Management' },
            children: [
              {
                path: '/gas/info/resource/network',
                name: 'GasInfoResourceNetwork',
                component: () => import('@/views/admin/gas/info/resource/network.vue'),
                meta: { title: '燃气管网基础信息管理', keepAlive: true }
              },
              {
                path: '/gas/info/resource/station',
                name: 'GasInfoResourceStation',
                component: () => import('@/views/admin/gas/info/resource/station.vue'),
                meta: { title: '燃气场站基础信息管理', keepAlive: true }
              },
              {
                path: '/gas/info/resource/manhole',
                name: 'GasInfoResourceManhole',
                component: () => import('@/views/admin/gas/info/resource/manhole.vue'),
                meta: { title: '窨井基础信息管理', keepAlive: true }
              },
              {
                path: '/gas/info/resource/danger',
                name: 'GasInfoResourceDanger',
                component: () => import('@/views/admin/gas/info/resource/danger.vue'),
                meta: { title: '危险源信息查询', keepAlive: true }
              },
              {
                path: '/gas/info/resource/protection',
                name: 'GasInfoResourceProtection',
                component: () => import('@/views/admin/gas/info/resource/protection.vue'),
                meta: { title: '防护目标信息查询', keepAlive: true }
              },
              {
                path: '/gas/info/resource/monitor',
                name: 'GasInfoResourceMonitor',
                component: () => import('@/views/admin/gas/info/resource/monitor.vue'),
                meta: { title: '燃气监测设备信息查询', keepAlive: true }
              }
            ]
          }
        ]
      },
      {
        path: '/gas/risk',
        name: 'GasRisk',
        redirect: '/gas/risk/explosion/assessment',
        meta: { title: '燃气管网爆炸风险评估', icon: 'Warning' },
        children: [
          {
            path: '/gas/risk/explosion',
            name: 'GasRiskExplosion',
            redirect: '/gas/risk/explosion/assessment',
            meta: { title: '燃气管网爆炸风险评估', icon: 'Histogram' },
            children: [
              {
                path: '/gas/risk/explosion/assessment',
                name: 'GasRiskExplosionAssessment',
                component: () => import('@/views/admin/gas/risk/explosion/assessment.vue'),
                meta: { title: '燃气管网爆炸风险评估管理', keepAlive: true }
              },
              {
                path: '/gas/risk/explosion/heatmap',
                name: 'GasRiskExplosionHeatmap',
                component: () => import('@/views/admin/gas/risk/explosion/heatmap.vue'),
                meta: { title: '燃气管网爆炸风险热力图', keepAlive: true }
              },
              {
                path: '/gas/risk/explosion/station-assessment',
                name: 'GasRiskExplosionStationAssessment',
                component: () => import('@/views/admin/gas/risk/explosion/station-assessment.vue'),
                meta: { title: '燃气场站爆炸风险评估', keepAlive: true }
              },
              {
                path: '/gas/risk/explosion/station-heatmap',
                name: 'GasRiskExplosionStationHeatmap',
                component: () => import('@/views/admin/gas/risk/explosion/station-heatmap.vue'),
                meta: { title: '燃气场站爆炸风险热力图', keepAlive: true }
              }
            ]
          }
        ]
      },
      {
        path: '/gas/leak',
        name: 'GasLeak',
        redirect: '/gas/leak/monitor/threshold',
        meta: { title: '燃气泄漏实时监测与报警', icon: 'AlarmClock' },
        children: [
          {
            path: '/gas/leak/monitor',
            name: 'GasLeakMonitor',
            redirect: '/gas/leak/monitor/threshold',
            meta: { title: '燃气泄漏实时监测与报警', icon: 'Monitor' },
            children: [
              {
                path: '/gas/leak/monitor/threshold',
                name: 'GasLeakMonitorThreshold',
                component: () => import('@/views/admin/gas/leak/monitor/threshold.vue'),
                meta: { title: '报警阈值管理', keepAlive: true }
              },
              {
                path: '/gas/leak/monitor/video',
                name: 'GasLeakMonitorVideo',
                component: () => import('@/views/admin/gas/leak/monitor/video.vue'),
                meta: { title: '视频监控监测', keepAlive: true }
              },
              {
                path: '/gas/leak/monitor/device',
                name: 'GasLeakMonitorDevice',
                component: () => import('@/views/admin/gas/leak/monitor/device.vue'),
                meta: { title: '设备运行监测', keepAlive: true }
              },
              {
                path: '/gas/leak/monitor/alert',
                name: 'GasLeakMonitorAlert',
                component: () => import('@/views/admin/gas/leak/monitor/alert.vue'),
                meta: { title: '燃气泄漏报警提醒', keepAlive: true }
              },
              {
                path: '/gas/leak/monitor/analysis',
                name: 'GasLeakMonitorAnalysis',
                component: () => import('@/views/admin/gas/leak/monitor/analysis.vue'),
                meta: { title: '燃气泄漏报警分析', keepAlive: true }
              },
              {
                path: '/gas/leak/monitor/disposal',
                name: 'GasLeakMonitorDisposal',
                component: () => import('@/views/admin/gas/leak/monitor/disposal.vue'),
                meta: { title: '燃气泄漏报警处置', keepAlive: true }
              }
            ]
          }
        ]
      },
      {
        path: '/gas/predict',
        name: 'GasPredict',
        redirect: '/gas/predict/warning/source',
        meta: { title: '燃气泄漏爆炸预测预警', icon: 'Bell' },
        children: [
          {
            path: '/gas/predict/warning',
            name: 'GasPredictWarning',
            redirect: '/gas/predict/warning/source',
            meta: { title: '燃气泄漏爆炸预测预警', icon: 'Lightning' },
            children: [
              {
                path: '/gas/predict/warning/source',
                name: 'GasPredictWarningSource',
                component: () => import('@/views/admin/gas/predict/warning/source.vue'),
                meta: { title: '可燃气体泄漏溯源分析', keepAlive: true }
              },
              {
                path: '/gas/predict/warning/diffusion',
                name: 'GasPredictWarningDiffusion',
                component: () => import('@/views/admin/gas/predict/warning/diffusion.vue'),
                meta: { title: '可燃气体扩散范围分析', keepAlive: true }
              },
              {
                path: '/gas/predict/warning/damage',
                name: 'GasPredictWarningDamage',
                component: () => import('@/views/admin/gas/predict/warning/damage.vue'),
                meta: { title: '爆炸损伤范围分析', keepAlive: true }
              }
            ]
          }
        ]
      },
      {
        path: '/gas/decision',
        name: 'GasDecision',
        redirect: '/gas/decision/support/plan',
        meta: { title: '燃气泄漏爆炸辅助决策', icon: 'Operation' },
        children: [
          {
            path: '/gas/decision/support',
            name: 'GasDecisionSupport',
            redirect: '/gas/decision/support/plan',
            meta: { title: '燃气泄漏爆炸辅助决策', icon: 'DocumentChecked' },
            children: [
              {
                path: '/gas/decision/support/plan',
                name: 'GasDecisionSupportPlan',
                component: () => import('@/views/admin/gas/decision/support/plan.vue'),
                meta: { title: '燃气泄漏处置方案管理', keepAlive: true }
              },
              {
                path: '/gas/decision/support/statistics',
                name: 'GasDecisionSupportStatistics',
                component: () => import('@/views/admin/gas/decision/support/statistics.vue'),
                meta: { title: '燃气综合统计分析', keepAlive: true }
              },
              {
                path: '/gas/decision/support/report',
                name: 'GasDecisionSupportReport',
                component: () => import('@/views/admin/gas/decision/support/report.vue'),
                meta: { title: '燃气安全运行评估报告', keepAlive: true }
              }
            ]
          }
        ]
      },

      // 排水路由
      {
        path: '/drainage/home',
        name: 'DrainageHome',
        component: () => import('@/views/admin/drainage/home.vue'),
        meta: { title: '排水首页', icon: 'HomeFilled', keepAlive: true }
      },
      {
        path: '/drainage/basic',
        name: 'DrainageBasic',
        redirect: '/drainage/basic/data/network',
        meta: { title: '排水基础数据管理', icon: 'DataLine' },
        children: [
          {
            path: '/drainage/basic/data',
            name: 'DrainageBasicData',
            redirect: '/drainage/basic/data/network',
            meta: { title: '排水基础数据管理', icon: 'Management' },
            children: [
              {
                path: '/drainage/basic/data/network',
                name: 'DrainageBasicDataNetwork',
                component: () => import('@/views/admin/drainage/basic/data/network.vue'),
                meta: { title: '管网基础信息管理', keepAlive: true }
              },
              {
                path: '/drainage/basic/data/outlet',
                name: 'DrainageBasicDataOutlet',
                component: () => import('@/views/admin/drainage/basic/data/outlet.vue'),
                meta: { title: '排水口基础信息管理', keepAlive: true }
              },
              {
                path: '/drainage/basic/data/grate',
                name: 'DrainageBasicDataGrate',
                component: () => import('@/views/admin/drainage/basic/data/grate.vue'),
                meta: { title: '雨水篦子基础信息管理', keepAlive: true }
              },
              {
                path: '/drainage/basic/data/manhole',
                name: 'DrainageBasicDataManhole',
                component: () => import('@/views/admin/drainage/basic/data/manhole.vue'),
                meta: { title: '排水窨井基础信息管理', keepAlive: true }
              },
              {
                path: '/drainage/basic/data/pump',
                name: 'DrainageBasicDataPump',
                component: () => import('@/views/admin/drainage/basic/data/pump.vue'),
                meta: { title: '排水泵站基础信息管理', keepAlive: true }
              },
              {
                path: '/drainage/basic/data/cctv',
                name: 'DrainageBasicDataCctv',
                component: () => import('@/views/admin/drainage/basic/data/cctv.vue'),
                meta: { title: 'CCTV检测信息管理', keepAlive: true }
              },
              {
                path: '/drainage/basic/data/plant',
                name: 'DrainageBasicDataPlant',
                component: () => import('@/views/admin/drainage/basic/data/plant.vue'),
                meta: { title: '污水厂基础信息管理', keepAlive: true }
              },
              {
                path: '/drainage/basic/data/flooding',
                name: 'DrainageBasicDataFlooding',
                component: () => import('@/views/admin/drainage/basic/data/flooding.vue'),
                meta: { title: '易涝点基础信息管理', keepAlive: true }
              },
              {
                path: '/drainage/basic/data/device',
                name: 'DrainageBasicDataDevice',
                component: () => import('@/views/admin/drainage/basic/data/device.vue'),
                meta: { title: '监测设备基础信息管理', keepAlive: true }
              },
              {
                path: '/drainage/basic/data/statistics',
                name: 'DrainageBasicDataStatistics',
                component: () => import('@/views/admin/drainage/basic/data/statistics.vue'),
                meta: { title: '排水基础数据统计分析', keepAlive: true }
              }
            ]
          }
        ]
      },
      {
        path: '/drainage/riskMis',
        name: 'DrainageRisk',
        redirect: '/drainage/riskMis/management/network',
        meta: { title: '排水风险隐患管理', icon: 'Warning' },
        children: [
          {
            path: '/drainage/riskMis/management',
            name: 'DrainageRiskManagement',
            redirect: '/drainage/riskMis/management/network',
            meta: { title: '排水风险隐患管理', icon: 'Histogram' },
            children: [
              {
                path: '/drainage/riskMis/management/network',
                name: 'DrainageRiskManagementNetwork',
                component: () => import('@/views/admin/drainage/risk/management/network.vue'),
                meta: { title: '排水管网风险评估', keepAlive: true }
              },
              {
                path: '/drainage/riskMis/management/plant',
                name: 'DrainageRiskManagementPlant',
                component: () => import('@/views/admin/drainage/risk/management/plant.vue'),
                meta: { title: '污水厂风险评估', keepAlive: true }
              },
              {
                path: '/drainage/riskMis/management/pump',
                name: 'DrainageRiskManagementPump',
                component: () => import('@/views/admin/drainage/risk/management/pump.vue'),
                meta: { title: '排水泵站风险评估', keepAlive: true }
              },
              {
                path: '/drainage/riskMis/management/hidden',
                name: 'DrainageRiskManagementHidden',
                component: () => import('@/views/admin/drainage/risk/management/hidden.vue'),
                meta: { title: '排水隐患信息管理', keepAlive: true }
              }
            ]
          }
        ]
      },
      {
        path: '/drainage/monitoringMis',
        name: 'DrainageMonitoring',
        redirect: '/drainage/monitoringMis/alarm/threshold',
        meta: { title: '排水监测报警管理', icon: 'AlarmClock' },
        children: [
          {
            path: '/drainage/monitoringMis/alarm',
            name: 'DrainageMonitoringAlarm',
            redirect: '/drainage/monitoringMis/alarm/threshold',
            meta: { title: '排水监测报警管理', icon: 'Monitor' },
            children: [
              {
                path: '/drainage/monitoringMis/alarm/threshold',
                name: 'DrainageMonitoringAlarmThreshold',
                component: () => import('@/views/admin/drainage/monitoring/alarm/threshold.vue'),
                meta: { title: '报警阈值管理', keepAlive: true }
              },
              {
                path: '/drainage/monitoringMis/alarm/video',
                name: 'DrainageMonitoringAlarmVideo',
                component: () => import('@/views/admin/drainage/monitoring/alarm/video.vue'),
                meta: { title: '视频监控监测', keepAlive: true }
              },
              {
                path: '/drainage/monitoringMis/alarm/flow',
                name: 'DrainageMonitoringAlarmFlow',
                component: () => import('@/views/admin/drainage/monitoring/alarm/flow.vue'),
                meta: { title: '管网流量监测', keepAlive: true }
              },
              {
                path: '/drainage/monitoringMis/alarm/overflow',
                name: 'DrainageMonitoringAlarmOverflow',
                component: () => import('@/views/admin/drainage/monitoring/alarm/overflow.vue'),
                meta: { title: '污水溢流监测', keepAlive: true }
              },
              {
                path: '/drainage/monitoringMis/alarm/gas',
                name: 'DrainageMonitoringAlarmGas',
                component: () => import('@/views/admin/drainage/monitoring/alarm/gas.vue'),
                meta: { title: '可燃气体积累监测', keepAlive: true }
              },
              {
                path: '/drainage/monitoringMis/alarm/water',
                name: 'DrainageMonitoringAlarmWater',
                component: () => import('@/views/admin/drainage/monitoring/alarm/water.vue'),
                meta: { title: '易涝点积水监测', keepAlive: true }
              },
              {
                path: '/drainage/monitoringMis/alarm/quality',
                name: 'DrainageMonitoringAlarmQuality',
                component: () => import('@/views/admin/drainage/monitoring/alarm/quality.vue'),
                meta: { title: '排污水质监测', keepAlive: true }
              },
              {
                path: '/drainage/monitoringMis/alarm/cover',
                name: 'DrainageMonitoringAlarmCover',
                component: () => import('@/views/admin/drainage/monitoring/alarm/cover.vue'),
                meta: { title: '井盖状态监测', keepAlive: true }
              },
              {
                path: '/drainage/monitoringMis/alarm/info',
                name: 'DrainageMonitoringAlarmInfo',
                component: () => import('@/views/admin/drainage/monitoring/alarm/info.vue'),
                meta: { title: '排水报警信息管理', keepAlive: true }
              },
              {
                path: '/drainage/monitoringMis/alarm/disposal',
                name: 'DrainageMonitoringAlarmDisposal',
                component: () => import('@/views/admin/drainage/monitoring/alarm/disposal.vue'),
                meta: { title: '排水报警信息处置', keepAlive: true }
              },
              {
                path: '/drainage/monitoringMis/alarm/analysis',
                name: 'DrainageMonitoringAlarmAnalysis',
                component: () => import('@/views/admin/drainage/monitoring/alarm/analysis.vue'),
                meta: { title: '排水报警统计分析', keepAlive: true }
              }
            ]
          }
        ]
      },
      {
        path: '/drainage/predict',
        name: 'DrainagePredict',
        redirect: '/drainage/predict/warning/network',
        meta: { title: '预测预警分析', icon: 'Bell' },
        children: [
          {
            path: '/drainage/predict/warning',
            name: 'DrainagePredictWarning',
            redirect: '/drainage/predict/warning/network',
            meta: { title: '预测预警分析', icon: 'Lightning' },
            children: [
              {
                path: '/drainage/predict/warning/network',
                name: 'DrainagePredictWarningNetwork',
                component: () => import('@/views/admin/drainage/predict/warning/network.vue'),
                meta: { title: '排水管网模型预测预警', keepAlive: true }
              },
              {
                path: '/drainage/predict/warning/flooding',
                name: 'DrainagePredictWarningFlooding',
                component: () => import('@/views/admin/drainage/predict/warning/flooding.vue'),
                meta: { title: '内涝模型预测预警', keepAlive: true }
              }
            ]
          }
        ]
      },
      {
        path: '/drainage/decisionMis',
        name: 'DrainageDecision',
        redirect: '/drainage/decisionMis/support/flood',
        meta: { title: '辅助决策支持', icon: 'Operation' },
        children: [
          {
            path: '/drainage/decisionMis/support',
            name: 'DrainageDecisionSupport',
            redirect: '/drainage/decisionMis/support/flood',
            meta: { title: '辅助决策支持', icon: 'DocumentChecked' },
            children: [
              {
                path: '/drainage/decisionMis/support/flood',
                name: 'DrainageDecisionSupportFlood',
                component: () => import('@/views/admin/drainage/decision/support/flood.vue'),
                meta: { title: '防汛调度辅助决策', keepAlive: true }
              },
              {
                path: '/drainage/decisionMis/support/maintenance',
                name: 'DrainageDecisionSupportMaintenance',
                component: () => import('@/views/admin/drainage/decision/support/maintenance.vue'),
                meta: { title: '管网运维改造辅助决策', keepAlive: true }
              },
              {
                path: '/drainage/decisionMis/support/report',
                name: 'DrainageDecisionSupportReport',
                component: () => import('@/views/admin/drainage/decision/support/report.vue'),
                meta: { title: '排水安全风险评估报告', keepAlive: true }
              },
              {
                path: '/drainage/decisionMis/support/emergency',
                name: 'DrainageDecisionSupportEmergency',
                component: () => import('@/views/admin/drainage/decision/support/emergency.vue'),
                meta: { title: '排水应急事件管理', keepAlive: true }
              },
              {
                path: '/drainage/decisionMis/support/material',
                name: 'DrainageDecisionSupportMaterial',
                component: () => import('@/views/admin/drainage/decision/support/material.vue'),
                meta: { title: '防汛物资管理', keepAlive: true }
              },
              {
                path: '/drainage/decisionMis/support/danger',
                name: 'DrainageDecisionSupportDanger',
                component: () => import('@/views/admin/drainage/decision/support/danger.vue'),
                meta: { title: '危险源信息管理', keepAlive: true }
              },
              {
                path: '/drainage/decisionMis/support/protection',
                name: 'DrainageDecisionSupportProtection',
                component: () => import('@/views/admin/drainage/decision/support/protection.vue'),
                meta: { title: '防护目标信息管理', keepAlive: true }
              }
            ]
          }
        ]
      },

      // 热力路由
      {
        path: '/heating/home',
        name: 'HeatingHome',
        component: () => import('@/views/admin/heating/home.vue'),
        meta: { title: '供热首页', icon: 'HomeFilled', keepAlive: true }
      },
      {
        path: '/heating/basic',
        name: 'HeatingBasic',
        redirect: '/heating/basic/data/enterprise',
        meta: { title: '供热基础信息管理', icon: 'DataLine' },
        children: [
          {
            path: '/heating/basic/data',
            name: 'HeatingBasicData',
            redirect: '/heating/basic/data/enterprise',
            meta: { title: '供热基础信息管理', icon: 'Management' },
            children: [
              {
                path: '/heating/basic/data/enterprise',
                name: 'HeatingBasicDataEnterprise',
                component: () => import('@/views/admin/heating/basic/data/enterprise.vue'),
                meta: { title: '供热企业信息管理', keepAlive: true }
              },
              {
                path: '/heating/basic/data/source',
                name: 'HeatingBasicDataSource',
                component: () => import('@/views/admin/heating/basic/data/source.vue'),
                meta: { title: '热源信息管理', keepAlive: true }
              },
              {
                path: '/heating/basic/data/station',
                name: 'HeatingBasicDataStation',
                component: () => import('@/views/admin/heating/basic/data/station.vue'),
                meta: { title: '换热站信息管理', keepAlive: true }
              },
              {
                path: '/heating/basic/data/network',
                name: 'HeatingBasicDataNetwork',
                component: () => import('@/views/admin/heating/basic/data/network.vue'),
                meta: { title: '管网信息管理', keepAlive: true }
              },
              {
                path: '/heating/basic/data/unit',
                name: 'HeatingBasicDataUnit',
                component: () => import('@/views/admin/heating/basic/data/unit.vue'),
                meta: { title: '机组信息管理', keepAlive: true }
              },
              {
                path: '/heating/basic/data/building',
                name: 'HeatingBasicDataBuilding',
                component: () => import('@/views/admin/heating/basic/data/building.vue'),
                meta: { title: '供热区域建筑信息管理', keepAlive: true }
              },
              {
                path: '/heating/basic/data/user',
                name: 'HeatingBasicDataUser',
                component: () => import('@/views/admin/heating/basic/data/user.vue'),
                meta: { title: '供热用户信息管理', keepAlive: true }
              },
              {
                path: '/heating/basic/data/manhole',
                name: 'HeatingBasicDataManhole',
                component: () => import('@/views/admin/heating/basic/data/manhole.vue'),
                meta: { title: '供热窨井信息管理', keepAlive: true }
              },
              {
                path: '/heating/basic/data/device',
                name: 'HeatingBasicDataDevice',
                component: () => import('@/views/admin/heating/basic/data/device.vue'),
                meta: { title: '供热设备信息管理', keepAlive: true }
              },
              {
                path: '/heating/basic/data/statistics',
                name: 'HeatingBasicDataStatistics',
                component: () => import('@/views/admin/heating/basic/data/statistics.vue'),
                meta: { title: '基础数据统计分析', keepAlive: true }
              }
            ]
          }
        ]
      },
      {
        path: '/heating/riskMis',
        name: 'HeatingRisk',
        redirect: '/heating/riskMis/management/network',
        meta: { title: '供热风险管理', icon: 'Warning' },
        children: [
          {
            path: '/heating/riskMis/management',
            name: 'HeatingRiskManagement',
            redirect: '/heating/riskMis/management/network',
            meta: { title: '供热风险管理', icon: 'Histogram' },
            children: [
              {
                path: '/heating/riskMis/management/network',
                name: 'HeatingRiskManagementNetwork',
                component: () => import('@/views/admin/heating/risk/management/network.vue'),
                meta: { title: '供热管网风险信息', keepAlive: true }
              },
              {
                path: '/heating/riskMis/management/station',
                name: 'HeatingRiskManagementStation',
                component: () => import('@/views/admin/heating/risk/management/station.vue'),
                meta: { title: '供热场站风险信息', keepAlive: true }
              },
              {
                path: '/heating/riskMis/management/hidden',
                name: 'HeatingRiskManagementHidden',
                component: () => import('@/views/admin/heating/risk/management/hidden.vue'),
                meta: { title: '供热隐患信息管理', keepAlive: true }
              },
              {
                path: '/heating/riskMis/management/accident',
                name: 'HeatingRiskManagementAccident',
                component: () => import('@/views/admin/heating/risk/management/accident.vue'),
                meta: { title: '供热安全事故信息管理', keepAlive: true }
              },
              {
                path: '/heating/riskMis/management/danger',
                name: 'HeatingRiskManagementDanger',
                component: () => import('@/views/admin/heating/risk/management/danger.vue'),
                meta: { title: '危险源信息管理', keepAlive: true }
              },
              {
                path: '/heating/riskMis/management/protection',
                name: 'HeatingRiskManagementProtection',
                component: () => import('@/views/admin/heating/risk/management/protection.vue'),
                meta: { title: '防护目标信息管理', keepAlive: true }
              },
              {
                path: '/heating/riskMis/management/network-distribution',
                name: 'HeatingRiskManagementNetworkDistribution',
                component: () => import('@/views/admin/heating/risk/management/network-distribution.vue'),
                meta: { title: '供热管网风险分布', keepAlive: true }
              },
              {
                path: '/heating/riskMis/management/station-distribution',
                name: 'HeatingRiskManagementStationDistribution',
                component: () => import('@/views/admin/heating/risk/management/station-distribution.vue'),
                meta: { title: '供热场站风险分布', keepAlive: true }
              },
              {
                path: '/heating/riskMis/management/hidden-distribution',
                name: 'HeatingRiskManagementHiddenDistribution',
                component: () => import('@/views/admin/heating/risk/management/hidden-distribution.vue'),
                meta: { title: '供热隐患分布', keepAlive: true }
              },
              {
                path: '/heating/riskMis/management/protection-distribution',
                name: 'HeatingRiskManagementProtectionDistribution',
                component: () => import('@/views/admin/heating/risk/management/protection-distribution.vue'),
                meta: { title: '防护目标分布', keepAlive: true }
              }
            ]
          }
        ]
      },
      {
        path: '/heating/monitoringMis',
        name: 'HeatingMonitoring',
        redirect: '/heating/monitoringMis/warning/threshold',
        meta: { title: '供热安全监测预警', icon: 'AlarmClock' },
        children: [
          {
            path: '/heating/monitoringMis/warning',
            name: 'HeatingMonitoringWarning',
            redirect: '/heating/monitoringMis/warning/threshold',
            meta: { title: '供热安全监测预警', icon: 'Monitor' },
            children: [
              {
                path: '/heating/monitoringMis/warning/threshold',
                name: 'HeatingMonitoringWarningThreshold',
                component: () => import('@/views/admin/heating/monitoring/warning/threshold.vue'),
                meta: { title: '监测预警阈值管理', keepAlive: true }
              },
              {
                path: '/heating/monitoringMis/warning/network',
                name: 'HeatingMonitoringWarningNetwork',
                component: () => import('@/views/admin/heating/monitoring/warning/network.vue'),
                meta: { title: '管网运行监测预警管理', keepAlive: true }
              },
              {
                path: '/heating/monitoringMis/warning/source',
                name: 'HeatingMonitoringWarningSource',
                component: () => import('@/views/admin/heating/monitoring/warning/source.vue'),
                meta: { title: '热源运行安全风险防控', keepAlive: true }
              },
              {
                path: '/heating/monitoringMis/warning/station',
                name: 'HeatingMonitoringWarningStation',
                component: () => import('@/views/admin/heating/monitoring/warning/station.vue'),
                meta: { title: '换热站运行安全风险防控', keepAlive: true }
              },
              {
                path: '/heating/monitoringMis/warning/leak',
                name: 'HeatingMonitoringWarningLeak',
                component: () => import('@/views/admin/heating/monitoring/warning/leak.vue'),
                meta: { title: '供热管网泄漏研判分析', keepAlive: true }
              },
              {
                path: '/heating/monitoringMis/warning/plan',
                name: 'HeatingMonitoringWarningPlan',
                component: () => import('@/views/admin/heating/monitoring/warning/plan.vue'),
                meta: { title: '预案管理', keepAlive: true }
              }
            ]
          }
        ]
      },
      {
        path: '/heating/decisionMis',
        name: 'HeatingDecision',
        redirect: '/heating/decisionMis/support/level',
        meta: { title: '辅助决策支持', icon: 'Operation' },
        children: [
          {
            path: '/heating/decisionMis/support',
            name: 'HeatingDecisionSupport',
            redirect: '/heating/decisionMis/support/level',
            meta: { title: '辅助决策支持', icon: 'DocumentChecked' },
            children: [
              {
                path: '/heating/decisionMis/support/level',
                name: 'HeatingDecisionSupportLevel',
                component: () => import('@/views/admin/heating/decision/support/level.vue'),
                meta: { title: '态势数据分级管理', keepAlive: true }
              },
              {
                path: '/heating/decisionMis/support/report',
                name: 'HeatingDecisionSupportReport',
                component: () => import('@/views/admin/heating/decision/support/report.vue'),
                meta: { title: '态势数据报送', keepAlive: true }
              },
              {
                path: '/heating/decisionMis/support/analysis',
                name: 'HeatingDecisionSupportAnalysis',
                component: () => import('@/views/admin/heating/decision/support/analysis.vue'),
                meta: { title: '空间分析', keepAlive: true }
              },
              {
                path: '/heating/decisionMis/support/display',
                name: 'HeatingDecisionSupportDisplay',
                component: () => import('@/views/admin/heating/decision/support/display.vue'),
                meta: { title: '态势标绘与展示', keepAlive: true }
              }
            ]
          }
        ]
      },
       
      // 桥梁路由
      {
        path: '/bridge/home',
        name: 'BridgeHome',
        component: () => import('@/views/admin/bridge/home.vue'),
        meta: { title: '桥梁首页', icon: 'HomeFilled', keepAlive: true }
      },
      {
        path: '/bridge/basic',
        name: 'BridgeBasic',
        redirect: '/bridge/basic/data/asset',
        meta: { title: '桥梁基础数据', icon: 'DataLine' },
        children: [
          {
            path: '/bridge/basic/data',
            name: 'BridgeBasicData',
            redirect: '/bridge/basic/data/asset',
            meta: { title: '桥梁基础数据', icon: 'Management' },
            children: [
              {
                path: '/bridge/basic/data/asset',
                name: 'BridgeBasicDataAsset',
                component: () => import('@/views/admin/bridge/basic/data/asset.vue'),
                meta: { title: '桥梁资产管理', keepAlive: true }
              },
              {
                path: '/bridge/basic/data/component',
                name: 'BridgeBasicDataComponent',
                component: () => import('@/views/admin/bridge/basic/data/component.vue'),
                meta: { title: '桥梁构件管理', keepAlive: true }
              },
              {
                path: '/bridge/basic/data/point',
                name: 'BridgeBasicDataPoint',
                component: () => import('@/views/admin/bridge/basic/data/point.vue'),
                meta: { title: '桥梁布点方案', keepAlive: true }
              }
            ]
          }
        ]
      },
      {
        path: '/bridge/device',
        name: 'BridgeDevice',
        redirect: '/bridge/device/management/info',
        meta: { title: '桥梁设备管理', icon: 'SetUp' },
        children: [
          {
            path: '/bridge/device/management',
            name: 'BridgeDeviceManagement',
            redirect: '/bridge/device/management/info',
            meta: { title: '桥梁设备管理', icon: 'Connection' },
            children: [
              {
                path: '/bridge/device/management/info',
                name: 'BridgeDeviceManagementInfo',
                component: () => import('@/views/admin/bridge/device/management/info.vue'),
                meta: { title: '桥梁设备信息', keepAlive: true }
              },
              {
                path: '/bridge/device/management/group',
                name: 'BridgeDeviceManagementGroup',
                component: () => import('@/views/admin/bridge/device/management/group.vue'),
                meta: { title: '设备分组管理', keepAlive: true }
              }
            ]
          }
        ]
      },
      {
        path: '/bridge/monitoring',
        name: 'BridgeMonitoring',
        redirect: '/bridge/monitoring/realtime/video',
        meta: { title: '桥梁实时监测', icon: 'Monitor' },
        children: [
          {
            path: '/bridge/monitoring/realtime',
            name: 'BridgeMonitoringRealtime',
            redirect: '/bridge/monitoring/realtime/video',
            meta: { title: '桥梁实时监测', icon: 'VideoPlay' },
            children: [
              {
                path: '/bridge/monitoring/realtime/video',
                name: 'BridgeMonitoringRealtimeVideo',
                component: () => import('@/views/admin/bridge/monitoring/realtime/video.vue'),
                meta: { title: '桥梁视频监控', keepAlive: true }
              },
              {
                path: '/bridge/monitoring/realtime/environment',
                name: 'BridgeMonitoringRealtimeEnvironment',
                component: () => import('@/views/admin/bridge/monitoring/realtime/environment.vue'),
                meta: { title: '环境数据监测与分析', keepAlive: true }
              },
              {
                path: '/bridge/monitoring/realtime/static',
                name: 'BridgeMonitoringRealtimeStatic',
                component: () => import('@/views/admin/bridge/monitoring/realtime/static.vue'),
                meta: { title: '静态响应监测与分析', keepAlive: true }
              },
              {
                path: '/bridge/monitoring/realtime/dynamic',
                name: 'BridgeMonitoringRealtimeDynamic',
                component: () => import('@/views/admin/bridge/monitoring/realtime/dynamic.vue'),
                meta: { title: '动态响应监测与分析', keepAlive: true }
              },
              {
                path: '/bridge/monitoring/realtime/vibration',
                name: 'BridgeMonitoringRealtimeVibration',
                component: () => import('@/views/admin/bridge/monitoring/realtime/vibration.vue'),
                meta: { title: '意外震动监测与分析', keepAlive: true }
              },
              {
                path: '/bridge/monitoring/realtime/traffic',
                name: 'BridgeMonitoringRealtimeTraffic',
                component: () => import('@/views/admin/bridge/monitoring/realtime/traffic.vue'),
                meta: { title: '交通荷载监测与分析', keepAlive: true }
              }
            ]
          }
        ]
      },
      {
        path: '/bridge/alarm',
        name: 'BridgeAlarm',
        redirect: '/bridge/alarm/management/threshold',
        meta: { title: '桥梁监测报警管理', icon: 'AlarmClock' },
        children: [
          {
            path: '/bridge/alarm/management',
            name: 'BridgeAlarmManagement',
            redirect: '/bridge/alarm/management/threshold',
            meta: { title: '桥梁监测报警管理', icon: 'Bell' },
            children: [
              {
                path: '/bridge/alarm/management/threshold',
                name: 'BridgeAlarmManagementThreshold',
                component: () => import('@/views/admin/bridge/alarm/management/threshold.vue'),
                meta: { title: '监测报警阈值管理', keepAlive: true }
              },
              {
                path: '/bridge/alarm/management/info',
                name: 'BridgeAlarmManagementInfo',
                component: () => import('@/views/admin/bridge/alarm/management/info.vue'),
                meta: { title: '桥梁报警信息管理', keepAlive: true }
              },
              {
                path: '/bridge/alarm/management/disposal',
                name: 'BridgeAlarmManagementDisposal',
                component: () => import('@/views/admin/bridge/alarm/management/disposal.vue'),
                meta: { title: '桥梁报警信息处置', keepAlive: true }
              }
            ]
          }
        ]
      },
      {
        path: '/bridge/inspection',
        name: 'BridgeInspection',
        redirect: '/bridge/inspection/maintenance/plan',
        meta: { title: '桥梁检测养护管理', icon: 'Checked' },
        children: [
          {
            path: '/bridge/inspection/maintenance',
            name: 'BridgeInspectionMaintenance',
            redirect: '/bridge/inspection/maintenance/plan',
            meta: { title: '桥梁检测养护管理', icon: 'Tools' },
            children: [
              {
                path: '/bridge/inspection/maintenance/plan',
                name: 'BridgeInspectionMaintenancePlan',
                component: () => import('@/views/admin/bridge/inspection/maintenance/plan.vue'),
                meta: { title: '检测养护计划管理', keepAlive: true }
              },
              {
                path: '/bridge/inspection/maintenance/record',
                name: 'BridgeInspectionMaintenanceRecord',
                component: () => import('@/views/admin/bridge/inspection/maintenance/record.vue'),
                meta: { title: '检测养护记录管理', keepAlive: true }
              },
              {
                path: '/bridge/inspection/maintenance/report',
                name: 'BridgeInspectionMaintenanceReport',
                component: () => import('@/views/admin/bridge/inspection/maintenance/report.vue'),
                meta: { title: '检测报告管理', keepAlive: true }
              },
              {
                path: '/bridge/inspection/maintenance/data',
                name: 'BridgeInspectionMaintenanceData',
                component: () => import('@/views/admin/bridge/inspection/maintenance/data.vue'),
                meta: { title: '维修养护数据管理', keepAlive: true }
              },
              {
                path: '/bridge/inspection/maintenance/disease',
                name: 'BridgeInspectionMaintenanceDisease',
                component: () => import('@/views/admin/bridge/inspection/maintenance/disease.vue'),
                meta: { title: '病害数据管理', keepAlive: true }
              }
            ]
          }
        ]
      },
      {
        path: '/bridge/analysis',
        name: 'BridgeAnalysis',
        redirect: '/bridge/analysis/data/extreme',
        meta: { title: '桥梁数据分析', icon: 'DataAnalysis' },
        children: [
          {
            path: '/bridge/analysis/data',
            name: 'BridgeAnalysisData',
            redirect: '/bridge/analysis/data/extreme',
            meta: { title: '桥梁数据分析', icon: 'PieChart' },
            children: [
              {
                path: '/bridge/analysis/data/extreme',
                name: 'BridgeAnalysisDataExtreme',
                component: () => import('@/views/admin/bridge/analysis/data/extreme.vue'),
                meta: { title: '极值分析', keepAlive: true }
              },
              {
                path: '/bridge/analysis/data/comparison',
                name: 'BridgeAnalysisDataComparison',
                component: () => import('@/views/admin/bridge/analysis/data/comparison.vue'),
                meta: { title: '对比分析', keepAlive: true }
              },
              {
                path: '/bridge/analysis/data/correlation',
                name: 'BridgeAnalysisDataCorrelation',
                component: () => import('@/views/admin/bridge/analysis/data/correlation.vue'),
                meta: { title: '关联分析', keepAlive: true }
              },
              {
                path: '/bridge/analysis/data/multi-channel',
                name: 'BridgeAnalysisDataMultiChannel',
                component: () => import('@/views/admin/bridge/analysis/data/multi-channel.vue'),
                meta: { title: '多通道分析', keepAlive: true }
              },
              {
                path: '/bridge/analysis/data/deflection',
                name: 'BridgeAnalysisDataDeflection',
                component: () => import('@/views/admin/bridge/analysis/data/deflection.vue'),
                meta: { title: '挠度分析', keepAlive: true }
              },
              {
                path: '/bridge/analysis/data/multi-device',
                name: 'BridgeAnalysisDataMultiDevice',
                component: () => import('@/views/admin/bridge/analysis/data/multi-device.vue'),
                meta: { title: '多设备频谱分析', keepAlive: true }
              },
              {
                path: '/bridge/analysis/data/multi-period',
                name: 'BridgeAnalysisDataMultiPeriod',
                component: () => import('@/views/admin/bridge/analysis/data/multi-period.vue'),
                meta: { title: '多时段频谱分析', keepAlive: true }
              },
              {
                path: '/bridge/analysis/data/filter',
                name: 'BridgeAnalysisDataFilter',
                component: () => import('@/views/admin/bridge/analysis/data/filter.vue'),
                meta: { title: '滤波分析', keepAlive: true }
              }
            ]
          }
        ]
      },
      {
        path: '/bridge/statistics',
        name: 'BridgeStatistics',
        redirect: '/bridge/statistics/analysis/info',
        meta: { title: '桥梁数据统计分析', icon: 'Histogram' },
        children: [
          {
            path: '/bridge/statistics/analysis',
            name: 'BridgeStatisticsAnalysis',
            redirect: '/bridge/statistics/analysis/info',
            meta: { title: '桥梁数据统计分析', icon: 'TrendCharts' },
            children: [
              {
                path: '/bridge/statistics/analysis/info',
                name: 'BridgeStatisticsAnalysisInfo',
                component: () => import('@/views/admin/bridge/statistics/analysis/info.vue'),
                meta: { title: '桥梁信息统计与分析', keepAlive: true }
              },
              {
                path: '/bridge/statistics/analysis/alarm',
                name: 'BridgeStatisticsAnalysisAlarm',
                component: () => import('@/views/admin/bridge/statistics/analysis/alarm.vue'),
                meta: { title: '报警信息统计与分析', keepAlive: true }
              },
              {
                path: '/bridge/statistics/analysis/safety',
                name: 'BridgeStatisticsAnalysisSafety',
                component: () => import('@/views/admin/bridge/statistics/analysis/safety.vue'),
                meta: { title: '安全评估统计与分析', keepAlive: true }
              },
              {
                path: '/bridge/statistics/analysis/overload',
                name: 'BridgeStatisticsAnalysisOverload',
                component: () => import('@/views/admin/bridge/statistics/analysis/overload.vue'),
                meta: { title: '超载数据统计与分析', keepAlive: true }
              },
              {
                path: '/bridge/statistics/analysis/monitoring',
                name: 'BridgeStatisticsAnalysisMonitoring',
                component: () => import('@/views/admin/bridge/statistics/analysis/monitoring.vue'),
                meta: { title: '监测数据统计与分析', keepAlive: true }
              }
            ]
          }
        ]
      },
      {
        path: '/bridge/safety',
        name: 'BridgeSafety',
        redirect: '/bridge/safety/assessment/report',
        meta: { title: '桥梁安全评估', icon: 'Warning' },
        children: [
          {
            path: '/bridge/safety/assessment',
            name: 'BridgeSafetyAssessment',
            redirect: '/bridge/safety/assessment/report',
            meta: { title: '桥梁安全评估', icon: 'DocumentChecked' },
            children: [
              {
                path: '/bridge/safety/assessment/report',
                name: 'BridgeSafetyAssessmentReport',
                component: () => import('@/views/admin/bridge/safety/assessment/report.vue'),
                meta: { title: '安全评估报告', keepAlive: true }
              },
              {
                path: '/bridge/safety/assessment/score',
                name: 'BridgeSafetyAssessmentScore',
                component: () => import('@/views/admin/bridge/safety/assessment/score.vue'),
                meta: { title: '桥梁安全评分', keepAlive: true }
              }
            ]
          }
        ]
      },

      // 综合专项路由
      {
        path: '/comprehensive/industry',
        name: 'ComprehensiveIndustry',
        redirect: '/comprehensive/industry/supervision/notice',
        meta: { title: '行业综合监管', icon: 'Office' },
        children: [
          {
            path: '/comprehensive/industry/supervision',
            name: 'ComprehensiveIndustrySupervision',
            redirect: '/comprehensive/industry/supervision/notice',
            meta: { title: '行业综合监管', icon: 'Management' },
            children: [
              {
                path: '/comprehensive/industry/supervision/notice',
                name: 'ComprehensiveIndustrySupervisionNotice',
                component: () => import('@/views/admin/comprehensive/industry/supervision/notice.vue'),
                meta: { title: '通知公告', keepAlive: true }
              },
              {
                path: '/comprehensive/industry/supervision/planning',
                name: 'ComprehensiveIndustrySupervisionPlanning',
                component: () => import('@/views/admin/comprehensive/industry/supervision/planning.vue'),
                meta: { title: '规划建设', keepAlive: true }
              },
              {
                path: '/comprehensive/industry/supervision/inspection',
                name: 'ComprehensiveIndustrySupervisionInspection',
                component: () => import('@/views/admin/comprehensive/industry/supervision/inspection.vue'),
                meta: { title: '督查督办', keepAlive: true }
              },
              {
                path: '/comprehensive/industry/supervision/evaluation',
                name: 'ComprehensiveIndustrySupervisionEvaluation',
                component: () => import('@/views/admin/comprehensive/industry/supervision/evaluation.vue'),
                meta: { title: '考核评价', keepAlive: true }
              },
              {
                path: '/comprehensive/industry/supervision/expert',
                name: 'ComprehensiveIndustrySupervisionExpert',
                component: () => import('@/views/admin/comprehensive/industry/supervision/expert.vue'),
                meta: { title: '专家咨询', keepAlive: true }
              },
              {
                path: '/comprehensive/industry/supervision/resource',
                name: 'ComprehensiveIndustrySupervisionResource',
                component: () => import('@/views/admin/comprehensive/industry/supervision/resource.vue'),
                meta: { title: '资料中心', keepAlive: true }
              }
            ]
          }
        ]
      },
      {
        path: '/comprehensive/public',
        name: 'ComprehensivePublic',
        redirect: '/comprehensive/public/service/alert',
        meta: { title: '面向公众服务', icon: 'Service' },
        children: [
          {
            path: '/comprehensive/public/service',
            name: 'ComprehensivePublicService',
            redirect: '/comprehensive/public/service/alert',
            meta: { title: '面向公众服务', icon: 'User' },
            children: [
              {
                path: '/comprehensive/public/service/alert',
                name: 'ComprehensivePublicServiceAlert',
                component: () => import('@/views/admin/comprehensive/public/service/alert.vue'),
                meta: { title: '报警通报', keepAlive: true }
              },
              {
                path: '/comprehensive/public/service/report',
                name: 'ComprehensivePublicServiceReport',
                component: () => import('@/views/admin/comprehensive/public/service/report.vue'),
                meta: { title: '公众报警', keepAlive: true }
              }
            ]
          }
        ]
      },
      {
        path: '/comprehensive/event',
        name: 'ComprehensiveEvent',
        redirect: '/comprehensive/event/analysis/management',
        meta: { title: '事件分析研判', icon: 'DataAnalysis' },
        children: [
          {
            path: '/comprehensive/event/analysis',
            name: 'ComprehensiveEventAnalysis',
            redirect: '/comprehensive/event/analysis/management',
            meta: { title: '事件分析研判', icon: 'TrendCharts' },
            children: [
              {
                path: '/comprehensive/event/analysis/management',
                name: 'ComprehensiveEventAnalysisManagement',
                component: () => import('@/views/admin/comprehensive/event/analysis/management.vue'),
                meta: { title: '应急事件管理', keepAlive: true }
              },
              {
                path: '/comprehensive/event/analysis/statistics',
                name: 'ComprehensiveEventAnalysisStatistics',
                component: () => import('@/views/admin/comprehensive/event/analysis/statistics.vue'),
                meta: { title: '应急事件统计', keepAlive: true }
              }
            ]
          }
        ]
      },
      {
        path: '/comprehensive/coordinationMis',
        name: 'ComprehensiveCoordination',
        redirect: '/comprehensive/coordinationMis/disposal/warning',
        meta: { title: '协同联动处置', icon: 'Connection' },
        children: [
          {
            path: '/comprehensive/coordinationMis/disposal',
            name: 'ComprehensiveCoordinationDisposal',
            redirect: '/comprehensive/coordinationMis/disposal/warning',
            meta: { title: '协同联动处置', icon: 'Operation' },
            children: [
              {
                path: '/comprehensive/coordinationMis/disposal/warning',
                name: 'ComprehensiveCoordinationDisposalWarning',
                component: () => import('@/views/admin/comprehensive/coordination/disposal/warning.vue'),
                meta: { title: '预警信息管理', keepAlive: true }
              },
              {
                path: '/comprehensive/coordinationMis/disposal/manage',
                name: 'ComprehensiveCoordinationDisposalManage',
                component: () => import('@/views/admin/comprehensive/coordination/disposal/manage.vue'),
                meta: { title: '预警信息处置', keepAlive: true }
              }
            ]
          }
        ]
      },
      {
        path: '/comprehensive/riskMis',
        name: 'ComprehensiveRisk',
        redirect: '/comprehensive/riskMis/control/hidden',
        meta: { title: '综合风险管控', icon: 'Warning' },
        children: [
          {
            path: '/comprehensive/riskMis/control',
            name: 'ComprehensiveRiskControl',
            redirect: '/comprehensive/riskMis/control/hidden',
            meta: { title: '综合风险管控', icon: 'Histogram' },
            children: [
              {
                path: '/comprehensive/riskMis/control/hidden',
                name: 'ComprehensiveRiskControlHidden',
                component: () => import('@/views/admin/comprehensive/risk/control/hidden.vue'),
                meta: { title: '隐患排查治理', keepAlive: true }
              },
              {
                path: '/comprehensive/riskMis/control/assessment',
                name: 'ComprehensiveRiskControlAssessment',
                component: () => import('@/views/admin/comprehensive/risk/control/assessment.vue'),
                meta: { title: '风险评估标识', keepAlive: true }
              },
              {
                path: '/comprehensive/riskMis/control/area',
                name: 'ComprehensiveRiskControlArea',
                component: () => import('@/views/admin/comprehensive/risk/control/area.vue'),
                meta: { title: '风险区域划分', keepAlive: true }
              },
              {
                path: '/comprehensive/riskMis/control/map',
                name: 'ComprehensiveRiskControlMap',
                component: () => import('@/views/admin/comprehensive/risk/control/map.vue'),
                meta: { title: '风险四色图', keepAlive: true }
              }
            ]
          }
        ]
      },
      {
        path: '/comprehensive/emergencyMis',
        name: 'ComprehensiveEmergency',
        redirect: '/comprehensive/emergencyMis/linkage/plan',
        meta: { title: '应急联动管理', icon: 'AlarmClock' },
        children: [
          {
            path: '/comprehensive/emergencyMis/linkage',
            name: 'ComprehensiveEmergencyLinkage',
            redirect: '/comprehensive/emergencyMis/linkage/plan',
            meta: { title: '应急联动管理', icon: 'Bell' },
            children: [
              {
                path: '/comprehensive/emergencyMis/linkage/plan',
                name: 'ComprehensiveEmergencyLinkagePlan',
                component: () => import('@/views/admin/comprehensive/emergency/linkage/plan.vue'),
                meta: { title: '应急预案管理', keepAlive: true }
              },
              {
                path: '/comprehensive/emergencyMis/linkage/resource',
                name: 'ComprehensiveEmergencyLinkageResource',
                component: () => import('@/views/admin/comprehensive/emergency/linkage/resource.vue'),
                meta: { title: '应急资源管理', keepAlive: true }
              },
              {
                path: '/comprehensive/emergencyMis/linkage/expert',
                name: 'ComprehensiveEmergencyLinkageExpert',
                component: () => import('@/views/admin/comprehensive/emergency/linkage/expert.vue'),
                meta: { title: '应急专家管理', keepAlive: true }
              }
            ]
          }
        ]
      },
      {
        path: '/comprehensive/equipment',
        name: 'ComprehensiveEquipment',
        redirect: '/comprehensive/equipment/maintenance/duty',
        meta: { title: '设备运维管理', icon: 'SetUp' },
        children: [
          {
            path: '/comprehensive/equipment/maintenance',
            name: 'ComprehensiveEquipmentMaintenance',
            redirect: '/comprehensive/equipment/maintenance/duty',
            meta: { title: '设备运维管理', icon: 'Tools' },
            children: [
              {
                path: '/comprehensive/equipment/maintenance/duty',
                name: 'ComprehensiveEquipmentMaintenanceDuty',
                component: () => import('@/views/admin/comprehensive/equipment/maintenance/duty.vue'),
                meta: { title: '值班管理', keepAlive: true }
              },
              {
                path: '/comprehensive/equipment/maintenance/inspection',
                name: 'ComprehensiveEquipmentMaintenanceInspection',
                component: () => import('@/views/admin/comprehensive/equipment/maintenance/inspection.vue'),
                meta: { title: '巡检管理', keepAlive: true }
              },
              {
                path: '/comprehensive/equipment/maintenance/order',
                name: 'ComprehensiveEquipmentMaintenanceOrder',
                component: () => import('@/views/admin/comprehensive/equipment/maintenance/order.vue'),
                meta: { title: '巡检工单', keepAlive: true }
              },
              {
                path: '/comprehensive/equipment/maintenance/repair',
                name: 'ComprehensiveEquipmentMaintenanceRepair',
                component: () => import('@/views/admin/comprehensive/equipment/maintenance/repair.vue'),
                meta: { title: '设备维修', keepAlive: true }
              },
              {
                path: '/comprehensive/equipment/maintenance/monitor',
                name: 'ComprehensiveEquipmentMaintenanceMonitor',
                component: () => import('@/views/admin/comprehensive/equipment/maintenance/monitor.vue'),
                meta: { title: '设备监控', keepAlive: true }
              }
            ]
          }
        ]
      },

      // 系统管理路由
      {
        path: '/system/management',
        name: 'SystemManagement',
        redirect: '/system/management/admin/user',
        meta: { title: '系统管理', icon: 'Setting' },
        children: [
          {
            path: '/system/management/admin',
            name: 'SystemManagementAdmin',
            redirect: '/system/management/admin/user',
            meta: { title: '系统管理', icon: 'SetUp' },
            children: [
              {
                path: '/system/management/admin/user',
                name: 'SystemManagementAdminUser',
                component: () => import('@/views/admin/system/management/admin/user.vue'),
                meta: { title: '用户管理', keepAlive: true }
              },
              {
                path: '/system/management/admin/organization',
                name: 'SystemManagementAdminOrganization',
                component: () => import('@/views/admin/system/management/admin/organization.vue'),
                meta: { title: '组织管理', keepAlive: true }
              },
              {
                path: '/system/management/admin/role',
                name: 'SystemManagementAdminRole',
                component: () => import('@/views/admin/system/management/admin/role.vue'),
                meta: { title: '角色管理', keepAlive: true }
              },
              {
                path: '/system/management/admin/login-log',
                name: 'SystemManagementAdminLoginLog',
                component: () => import('@/views/admin/system/management/admin/login-log.vue'),
                meta: { title: '登陆日志', keepAlive: true }
              }
            ]
          }
        ]
      },
      // ...(import.meta.env.MODE !== 'production' ? [{
      //   path: '/system/settings/code-generator',
      //   name: 'SystemSettingsCodeGenerator',
      //   component: () => import('@/views/admin/system/code-generator/index.vue'),
      //   meta: { title: '代码生成器', icon: 'Document' }
      // }] : [])
    ]
  }
]