/**
 *    ████████                   ██                   ██       ██                  ██      ██
 *   ██░░░░░░██                 ░░                   ░██      ░██                 ░██     ░██
 *  ██      ░░   █████  ███████  ██ ██   ██  ██████  ░██   █  ░██  ██████  ██████ ░██     ░██
 * ░██          ██░░░██░░██░░░██░██░██  ░██ ██░░░░   ░██  ███ ░██ ██░░░░██░░██░░█ ░██  ██████
 * ░██    █████░███████ ░██  ░██░██░██  ░██░░█████   ░██ ██░██░██░██   ░██ ░██ ░  ░██ ██░░░██
 * ░░██  ░░░░██░██░░░░  ░██  ░██░██░██  ░██ ░░░░░██  ░████ ░░████░██   ░██ ░██    ░██░██  ░██
 *  ░░████████ ░░██████ ███  ░██░██░░██████ ██████   ░██░   ░░░██░░██████ ░███    ███░░██████
 *   ░░░░░░░░   ░░░░░░ ░░░   ░░ ░░  ░░░░░░ ░░░░░░    ░░       ░░  ░░░░░░  ░░░    ░░░  ░░░░░░
 *
 * @license
 * GeniusWorld Web SDK - http://zydlxx.cn:1234/
 * Version 4.0.0.B#develop-d3ff2330@202405231107
 *
 *
 * Copyright © 2020 - 2023 正元地理信息集团股份有限公司. All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
define(["./arrayRemoveDuplicates-3cf34348","./Transforms-6a5d79d3","./Matrix3-79d15570","./ComponentDatatype-e95dda25","./CoplanarPolygonGeometryLibrary-30c65fb7","./defaultValue-7b61670d","./GeometryAttribute-d24f9032","./GeometryAttributes-410c425f","./GeometryInstance-b3218e0a","./GeometryPipeline-ab8f5560","./IndexDatatype-7c192505","./PolygonGeometryLibrary-d2cb98fc","./Math-6acd1674","./Matrix2-d550732e","./RuntimeError-7dc4ea5a","./combine-bc3d0d90","./WebGLConstants-68839929","./OrientedBoundingBox-82cae8c7","./EllipsoidTangentPlane-d7ae8406","./AxisAlignedBoundingBox-5054a700","./IntersectionTests-044bd161","./Plane-e4eb0e88","./AttributeCompression-aa106b76","./EncodedCartesian3-e2f2e578","./ArcType-378e21f1","./EllipsoidRhumbLine-997e9b1a","./PolygonPipeline-de89f886"],(function(e,t,n,o,r,i,a,y,c,l,s,u,p,d,m,g,f,b,h,P,G,L,C,T,E,A,H){"use strict";function k(e){const t=e.length,n=new Float64Array(3*t),r=s.IndexDatatype.createTypedArray(t,2*t);let i=0,c=0;for(let o=0;o<t;o++){const a=e[o];n[i++]=a.x,n[i++]=a.y,n[i++]=a.z,r[c++]=o,r[c++]=(o+1)%t}const l=new y.GeometryAttributes({position:new a.GeometryAttribute({componentDatatype:o.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:n})});return new a.Geometry({attributes:l,indices:r,primitiveType:a.PrimitiveType.LINES})}function x(e){const t=(e=i.defaultValue(e,i.defaultValue.EMPTY_OBJECT)).polygonHierarchy;this._polygonHierarchy=t,this._workerName="createCoplanarPolygonOutlineGeometry",this.packedLength=u.PolygonGeometryLibrary.computeHierarchyPackedLength(t,n.Cartesian3)+1}x.fromPositions=function(e){return new x({polygonHierarchy:{positions:(e=i.defaultValue(e,i.defaultValue.EMPTY_OBJECT)).positions}})},x.pack=function(e,t,o){return o=i.defaultValue(o,0),t[o=u.PolygonGeometryLibrary.packPolygonHierarchy(e._polygonHierarchy,t,o,n.Cartesian3)]=e.packedLength,t};const w={polygonHierarchy:{}};return x.unpack=function(e,t,o){t=i.defaultValue(t,0);const r=u.PolygonGeometryLibrary.unpackPolygonHierarchy(e,t,n.Cartesian3);t=r.startingIndex,delete r.startingIndex;const a=e[t];return i.defined(o)||(o=new x(w)),o._polygonHierarchy=r,o.packedLength=a,o},x.createGeometry=function(o){const i=o._polygonHierarchy;let y=i.positions;if(y=e.arrayRemoveDuplicates(y,n.Cartesian3.equalsEpsilon,!0),y.length<3)return;if(!r.CoplanarPolygonGeometryLibrary.validOutline(y))return;const s=u.PolygonGeometryLibrary.polygonOutlinesFromHierarchy(i,!1);if(0===s.length)return;const p=[];for(let e=0;e<s.length;e++){const t=new c.GeometryInstance({geometry:k(s[e])});p.push(t)}const d=l.GeometryPipeline.combineInstances(p)[0],m=t.BoundingSphere.fromPoints(i.positions);return new a.Geometry({attributes:d.attributes,indices:d.indices,primitiveType:d.primitiveType,boundingSphere:m})},function(e,t){return i.defined(t)&&(e=x.unpack(e,t)),e._ellipsoid=n.Ellipsoid.clone(e._ellipsoid),x.createGeometry(e)}}));
