import request from '@/utils/request'

// 获取桥梁设备信息分页列表
export function getPipelineInfoPage(pageNum, pageSize, params = {}) {
  return request({
    url: `/bridge/usmMonitorDevice/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

// 获取桥梁设备列表
export function getPipelineInfoList(params = {}) {
  return request({
    url: '/bridge/usmMonitorDevice/list',
    method: 'post',
    data: params
  })
}
// 获取桥梁设备指标列表
export function getMonitorIndicatorsList(data) {
  return request({
    url: '/bridge/usmMonitorIndicators/list',
    method: 'post',
    data
  })
}

// 获取桥梁设备信息详情
export function getPipelineInfoDetail(id) {
  return request({
    url: `/bridge/usmMonitorDevice/${id}`,
    method: 'get'
  })
}

// 新增桥梁设备信息
export function savePipelineInfo(data) {
  return request({
    url: '/bridge/usmMonitorDevice/save',
    method: 'post',
    data
  })
}

// 更新桥梁设备信息
export function updatePipelineInfo(data) {
  return request({
    url: '/bridge/usmMonitorDevice/update',
    method: 'post',
    data
  })
}

// 删除桥梁设备信息
export function deletePipelineInfo(id) {
  return request({
    url: `/bridge/usmMonitorDevice/${id}`,
    method: 'delete'
  })
}

// 获取桥梁基础信息列表（用于对象选择下拉框）
export function getBridgeBasicInfoList(params = {}) {
  return request({
    url: '/bridge/usmBridgeBasicInfo/list',
    method: 'post',
    data: params
  })
}

// ==================== 桥梁基础信息管理 API ====================

// 获取桥梁基础信息分页列表
export function getBridgeBasicInfoPage(pageNum, pageSize, params = {}) {
  return request({
    url: `/bridge/usmBridgeBasicInfo/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

// 获取桥梁基础信息详情
export function getBridgeBasicInfoDetail(id) {
  return request({
    url: `/bridge/usmBridgeBasicInfo/${id}`,
    method: 'get'
  })
}

// 新增桥梁基础信息
export function saveBridgeBasicInfo(data) {
  return request({
    url: '/bridge/usmBridgeBasicInfo/save',
    method: 'post',
    data
  })
}

// 更新桥梁基础信息
export function updateBridgeBasicInfo(data) {
  return request({
    url: '/bridge/usmBridgeBasicInfo/update',
    method: 'post',
    data
  })
}

// 删除桥梁基础信息
export function deleteBridgeBasicInfo(id) {
  return request({
    url: `/bridge/usmBridgeBasicInfo/${id}`,
    method: 'delete'
  })
}

// ==================== 一般资料 API ====================

// 获取一般资料详情
export function getBridgeProfileGeneralDetail(bridgeId) {
  return request({
    url: `/bridge/usmBridgeProfileGeneral/${bridgeId}`,
    method: 'get'
  })
}

// 新增一般资料
export function saveBridgeProfileGeneral(data) {
  return request({
    url: '/bridge/usmBridgeProfileGeneral/save',
    method: 'post',
    data
  })
}

// 更新一般资料
export function updateBridgeProfileGeneral(data) {
  return request({
    url: '/bridge/usmBridgeProfileGeneral/update',
    method: 'post',
    data
  })
}

// 删除一般资料
export function deleteBridgeProfileGeneral(id) {
  return request({
    url: `/bridge/usmBridgeProfileGeneral/${id}`,
    method: 'delete'
  })
}

// ==================== 上部结构 API ====================

// 获取上部结构详情
export function getBridgeProfileSuperstructureDetail(bridgeId) {
  return request({
    url: `/bridge/usmBridgeProfileSuperstructure/${bridgeId}`,
    method: 'get'
  })
}

// 新增上部结构
export function saveBridgeProfileSuperstructure(data) {
  return request({
    url: '/bridge/usmBridgeProfileSuperstructure/save',
    method: 'post',
    data
  })
}

// 更新上部结构
export function updateBridgeProfileSuperstructure(data) {
  return request({
    url: '/bridge/usmBridgeProfileSuperstructure/update',
    method: 'post',
    data
  })
}

// 删除上部结构
export function deleteBridgeProfileSuperstructure(id) {
  return request({
    url: `/bridge/usmBridgeProfileSuperstructure/${id}`,
    method: 'delete'
  })
}

// ==================== 下部结构 API ====================

// 获取下部结构详情
export function getBridgeProfileSubstructureDetail(bridgeId) {
  return request({
    url: `/bridge/usmBridgeProfileSubstructure/${bridgeId}`,
    method: 'get'
  })
}

// 新增下部结构
export function saveBridgeProfileSubstructure(data) {
  return request({
    url: '/bridge/usmBridgeProfileSubstructure/save',
    method: 'post',
    data
  })
}

// 更新下部结构
export function updateBridgeProfileSubstructure(data) {
  return request({
    url: '/bridge/usmBridgeProfileSubstructure/update',
    method: 'post',
    data
  })
}

// 删除下部结构
export function deleteBridgeProfileSubstructure(id) {
  return request({
    url: `/bridge/usmBridgeProfileSubstructure/${id}`,
    method: 'delete'
  })
}

// ==================== 附属工程 API ====================

// 获取附属工程详情
export function getBridgeProfileAccessoryProjectDetail(bridgeId) {
  return request({
    url: `/bridge/usmBridgeProfileAccessoryProject/${bridgeId}`,
    method: 'get'
  })
}

// 新增附属工程
export function saveBridgeProfileAccessoryProject(data) {
  return request({
    url: '/bridge/usmBridgeProfileAccessoryProject/save',
    method: 'post',
    data
  })
}

// 更新附属工程
export function updateBridgeProfileAccessoryProject(data) {
  return request({
    url: '/bridge/usmBridgeProfileAccessoryProject/update',
    method: 'post',
    data
  })
}

// 删除附属工程
export function deleteBridgeProfileAccessoryProject(id) {
  return request({
    url: `/bridge/usmBridgeProfileAccessoryProject/${id}`,
    method: 'delete'
  })
}

// ==================== 附挂管线 API ====================

// 获取附挂管线详情
export function getBridgeProfilePipelineDetail(bridgeId) {
  return request({
    url: `/bridge/usmBridgeProfilePipeline/${bridgeId}`,
    method: 'get'
  })
}

// 新增附挂管线
export function saveBridgeProfilePipeline(data) {
  return request({
    url: '/bridge/usmBridgeProfilePipeline/save',
    method: 'post',
    data
  })
}

// 更新附挂管线
export function updateBridgeProfilePipeline(data) {
  return request({
    url: '/bridge/usmBridgeProfilePipeline/update',
    method: 'post',
    data
  })
}

// 删除附挂管线
export function deleteBridgeProfilePipeline(id) {
  return request({
    url: `/bridge/usmBridgeProfilePipeline/${id}`,
    method: 'delete'
  })
}

// ==================== 组成信息 API ====================

// 获取组成信息详情
export function getBridgeComponentInfoDetail(bridgeId) {
  return request({
    url: `/bridge/usmBridgeComponentInfo/${bridgeId}`,
    method: 'get'
  })
}

// 新增组成信息
export function saveBridgeComponentInfo(data) {
  return request({
    url: '/bridge/usmBridgeComponentInfo/save',
    method: 'post',
    data
  })
}

// 更新组成信息
export function updateBridgeComponentInfo(data) {
  return request({
    url: '/bridge/usmBridgeComponentInfo/update',
    method: 'post',
    data
  })
}

// 删除组成信息
export function deleteBridgeComponentInfo(id) {
  return request({
    url: `/bridge/usmBridgeComponentInfo/${id}`,
    method: 'delete'
  })
}

// ==================== 附件资料 API ====================

// 获取附件资料详情
export function getBridgeAttachmentInfoDetail(bridgeId) {
  return request({
    url: `/bridge/usmBridgeAttachmentInfo/${bridgeId}`,
    method: 'get'
  })
}

// 新增附件资料
export function saveBridgeAttachmentInfo(data) {
  return request({
    url: '/bridge/usmBridgeAttachmentInfo/save',
    method: 'post',
    data
  })
}

// 更新附件资料
export function updateBridgeAttachmentInfo(data) {
  return request({
    url: '/bridge/usmBridgeAttachmentInfo/update',
    method: 'post',
    data
  })
}

// 删除附件资料
export function deleteBridgeAttachmentInfo(id) {
  return request({
    url: `/bridge/usmBridgeAttachmentInfo/${id}`,
    method: 'delete'
  })
}

// ==================== 养护单位 API ====================

// 获取养护单位列表
export function getMaintenanceEnterpriseList(data) {
  return request({
    url: '/bridge/usmMaintenanceEnterprise/list',
    method: 'post',
    data
  })
}

// ==================== 文件上传 API ====================

// 文件上传
export function uploadFile(file) {
  const formData = new FormData()
  formData.append('file', file)
  return request({
    url: '/common/upload',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// ==================== 桥梁监测报警阈值管理 API ====================

// 获取报警阈值分页列表
export function getAlarmThresholdPage(pageNum, pageSize, params = {}) {
  return request({
    url: `/bridge/usmAlarmThreshold/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

// 获取报警阈值详情
export function getAlarmThresholdDetail(id) {
  return request({
    url: `/bridge/usmAlarmThreshold/${id}`,
    method: 'get'
  })
}

// 新增报警阈值
export function saveAlarmThreshold(data) {
  return request({
    url: '/bridge/usmAlarmThreshold/save',
    method: 'post',
    data
  })
}

// 更新报警阈值
export function updateAlarmThreshold(data) {
  return request({
    url: '/bridge/usmAlarmThreshold/update',
    method: 'post',
    data
  })
}

// 删除报警阈值
export function deleteAlarmThreshold(id) {
  return request({
    url: `/bridge/usmAlarmThreshold/${id}`,
    method: 'delete'
  })
}

// ==================== 桥梁报警信息管理 API ====================

// 获取桥梁报警列表
export function getBridgeAlarmList(pageNum, pageSize, params = {}) {
  return request({
    url: `/bridge/usmMonitorAlarm/search/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

// 获取桥梁报警统计数据
export function getBridgeAlarmStatistics(params = {}) {
  return request({
    url: '/bridge/usmAlarmStatisticsAnalysis/statistics',
    method: 'post',
    data: params
  })
}

// 获取桥梁报警等级统计数据
export function getBridgeAlarmLevelStatistics(params = {}) {
  return request({
    url: '/bridge/usmAlarmStatisticsAnalysis/level/statistics',
    method: 'post',
    data: params
  })
}

// 获取桥梁报警详情
export function getBridgeAlarmDetail(id) {
  return request({
    url: `/bridge/usmMonitorAlarm/${id}`,
    method: 'get'
  })
}

// 确认桥梁报警
export function confirmBridgeAlarm(data) {
  return request({
    url: '/bridge/usmMonitorAlarm/alarm/confirm',
    method: 'post',
    data
  })
}

// 处置桥梁报警
export function handleBridgeAlarm(data) {
  return request({
    url: '/bridge/usmMonitorAlarm/handle',
    method: 'post',
    data
  })
}

// 获取桥梁报警监测曲线数据
export function getBridgeAlarmMonitorCurve(params) {
  return request({
    url: '/bridge/usmMonitorRecord/monitorCurve',
    method: 'post',
    data: params
  })
}

// 获取桥梁报警记录列表（根据设备ID）
export function getBridgeAlarmRecords(deviceId) {
  return request({
    url: `/bridge/usmMonitorAlarm/alarmRecord/${deviceId}`,
    method: 'get'
  })
}

// 获取桥梁报警状态记录列表
export function getBridgeAlarmStatusList(params) {
  return request({
    url: '/bridge/usmMonitorAlarmStatus/list',
    method: 'post',
    data: params
  })
}

// 获取桥梁报警处置记录列表
export function getBridgeAlarmHandleList(alarmId) {
  return request({
    url: `/bridge/usmMonitorAlarm/alarm/handleList/${alarmId}`,
    method: 'get'
  })
}

// 新增桥梁报警处置记录
export function addBridgeAlarmHandle(data) {
  return request({
    url: '/bridge/usmMonitorAlarm/alarm/handle',
    method: 'post',
    data: data
  })
}

// 删除桥梁报警处置记录
export function deleteBridgeAlarmHandle(id) {
  return request({
    url: `/bridge/usmMonitorAlarmStatus/${id}`,
    method: 'delete'
  })
}

// 获取桥梁报警类型
export function getBridgeAlarmTypes() {
  return request({
    url: '/bridge/usmMonitorAlarm/getAlarmType',
    method: 'get'
  })
}
