<template>
  <PanelBox title="隐患类型统计">
    <template #extra>
      <div class="com-select">
        <CommonSelect v-model="timeRange" :options="timeOptions" @change="handleTimeChange" />
      </div>
    </template>
    <div class="panel-content">
      <div class="content-wrapper">
        <div class="chart-container">
          <PieChart3D :data="chartData" height="220px" :internal-diameter-ratio="0.6" />
          <!-- <div class="pie-bg"></div> -->
        </div>
        <div class="legend-container">
          <div class="legend-item" v-for="(item, index) in chartData" :key="index">
            <div class="legend-color" :style="{ backgroundColor: item.itemStyle.color }"></div>
            <div class="legend-text">
              <span class="legend-name">{{ item.name }}</span>
              <span class="legend-value">{{ item.percentage }}%</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </PanelBox>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import PanelBox from '@/components/screen/PanelBox.vue'
import CommonSelect from '@/components/screen/CommonSelect.vue'
import PieChart3D from '@/components/screen/common/PieChart3D.vue'

// 时间选择
const timeRange = ref('week')
const timeOptions = [
  { label: '近一周', value: 'week' },
  { label: '近一月', value: 'month' },
  { label: '近一年', value: 'year' }
]

// 不同时间段的图表数据
const riskTypeDataMap = {
  week: [
    { name: '机构人员配配置', value: 23, color: '#4D66F3' },
    { name: '责任制类', value: 23, color: '#38C5FF' },
    { name: '设施老化', value: 23, color: '#56DAD5' },
    { name: '维护不到位', value: 33, color: '#40E6C2' },
    { name: '车辆伤害', value: 25, color: '#36F097' },
    { name: '设备设施故障', value: 11, color: '#70FF8B' },
    { name: '钢炉运行问题', value: 9.6, color: '#95FF76' },
    { name: '燃气燃油泄漏', value: 7.4, color: '#E8FF54' },
    { name: '人为原因', value: 6.8, color: '#FFD049' },
    { name: '其他', value: 1.2, color: '#FF9D4D' }
  ],
  month: [
    { name: '机构人员配配置', value: 24, color: '#4D66F3' },
    { name: '责任制类', value: 20, color: '#38C5FF' },
    { name: '设施老化', value: 26, color: '#56DAD5' },
    { name: '维护不到位', value: 30, color: '#40E6C2' },
    { name: '车辆伤害', value: 23, color: '#36F097' },
    { name: '设备设施故障', value: 14, color: '#70FF8B' },
    { name: '钢炉运行问题', value: 8.5, color: '#95FF76' },
    { name: '燃气燃油泄漏', value: 8.2, color: '#E8FF54' },
    { name: '人为原因', value: 5.6, color: '#FFD049' },
    { name: '其他', value: 2.1, color: '#FF9D4D' }
  ],
  year: [
    { name: '机构人员配配置', value: 22, color: '#4D66F3' },
    { name: '责任制类', value: 21, color: '#38C5FF' },
    { name: '设施老化', value: 25, color: '#56DAD5' },
    { name: '维护不到位', value: 31, color: '#40E6C2' },
    { name: '车辆伤害', value: 27, color: '#36F097' },
    { name: '设备设施故障', value: 13, color: '#70FF8B' },
    { name: '钢炉运行问题', value: 10.2, color: '#95FF76' },
    { name: '燃气燃油泄漏', value: 6.8, color: '#E8FF54' },
    { name: '人为原因', value: 7.4, color: '#FFD049' },
    { name: '其他', value: 1.5, color: '#FF9D4D' }
  ]
}

// 当前选择的图表数据
const currentRiskTypeData = computed(() => {
  return riskTypeDataMap[timeRange.value]
})

// 为ECharts 3D饼图准备数据
const chartData = computed(() => {
  return currentRiskTypeData.value.map(item => ({
    name: item.name,
    value: item.value,
    percentage: item.value,
    itemStyle: {
      color: item.color
    }
  }))
})

// 处理时间变更
const handleTimeChange = () => {
  console.log('时间范围变更为:', timeRange.value)
}

onMounted(() => {
  handleTimeChange() // 初始化数据
})
</script>

<style scoped>
.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 15px;
  position: relative;
}

.com-select {
  margin-right: 20px;
}

.content-wrapper {
  display: flex;
  height: 100%;
  width: 100%;
  gap: 10px;
}

.chart-container {
  position: relative;
  width: 50%;
  height: 220px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.pie-bg {
  position: absolute;
  bottom: -12px;
  left: 50%;
  transform: translateX(-50%);
  width: 238px;
  height: 119px;
  background-image: url('@/assets/images/screen/heating/pie_bg.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center bottom;
  z-index: 1;
  pointer-events: none;
}

.legend-container {
  display: flex;
  flex-direction: column;
  width: 50%;
  padding-left: 3rem;
  justify-content: center;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  height: 20px;
  margin-bottom: 2px;
}

.legend-color {
  width: 8px;
  height: 8px;
  flex-shrink: 0;
}

.legend-text {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.legend-name {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.legend-value {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 14px;
  color: #FFFFFF;
  margin-left: 8px;
  flex-shrink: 0;
}

/* 响应式布局适配 */
@media screen and (min-width: 1920px) and (max-width: 2560px) {
  .panel-content {
    padding: 15px;
  }
}

@media screen and (max-width: 1919px) {
  .panel-content {
    padding: 12px;
  }
  
  .legend-item {
    height: 18px;
    margin-bottom: 1px;
  }
}

@media screen and (min-width: 2561px) {
  .panel-content {
    padding: 18px;
  }
}

/* 添加全屏模式(1080px高度)的适配 */
@media screen and (min-height: 1056px) and (max-height: 1080px) {
  .panel-content {
    padding: 15px;
  }
}

@media screen and (min-height: 940px) and (max-height: 1055px) {
  .panel-content {
    padding: 10px;
    gap: 10px;
  }
  
  .legend-name {
    font-size: 11px;
  }
  
  .legend-value {
    font-size: 13px;
  }
  
  .legend-item {
    height: 16px;
    margin-bottom: 1px;
  }
}

@media screen and (min-height: 900px) and (max-height: 940px) {
  .panel-content {
    padding: 8px;
    gap: 8px;
  }
  
  .legend-name {
    font-size: 10px;
  }
  
  .legend-value {
    font-size: 12px;
  }
  
  .legend-item {
    height: 15px;
    margin-bottom: 1px;
  }
}
</style> 