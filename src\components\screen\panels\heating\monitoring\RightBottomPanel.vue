<template>
  <PanelBox title="高发报警设备">
    <template #extra>
      <div class="com-select">
        <CommonSelect v-model="timeRange" :options="timeOptions" @change="handleTimeChange" />
      </div>
    </template>
    <div class="panel-content">
      <!-- 使用滚动表格组件 -->
      <ScrollTable 
        :columns="tableColumns" 
        :data="deviceList" 
        :autoScroll="true" 
        :scrollSpeed="3000"
        :tableHeight="tableHeight" 
        :visibleRows="6" 
        @row-click="openDetailModal"
      >
        <!-- 自定义排名列 -->
        <template #ranking="{ row, index }">
          <div class="rank-box" :class="`rank-${row.ranking}`">
            {{ row.ranking }}
          </div>
        </template>
      </ScrollTable>
    </div>
  </PanelBox>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import PanelBox from '@/components/screen/PanelBox.vue'
import CommonSelect from '@/components/screen/CommonSelect.vue'
import ScrollTable from '@/components/screen/common/ScrollTable.vue'
import { getHighFrequencyDevices } from '@/api/heating'

// 时间选择
const timeRange = ref('week')
const timeOptions = [
  { label: '近一周', value: 'week' },
  { label: '近一月', value: 'month' },
  { label: '近一年', value: 'year' }
]

// 表格列配置
const tableColumns = [
  { title: '排名', dataIndex: 'ranking', width: '10%', fontSize: '13px' },
  { 
    title: '设备名称', 
    dataIndex: 'deviceName', 
    width: '60%', 
    fontSize: '13px',
    ellipsis: true,
    tooltip: true
  },
  { title: '报警数量', dataIndex: 'alarmCount', width: '10%', fontSize: '13px' },
  { title: '处置完成率', dataIndex: 'resolveRate', width: '20%', fontSize: '13px' }
]

// 动态计算表格高度
const tableHeight = computed(() => {
  // 屏幕高度小于 1055px 时，表格高度为 225px 
  if (window.innerHeight < 1055) {
    return '320px'
  }
  if (window.innerHeight > 1054) {
    return '395px'
  }
  return '415px' // 可以根据不同分辨率动态调整
})

// 当前展示的设备列表数据
const deviceList = ref([])

// 获取设备列表数据
const fetchDeviceList = async () => {
  try {
    const dayIndex = timeRange.value === 'week' ? 7 : timeRange.value === 'month' ? 30 : 365
    const { data } = await getHighFrequencyDevices({
      dayIndex,
      pageNum: 1,
      pageSize: 10
    })
    
    if (data?.records) {
      deviceList.value = data.records.map((item, index) => ({
        ranking: index + 1,
        deviceName: item.deviceName,
        alarmCount: item.alarmCount,
        resolveRate: `${Math.round(item.handleRate * 100)}%`,
        deviceId: item.deviceCode
      }))
    }
  } catch (error) {
    console.error('获取高发报警设备列表失败:', error)
    deviceList.value = []
  }
}

// 处理时间范围变化
const handleTimeChange = (value) => {
  console.log('时间范围变更为:', value)
  fetchDeviceList()
}

// 打开详情弹窗
const openDetailModal = (row) => {
  console.log('打开设备详情', row.deviceId)
  // 实际项目中可能需要打开设备详情弹窗
}

onMounted(() => {
  fetchDeviceList()
})
</script>

<style scoped>

.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 15px; 
  position: relative;
}

.com-select {
  margin-right: 20px;
}

/* 排名样式 */
.rank-box {
  width: 21px;
  height: 21px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto;
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 14px;
  color: #FFFFFF;
}

.rank-1 {
  background: rgba(242,140,45,0.2);
  border: 1px solid #F28C2D;
}

.rank-2 {
  background: rgba(212,212,212,0.2);
  border: 1px solid #D4D4D4;
}

.rank-3 {
  background: rgba(254,208,138,0.2);
  border: 1px solid #FED08A;
}

.rank-4, .rank-5, .rank-6, .rank-7, .rank-8, .rank-9, .rank-10 {
  background: rgba(0,170,255,0.2);
  border: 1px solid #00AAFF;
  opacity: 0.7;
}

/* 点击行样式 */
:deep(.scroll-table tr) {
  cursor: pointer;
  transition: background-color 0.2s;
}

:deep(.scroll-table tr:hover) {
  background-color: rgba(0, 163, 255, 0.2) !important;
}

/* 媒体查询适配不同分辨率 */
@media screen and (min-width: 1920px) and (max-width: 2560px) {
  :deep(.scroll-table th) {
    font-size: 14px;
    height: 40px;
    line-height: 40px;
  }
  
  :deep(.scroll-table td) {
    font-size: 14px;
    height: 40px;
    line-height: 40px;
  }
}


/* 940px-1055px高度的屏幕特别优化 */
@media screen and (min-height: 910px) and (max-height: 1055px) {
  .panel-content {
    padding: 5px 10px;
    gap: 0px;
  }
  
  .com-select {
    margin-right: 25px;
  }
  
  .rank-box {
    width: 19px;
    height: 19px;
    font-size: 13px;
  }
  
  :deep(.scroll-table th) {
    font-size: 13px;
    height: 36px;
    line-height: 36px;
  }
  
  :deep(.scroll-table td) {
    font-size: 13px;
    height: 36px;
    line-height: 36px;
  }
}
</style>