
export const layerQueryInfo = {
  mis_gas_station_risk: [
    "gas_station_risk1",
    "gas_station_risk2",
    "gas_station_risk3",
    "gas_station_risk4",
  ],
  mis_gas_pipeline_risk: [
    "gas_pipeline_risk1",
    "gas_pipeline_risk2",
    "gas_pipeline_risk3",
    "gas_pipeline_risk4",
  ],
  mis_gas_monitor_video:[
      "gas_video_online",
      "gas_video_offline",
  ]
};

export const layerParentId = {
  supplyWater_Flowmeter: "supplyWater_Flowmeter",
  supplyWater_Manometer: "supplyWater_Manometer",
  supplyWater_WaterQualityAnalyzer: "supplyWater_WaterQualityAnalyzer",
  supplyWater_LevelGauge: "supplyWater_LevelGauge",
  supplyWater_Riometer: "supplyWater_Riometer",
  supplyWater_FireHydrant: "supplyWater_FireHydrant",
  supplyWater_Video: "supplyWater_Video",
  supplyWater_ManholeCover: "supplyWater_ManholeCover",
};

export const requestDataMap = {
  supplyWater_Flowmeter: "device-ll",
  supplyWater_Manometer: "device-yl",
  supplyWater_WaterQualityAnalyzer: "device",
  supplyWater_LevelGauge: "device",
  supplyWater_Riometer: "device",
  supplyWater_FireHydrant: "device",
  supplyWater_Video: "device",
  supplyWater_ManholeCover: "device",
};
