/**
 * 代码生成器默认配置
 */
export const defaultConfig = {
  // 基础信息
  basic: {
    moduleName: '', // 模块名称，用于生成文件名和组件名
    apiPrefix: '', // API前缀，用于生成API请求路径
    description: '', // 功能描述
    author: '', // 作者
    pageTitle: '' // 页面标题
  },
  
  // 表格配置
  table: {
    columns: [] // 表格列配置
    // 列配置示例：
    // {
    //   label: '用户名', // 列标签
    //   prop: 'username', // 属性名
    //   width: '120px', // 列宽
    //   align: 'left', // 对齐方式：left, center, right
    //   sortable: true, // 是否可排序
    //   fixed: '' // 是否固定：left, right, ''
    // }
  },
  
  // 搜索配置
  search: {
    fields: [] // 搜索字段配置
    // 字段配置示例：
    // {
    //   label: '用户名', // 字段标签
    //   prop: 'username', // 属性名
    //   type: 'input', // 组件类型：input, select, date, daterange, number
    //   placeholder: '请输入用户名', // 占位文本
    //   options: [] // 选择器选项，仅当type为select时有效
    // }
  },
  
  // 表单配置
  form: {
    fields: [] // 表单字段配置
    // 字段配置示例：
    // {
    //   label: '用户名', // 字段标签
    //   prop: 'username', // 属性名
    //   type: 'input', // 组件类型：input, select, date, time, number, textarea, switch, radio, checkbox, upload
    //   placeholder: '请输入用户名', // 占位文本
    //   required: true, // 是否必填
    //   rule: 'email', // 验证规则：email, phone, url, number, integer, regexp
    //   regexp: '', // 自定义正则表达式，仅当rule为regexp时有效
    //   errorMsg: '请输入有效的用户名', // 验证失败时的错误提示
    //   options: [] // 选项，仅当type为select, radio, checkbox时有效
    // }
  },
  
  // API配置
  api: {
    methods: {
      list: true, // 获取列表
      detail: true, // 获取详情
      create: true, // 创建
      update: true, // 更新
      delete: true // 删除
    },
    customMethods: [] // 自定义API方法
    // 自定义方法示例：
    // {
    //   name: 'exportData', // 方法名称
    //   method: 'get', // 请求方法：get, post, put, delete
    //   url: '/export', // URL路径
    //   params: 'query', // 参数
    //   description: '导出数据' // 描述
    // }
  }
}

/**
 * 模板变量映射
 */
export const templateVariables = {
  // 基础变量
  moduleName: config => config.basic.moduleName,
  ModuleName: config => {
    const name = config.basic.moduleName
    return name.charAt(0).toUpperCase() + name.slice(1)
  },
  apiPrefix: config => config.basic.apiPrefix,
  description: config => config.basic.description,
  author: config => config.basic.author,
  pageTitle: config => config.basic.pageTitle,
  
  // 日期相关
  currentDate: () => {
    const date = new Date()
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
  },
  
  // 表格相关
  hasTable: config => config.table.columns.length > 0,
  tableColumns: config => config.table.columns,
  
  // 搜索相关
  hasSearch: config => config.search.fields.length > 0,
  searchFields: config => config.search.fields,
  
  // 表单相关
  hasForm: config => config.form.fields.length > 0,
  formFields: config => config.form.fields,
  
  // API相关
  apiMethods: config => config.api.methods,
  hasCustomMethods: config => config.api.customMethods.length > 0,
  customMethods: config => config.api.customMethods
}

/**
 * 示例配置
 */
export const exampleConfig = {
  basic: {
    moduleName: 'user',
    apiPrefix: '/api/users',
    description: '用户管理模块，包含用户的增删改查功能',
    author: '系统管理员',
    pageTitle: '用户管理'
  },
  table: {
    columns: [
      {
        label: '用户名',
        prop: 'username',
        width: '120px',
        align: 'left',
        sortable: true,
        fixed: ''
      },
      {
        label: '姓名',
        prop: 'name',
        width: '120px',
        align: 'left',
        sortable: false,
        fixed: ''
      },
      {
        label: '邮箱',
        prop: 'email',
        width: '180px',
        align: 'left',
        sortable: false,
        fixed: ''
      },
      {
        label: '角色',
        prop: 'role',
        width: '120px',
        align: 'center',
        sortable: false,
        fixed: ''
      },
      {
        label: '状态',
        prop: 'status',
        width: '100px',
        align: 'center',
        sortable: false,
        fixed: ''
      },
      {
        label: '创建时间',
        prop: 'createTime',
        width: '180px',
        align: 'center',
        sortable: true,
        fixed: ''
      }
    ]
  },
  search: {
    fields: [
      {
        label: '用户名',
        prop: 'username',
        type: 'input',
        placeholder: '请输入用户名'
      },
      {
        label: '角色',
        prop: 'role',
        type: 'select',
        placeholder: '请选择角色',
        options: [
          {
            label: '管理员',
            value: 'admin'
          },
          {
            label: '普通用户',
            value: 'user'
          }
        ]
      },
      {
        label: '状态',
        prop: 'status',
        type: 'select',
        placeholder: '请选择状态',
        options: [
          {
            label: '启用',
            value: '1'
          },
          {
            label: '禁用',
            value: '0'
          }
        ]
      },
      {
        label: '创建时间',
        prop: 'createTime',
        type: 'daterange',
        placeholder: '请选择创建时间范围'
      }
    ]
  },
  form: {
    fields: [
      {
        label: '用户名',
        prop: 'username',
        type: 'input',
        placeholder: '请输入用户名',
        required: true,
        rule: '',
        errorMsg: '请输入用户名'
      },
      {
        label: '姓名',
        prop: 'name',
        type: 'input',
        placeholder: '请输入姓名',
        required: true,
        rule: '',
        errorMsg: '请输入姓名'
      },
      {
        label: '邮箱',
        prop: 'email',
        type: 'input',
        placeholder: '请输入邮箱',
        required: true,
        rule: 'email',
        errorMsg: '请输入有效的邮箱地址'
      },
      {
        label: '角色',
        prop: 'role',
        type: 'select',
        placeholder: '请选择角色',
        required: true,
        rule: '',
        errorMsg: '请选择角色',
        options: [
          {
            label: '管理员',
            value: 'admin'
          },
          {
            label: '普通用户',
            value: 'user'
          }
        ]
      },
      {
        label: '状态',
        prop: 'status',
        type: 'radio',
        required: true,
        rule: '',
        errorMsg: '请选择状态',
        options: [
          {
            label: '启用',
            value: '1'
          },
          {
            label: '禁用',
            value: '0'
          }
        ]
      },
      {
        label: '备注',
        prop: 'remark',
        type: 'textarea',
        placeholder: '请输入备注',
        required: false
      }
    ]
  },
  api: {
    methods: {
      list: true,
      detail: true,
      create: true,
      update: true,
      delete: true
    },
    customMethods: [
      {
        name: 'resetPassword',
        method: 'post',
        url: '/reset-password',
        params: 'id',
        description: '重置用户密码'
      },
      {
        name: 'exportUsers',
        method: 'get',
        url: '/export',
        params: 'query',
        description: '导出用户数据'
      }
    ]
  }
}
